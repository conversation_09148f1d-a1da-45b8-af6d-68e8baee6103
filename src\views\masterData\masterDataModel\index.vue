<template>
    <div class="tabSwitchClass">
        <TabSwitch :titleList="tabList" @change="handleTabChange" />
    </div>
    <SplitPanes class="App-theme">
        <template #left>
            <!-- 左侧目录树 -->
            <tree-nav ref="treeNavRef" @node-click="handleTreeNodeClick" />
        </template>
        <template #right>
            <!-- 右侧内容区域 -->
            <main-content ref="mainContentRef" />
        </template>
    </SplitPanes>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, provide } from 'vue'
import SplitPanes from '@/components/SplitPanes/index';
import TreeNav from './components/TreeNav.vue'
import MainContent from './components/MainContent.vue'
import TabSwitch from '@/components/tabSwitch/index.vue'
import { treeData } from './mockData'

// 组件引用
const treeNavRef = ref(null)
const mainContentRef = ref(null)

/**
 * 标签页数据 - 使用reactive创建响应式数据
 */
const tabList = reactive([
    {
        label: '实体',
        value: 'entity',
        isActive: true
    },
    {
        label: '分类',
        value: 'category',
        isActive: false
    },
    {
        label: 'BOM',
        value: 'bom',
        isActive: false
    }
])

/**
 * 当前选中的标签页
 */
const currentTab = ref('entity')

/**
 * 将当前选中的标签页值提供给子组件
 */
provide('currentTab', currentTab)

/**
 * 处理标签页切换事件
 * @param {String|Boolean} value - 标签页值或false（当点击已激活的标签页时）
 */
const handleTabChange = (value) => {
    if (value === false) return

    console.log('标签页切换:', value)
    currentTab.value = value

    // 这里可以根据标签页切换执行不同的逻辑
    // 例如：加载不同类型的数据、更新视图等
}

/**
 * 处理树节点点击事件
 * @param {Object} data - 节点数据
 * @param {Object} node - 节点对象
 */
const handleTreeNodeClick = (data, node) => {
    console.log('节点点击:', data.label)
    // 将选中的节点数据传递给MainContent组件
    if (mainContentRef.value) {
        mainContentRef.value.updateContentType(data)
    }
}

/**
 * 组件挂载后的处理
 */
onMounted(async () => {
    // 等待下一个DOM更新周期，确保组件已完全渲染
    await nextTick()

    // 默认选中第一个根节点
    if (treeNavRef.value && treeData.length > 0) {
        // 模拟点击第一个根节点
        const firstNode = treeData[0]
        handleTreeNodeClick(firstNode)
    }
})
</script>

<style lang="scss" scoped>
.tabSwitchClass{
    margin: 0 auto;
    margin-top: 10px;
    width: 50%
}
</style>
