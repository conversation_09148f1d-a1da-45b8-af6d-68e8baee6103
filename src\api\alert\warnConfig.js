import request from '@/utils/request';
// 新增、更新告警配置
export function saveOrUpdateConfig(data) {
  return request({
    url: '/alert/conf/saveOrUpdate',
    method: 'post',
    data,
  });
}

// 告警配置详情
export function getConfigInfo(params) {
  return request({
    url: '/alert/conf/getInfo',
    method: 'get',
    params,
  });
}

// 获取所有用户
export function getAllUser() {
  return request({
    url: '/system/user/getAllSysUser',
    method: 'get',
  });
}

// 新增告警人
export function createWarningMan(data) {
  return request({
    url: '/alert/conf/systemAlertSub',
    method: 'post',
    data,
  });
}

// 新增告警人
export function deleteWarningMan(data) {
  return request({
    url: '/alert/conf/systemSubRemove/' + data,
    method: 'delete',
    data,
  });
}

// 获取告警人列表
export function getAllList(params) {
  return request({
    url: '/alert/conf/list',
    method: 'get',
    params,
  });
}
