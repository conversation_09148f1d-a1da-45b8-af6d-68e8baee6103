<template>
  <div class="monaco-editor-example">
    <h3>Monaco Editor 示例</h3>
    
 

    <div class="editor-container">
      <MonacoEditor
        v-model="code"
        :height="400"
        language="javascript"
        @content-change="onContentChange"
        @write-to-field="onWriteToField"
      />
    </div>

    <div class="output">
      <h4>编辑器内容:</h4>
      <pre>{{ code }}</pre>
    </div>

    <div class="events">
      <h4>事件日志:</h4>
      <div class="event-log">
        <div v-for="(event, index) in eventLog" :key="index" class="event-item">
          {{ event }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MonacoEditor from '@/components/monacoEditor/index.vue';

const code = ref(`// 这是一个示例代码
function hello() {
  console.log('Hello Monaco Editor!');
  // 右键点击可以看到自定义菜单项
}`);

const hideDefaultMenu = ref(true);
const eventLog = ref([]);

const addEvent = (message) => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);
  if (eventLog.value.length > 10) {
    eventLog.value.pop();
  }
};

const onContentChange = (content) => {
  addEvent(`内容变化: ${content.length} 个字符`);
};

const onWriteToField = (data) => {
  addEvent(`触发写入字段事件: 选中文本="${data.selectedText}"`);
  console.log('写入字段事件数据:', data);
};
</script>

<style scoped>
.monaco-editor-example {
  padding: 20px;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.editor-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
}

.output {
  margin-bottom: 20px;
}

.output pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.events {
  margin-bottom: 20px;
}

.event-log {
  background: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.event-item {
  padding: 2px 0;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.event-item:first-child {
  color: #333;
  font-weight: bold;
}
</style>
