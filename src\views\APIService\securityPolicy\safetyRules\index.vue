<template>
  <div class="App-theme">
    <TabSwitch class="tab-container" :title-list="tab" @change="getData"></TabSwitch>

    <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
    <el-form ref="" label-position="left" label-width="auto">
      <el-form-item label="规则名称" prop="roleName">
        <!-- <el-row :gutter="10"> -->
        <!-- <el-col :span="20"> -->
        <el-input v-model="groupName" placeholder="请输入名称" clearable style="width: 250px" />
        <!-- </el-col> -->
        <!-- <el-col :span="2"> -->
        <el-button type="primary" style="margin-left: 10px" icon="Search" @click="listPage"
          >查询</el-button
        >
        <!-- @keyup.enter="handleQuery" -->
        <!-- </el-col> -->
        <!-- <el-col :span="1.5">
            <el-button icon="Plus" type="primary" @click="jumpTo">新增规则</el-button>
          </el-col> -->
        <!-- </el-row> -->
      </el-form-item>
    </el-form>
    <el-button icon="Plus" type="primary" @click="jumpTo">新增规则</el-button>
    <div class="table-box">
      <div v-show="activeName === 'first'" label="流量控制" name="first">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          height="100%"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button>
              <el-button type="text" size="small" @click="relating(scope)">关联服务</el-button>
              <el-button type="text" size="small" @click="del(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-show="activeName === 'second'" label="熔断降级" name="second">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="100%"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
            <!-- 如果是 ruleCode -->
            <template v-if="item.prop === 'ruleCode'" #default="scope">
              {{ ruleCode(scope.row.ruleCode) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button>
              <el-button type="text" size="small" @click="relating(scope)">关联服务</el-button>
              <el-button type="text" size="small" @click="del(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table></div
      >

      <div v-show="activeName === 'third'" label="缓存配置" name="third">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          height="100%"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button>
              <el-button type="text" size="small" @click="relating(scope)">关联服务</el-button>
              <el-button type="text" size="small" @click="del(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table></div
      >
    </div>

    <div>
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="listPage"
      />
    </div>
    <!-- </el-tabs> -->

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeSpatial"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="left" label-width="auto">
        <div v-if="spatialTitle === '新增流量控制规则' || spatialTitle === '修改流量控制规则'">
          <el-form-item label="规则名称" prop="flowName">
            <el-input v-model="form.flowName" placeholder="请输入" />
          </el-form-item>

          <!-- <el-form-item label="时间窗口" prop="unitTime"> -->
          <!-- <el-input v-model="form.unitTime" placeholder="请输入" /> -->
          <!-- </el-form-item> -->
          <el-form-item label="QPS 阈值" prop="flowCount">
            <el-input v-model="form.flowCount" placeholder="请输入" />
          </el-form-item>
        </div>

        <div v-if="spatialTitle === '新增熔断降级规则' || spatialTitle === '修改熔断降级规则'">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="form.ruleName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="熔断策略" prop="ruleCode">
            <el-radio-group v-model="form.ruleCode" @change="handleChange">
              <el-radio label="SLOW_REQUEST_RATIO">慢调用比例</el-radio>
              <el-radio label="ERROR_RATIO">异常比例</el-radio>
              <el-radio label="ERROR_COUNT">异常数</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="showForms">
            <el-form-item
              v-if="form.ruleCode === 'SLOW_REQUEST_RATIO'"
              label="响应时间(RT)"
              prop="count"
            >
              <el-input v-model="form.count" placeholder="最大响应时间" type="number">
                <template #suffix>(ms)</template>
              </el-input>
            </el-form-item>
            <el-form-item
              v-if="form.ruleCode === 'SLOW_REQUEST_RATIO'"
              label="比例阈值"
              prop="slowRatioThreshold"
            >
              <el-input
                v-model="form.slowRatioThreshold"
                placeholder="取值范围[0.0-1.0]"
                type="number"
              >
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="慢调用比例模式下为慢调用比例的阈值(超出慢调用临界RT为慢调用);异常比例/异常数模式下为对应阈值"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-else label="阈值" prop="count">
              <el-input
                v-model="form.count"
                :placeholder="form.ruleCode === 'ERROR_COUNT' ? '至少是1' : '取值范围[0.0-1.0]'"
                type="number"
              >
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="慢调用比例模式下为慢调用比例的阈值(超出慢调用临界RT为慢调用);异常比例/异常数模式下为对应阈值"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>

              <!-- 描述 -->
            </el-form-item>
            <!-- 时间窗口 -->
            <el-form-item label="时间窗口" prop="statIntervalMs">
              <el-input v-model="form.statIntervalMs" placeholder="请输入" type="number">
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="单位为 ms,如 60*1000 代表分钟级"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <template #suffix>(ms)</template>
              </el-input>
            </el-form-item>
            <!-- 最少请求数 -->
            <el-form-item label="最少请求数" prop="minRequestAmount">
              <el-input v-model="form.minRequestAmount" placeholder="请输入" type="number">
                <template #suffix> (个) </template>
              </el-input>
            </el-form-item>
            <!-- 熔断时长 -->
            <el-form-item label="熔断时长" prop="timeWindow">
              <el-input v-model="form.timeWindow" placeholder="请输入" type="number">
                <template #suffix> (S) </template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div v-if="spatialTitle === '新增缓存配置规则' || spatialTitle === '修改缓存配置规则'">
          <!-- name -->
          <!-- cacheTime -->
          <!-- type -->
          <!-- maxCacheBody -->
          <!-- maxCacheHeader -->
          <!-- maxCacheParameter -->
          <el-form-item label="规则名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="缓存时长" prop="cacheTime">
            <el-input
              v-model="form.cacheTime"
              placeholder="缓存时长"
              type="number"
              oninput="if(value>1000)value=1000"
            >
              <template #suffix> (S) </template>
            </el-input>
          </el-form-item>
          <el-form-item label="缓存类型" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio label="URL">URL</el-radio>
              <el-radio label="URL_QUERY">URL_QUERY</el-radio>
              <el-radio label="URL_BODY">URL_BODY</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="最大缓存内容" prop="maxCacheBody">
            <el-input
              v-model="form.maxCacheBody"
              placeholder="最大缓存接口的结果"
              type="number"
              max="1000"
              :maxlength="4"
              oninput="if(value>1000)value=1000"
            >
              <template #suffix> (KB) </template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="apiVisible" title="关联服务" width="40%" append-to-body :draggable="true">
      <el-tree-transfer
        v-if="apiVisible"
        v-model="valueTree"
        :data="dataTree"
        :to-data="toData"
        :children-is-empty="true"
        :show-filter="true"
        :node-props="nodeProps"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeApi">取 消</el-button>
          <el-button type="primary" @click="submitApi">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { nextTick, ref } from 'vue';

  import {
    addCache,
    addDegrade,
    addFlow,
    delCache,
    delDegrade,
    delFlow,
    getCacheList,
    getDegradeList,
    getFlowList,
    updateCache,
    updateDegrade,
    updateFlow,
    getFlowAuthList,
    getCacheAuthList,
    getDegradeAuthList,
    saveFlowAuth,
    saveCacheAuth,
    saveDegradeAuth,
  } from '@/api/APIService';
  // el-tree-transfer
  import ElTreeTransfer from '../../components/el-tree-transfer/el-tree-transfer.vue';
  import TabSwitch from '../../../APIService/bazaarApproval/applyApprovalDetail/components/TabSwitch.vue';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();
  const tab = ref([
    { value: 'first', label: '流量控制', isActive: true },
    { value: 'second', label: '熔断降级', isActive: false },
    { value: 'third', label: '缓存配置', isActive: false },
  ]);
  // 使用计算属性判断 ruleCode
  const ruleCode = (ruleCode) => {
    if (ruleCode === 'ERROR_RATIO') {
      return '异常比例';
    } else if (ruleCode === 'SLOW_REQUEST_RATIO') {
      return '慢调用率';
    } else if (ruleCode === 'ERROR_COUNT') {
      return '异常数';
    }
  };
  const validateCount = (rule, value, callback) => {
    if (form.value.ruleCode === 'SLOW_REQUEST_RATIO') {
      if (!value || isNaN(value) || value < 1 || value > 30000) {
        callback(new Error('必须是 1 到 30000 之间的数字'));
      } else {
        callback();
      }
    } else if (form.value.ruleCode === 'ERROR_RATIO') {
      if (!value || isNaN(value) || value < 0.0 || value > 1.0) {
        callback(new Error('阈值必须是 0.0 到 1.0 之间的数字'));
      } else {
        callback();
      }
    } else if (form.value.ruleCode === 'ERROR_COUNT') {
      if (!value || isNaN(value) || value < 1 || value > 100) {
        callback(new Error('阈值必须是 1 到 100 之间的数字'));
      } else {
        callback();
      }
    }
  };
  const validateSlowR = (rule, value, callback) => {
    if (form.value.ruleCode === 'SLOW_REQUEST_RATIO') {
      if (!value || isNaN(value) || value < 0.0 || value > 1.0) {
        callback(new Error('阈值必须是 0.0 到 1.0 之间的数字'));
      } else {
        callback();
      }
    } else if (form.value.ruleCode === 'ERROR_RATIO') {
      if (!value || isNaN(value) || value < 0.0 || value > 1.0) {
        callback(new Error('阈值必须是 0.0 到 1.0 之间的数字'));
      } else {
        callback();
      }
    } else if (form.value.ruleCode === 'ERROR_COUNT') {
      if (!value || isNaN(value) || value < 1 || value > 100) {
        callback(new Error('阈值必须是 1 到 100 之间的数字'));
      } else {
        callback();
      }
    }
  };
  const data = reactive({
    form: {},
    rules: {
      flowName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        // 不允许特殊字符
        { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: '不允许特殊字符', trigger: 'change' },
      ],
      unitTime: [
        { required: true, message: '请输入时间窗口', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      flowCount: [
        { required: true, message: '请输入阈值', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      ruleName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: '不允许特殊字符', trigger: 'change' },
      ],
      ruleCode: [
        { required: true, message: '请选择熔断策略', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      slowRatioThreshold: [
        { required: true, trigger: 'change', validator: validateSlowR },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      count: [{ required: true, trigger: 'change', validator: validateCount }],

      statIntervalMs: [
        { required: true, message: '请输入统计间隔', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      minRequestAmount: [
        { required: true, message: '请输入最小请求数', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      timeWindow: [
        { required: true, message: '请输入熔断时长', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [{ required: true, message: '请输入类型', trigger: 'change' }],
      cacheTime: [
        { required: true, message: '请输入缓存时间', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      maxCacheBody: [
        { required: true, message: '请输入缓存大小', trigger: 'change' },
        // { min: 1, max: 1001, message: '长度在 2 到 1000 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const maxCount = ref(5);
  const total = ref(1);

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([]);

  const activeName = ref('first');
  const handleClick = (data) => {
    if (data === 'first') {
      getDataUtil();
    } else if (data === 'second') {
      getDegradeListUtil();
    } else if (data === 'third') {
      getCacheListUtil();
    }
  };

  const listPage = async () => {
    if (activeName.value === 'first') {
      getDataUtil();
    } else if (activeName.value === 'second') {
      getDegradeListUtil();
    } else if (activeName.value === 'third') {
      getCacheListUtil();
    }
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getDataUtil = async () => {
    // const params = new URLSearchParams({}).toString();
    closeSpatial();
    columns.value = [
      {
        key: 0,
        label: `规则 ID`,
        visible: true,
        prop: 'flowId',
        width: '100',
        minWidth: '100',
      },
      {
        key: 1,
        label: `规则名称`,
        visible: true,
        prop: 'flowName',
        width: '100',
        minWidth: '100',
      },
      //   {
      //     key: 2,
      //     label: `时间窗口/s`,
      //     visible: true,
      //     prop: 'unitTime',
      //     minWidth: '100',
      //   },
      {
        key: 3,
        label: `QPS 阈值`,
        visible: true,
        prop: 'flowCount',
        minWidth: '100',
      },
      {
        key: 4,
        label: `关联服务`,
        visible: true,
        prop: 'apiCount',
        minWidth: '100',
      },
      {
        key: 5,
        label: `修改时间`,
        visible: true,
        prop: 'updateTime',
        minWidth: '100',
      },
    ];

    try {
      const result = await getFlowList({
        ...queryParams.value,
        keyword: groupName.value,
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });
      tableData.value = result.rows;
      total.value = result.total;
      // getGroupList();
    } catch (error) {
      console.error('Error fetching table data:', error);
    }
  };

  const groupName = ref('');

  const addUtil = async () => {
    try {
      const result = await addFlow({
        ...form.value,
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });

      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      getDataUtil();
    } catch (error) {
      console.error('Error submitting form data:', error);
    }
  };
  const getData = (data) => {
    if (!data) return false;
    console.log(data);
    activeName.value = data;
    handleClick(data);
  };
  const deleteDataUtil = async (id) => {
    //  删除前提示
    const result = await proxy.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    if (!result) return;

    try {
      const result = await delFlow({
        flowId: id,
      });
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      getDataUtil();
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const updateGroupUtil = async () => {
    try {
      const result = await updateFlow({
        ...form.value,
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);

      getDataUtil();
    } catch (error) {
      console.error('Error updating data:', error);
    }
  };

  const jumpTo = (row) => {
    spatialVisible.value = true;
    if (activeName.value === 'first') {
      spatialTitle.value = '新增流量控制规则';
    } else if (activeName.value === 'second') {
      form.value = {
        degradeType: 0,
      };
      spatialTitle.value = '新增熔断降级规则';
    } else if (activeName.value === 'third') {
      spatialTitle.value = '新增缓存配置规则';
    }
  };

  const revamp = (row) => {
    spatialVisible.value = true;
    if (activeName.value === 'first') {
      spatialTitle.value = '修改流量控制规则';
      form.value = {
        flowId: row.row.flowId,
        flowName: row.row.flowName,
        unitTime: row.row.unitTime,
        flowCount: row.row.flowCount,
      };
    } else if (activeName.value === 'second') {
      spatialTitle.value = '修改熔断降级规则';
      form.value = {
        id: row.row.id,
        ...row.row,
      };
    } else if (activeName.value === 'third') {
      spatialTitle.value = '修改缓存配置规则';
      form.value = {
        id: row.row.id,
        name: row.row.name,
        cacheTime: row.row.cacheTime,
        type: row.row.type,
        maxCacheBody: row.row.maxCacheBody,
      };
    }
  };

  const del = (row) => {
    if (activeName.value === 'first') {
      deleteDataUtil(row.row.flowId);
    } else if (activeName.value === 'second') {
      delDegradeUtl(row.row.id);
    } else if (activeName.value === 'third') {
      delCacheUtil(row.row.id);
    }
  };

  const delDegradeUtl = async (id) => {
    const confirm = await proxy.$modal.confirm('是否确认删除熔断降级规则？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    if (!confirm) return;

    const res = await delDegrade({
      id,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getDegradeListUtil();
  };

  const updateDegradeUtil = async () => {
    const res = await updateDegrade({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getDegradeListUtil();
  };

  const apiVisible = ref();
  const rowId = ref();

  const authListMap = {
    first: getFlowAuthList,
    second: getDegradeAuthList,
    third: getCacheAuthList,
  };

  const relating = async (row) => {
    rowId.value = row.row.flowId || row.row.id;
    const fetchAuthList = authListMap[activeName.value];

    if (!fetchAuthList) {
      return proxy.$modal.msgError('无效的活动名称');
    }

    const res = await fetchAuthList({
      id: rowId.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data) return;

    dataTree.value = transform(res.data.left);
    toData.value = transform(res.data.right);
    apiVisible.value = true;
  };

  function unTransform(data) {

    const apiIds = [];
    function traverse(node) {
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          apiIds.push(child.id);
          traverse(child); // Recursively traverse children
        });
      }
    }

    Object.values(data).forEach((node) => traverse(node));

    return apiIds;
  }

  function transform(data) {
    return data.map((item) => {
      const children = item.child && item.child.length > 0 ? transform(item.child) : [];
      let pid = 0;
      if (item.child.length <= 0) {
        pid = item.groupId;
      }
      return {
        id: item.apiId !== null ? item.apiId : item.groupId,
        label: item.apiName !== null ? item.apiName : item.groupName,
        pid,
        children,
      };
    });
  }

  const submitApi = () => {
    apiVisible.value = false;
    saveFlowAuthUtil();
  };

  const saveFlowAuthUtil = async () => {
    if (activeName.value === 'first') {
      const res = await saveFlowAuth({
        id: rowId.value,
        // apiId: unTransform(toData.value),
        apiId:valueTree.value
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    } else if (activeName.value === 'second') {
      const res = await saveDegradeAuth({
        id: rowId.value,
        // apiId: unTransform(toData.value),
        apiId:valueTree.value
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    } else if (activeName.value === 'third') {
      const res = await saveCacheAuth({
        id: rowId.value,
        // apiId: unTransform(toData.value),
        apiId:valueTree.value
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    }
    listPage()
  };

  const closeApi = () => {
    apiVisible.value = false;
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
    proxy.$refs.formRef?.resetFields();
  };

  const submitSpatial = async () => {
    const ref = await proxy.$refs.formRef.validate((valid) => valid);
    if (!ref) return;
    if (spatialTitle.value === '修改流量控制规则') {
      updateGroupUtil();
    } else if (spatialTitle.value === '新增流量控制规则') {
      addUtil();
    } else if (spatialTitle.value === '修改熔断降级规则') {
      updateDegradeUtil();
    } else if (spatialTitle.value === '新增熔断降级规则') {
      addDegradeUtil();
    } else if (spatialTitle.value === '新增缓存配置规则') {
      addCacheUtil();
    } else if (spatialTitle.value === '修改缓存配置规则') {
      updateCacheUtil();
    }
  };

  const nodeProps = ref({
    label: 'label',
    children: 'children',
    value: 'id', // value field
    disabled: 'disabled',
  });

  const valueTree = ref();

  watch(valueTree, (newValue) => {
    console.log('el-tree-transfer-pro', newValue);
  });

  const dataTree = ref();

  //   [
  //     // {
  //     //   id: 1,
  //     //   label: 'Level one 1',
  //     //   pid: 0,
  //     //   children: [
  //     //     {
  //     //       id: 4,
  //     //       pid: 1,
  //     //       label: 'Level two 1-1',
  //     //       children: [
  //     //         {
  //     //           id: 9,
  //     //           pid: 4,
  //     //           label: 'Level third 1-1-1',
  //     //         },
  //     //         {
  //     //           id: 10,
  //     //           pid: 4,
  //     //           label: 'Level third 1-1-2',
  //     //         },
  //     //       ],
  //     //     },
  //     //   ],
  //     // },
  //     // {
  //     //   id: 2,
  //     //   pid: 0,
  //     //   label: 'Level one 2',
  //     //   children: [
  //     //     {
  //     //       id: 5,
  //     //       pid: 2,
  //     //       label: 'Level two 2-1',
  //     //     },
  //     //     {
  //     //       id: 6,
  //     //       pid: 2,
  //     //       label: 'Level two 2-2',
  //     //     },
  //     //   ],
  //     // },
  //     // {
  //     //   id: 3,
  //     //   pid: 0,
  //     //   label: 'Level one 3',
  //     //   children: [
  //     //     {
  //     //       id: 7,
  //     //       pid: 3,
  //     //       label: 'Level two 3-1',
  //     //     },
  //     //     {
  //     //       id: 8,
  //     //       pid: 3,
  //     //       label: 'Level two 3-2',
  //     //     },
  //     //   ],
  //     // },
  //   ]
  const toData = ref([]);

  const getDegradeListUtil = async () => {
    closeSpatial();
    columns.value = [
      {
        key: 0,
        label: `规则 ID`,
        visible: true,
        prop: 'id',
        width: '100',
        minWidth: '100',
      },
      {
        key: 1,
        label: `规则名称`,
        visible: true,
        prop: 'ruleName',
        width: '100',
        minWidth: '100',
      },
      {
        key: 2,
        label: `熔断策略`,
        visible: true,
        prop: 'ruleCode',
        minWidth: '100',
      },
      {
        key: 3,
        label: `慢调用比例阈值`,
        visible: true,
        prop: 'slowRatioThreshold',
        minWidth: '100',
      },
      {
        key: 4,
        label: `统计时长`,
        visible: true,
        prop: 'statIntervalMs',
        minWidth: '100',
      },
      {
        key: 5,
        label: `最少请求数`,
        visible: true,
        prop: 'minRequestAmount',
        minWidth: '100',
      },
      {
        key: 6,
        label: `熔断时长`,
        visible: true,
        prop: 'timeWindow',
        minWidth: '100',
      },
      {
        key: 7,
        label: `修改时间`,
        visible: true,
        prop: 'updateTime',
        minWidth: '100',
      },
    ];
    const res = await getDegradeList({
      ...queryParams.value,
      keyword: groupName.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.rows;
    total.value = res.total;
    console.log(res);
  };
  const addDegradeUtil = async () => {
    const res = await addDegrade({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getDegradeListUtil();
  };
  const getCacheListUtil = async () => {
    closeSpatial();
    columns.value = [
      {
        key: 0,
        label: `规则 ID`,
        visible: true,
        prop: 'id',
        width: '100',
        minWidth: '100',
      },
      {
        key: 1,
        label: `规则名称`,
        visible: true,
        prop: 'name',
        width: '100',
        minWidth: '100',
      },
      {
        key: 2,
        label: `缓存类型`,
        visible: true, // 是否显示
        prop: 'type',
        minWidth: '100',
      },
      {
        key: 3,
        label: `最大缓存体积`,
        visible: true, // 是否显示
        prop: 'maxCacheBody',
        minWidth: '100',
      },
      {
        key: 4,
        label: `修改时间`,
        visible: true,
        prop: 'updateTime',
        minWidth: '100',
      },
    ];

    const res = await getCacheList({
      ...queryParams.value,
      keyword: groupName.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.rows;
    total.value = res.total;
    console.log(res);
  };
  const addCacheUtil = async () => {
    const res = await addCache({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getCacheListUtil();
  };
  const updateCacheUtil = async () => {
    const res = await updateCache({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getCacheListUtil();
  };
  const delCacheUtil = async (id) => {
    const confirm = await proxy.$modal.confirm('是否确认删除缓存规则？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    if (!confirm) return;
    const res = await delCache({
      id,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getCacheListUtil();
  };
  onMounted(async () => {
    await getDataUtil();
  });

  watch(workspaceId, (val) => {
    listPage();
  });

  const rulesType = ref();
  const showForms = ref(true);

  const handleChange = async (val) => {
    showForms.value = false;

    await nextTick(() => {
      proxy.$refs.formRef.clearValidate();
    });

    rulesType.value = val;

    form.value = {
      flowName: groupName.value,
      ruleName: form.value.ruleName,
      ruleCode: form.value.ruleCode,
      id: form.value.id,
    };

    await nextTick(() => {
      showForms.value = true;
    });
  };
</script>

<style lang="scss" scoped>
  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
    // background-color: #fff;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    .table-box {
      height: calc(100% - 220px);
      margin-top: 20px;
    }
    .pagination-container {
      position: relative;
    }
    .el-form-item {
      justify-content: right;
      :deep .el-form-item__content {
        flex: none;
        width: 340px;
      }
    }
  }
  .tab-container {
    width: 560px;
    margin: 0 auto;
    margin-bottom: 20px;
  }
</style>
