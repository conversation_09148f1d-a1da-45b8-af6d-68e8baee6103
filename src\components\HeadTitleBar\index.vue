<template>
  <template v-if="pullDown">
    <el-form inline>
      <!-- <el-row :gutter="0">
        <el-col :span="12"> -->
      <el-form-item v-if="isAdmin" class="form-item" label="租户">
        <el-select v-model="tenantId" placeholder="租户名称" @change="getWorkspace">
          <el-option
            v-for="data in tenantList"
            :key="data.tenantId"
            :label="data.tenantName"
            :value="data.tenantId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-else>
        <el-select v-model="tenantId" class="show" @change="getWorkspace">
          <!-- <el-option v-for="data in tenantList" :key="data.tenantId" :label="data.tenantName" :value="data.tenantId"> -->
          <!-- </el-option> -->
        </el-select>
      </el-form-item>
      <!-- </el-col> -->
      <!-- <el-col :span="12"> -->
      <el-form-item v-if="!isWorkspacePage" label="工作空间" class="form-item">
        <el-select v-model="workSpaceId" placeholder="工作空间" @change="getDatasourceList">
          <el-option
            v-for="data in workSpaceList"
            :key="data.workspaceId"
            :label="data.workspaceName"
            :value="data.workspaceId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <!-- </el-col> -->
      <!-- </el-row> -->
    </el-form>
  </template>
</template>

<script setup>
  import { getWorkspaceList } from '@/api/dataSourceManageApi';
  import { getInfo } from '@/api/login';
  import { getTenantList } from '@/api/system/user';
  //   import useHeaderStore from '@/store/modules/header';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { watch } from 'vue';
  import { useRouter } from 'vue-router';

  import useUserStore from '@/store/modules/user';
  const userStore = useUserStore();
  const stroe = useWorkFLowStore();
  //   const headerStore = useHeaderStore();

  const route = useRouter();
  const pageUrl = ref(window.location.href);
  const isWorkspacePage = ref(false);
  // stroe.$subscribe((mutation, state) => {
  //   console.log('改变了', state)
  // })

  watch(
    stroe,
    (state) => {
      // 监听每次变化
      console.log(state);
    },
    { deep: true },
  );
  //   watch(
  //     headerStore,
  //     (time) => {
  //       changeUrl(time);
  //       reSetTopData();
  //     },
  //     { deep: true },
  //   );

  const { proxy } = getCurrentInstance();

  const emit = defineEmits();

  // 组件接收传参
  const props = defineProps({
    title: {
      type: String,
      default: () => '',
    },

    // 下划线 显隐
    dividerShow: {
      type: Boolean,
      default: () => true,
    },

    // 租户和工作空间  显隐
    pullDown: {
      type: Boolean,
      default: () => false,
    },
  });

  const tenantList = ref(stroe.tenantList);
  console.log('stroe.tenantList', stroe.tenantList);
  console.log('tenantList', tenantList.value);

  const workSpaceList = ref([]);
  const tenantId = ref(undefined);
  const isAdmin = ref(true);
  const workSpaceId = ref();

  // 获取工作空间
  const getWorkspace = (data) => {
    // sessionStorage.setItem('tenantId', tenantId.value)

    workSpaceList.value = [];
    workSpaceId.value = undefined;

    return new Promise((resolve, reject) => {
      getWorkspaceList({ tenantId: data })
        .then((res) => {
          if (res.data.length) {
            workSpaceList.value = res.data;
            const storedWorkSpaceId = getStoredValue('workSpaceId');
            const storedWorkspace = workSpaceList.value.find(
              (workspace) => workspace.workspaceId === storedWorkSpaceId,
            );
            workSpaceId.value = storedWorkspace
              ? storedWorkSpaceId
              : workSpaceList.value[0].workspaceId;
            getDatasourceList();
            resolve(workSpaceList.value);
          } else {
            workSpaceList.value = [];
            proxy.$modal.msgWarning('该租户下没有工作空间');
            reject('该租户下没有工作空间');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 获取数据源列表
  const getDatasourceList = () => {
    if (workSpaceId.value !== null) {
      const obj = { selectedWorkspaceId: workSpaceId.value, tid: tenantId };
      stroe.setWorkSpaceId(workSpaceId.value);
      emit('update-list', obj);
    }
  };

  // Function to get information from persistent storage
  const getStoredValue = (key) => {
    let storedValue = '';
    if (key == 'tenantId') {
      storedValue = stroe.getTenantId();
    } else if (key == 'workSpaceId') {
      storedValue = stroe.getWorkSpaceId();
    }

    return storedValue || null;
  };

  // Function to set information in persistent storage
  const setStoredValue = (key, value) => {
    console.log('key', key);

    if (key == 'tenantId') {
      stroe.setTenantId(value);
    } else if (key == 'workSpaceId') {
      stroe.setWorkSpaceId(value);
    }
  };

  // 获取顶部用户、空间所有数据
  const reSetTopData = async () => {
    if (!props.pullDown) return;
    // 检索存储的值
    storedTenantId.value = getStoredValue('tenantId');
    storedWorkSpaceId.value = getStoredValue('workSpaceId');

    try {
      //   const res = await getInfo();
      //   if (res.data.user.userType !== 'sys_user') {

      // 判断是不是特定用户
      if (userStore.user.userType !== 'sys_user') {
        isAdmin.value = false;
        // 不是特定用户 直接请求 获取工作空间列表
        await getWorkspace(userStore.tenantId);
        // await getWorkspace(res.data.user.tenantId);
        nextTick(() => {
          if (workSpaceList.value.length > 0) {
            // console.log('res.data.user.tenantId', res.data.user.tenantId);
            // 如果与当前值不同，请使用存储值
            // 储存的值不为空 使用储存的值   为空使用 列表中的第一位
            // 查询 workSpaceList.value 内是否有 storedWorkSpaceId 如果没有 使用第一位
            const storedWorkspace = workSpaceList.value.find(
              (workspace) => workspace.workspaceId === storedWorkSpaceId.value,
            );
            workSpaceId.value = storedWorkspace
              ? storedWorkSpaceId.value
              : workSpaceList.value[0].workspaceId;
            setStoredValue('workSpaceId', workSpaceId.value);
            getDatasourceList();
          }
        });
      }
      // 不是特定用户
      else {
        isAdmin.value = true;
        // 先请求租户
        await stroe.getTenantInfo();
        tenantList.value = stroe.tenantList;
        if (tenantList.value.length > 0) {
          // 如果与当前值不同，请使用存储值
          // 检查 tenantList.value 中是否存在 storedTenantId
          const storedTenant = tenantList.value.find(
            (tenant) => tenant.tenantId === storedTenantId.value,
          );
          // 如果存在，请使用 storedTenantId;否则，请使用列表中的第一个租户
          tenantId.value = storedTenant ? storedTenantId.value : tenantList.value[0].tenantId;
          setStoredValue('tenantId', tenantId.value);

          // 请求工作空间
          await getWorkspace(tenantId.value);
          nextTick(() => {
            if (workSpaceList.value.length > 0) {
              // 如果与当前值不同，请使用存储值
              const storedWorkspace = workSpaceList.value.find(
                (workspace) => workspace.workspaceId === storedWorkSpaceId.value,
              );

              workSpaceId.value = storedWorkspace
                ? storedWorkSpaceId.value
                : workSpaceList.value[0].workspaceId;
              setStoredValue('workSpaceId', workSpaceId.value);

              getDatasourceList();
            }
          });
        }
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const consoleData = () => {
    console.log('445566');
  };
  const changeUrl = (url) => {
    // const thisUrl = window.location.href;
    if (url.pageData.isWorkspace) {
      isWorkspacePage.value = true;
    } else {
      isWorkspacePage.value = false;
    }
  };

  const storedTenantId = ref();
  const storedWorkSpaceId = ref();
  onMounted(async () => {
    // changeUrl(window.location.href);
    if (window.location.href.indexOf('/centralAdmin/workspace') >= 0) {
      isWorkspacePage.value = true;
    } else {
      isWorkspacePage.value = false;
    }

    await reSetTopData();
    // 当存储的值发生更改时，请更新存储值
    watch(tenantId, (newValue) => {
      if (
        newValue !== storedTenantId.value &&
        newValue !== null &&
        newValue !== '' &&
        newValue !== 'null' &&
        newValue !== undefined
      ) {
        setStoredValue('tenantId', newValue);
      }
    });

    watch(workSpaceId, (newValue) => {
      if (newValue !== undefined && newValue !== null && newValue !== '' && newValue !== 'null') {
        if (newValue !== storedWorkSpaceId.value) {
          setStoredValue('workSpaceId', newValue);
        }
      }
    });
  });
  //   window.addEventListener('message', (event) => {
  //     // if (event.origin !== 'http://expected-origin.com') {
  //     //   // 验证消息的来源
  //     //   return;
  //     // }
  //     switch (event.data.action) {
  //       // 会跳事件
  //       case 'changeInfoAction':
  //         debugger;
  //         if (event.data.changeInfo) {
  //           reSetTopData();
  //         }
  //         break;
  //       default:
  //         break;
  //     }
  //   });
  window.addEventListener('changeInfoAction', async function (event) {
    if (event.detail.changeInfo) {
      await reSetTopData();
      if (window.location.href.indexOf('/centralAdmin/workspace') >= 0) {
        isWorkspacePage.value = true;
      } else {
        isWorkspacePage.value = false;
      }
    }
  });
</script>
<style lang="scss" scoped>
  :deep .el-form-item__label {
    color: #434343;
    font-size: 14px;
    font-weight: 500;
  }

  // :deep .el-form-item__content {
  //   color: rgb(0, 255, 255);
  // }

  // :deep .el-form-item__content .el-input__inner {
  //   color: rgb(0, 255, 21);
  // }

  // :deep .el-form-item {
  //   color: rgb(255, 0, 0);
  //   font-size: 100;
  // }

  // :deep .el-form-item--default {}
  :deep .el-input__wrapper {
    box-shadow: none;
    background-color: #f2f4f8;
  }

  // :deep .el-form-item__label {
  //   transform: translate(100px, -25px);
  //   z-index: 999;
  // }

  .show {
    // 使用 css 隐藏原生但不影响
    opacity: 0;
    // 禁用鼠标
    pointer-events: none;
    // 禁用点击
    user-select: none;
    visibility: hidden;
  }
  .form-item {
    margin: 40px;
    margin-top: 0px;
    width: 240px;
  }
</style>
