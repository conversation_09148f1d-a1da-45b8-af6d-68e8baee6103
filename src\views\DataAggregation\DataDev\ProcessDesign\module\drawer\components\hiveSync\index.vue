<template>
  <template v-if="NodeData?.program === 'ETL_OFFLINE_SOURCE_HIVE'">
    <el-form ref="dataSourceRef" :model="JDBCForm" :rules="rules" label-width="100px">
      <!-- <el-form-item label="数据源类型" prop="dataSourceType"> -->
      <!-- <el-select v-model="JDBCForm.dataSourceType" placeholder="" style="width: 100%;" @change="getType" -->
      <!-- clearable :disabled="!CanvasActions"> -->
      <!-- <el-option v-for="dict in dataSourceTypeList" :key="dict" :value="dict" :label="dict"></el-option> -->
      <!-- </el-select> -->
      <!-- </el-form-item> -->
      <!--  -->
      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="JDBCForm.dataSource"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDB"
        >
          <el-option
            v-for="dict in dataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="isNotOracleOrDameng" label="数据库" prop="database">
        <el-select
          v-model="JDBCForm.database"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDataTable"
        >
          <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item v-else label="模式" prop="database">
        <el-select
          v-model="JDBCForm.database"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDataTable"
        >
          <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          JDBCForm.dataSourceType &&
          JDBCForm.dataSourceType != 'DAMENG' &&
          JDBCForm.dataSourceType != 'MYSQL' &&
          JDBCForm.dataSourceType != 'ORACLE' &&
          JDBCForm.dataSourceType != 'HIVE'
        "
        label="模式"
        prop="dataSchema"
      >
        <el-select
          v-model="JDBCForm.dataSchema"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getSourceGP"
        >
          <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
        </el-select>
      </el-form-item>

      <el-form-item label="数据表" prop="datasheet">
        <el-select
          v-model="JDBCForm.datasheet"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getFiled"
        >
          <el-option
            v-for="dict in datasheetList"
            :key="dict.tableName"
            :label="dict.tableName"
            :value="dict.tableName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分区字段" prop="fields">
        <el-input v-model="JDBCForm.fields" placeholder="" :disabled="!CanvasActions"></el-input>
      </el-form-item>
    </el-form>
  </template>

  <template v-else-if="NodeData?.program === 'ETL_OFFLINE_SINK_HIVE'">
    <el-form ref="dataSourceRef" :model="JDBCForm" :rules="rules" label-width="100px">
      <!-- <el-form-item label="数据源类型" prop="dataSourceType"> -->
      <!-- <el-select v-model="JDBCForm.dataSourceType" placeholder="" style="width: 100%;" @change="getType" -->
      <!-- clearable :disabled="!CanvasActions"> -->
      <!-- <el-option v-for="dict in dataSourceTypeList" :key="dict" :value="dict" :label="dict"></el-option> -->
      <!-- </el-select> -->
      <!-- </el-form-item> -->

      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="JDBCForm.dataSource"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDB"
        >
          <el-option
            v-for="dict in dataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="isNotOracleOrDameng" label="数据库" prop="database">
        <el-select
          v-model="JDBCForm.database"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDataTable"
        >
          <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item v-else label="模式" prop="database">
        <el-select
          v-model="JDBCForm.database"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getDataTable"
        >
          <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          JDBCForm.dataSourceType &&
          JDBCForm.dataSourceType != 'DAMENG' &&
          JDBCForm.dataSourceType != 'MYSQL' &&
          JDBCForm.dataSourceType != 'ORACLE' &&
          JDBCForm.dataSourceType != 'HIVE'
        "
        label="模式"
        prop="dataSchema"
      >
        <el-select
          v-model="JDBCForm.dataSchema"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getSourceGP"
        >
          <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
        </el-select>
      </el-form-item>

      <el-form-item label="数据表" prop="datasheet">
        <el-select
          v-model="JDBCForm.datasheet"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          @change="getFiled"
        >
          <el-option
            v-for="dict in datasheetList"
            :key="dict.tableName"
            :label="dict.tableName"
            :value="dict.tableName"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="存储模式" prop="saveMode"> -->
      <!-- <el-select v-model="saveMode" placeholder="" style="width: 100%;" :disabled="!CanvasActions"> -->
      <!-- <el-option v-for="mode in saveModeList" :key="mode.value" :label="mode.label" :value="mode.value" -->
      <!-- clearable /> -->
      <!-- </el-select> -->
      <!-- </el-form-item> -->

      <el-form-item label="字段映射" prop="editField">
        <template v-if="isDataSourceType">
          <el-button type="text" :disabled="true" @click="editField">请先配置数据源</el-button>
        </template>

        <template v-else>
          <el-button type="text" :disabled="!CanvasActions" @click="editField">配置</el-button>
        </template>
      </el-form-item>
    </el-form>
  </template>

  <!-- 字段映射 -->
  <el-dialog
    v-model="open"
    title="字段映射"
    width="780px"
    :close-on-click-modal="false"
    style="height: auto"
    append-to-body
    @close="cancelEditField()"
  >
    <div v-loading="loadingForField" class="fieldBox">
      <div class="tip-box">
        <div class="tags-top-prompt-top">
          <IconStateTips />
        </div>
        <div class="prompt-text"
          >字段映射连接时必须保证源字段和目标字段的名称、数量、顺序一致。</div
        >
      </div>
      <div class="btnGruop">
        <!-- <el-button @click="sameNameConnect">同名连接</el-button> -->
        <el-button @click="peerConnect">同行连接</el-button>
        <el-button @click="cancelAllConnection">移除所有连接</el-button>
      </div>
      <div class="field-container-box">
        <div class="fieldContainer">
          <!-- <el-scrollbar> -->
          <div class="list left">
            <div
              v-for="sourceField in sourceFieldList"
              :key="sourceField.columnName"
              class="listItem"
            >
              <div :id="`${sourceField.columnName}*leftItem`" class="listItenInner leftItem">
                <!-- <el-tooltip effect="dark" :content="sourceField.isPartitionField" placement="top">
                <div>{{ sourceField.isPartitionField }}</div>
              </el-tooltip> -->

                <div>
                  <el-tooltip effect="dark" :content="sourceField.columnName" placement="top">
                    <span class="inner-content">{{ sourceField.columnName }}</span>
                  </el-tooltip>
                  <el-tooltip
                    :disabled="true"
                    effect="dark"
                    :content="sourceField.columnType"
                    placement="top"
                  >
                    <el-tag style="margin-left: 5px" size="mini">
                      <span>{{ sourceField.columnType }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="sourceField.comment">
                  <el-tooltip
                    :disabled="!sourceField.comment"
                    effect="dark"
                    :content="sourceField.comment"
                    placement="top"
                  >
                    <div class="inner-div-content">{{ sourceField.comment }}</div>
                  </el-tooltip>
                </div>
                <div v-else style="opacity: 0">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <div class="list right">
            <div
              v-for="targetField in targetFieldList"
              :key="targetField.columnName"
              class="listItem"
            >
              <div
                :id="`${targetField.columnName}*rightItem/${JDBCForm.datasheet}`"
                class="listItenInner rightItem"
              >
                <!-- <el-tooltip effect="dark" :content="targetField.isPartitionField" placement="top">
                  <div>{{ targetField.isPartitionField }}</div>
                </el-tooltip> -->
                <div>
                  <el-tooltip effect="dark" :content="targetField.columnName" placement="top">
                    <span class="inner-content">{{ targetField.columnName }}</span>
                  </el-tooltip>
                  <el-tooltip
                    :disabled="true"
                    effect="dark"
                    :content="targetField.columnType"
                    placement="top"
                  >
                    <el-tag style="margin-left: 5px" size="mini">
                      <span>{{ targetField.columnType }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="targetField.comment">
                  <el-tooltip
                    :disabled="!targetField.comment"
                    effect="dark"
                    :content="targetField.comment"
                    placement="top"
                  >
                    <div class="inner-div-content">{{ targetField.comment }}</div>
                  </el-tooltip>
                </div>
                <div v-else style="opacity: 0">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <!-- </el-scrollbar> -->
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField">取 消</el-button>
        <el-button type="primary" @click="submitFormField">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    getNodeData,
    getTableList,
    schemaForGP,
    tableForGP,
    getColumnTypeMap,
  } from '@/api/DataDev';
  import { IconStateTips } from '@arco-iconbox/vue-update-color-icon';
  import jsPlumb from 'jsplumb'; // 导入连线插件
  import { nextTick, onMounted } from 'vue';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    workspaceId: {
      type: String,
      default: null,
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  // 使用计算属性判断 JDBCForm.dataSourceType 是否 有值
  const isDataSourceType = computed(() => {
    return !JDBCForm.value.dataSourceType;
  });

  // 使用计算属性判断 JDBCForm.dataSourceType 不等于 ORACLE 或者 DAMENG
  const isNotOracleOrDameng = computed(() => {
    return !!(
      JDBCForm.value.dataSourceType != 'ORACLE' && JDBCForm.value.dataSourceType != 'DAMENG'
    );
  });

  const { NodeData, workspaceId, CanvasActions } = toRefs(props);
  console.log('CanvasActions.value', CanvasActions.value);

  const emit = defineEmits();

  const data = reactive({
    JDBCForm: {
      dataSourceType: '',
      dataSource: '',
      database: '',
      dataSchema: '',
      datasheet: '',
    },
    rules: {
      dataSourceType: [{ required: true, message: '请选择数据源类型', trigger: 'foucs' }],
      dataSource: [{ required: true, message: '请选择数据源', trigger: 'foucs' }],
      database: [{ required: true, message: '请选择数据库', trigger: 'foucs' }],
      dataSchema: [{ required: true, message: '请选择模式', trigger: 'foucs' }],
      datasheet: [{ required: true, message: '请选择数据表', trigger: 'foucs' }],
      // saveMode: [
      //     { required: true, message: '请选择存储模式', trigger: 'change' }
      // ],
    },
  });

  const { JDBCForm, rules } = toRefs(data);
  const dataSourceList = ref();

  const databaseList = ref();
  const dataSchemaList = ref();
  const datasheetList = ref();
  // 存储模式的默认值
  const saveMode = ref('append');

  // jsplumb 实例
  const plumbIns = ref(null);
  // 连线数据的左侧集合
  const leftElList = ref();
  // 连线数据的右侧集合
  const rightElList = ref();
  // 保存连线数据
  const connectData = ref([]);
  const srcDbType = ref('');

  const getType = async () => {
    // 改变数据 先清空已有数据
    JDBCForm.value.dataSource = '';
    JDBCForm.value.database = '';
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!JDBCForm.value.dataSourceType) {
      JDBCForm.value.dataSource = '';
      JDBCForm.value.database = '';
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: JDBCForm.value.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    dataSourceList.value = res.data;
  };
  const getDB = async () => {
    // 改变数据 先清空已有数据
    JDBCForm.value.database = '';
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!JDBCForm.value.dataSource) {
      JDBCForm.value.database = '';
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDatabaseList({ datasourceId: JDBCForm.value.dataSource });
    console.log(res.data);
    databaseList.value = res.data;
    console.log(databaseList.value);
  };
  const getDataTable = async () => {
    console.log('被调用了');
    // 改变数据 先清空已有数据
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!JDBCForm.value.database) {
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    // 根据不同数据源获取不同的接口
    const objForOr = {};
    objForOr.datasourceId = JDBCForm.value.dataSource;
    objForOr.databaseName = JDBCForm.value.database;

    const res = await getTableList(objForOr);
    datasheetList.value = res.data;
  };
  const getSourceGP = async (data) => {
    console.log('获取模式了', data);
    // 改变数据 先清空已有数据
    // JDBCForm.value.datasheet = ''
    // datasheetList.value = []
    if (!JDBCForm.value.dataSchema) {
      JDBCForm.value.datasheet = '';
      datasheetList.value = [];
      return;
    }

    if (data) {
      const obj = {};
      if (JDBCForm.value.dataSourceType == 'XUGU') {
        (obj.datasourceId = JDBCForm.value.dataSource), (obj.schemaName = data);
      } else {
        (obj.datasourceId = JDBCForm.value.dataSource),
          (obj.databaseName = JDBCForm.value.database),
          (obj.schemaName = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          datasheetList.value = res.data;
          console.log(datasheetList.value);
        } else {
          datasheetList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const sourceFieldList = ref([]);
  const targetFieldList = ref([]);
  const getFiled = async () => {
    console.log('被调用了');
    // 改变数据 先清空已有数据

    if (!JDBCForm.value.datasheet) {
      sourceFieldList.value = [];
      targetFieldList.value = [];
      return;
    }

    const params = {
      tableName: JDBCForm.value.datasheet,
      datasourceId: JDBCForm.value.dataSource,
      databaseName: JDBCForm.value.database,
    };

    const res = await getFieldList(params);
    targetFieldList.value = res.data;
    console.log(targetFieldList.value);
  };
  const mappingData = ref([]);
  // 字段映射配置加载画面
  const loadingForField = ref(false);
  // 弹窗的显示
  const open = ref(false);
  // 打开字段映射配置弹窗
  const editField = async () => {
    srcDbType.value = JDBCForm.value.dataSourceType;
    // loadingForField.value = true;
    // 获取源表字段数据
    // const res = await getNodeData(NodeData.value.id);
    // sourceFieldList.value = res.data.metadata.length ? res.data.metadata[0].columns : []

    // 重新获取数据源与输出数据关系
    const reqData = {
      destDbType: JDBCForm.value.dataSourceType,
      srcDbType: srcDbType.value,
    };

    if (!reqData.srcDbType.length) return proxy.$modal.msgWarning('请选择数据源类型');
    const resTypeMap = await getColumnTypeMap(reqData);
    if (!resTypeMap.data.length) return proxy.$modal.msgWarning(resTypeMap.msg);

    mappingData.value = mappingData.value?.map((item) => {
      const allowConnection = resTypeMap?.data?.find((type) => {
        return type.srcType.toUpperCase() === item.columnType || type.srcType === item.columnType;
      });
      item.allowConnection = allowConnection;
      return item;
    });
    sourceFieldList.value = mappingData.value;

    // if (res.data && res.data.metadata.length) {
    //   const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
    //   if (currentIndex > 0) {
    //     const previousNode = res.data.metadata[currentIndex - 1];
    //     sourceFieldList.value = previousNode ? previousNode.columns : [];
    //   } else if (res.data.metadata.length === 1) {
    //     const currentNode = res.data.metadata[0];
    //     sourceFieldList.value = currentNode ? currentNode.columns : [];
    //   }
    // }

    nextTick(async () => {
      await document.getElementsByClassName('fieldContainer')[0]?.scrollTo(0, 0);
      plumbIns.value = jsPlumb.jsPlumb.getInstance({ Container: 'content' });
      // 获取左边字段的选择器
      if (sourceFieldList?.value?.length) {
        leftElList.value = document.querySelectorAll('.leftItem');
      }
      // 获取右边字段的选择器
      if (targetFieldList?.value?.length) {
        rightElList.value = document.querySelectorAll('.rightItem');
      }
      if (connectData.value.length) {
        connectData.value.forEach((res) => {
          plumbIns.value.ready(() => {
            // const sourceNode = sourceFieldList.value.find((source) => {
            //   return source.columnName === res.source;
            // });
            // const targetNode = targetFieldList.value.find((target) => {
            //   return target.columnName === res.target.split('*rightItem/')[0];
            // });
            // // 数据类型不匹配的不给连线
            // if (
            //   sourceNode.allowConnection.desType.indexOf(targetNode.columnType.toLowerCase()) >= 0
            // ) {
            plumbIns.value.connect({
              // 连线起点
              source: res.source,
              // 连线终点
              target: res.target,
              anchor: ['Left', 'Right', [0.3, 0, 0, -1], [0.7, 0, 0, -1]],
              connector: ['Straight'],
              endpoint: 'Blank',
              overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
              // 添加样式
              paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
            });
            // }
          });
        });
      }
      loadingForField.value = false;
    });
    open.value = true;
  };

  // 存放端点数组
  const point = ref([]);
  // 存放所有的连接信息
  const allData = ref([]);
  // 删除所有连线
  const cancelAllConnection = () => {
    allData.value = [];
    point.value = [];
    plumbIns.value.deleteEveryConnection();
    plumbIns.value.deleteEveryEndpoint();
  };
  // 判断字段是否为空
  const connectionJudgment = () => {
    return !!(leftElList.value?.length && rightElList.value?.length);
  };

  // 同行连接
  const peerConnect = async () => {
    cancelAllConnection();
    const res = await connectionJudgment();
    if (!res) return proxy.$modal.msgWarning('字段不能为空');
    document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);
    // 根据长度判断循环连线的数据
    if (leftElList?.value?.length <= rightElList?.value?.length) {
      leftElList.value.forEach((res, index) => {
        const sourceNode = sourceFieldList.value.find((source) => {
          return source.columnName === res.id;
        });
        const targetNode = targetFieldList.value.find((target) => {
          return target.columnName === rightElList.value[index].id.split('*rightItem/')[0];
        });
        // 数据类型不匹配的不给连线
        // if (
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
        // ) {
        plumbIns.value.connect({
          // 连线起点
          source: res.id,
          // 连线终点
          target: rightElList.value[index].id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
        // }
      });
    } else if (leftElList?.value?.length > rightElList?.value?.length) {
      rightElList.value.forEach((res, index) => {
        const sourceNode = sourceFieldList.value.find((source) => {
          return source.columnName === leftElList.value[index].id;
        });
        const targetNode = targetFieldList.value.find((target) => {
          return target.columnName === res.id.split('*rightItem/')[0];
        });
        // 数据类型不匹配的不给连线
        // if (
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
        // ) {
        plumbIns.value.connect({
          // 连线起点
          source: leftElList.value[index].id,
          // 连线终点
          target: res.id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
        // }
      });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };

  // New 同行连接
  // const peerConnect = async () => {
  //   try {
  //     cancelAllConnection();
  //     const res = await connectionJudgment();
  //     if (!res) throw new Error('字段不能为空');

  //     document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);

  //     if (!plumbIns.value) throw new Error('jsPlumb 实例未初始化');

  //     let connectionsMade = 0;

  //     const connectFields = (sourceEl, targetEl) => {
  //       const sourceNode = sourceFieldList.value.find(source => source.columnName === sourceEl.id);
  //       const targetNode = targetFieldList.value.find(target => target.columnName === targetEl.id.split('*rightItem/')[0]);

  //       if (!sourceNode || !targetNode) return;

  //       const isTypeMatched = sourceNode.allowConnection?.desType?.some(type =>
  //         type.toLowerCase() === targetNode.columnType.toLowerCase()
  //       );

  //       if (!isTypeMatched) return;

  //       try {
  //         const connection = plumbIns.value.connect({
  //           source: sourceEl.id,
  //           target: targetEl.id,
  //           anchor: ['Left', 'Right', 'Top', 'Bottom', [0.3, 0, 0, -1], [0.7, 0, 0, -1], [0.3, 1, 0, 1], [0.7, 1, 0, 1]],
  //           connector: ['Straight'],
  //           endpoint: 'Blank',
  //           overlays: [['Arrow', { width: 8, length: 8, location: 1 }]],
  //           paintStyle: { stroke: '#909399', strokeWidth: 2 },
  //         });

  //         if (connection) {
  //           connectionsMade++;
  //           console.log('Successfully connected:', sourceEl.id, 'to', targetEl.id);
  //         } else {
  //           console.error('Connection object is null:', sourceEl.id, 'to', targetEl.id);
  //         }
  //       } catch (error) {
  //         console.error('Failed to connect:', sourceEl.id, 'to', targetEl.id, 'Error:', error);
  //       }
  //     };

  //     if (leftElList?.value?.length <= rightElList?.value?.length) {
  //       leftElList.value.forEach((sourceEl, index) => {
  //         if (index < rightElList.value.length) {
  //           connectFields(sourceEl, rightElList.value[index]);
  //         }
  //       });
  //     } else {
  //       rightElList.value.forEach((targetEl, index) => {
  //         if (index < leftElList.value.length) {
  //           connectFields(leftElList.value[index], targetEl);
  //         }
  //       });
  //     }

  //     // 获取所有的连接信息
  //     allData.value = plumbIns.value.getAllConnections();

  //     if (connectionsMade === 0) {
  //       throw new Error('没有成功建立任何连接，请检查字段类型是否匹配');
  //     } else {
  //       proxy.$modal.msgSuccess(`成功建立 ${connectionsMade} 个连接`);
  //     }
  //   } catch (error) {
  //     proxy.$modal.msgWarning(`连线失败: ${error.message}`);
  //   }
  // };

  // 保存字段映射
  const submitFormField = () => {
    // 获取所有的连接信息
    const item = plumbIns.value.getAllConnections();
    // 清空保存的连线数据
    connectData.value = [];
    // 保存连线数据用以回显连线
    if (item.length) {
      item.map((i) => {
        connectData.value.push({ source: i.sourceId, target: i.targetId });
      });
    }
    // 如果有连线则保存数据
    if (item.length) {
      const value = [];
      item.map((i) => {
        value.push({ source: i.sourceId.split('*')[0], target: i.targetId.split('*')[0] });
      });
      // 需要将连线数据转为字段串格式
      NodeData.value.inputProperties.filter((res) => res.name == 'field_mapping')[0].value =
        JSON.stringify(value);
    } else {
      proxy.$modal.msgWarning('字段映射不能为空');
    }
    open.value = false;
  };

  // 取消配置
  const cancelEditField = () => {
    cancelAllConnection();
    open.value = false;
  };

  const cancelDrawer = () => {
    proxy.resetForm('dataSourceRef');
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    if (NodeData.value.operatorName == 'Hive输入') {
      const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
      if (!res) return;

      const formObj = {};
      formObj.datasourceType = JDBCForm.value.dataSourceType;
      formObj.datasourceId = JDBCForm.value.dataSource;
      formObj.databaseName = isNotOracleOrDameng.value ? JDBCForm.value.database : null;
      // JDBCForm.value.dataSourceType == 'ORACLE' ? '' : JDBCForm.value.database

      //  如果是 oracle 数据源
      formObj.schemaName = isNotOracleOrDameng.value
        ? JDBCForm.value.dataSchema
        : JDBCForm.value.database;

      formObj.tableName = JDBCForm.value.datasheet;
      formObj.connectionParams = dataSourceList.value.filter(
        (res) => res.id == JDBCForm.value.dataSource,
      )[0].connectionParams;
      NodeData.value.inputProperties.filter((res) => res.name == 'datasource')[0].value =
        JSON.stringify(formObj);
      // NodeData.value.inputProperties.filter(res => res.name == 'result_table_name')[0].value = JDBCForm.value.datasheet
      NodeData?.value.program === 'ETL_OFFLINE_SOURCE_HIVE' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'result_table_name')[0].value =
          JDBCForm.value.datasheet);
      NodeData?.value.program === 'ETL_OFFLINE_SOURCE_HIVE' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'read_partitions')[0].value =
          JDBCForm.value.fields);

      NodeData?.value.program === 'ETL_OFFLINE_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'result_table_name')[0].value =
          JDBCForm.value.datasheet);

      NodeData?.value.program === 'REALTIME_ALG_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'outputFields')[0].value =
          JSON.stringify(
            targetFieldList.value.map((res) => {
              res.checked = true;
              return res;
            }),
          ));
    }
    // 可能还需要字段映射连线数据
    if (NodeData.value.operatorName == 'Hive输出') {
      console.log('isNotOracleOrDameng', isNotOracleOrDameng.value);
      const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
      if (!res) return;

      const formObj = {};
      formObj.datasourceType = JDBCForm.value.dataSourceType;
      formObj.datasourceId = JDBCForm.value.dataSource;
      formObj.databaseName = isNotOracleOrDameng.value ? JDBCForm.value.database : null;
      formObj.schemaName = isNotOracleOrDameng.value
        ? JDBCForm.value.dataSchema
        : JDBCForm.value.database;

      // JDBCForm.value.dataSourceType == 'ORACLE' ? JDBCForm.value.database : JDBCForm.value.dataSchema
      formObj.tableName = JDBCForm.value.datasheet;
      formObj.connectionParams = dataSourceList.value.filter(
        (res) => res.id == JDBCForm.value.dataSource,
      )[0].connectionParams;
      NodeData.value.inputProperties.filter((res) => res.name == 'datasource')[0].value =
        JSON.stringify(formObj);
      // TODO 这里没有校验 是否是 Kettle 程序   如果是 KEttle 程序 需要把 saveMode 隐藏掉
      // NodeData.value.inputProperties.filter(res => res.name == 'saveMode')[0].value = saveMode.value
      if (NodeData?.value.program === 'ETL_OFFLINE_SINK_HIVE') {
        const item = NodeData.value.inputProperties.find((res) => res.name == 'save_mode');
        if (item) {
          item.value = item.defaultValue ? item.defaultValue : 'append';
        }
      }

      // NodeData?.value.program === 'ETL_OFFLINE_SINK_HIVE' && (NodeData.value.inputProperties.filter(res => res.name == 'outputFields')[0].value = JSON.stringify(
      //     targetFieldList.value.map(res => {
      //         res.checked = true
      //         return res
      //     })))
    }
    emit('submitDrawer', NodeData.value);
  };

  let hasWarned = false;
  const init = async () => {
    nextTick(() => {
      JDBCForm.value.dataSourceType = 'HIVE';
      JDBCForm.value.dataSourceType && getType();
      JDBCForm.value.fields = '';
    });

    if (NodeData.value?.operatorName == 'Hive输入') {
      // 数据源连接信息
      const a = NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]?.value;
      const parsedValue = a ? JSON.parse(a) : null;

      if (parsedValue) {
        if (parsedValue.datasourceType == 'ERR:原数据源已不存在') {
          JDBCForm.value.dataSourceType = null;
          JDBCForm.value.dataSource = null;
          JDBCForm.value.database = null;
          JDBCForm.value.dataSchema = null;
          JDBCForm.value.datasheet = null;

          // 只提示一次
          if (!hasWarned) {
            proxy.$modal.msgWarning('原数据源已不存在');
            hasWarned = true; // 显示警告后，将变量设置为 true
            // 停止继续请求
            return;
          }
        } else {
          nextTick(() => {
            JDBCForm.value.dataSourceType = parsedValue.datasourceType;
            JDBCForm.value.dataSourceType && getType();

            JDBCForm.value.dataSource = parsedValue.datasourceId;
            JDBCForm.value.dataSource && getDB();

            JDBCForm.value.database = isNotOracleOrDameng.value
              ? parsedValue.databaseName
              : parsedValue.schemaName;
            JDBCForm.value.database && getDataTable();

            JDBCForm.value.dataSchema = parsedValue.schemaName;
            JDBCForm.value.dataSchema && getSourceGP(JDBCForm.value.dataSchema);

            JDBCForm.value.datasheet = parsedValue.tableName;
            JDBCForm.value.datasheet && getFiled();

            JDBCForm.value.fields = NodeData.value?.inputProperties.filter(
              (res) => res.name == 'read_partitions',
            )[0].value;
          });
        }
      } else {
        JDBCForm.value.dataSourceType = null;
        JDBCForm.value.dataSource = null;
        JDBCForm.value.database = null;
        JDBCForm.value.dataSchema = null;
        JDBCForm.value.datasheet = null;
      }

      NodeData?.value.program === 'REALTIME_ALG_SOURCE_JDBC' &&
        (targetFieldList.value = JSON.parse(
          NodeData.value.inputProperties.filter((res) => res.name == 'outputFields')[0]?.value,
        ));
    } else if (NodeData.value?.operatorName == 'Hive输出') {
      // 获取数据源信息（库，模式，表，id等）
      const b = NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]?.value;
      const parsedValue = b ? JSON.parse(b) : null;
      if (parsedValue) {
        if (parsedValue.datasourceType == 'ERR:原数据源已不存在') {
          JDBCForm.value.dataSourceType = null;
          JDBCForm.value.dataSource = null;
          JDBCForm.value.database = null;
          JDBCForm.value.dataSchema = null;
          JDBCForm.value.datasheet = null;

          // 只提示一次
          if (!hasWarned) {
            proxy.$modal.msgWarning('原数据源已不存在');
            hasWarned = true; // 显示警告后，将变量设置为 true
            // 停止继续请求
            return;
          }
        } else {
          nextTick(() => {
            JDBCForm.value.dataSourceType = parsedValue.datasourceType;
            JDBCForm.value.dataSourceType && getType();

            JDBCForm.value.dataSource = parsedValue.datasourceId;
            JDBCForm.value.dataSource && getDB();

            JDBCForm.value.database = isNotOracleOrDameng.value
              ? parsedValue.databaseName
              : parsedValue.schemaName;
            JDBCForm.value.database && getDataTable();

            JDBCForm.value.dataSchema = parsedValue.schemaName;
            JDBCForm.value.dataSchema && getSourceGP(JDBCForm.value.dataSchema);

            JDBCForm.value.datasheet = parsedValue.tableName;
            JDBCForm.value.datasheet && getFiled();
          });
        }
      } else {
        JDBCForm.value.dataSourceType = null;
        JDBCForm.value.dataSource = null;
        JDBCForm.value.database = null;
        JDBCForm.value.dataSchema = null;
        JDBCForm.value.datasheet = null;
      }

      // 获取存储模式对象数据
      const c = NodeData.value?.inputProperties.filter((res) => res.name == 'saveMode')[0]?.value;
      // 对存储模式进行赋值
      saveMode.value =
        c ||
        NodeData.value?.inputProperties.filter((res) => res.name == 'saveMode')[0]?.defaultValue;

      // 获取字段映射的数据
      const d = NodeData.value?.inputProperties.filter((res) => res.name == 'field_mapping')[0]
        ?.value;
      // 获取连线数据
      const item = d ? JSON.parse(d) : null;
      // 获取字段映射数据
      const resNodeData = await getNodeData(NodeData.value.id);
      srcDbType.value = '';
      if (resNodeData.data && resNodeData.data.metadata.length) {
        const currentIndex = resNodeData.data.metadata.findIndex(
          (item) => item.from === NodeData.value.id,
        );
        if (currentIndex > 0) {
          const previousNode = resNodeData.data.metadata[currentIndex - 1];
          srcDbType.value = previousNode.type;
          mappingData.value = previousNode ? previousNode.columns : [];
        } else if (resNodeData.data.metadata.length === 1) {
          const currentNode = resNodeData.data.metadata[0];
          srcDbType.value = currentNode.type;
          mappingData.value = currentNode ? currentNode.columns : [];
        } else {
          srcDbType.value = resNodeData.data.metadata[resNodeData.data.metadata.length - 1].type;
          mappingData.value =
            resNodeData.data.metadata[resNodeData.data.metadata.length - 1].columns;
        }
      }
      nextTick(async () => {
        // const reqData = {
        //   destDbType: JDBCForm.value.dataSourceType,
        //   srcDbType,
        // };
        // const resTypeMap = await getColumnTypeMap(reqData);
        // mappingData.value = mappingData.value.map((item) => {
        //   const allowConnection = resTypeMap.data.find((type) => {
        //     return type.srcType.toUpperCase() === item.columnType;
        //   });
        //   item.allowConnection = allowConnection;
        //   return item;
        // });
        if (item && item.length) {
          item.forEach((res) => {
            connectData.value.push({
              source: `${res.source}*leftItem`,
              target: `${res.target}*rightItem/${JDBCForm.value.datasheet}`,
            });
          });
        }
      });
    }
  };
  onMounted(() => {
    proxy.resetForm('dataSourceRef');
    init();
  });
  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .btnGruop {
    text-align: center;
    margin-bottom: 25px;
  }

  .field-container-box {
    height: calc(100vh - 440px);
    overflow: scroll;
    .fieldContainer {
      height: auto;
      overflow: auto;
      position: relative;
      padding: 10px;

      .list {
        float: left;

        .listItem {
          height: 60px;
          text-align: left;
          line-height: 25px;
          padding-top: 5px;
          box-sizing: border-box;
          cursor: pointer;
          min-width: 270px;
          max-width: 270px;
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .listItenInner {
            position: relative;
            border: 1px solid #d8dade;
            color: #4e5370;
            border-radius: 2px;
            height: 52px;
            padding: 5px 10px;

            .inner-content {
              display: inline-block;
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #434343;
            }

            .inner-div-content {
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #8c8c8c;
            }
          }
        }
      }

      .left {
        margin-right: 160px;
      }
    }
  }
  .tip-box {
    width: 100%;
    padding: 10px 10px 10px 36px;
    background: $--base-color-tag-primary;
    font-size: 12px;
    color: $--base-color-text2;
    position: relative;
    border-radius: 4px;
    margin-bottom: 20px;
    .tags-top-prompt-top {
      width: 16px;
      height: 16px;
      position: absolute;
      top: calc(50% - 8px);
      left: 10px;
      font-size: 16px;
    }
    .prompt-text {
      line-height: 20px;
    }
    .prompt-turn {
      & > span {
        font-size: 12px;
        color: $--base-color-primary;
        cursor: pointer;
      }
    }
  }
</style>
