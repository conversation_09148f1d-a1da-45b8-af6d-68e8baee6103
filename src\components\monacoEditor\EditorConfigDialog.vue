<template>
  <el-dialog
    title="编辑器配置"
    v-model="dialogVisible"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form :model="configForm" label-width="120px">
      <el-form-item label="语言">
        <el-select v-model="configForm.language" placeholder="选择语言">
          <el-option
            v-for="lang in supportedLanguages"
            :key="lang.value"
            :label="lang.label"
            :value="lang.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="主题">
        <el-select v-model="configForm.theme" placeholder="选择主题">
          <el-option label="暗色主题" value="vs-dark" />
          <el-option label="亮色主题" value="vs" />
          <el-option label="高对比度" value="hc-black" />
        </el-select>
      </el-form-item>

      <el-form-item label="只读模式">
        <el-switch v-model="configForm.readOnly" />
      </el-form-item>

      <el-form-item label="显示小地图">
        <el-switch v-model="configForm.minimap.enabled" />
      </el-form-item>

      <el-form-item label="自动格式化粘贴">
        <el-switch v-model="configForm.formatOnPaste" />
      </el-form-item>

      <el-form-item label="鼠标滚轮缩放">
        <el-switch v-model="configForm.mouseWheelZoom" />
      </el-form-item>

      <el-form-item label="代码折叠">
        <el-switch v-model="configForm.folding" />
      </el-form-item>

      <el-form-item label="自动闭合括号">
        <el-select v-model="configForm.autoClosingBrackets" placeholder="选择模式">
          <el-option label="总是" value="always" />
          <el-option label="从不" value="never" />
          <el-option label="在前面有文本时" value="beforeWhitespace" />
        </el-select>
      </el-form-item>

      <el-form-item label="自动闭合引号">
        <el-select v-model="configForm.autoClosingQuotes" placeholder="选择模式">
          <el-option label="总是" value="always" />
          <el-option label="从不" value="never" />
          <el-option label="在前面有文本时" value="beforeWhitespace" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { supportedLanguages } from './config.js';

  const props = defineProps({
    visible: { type: Boolean, default: false },
    config: { type: Object, default: () => ({}) },
  });

  const emit = defineEmits(['update:visible', 'update:config']);

  // 创建一个深拷贝的配置表单
  const configForm = ref(JSON.parse(JSON.stringify(props.config)));

  // 监听visible变化，当对话框打开时重置表单
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        configForm.value = JSON.parse(JSON.stringify(props.config));
      }
    },
  );

  // 监听config变化，更新表单
  watch(
    () => props.config,
    (val) => {
      configForm.value = JSON.parse(JSON.stringify(val));
    },
    { deep: true },
  );

  // 关闭对话框
  const handleClose = () => {
    emit('update:visible', false);
  };

  // 确认修改
  const handleConfirm = () => {
    emit('update:config', configForm.value);
    handleClose();
  };

  // 计算属性：对话框可见性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });
</script>

<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
</style>
