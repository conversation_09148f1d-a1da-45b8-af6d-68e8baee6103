<template>
  <div class="physical-model">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form
        ref="queryFormRef"
        :model="queryForm"
        :inline="true"
        label-width="auto"
      >
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            v-model="queryForm.modelName"
            placeholder="请输入模型名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增模型
        </el-button>
        <el-button @click="handleMove" :disabled="!hasSelection">
          <el-icon><Position /></el-icon>
          移动
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button type="text" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="50" align="center" />
        
        <el-table-column label="序号" type="index" width="60" align="center">
          <template #default="{ $index }">
            {{ (pagination.currentPage - 1) * pagination.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        
        <el-table-column prop="modelName" label="模型名称" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-button type="text" @click="handleModelDetailClick(row)">
              {{ row.modelName }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column prop="version" label="版本" width="100" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="handleVersionClick(row)">
              {{ row.version }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '启用' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="approvalStatus" label="审批状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.approvalStatus === '已通过' ? 'success' : 
                    row.approvalStatus === '审批中' ? 'warning' : 
                    row.approvalStatus === '已拒绝' ? 'danger' : 'info'"
            >
              {{ row.approvalStatus || '无需审批' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="creator" label="创建人" width="100" align="center" />
        
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        
        <el-table-column prop="lastModifier" label="最后修改人" width="120" align="center" />
        
        <el-table-column prop="lastModifyTime" label="最后修改时间" width="160" align="center" />
        
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handlePublish(row)">
              发布
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleDelete(row)" style="color: #f56c6c">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>

  <!-- 新增/编辑模型对话框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑模型' : '新增模型'"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form
      ref="modelFormRef"
      :model="modelForm"
      :rules="modelRules"
      label-width="120px"
    >
      <!-- 通用表单项 -->
      <el-form-item label="模型名称" prop="name" required>
        <el-input v-model="modelForm.name" placeholder="请输入内容" maxlength="50" show-word-limit />
      </el-form-item>
      
      <el-form-item label="模型代号" prop="code" required>
        <el-input v-model="modelForm.code" placeholder="请输入内容" maxlength="50" />
      </el-form-item>
      
      <el-form-item label="模式" prop="schema" required>
        <el-select v-model="modelForm.schema" placeholder="请选择内容" style="width: 100%">
          <el-option v-for="item in schemaOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      
      <!-- BOM模型特有表单项 -->
      <template v-if="currentModelType === 'bom'">
        <el-form-item label="母件模型" prop="parentModel" required>
          <el-select v-model="modelForm.parentModel" placeholder="请选择内容" style="width: 100%">
            <el-option v-for="item in entityModelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="子件模型" prop="childModels" required>
          <el-select
            v-model="modelForm.childModels"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择内容"
            style="width: 100%"
          >
            <el-option v-for="item in entityModelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      
      <!-- 通用表单项 -->
      <el-form-item label="模型密级" prop="securityLevel">
        <el-select v-model="modelForm.securityLevel" placeholder="请选择内容" style="width: 100%">
          <el-option v-for="item in securityLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="是否需要审批" prop="needApproval">
        <el-radio-group v-model="modelForm.needApproval">
          <el-radio :label="false">不使用审批流程</el-radio>
          <el-radio :label="true">使用审批流程</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="审批流程" prop="approvalProcess" v-if="modelForm.needApproval">
        <el-select v-model="modelForm.approvalProcess" placeholder="请选择内容" style="width: 100%">
          <el-option v-for="item in approvalProcessOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="modelForm.description"
          type="textarea"
          placeholder="请输入内容"
          maxlength="200"
          show-word-limit
          :rows="4"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 数据版本侧边抽屉 -->
  <el-drawer
    v-model="versionDrawerVisible"
    title="数据版本"
    size="60%"
    direction="rtl"
  >
    <div class="version-drawer-content">
      <div class="version-header">
        <el-button type="primary" @click="handleVersionCompare">
          版本对比
        </el-button>
      </div>
      
      <div class="version-table">
        <el-table :data="versionData" style="width: 100%">
          <el-table-column prop="version" label="版本" width="100" />
          <el-table-column prop="creator" label="创建人" width="120" />
          <el-table-column prop="createTime" label="创建时间"  />
          <el-table-column prop="lastModifier" label="修改人"  />
          <el-table-column prop="lastModifyTime" label="修改时间" />
        </el-table>
      </div>
      
      <!-- 版本分页组件 -->
      <div class="version-pagination">
        <el-pagination
          v-model:current-page="versionPagination.currentPage"
          v-model:page-size="versionPagination.pageSize"
          :page-sizes="versionPagination.pageSizes"
          :total="versionPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleVersionSizeChange"
          @current-change="handleVersionCurrentChange"
        />
      </div>
    </div>
  </el-drawer>

  <!-- 版本对比弹窗 -->
  <el-dialog
    v-model="compareDialogVisible"
    title="版本对比"
    width="800px"
    :close-on-click-modal="false"
  >
    <h3>选择版本</h3>
    <el-form :model="compareForm" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="9">
          <el-form-item label="基准版本" required>
            <el-select v-model="compareForm.baseVersion" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in versionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="对比版本" required>
            <el-select v-model="compareForm.compareVersion" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in versionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
            <el-button @click="resetCompareForm">重置</el-button>
            <el-button type="primary" @click="performCompare">对比</el-button>
        </el-col>
      </el-row>
    </el-form>
    
    
    
    <!-- 对比结果 -->
    <div v-if="compareResult.length > 0" class="compare-result">
      <h3>对比结果</h3>
      <el-table :data="compareResult" style="width: 100%">
        <el-table-column prop="baseFieldName" label="基准版本字段名称"  />
        <el-table-column prop="compareFieldName" label="对比版本字段名称" >
          <template #default="{ row }">
            <span :class="{ 'field-different': row.isDifferent }">
              {{ row.compareFieldName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="changeType" label="变更类型" >
          <template #default="{ row }">
            <el-tag
              v-if="row.changeType"
              :type="getChangeTypeColor(row.changeType)"
            >
              {{ row.changeType }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="compareDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 模型详情弹窗 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="详情"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="model-detail">
      <el-descriptions :column="1" >
        <el-descriptions-item label="模型名称">
          {{ modelDetail.modelName }}
        </el-descriptions-item>
        <el-descriptions-item label="模型代号">
          {{ modelDetail.code }}
        </el-descriptions-item>
        
        <!-- BOM模型特有字段 -->
        <template v-if="currentModelType === 'bom'">
          <el-descriptions-item label="母件模型">
            {{ modelDetail.parentModel || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="子件模型">
            {{ modelDetail.childModels && modelDetail.childModels.length > 0 ? modelDetail.childModels.join('、') : '未设置' }}
          </el-descriptions-item>
        </template>
        
        <el-descriptions-item label="审批流程">
          {{ modelDetail.needApproval ? (modelDetail.approvalProcess || '未设置审批流程') : '不使用审批流程' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">
          {{ modelDetail.creator }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ modelDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="修改者">
          {{ modelDetail.lastModifier }}
        </el-descriptions-item>
        <el-descriptions-item label="修改时间">
          {{ modelDetail.lastModifyTime }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
          {{ modelDetail.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script setup>
import { ref, reactive, computed, inject } from 'vue'
import { Search, Refresh, Plus, Position } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 状态选项
 */
const statusOptions = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' }
]

/**
 * 表单引用
 */
const queryFormRef = ref()
const tableRef = ref()

/**
 * 查询表单数据
 */
const queryForm = reactive({
  modelName: '',
  status: ''
})

/**
 * 表格数据加载状态
 */
const loading = ref(false)

/**
 * 表格数据
 */
const tableData = ref([])

/**
 * 分页配置
 */
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

/**
 * 选中的行数据
 */
const selectedRows = ref([])

/**
 * 是否有选中项
 */
const hasSelection = computed(() => selectedRows.value.length > 0)

/**
 * 处理查询
 */
const handleQuery = () => {
  // 实际应用中这里应该调用API获取数据
  loading.value = true
  setTimeout(() => {
    // 模拟完整数据源
    const allData = [
      {
        id: 1,
        modelName: '内部人员',
        code: 'NBRY',
        version: 'v1.0',
        status: '启用',
        creator: 'admin',
        createTime: '2024-02-03 12:12:12',
        lastModifier: 'admin',
        lastModifyTime: '2024-02-03 12:12:12',
        needApproval: false,
        approvalProcess: '',
        approvalStatus: '无需审批',
        description: '内部人员模型，用于管理公司内部员工信息',
        parentModel: '',
        childModels: []
      },
      {
        id: 2,
        modelName: 'BOM产品模型',
        code: 'BOMCP',
        version: 'v1.2',
        status: '启用',
        creator: 'admin',
        createTime: '2024-02-03 09:15:00',
        lastModifier: 'admin',
        lastModifyTime: '2024-02-03 11:20:00',
        needApproval: true,
        approvalProcess: '产品发布审批流程',
        approvalStatus: '审批中',
        description: 'BOM产品模型，用于管理产品的物料清单结构',
        parentModel: '主产品模型',
        childModels: ['零件模型A', '零件模型B', '组件模型C']
      },
      {
        id: 3,
        modelName: '库存管理模型',
        code: 'INVENTORY_MODEL',
        version: 'v2.1',
        status: '禁用',
        creator: '赵六',
        createTime: '2024-01-10 09:15:00',
        lastModifier: '赵六',
        lastModifyTime: '2024-01-25 16:45:00',
        description: '用于库存管理的数据模型，当前已停用',
        needApproval: false,
        approvalProcess: '',
        approvalStatus: '无需审批'
      }
    ]
    
    // 根据搜索条件过滤数据
    let filteredData = allData
    
    // 按模型名称过滤
    if (queryForm.modelName) {
      filteredData = filteredData.filter(item => 
        item.modelName.toLowerCase().includes(queryForm.modelName.toLowerCase())
      )
    }
    
    // 按状态过滤
    if (queryForm.status) {
      filteredData = filteredData.filter(item => {
        const statusMap = {
          'enabled': '启用',
          'disabled': '禁用'
        }
        return item.status === statusMap[queryForm.status]
      })
    }
    
    tableData.value = filteredData
    pagination.total = filteredData.length
    loading.value = false
  }, 500)
}

/**
 * 重置查询
 */
const resetQuery = () => {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  handleQuery()
}

/**
 * 对话框可见性控制
 */
const dialogVisible = ref(false)

/**
 * 模型表单引用
 */
const modelFormRef = ref()

/**
 * 从父组件获取当前选中的标签页值
 */
const currentModelType = inject('currentTab', ref('entity'))

/**
 * 模型表单数据
 */
const modelForm = reactive({
  name: '', // 模型名称
  code: '', // 模型代号
  schema: '', // 模式
  parentModel: '', // 母件模型（BOM模型特有）
  childModels: [], // 子件模型（BOM模型特有）
  securityLevel: '', // 模型密级
  needApproval: false, // 是否需要审批
  approvalProcess: '', // 审批流程
  description: '' // 描述
})

/**
 * 模型表单验证规则
 */
const modelRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入模型代号', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '只能输入字母、数字、下划线，且开头必须是字母', trigger: 'blur' }
  ],
  schema: [
    { required: true, message: '请选择模式', trigger: 'change' }
  ],
  parentModel: [
    { required: true, message: '请选择母件模型', trigger: 'change' }
  ],
  childModels: [
    { required: true, message: '请选择子件模型', trigger: 'change' },
    { type: 'array', min: 1, message: '请至少选择一个子件模型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
  ]
}

/**
 * 模式选项
 */
const schemaOptions = [
  { value: 'schema1', label: '模式一' },
  { value: 'schema2', label: '模式二' },
  { value: 'schema3', label: '模式三' }
]

/**
 * 实体模型选项（用于BOM模型的母件和子件选择）
 */
const entityModelOptions = [
  { value: 'model1', label: '产品模型' },
  { value: 'model2', label: '设备模型' },
  { value: 'model3', label: '零件模型' }
]

/**
 * 密级选项
 */
const securityLevelOptions = [
  { value: 'public', label: '公开' },
  { value: 'internal', label: '内部' },
  { value: 'confidential', label: '保密' },
  { value: 'secret', label: '机密' }
]

/**
 * 审批流程选项
 */
const approvalProcessOptions = [
  { value: 'process1', label: '产品发布审批流程' },
  { value: 'process2', label: '设备变更审批流程' },
  { value: 'process3', label: '数据修改审批流程' }
]

/**
 * 版本抽屉可见性
 */
const versionDrawerVisible = ref(false)

/**
 * 版本对比弹窗可见性
 */
const compareDialogVisible = ref(false)

/**
 * 当前选中的模型
 */
const currentModel = ref(null)

/**
 * 版本数据
 */
const versionData = ref([])

/**
 * 版本分页配置
 */
const versionPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

/**
 * 版本对比表单
 */
const compareForm = reactive({
  baseVersion: '',
  compareVersion: ''
})

/**
 * 版本选项
 */
const versionOptions = ref([])

/**
 * 对比结果
 */
const compareResult = ref([])

/**
 * 详情弹窗可见性
 */
const detailDialogVisible = ref(false)

/**
 * 模型详情数据
 */
const modelDetail = ref({})

/**
 * 重置表单
 */
const resetForm = () => {
  if (modelFormRef.value) {
    modelFormRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(modelForm, {
    name: '',
    code: '',
    schema: '',
    parentModel: '',
    childModels: [],
    securityLevel: '',
    needApproval: false,
    approvalProcess: '',
    description: ''
  })
}

/**
 * 提交表单
 */
const submitForm = () => {
  if (!modelFormRef.value) return
  
  modelFormRef.value.validate((valid) => {
    if (valid) {
      // 根据不同的模型类型处理表单数据
      const formData = { ...modelForm }
      
      // 如果不是BOM模型，删除BOM特有字段
      if (currentModelType.value !== 'bom') {
        delete formData.parentModel
        delete formData.childModels
      }
      
      // 如果不需要审批，删除审批流程字段
      if (!formData.needApproval) {
        delete formData.approvalProcess
      }
      
      console.log('提交的表单数据:', formData)
      
      if (isEdit.value) {
        // 编辑模式
        ElMessage.success(`成功编辑${currentModelType.value}模型: ${formData.name}`)
      } else {
        // 新增模式
        ElMessage.success(`成功创建${currentModelType.value}模型: ${formData.name}`)
      }
      
      // 关闭对话框
      dialogVisible.value = false
      
      // 重置编辑状态
      isEdit.value = false
      
      // 刷新表格数据
      handleQuery()
    } else {
      ElMessage.error('表单验证失败，请检查输入')
      return false
    }
  })
}

/**
 * 处理新增
 */
const handleAdd = () => {
  // 设置为新增模式
  isEdit.value = false
  
  // 重置表单
  resetForm()
  
  // 打开对话框
  dialogVisible.value = true
}

/**
 * 处理取消
 */
const handleCancel = () => {
  // 关闭对话框
  dialogVisible.value = false
  
  // 重置编辑状态
  isEdit.value = false
}

/**
 * 处理移动
 */
const handleMove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要移动的数据')
    return
  }
  ElMessage.success(`选中了${selectedRows.value.length}条数据进行移动操作`)
  // 实际应用中这里应该打开移动的对话框
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  handleQuery()
}

/**
 * 处理发布
 * @param {Object} row - 行数据
 */
const handlePublish = (row) => {
  ElMessage.success(`发布模型: ${row.modelName}`)
  // 实际应用中这里应该调用发布API
}

/**
 * 是否为编辑模式
 */
const isEdit = ref(false)

/**
 * 处理编辑
 * @param {Object} row - 行数据
 */
const handleEdit = (row) => {
  // 设置为编辑模式
  isEdit.value = true
  
  // 填充表单数据
  Object.assign(modelForm, {
    id: row.id,
    name: row.modelName,
    code: row.code,
    schema: 'entity', // 默认值，实际应该从数据中获取
    parentModel: row.parentModel || '',
    childModels: row.childModels || [],
    securityLevel: 'public', // 默认值，实际应该从数据中获取
    needApproval: row.needApproval,
    approvalProcess: row.approvalProcess,
    description: row.description
  })
  
  // 打开对话框
  dialogVisible.value = true
}

/**
 * 处理删除
 * @param {Object} row - 行数据
 */
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除模型"${row.modelName}"吗？此操作不可恢复！`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    }
  ).then(() => {
    // 实际应用中这里应该调用删除API
    // 模拟删除操作
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success(`删除模型 "${row.modelName}" 成功`)
      // 如果当前页没有数据了，回到上一页
      if (tableData.value.length === 0 && pagination.currentPage > 1) {
        pagination.currentPage--
        handleQuery()
      }
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/**
 * 处理选择变化
 * @param {Array} selection - 选中的行
 */
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

/**
 * 处理每页大小变化
 * @param {Number} size - 每页大小
 */
const handleSizeChange = (size) => {
  pagination.pageSize = size
  handleQuery()
}

/**
 * 处理当前页变化
 * @param {Number} page - 当前页
 */
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  handleQuery()
}

/**
 * 处理版本点击
 * @param {Object} row - 行数据
 */
const handleVersionClick = (row) => {
  currentModel.value = row
  
  // 重置版本分页
  versionPagination.currentPage = 1
  
  // 加载版本数据
  loadVersionData()
  
  versionDrawerVisible.value = true
}

/**
 * 获取审批状态对应的标签类型
 * @param {string} status - 审批状态
 * @returns {string} - 标签类型
 */
const getApprovalStatusType = (status) => {
  const typeMap = {
    '已通过': 'success',
    '审批中': 'warning',
    '已拒绝': 'danger',
    '无需审批': 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 加载版本数据
 */
const loadVersionData = () => {
  // 模拟版本数据（实际应用中应该根据分页参数调用API）
  const allVersions = [
    {
      version: 'V10',
      creator: 'admin',
      createTime: '2025-04-30 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-30 12:12:12'
    },
    {
      version: 'V9',
      creator: 'admin',
      createTime: '2025-04-29 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-29 12:12:12'
    },
    {
      version: 'V8',
      creator: 'admin',
      createTime: '2025-04-28 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-28 12:12:12'
    },
    {
      version: 'V7',
      creator: 'admin',
      createTime: '2025-04-27 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-27 12:12:12'
    },
    {
      version: 'V6',
      creator: 'admin',
      createTime: '2025-04-26 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-26 12:12:12'
    },
    {
      version: 'V5',
      creator: 'admin',
      createTime: '2025-04-25 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-25 12:12:12'
    },
    {
      version: 'V4',
      creator: 'admin',
      createTime: '2025-04-24 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-24 12:12:12'
    },
    {
      version: 'V3',
      creator: 'admin',
      createTime: '2025-04-23 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-23 12:12:12'
    },
    {
      version: 'V2',
      creator: 'admin',
      createTime: '2025-04-22 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-22 12:12:12'
    },
    {
      version: 'V1',
      creator: 'admin',
      createTime: '2025-04-21 12:12:12',
      lastModifier: 'admin',
      lastModifyTime: '2025-04-21 12:12:12'
    }
  ]
  
  // 计算分页数据
  const startIndex = (versionPagination.currentPage - 1) * versionPagination.pageSize
  const endIndex = startIndex + versionPagination.pageSize
  
  versionData.value = allVersions.slice(startIndex, endIndex)
  versionPagination.total = allVersions.length
  
  // 更新版本选项（用于版本对比）
  versionOptions.value = allVersions.map(item => ({
    value: item.version,
    label: item.version
  }))
}

/**
 * 处理版本对比
 */
const handleVersionCompare = () => {
  compareDialogVisible.value = true
}

/**
 * 重置对比表单
 */
const resetCompareForm = () => {
  compareForm.baseVersion = ''
  compareForm.compareVersion = ''
  compareResult.value = []
}

/**
 * 执行版本对比
 */
const performCompare = () => {
  if (!compareForm.baseVersion || !compareForm.compareVersion) {
    ElMessage.warning('请选择基准版本和对比版本')
    return
  }
  
  if (compareForm.baseVersion === compareForm.compareVersion) {
    ElMessage.warning('基准版本和对比版本不能相同')
    return
  }
  
  // 模拟对比结果
  compareResult.value = [
    {
      baseFieldName: '名称a',
      compareFieldName: '件',
      isDifferent: true,
      changeType: '修改'
    },
    {
      baseFieldName: '名称b',
      compareFieldName: '名称b（标记了不同）',
      isDifferent: true,
      changeType: '修改'
    },
    {
      baseFieldName: '名称a',
      compareFieldName: '女',
      isDifferent: true,
      changeType: '修改'
    },
    {
      baseFieldName: '名称b',
      compareFieldName: '新增字段',
      isDifferent: true,
      changeType: '新增'
    },
    {
      baseFieldName: '名称a',
      compareFieldName: '件',
      isDifferent: false,
      changeType: ''
    },
    {
      baseFieldName: '名称b',
      compareFieldName: '吨',
      isDifferent: true,
      changeType: '修改'
    }
  ]
  
  ElMessage.success('版本对比完成')
}

/**
 * 处理版本每页大小变化
 * @param {Number} size - 每页大小
 */
const handleVersionSizeChange = (size) => {
  versionPagination.pageSize = size
  versionPagination.currentPage = 1
  loadVersionData()
}

/**
 * 处理版本当前页变化
 * @param {Number} page - 当前页
 */
const handleVersionCurrentChange = (page) => {
  versionPagination.currentPage = page
  loadVersionData()
}

/**
 * 处理模型详情点击
 * @param {Object} row - 行数据
 */
const handleModelDetailClick = (row) => {
  modelDetail.value = { ...row }
  detailDialogVisible.value = true
}



/**
 * 获取变更类型颜色
 * @param {String} changeType - 变更类型
 */
const getChangeTypeColor = (changeType) => {
  switch (changeType) {
    case '新增':
      return 'success'
    case '删除':
      return 'danger'
    case '修改':
      return 'warning'
    default:
      return 'info'
  }
}

// 初始加载数据
handleQuery()
</script>

<style lang="scss" scoped>
.physical-model {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  
  .search-form {
    // margin-bottom: 16px;
    padding: 16px;
    // background: #f8f9fa;
    border-radius: 4px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .toolbar-left {
      display: flex;
      gap: 8px;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;

    .el-table {
      height: 100%;
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

// 版本抽屉样式
.version-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .version-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .version-table {
    flex: 1;
    margin-bottom: 20px;
  }
  
  .version-pagination {
    display: flex;
    justify-content: center;
    padding: 16px 0;
    border-top: 1px solid #ebeef5;
  }
}

// 版本对比样式
.compare-actions {
  margin: 20px 0;
  text-align: center;
}
    h3 {
        margin-bottom: 16px;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
    }
.compare-result {
  margin-top: 20px;
  

}

.field-different {
  color: #e6a23c;
  font-weight: 600;
}

.model-detail {
  .el-descriptions {
    margin-top: 20px;
  }
  
  .el-descriptions-item__label {
    font-weight: bold;
    width: 120px;
  }
  
  .el-descriptions-item__content {
    word-break: break-all;
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-drawer__body) {
  padding: 20px;
}
</style>