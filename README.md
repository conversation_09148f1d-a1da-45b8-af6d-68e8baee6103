# 平台简介

* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。

## 前端运行

```bash
# 进入项目目录
cd xugurtp-ui-vue3

# 安装依赖
pnpm install

# 启动服务
npm run dev 

# 构建生产环境 npm run build:prod 
# 前端访问地址 http://localhost
```

## 内置功能

1. 用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2. 菜单管理：配置系统菜单，操作权限，按钮权限标识等。
3. 角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
4. 字典管理：对系统中经常使用的一些较为固定的数据进行维护。
5. 通知公告：系统通知公告信息发布维护。
6. 操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
7. 登录日志：系统登录日志记录查询包含登录异常。

## 演示图

| 0   | 1   |
| --- | --- |
|     |     |
|     |     |
|     |     |
