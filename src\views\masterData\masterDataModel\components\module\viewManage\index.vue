<template>
    <div class="classification-model">
        <!-- 搜索表单 -->
        <div class="search-form">
            <el-form ref="queryFormRef" :model="queryForm" :inline="true" label-width="auto">
                <el-form-item label="视图名称" prop="viewName">
                    <el-input v-model="queryForm.viewName" placeholder="请输入视图名称" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">
                        <el-icon>
                            <Search />
                        </el-icon>
                        搜索
                    </el-button>
                    <el-button @click="resetQuery">
                        <el-icon>
                            <Refresh />
                        </el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 操作按钮 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <el-button v-if="showAddViewButton" type="primary" @click="handleAddView">
                    <el-icon>
                        <Plus />
                    </el-icon>
                    新增视图
                </el-button>
                <el-button @click="handleCodeClassification">
                    <el-icon>
                        <Document />
                    </el-icon>
                    编码与分类
                </el-button>
                <el-button @click="handleSimilaritySetting">
                    <el-icon>
                        <Setting />
                    </el-icon>
                    相似度设置
                </el-button>
                <el-button @click="handleSecuritySetting">
                    <el-icon>
                        <Lock />
                    </el-icon>
                    密级设置
                </el-button>
                <el-button type="danger" @click="handleBatchDelete">
                    <el-icon>
                        <Delete />
                    </el-icon>
                    删除
                </el-button>
            </div>

            <div class="toolbar-right">
                <el-button type="text" @click="handleRefresh">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                </el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <el-table ref="tableRef" :data="tableData" v-loading="loading" @selection-change="handleSelectionChange"
                style="width: 100%">
                <el-table-column type="selection" width="50" align="center" />

                <el-table-column label="序号" type="index" width="60" align="center">
                    <template #default="{ $index }">
                        {{ (pagination.currentPage - 1) * pagination.pageSize + $index + 1 }}
                    </template>
                </el-table-column>

                <el-table-column prop="viewName" label="视图名称" min-width="150" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-button type="text" @click="handleEditView(row)" class="view-name-link">
                            {{ row.viewName }}
                        </el-button>
                    </template>
                </el-table-column>

                <el-table-column prop="creator" label="创建人" width="100" align="center" />

                <el-table-column prop="createTime" label="创建时间" width="160" align="center" />

                <el-table-column prop="lastModifier" label="最后修改人" width="120" align="center" />

                <el-table-column prop="lastModifyTime" label="最后修改时间" width="160" align="center" />

                <el-table-column label="操作" width="150" align="center" fixed="right">
                    <template #default="{ row }">
                        <el-button type="text" size="small" @click="handleEditView(row)">
                            编辑
                        </el-button>
                        <el-button type="text" size="small" @click="handleView(row)">
                            查看
                        </el-button>
                        <el-button type="text" size="small" @click="handleDeleteSingle(row)" style="color: #f56c6c">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
            <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                :page-sizes="pagination.pageSizes" :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>

    <!-- 新增视图侧边抽屉 -->
    <el-drawer v-model="drawerVisible" :title="getDrawerTitle()" size="60%" :before-close="handleDrawerClose">
        <el-form ref="viewFormRef" :model="viewForm" :rules="viewRules" label-width="100px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="视图名称" prop="viewName">
                        <el-input v-model="viewForm.viewName" placeholder="请输入视图名称" maxlength="100" show-word-limit :disabled="editMode === 'view'" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="viewForm.description" placeholder="请输入描述" maxlength="200" show-word-limit :disabled="editMode === 'view'" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 字段表格 -->
        <div class="field-table-container">
                <div class="field-table-header">
                    <h3>字段列表</h3>
                    <div class="field-table-actions" v-if="editMode !== 'view'">
                        <!-- 基本信息视图或非BOM模型的操作按钮 -->
                        <template v-if="!currentEditView || currentEditView.viewType === 'basic' || currentTab !== 'bom'">
                            <el-button type="primary" size="small" @click="handleAddField">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                                新建
                            </el-button>
                            <el-dropdown>
                                <el-button type="primary" size="small">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                    批量新建
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="handleImportFromTable">库表导入</el-dropdown-item>
                                        <el-dropdown-item @click="handleImportFromExcel">Excel导入</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                        
                        <!-- 母件信息视图的操作按钮 -->
                        <template v-else-if="currentEditView && currentEditView.viewType === 'parent'">
                            <el-button type="primary" size="small" @click="handlePickParent">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                                拾取
                            </el-button>
                        </template>
                        
                        <!-- 子件清单信息视图的操作按钮 -->
                        <template v-else-if="currentEditView && currentEditView.viewType === 'child'">
                            <el-dropdown>
                                <el-button type="primary" size="small">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                    新增
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="handleAddChildField">新增字段</el-dropdown-item>
                                        <el-dropdown-item @click="handleReferenceField">引用字段</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                        
                        <!-- 通用操作按钮 -->
                        <el-button type="primary" size="small" @click="handleMoveUp" :disabled="!hasSelectedSegments">
                            <el-icon>
                                <ArrowUp />
                            </el-icon>
                            上移
                        </el-button>
                        <el-button type="primary" size="small" @click="handleMoveDown" :disabled="!hasSelectedSegments">
                            <el-icon>
                                <ArrowDown />
                            </el-icon>
                            下移
                        </el-button>
                        <el-button type="primary" size="small" @click="handleDeleteField" :disabled="!hasSelectedSegments">
                            <el-icon>
                                <Delete />
                            </el-icon>
                            删除
                        </el-button>
                    </div>
                </div>
            <el-table :data="viewForm.fields" style="width: 100%" @selection-change="handleSegmentSelectionChange"
                 highlight-current-row>
                 <el-table-column type="selection" width="55" v-if="editMode !== 'view'" />
                <el-table-column prop="fieldName" label="字段名称" min-width="120" />
                <el-table-column prop="fieldComment" label="字段注释" min-width="120" />
                <el-table-column label="数据类型" width="120">
                     <template #default="{ row }">
                         <span v-if="currentEditView && currentEditView.viewType === 'child'">{{ row.fieldType || row.dataType }}</span>
                         <span v-else>{{ row.dataType }}</span>
                     </template>
                 </el-table-column>
                <el-table-column prop="securityLevel" label="数据密级" width="120" />
                <el-table-column prop="isPrimaryKey" label="是否主键" width="80">
                    <template #default="{ row }">
                        <el-tag :type="row.isPrimaryKey ? 'success' : 'info'">
                            {{ row.isPrimaryKey ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isNullable" label="是否可为空" width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.isNullable ? 'info' : 'danger'">
                            {{ row.isNullable ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isListDisplay" label="是否列表显示" width="120">
                    <template #default="{ row }">
                        <el-tag :type="row.isListDisplay ? 'success' : 'info'">
                            {{ row.isListDisplay ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right" v-if="editMode !== 'view'">
                    <template #default="{ row, $index }">
                        <!-- 母件信息视图：不能编辑 -->
                        <template v-if="currentEditView && currentEditView.viewType === 'parent'">
                            <span class="text-gray-400">不可编辑</span>
                        </template>
                        <!-- 其他视图：正常编辑 -->
                        <template v-else>
                            <el-button type="primary" link @click="handleEditField(row, $index)">
                                <el-icon>
                                    <Edit />
                                </el-icon>
                            </el-button>
                            <el-button v-if="!row.isDefault" type="danger" link @click="handleDeleteSingleField(row, $index)">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <template #footer>
            <div style="flex: auto">
                <el-button @click="handleDrawerClose">{{ editMode === 'view' ? '关闭' : '取消' }}</el-button>
                <el-button v-if="editMode !== 'view'" type="primary" @click="handleSubmitView">确认</el-button>
            </div>
        </template>
    </el-drawer>

    <!-- 新增/编辑字段弹窗 -->
    <el-dialog v-model="fieldDialogVisible" :title="isEditingField ? '编辑字段' : '新增字段'" width="600px"
        :before-close="handleFieldDialogClose">
        <el-form ref="fieldFormRef" :model="fieldForm" :rules="fieldRules" label-width="120px">
            <el-form-item label="字段名称" prop="fieldName">
                <el-input v-model="fieldForm.fieldName" placeholder="仅字母数字下划线且字母开头" maxlength="100" show-word-limit />
            </el-form-item>
            <el-form-item label="字段注释" prop="fieldComment">
                <el-input v-model="fieldForm.fieldComment" placeholder="请输入字段注释" maxlength="100" show-word-limit />
            </el-form-item>
            <el-form-item :label="currentEditView && currentEditView.viewType === 'child' ? '字段类型' : '数据类型'" prop="dataType">
                <el-select v-model="fieldForm.dataType" placeholder="请选择类型" style="width: 100%">
                    <template v-if="currentEditView && currentEditView.viewType === 'child'">
                        <el-option label="普通字段" value="varchar" />
                        <el-option label="引用字段" value="reference" />
                        <el-option label="数值字段" value="numeric" />
                        <el-option label="日期字段" value="date" />
                        <el-option label="布尔字段" value="boolean" />
                    </template>
                    <template v-else>
                        <el-option v-for="item in dataTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </template>
                </el-select>
            </el-form-item>
            <el-form-item label="字段长度" prop="fieldLength">
                <el-input-number v-model="fieldForm.fieldLength" :min="0" placeholder="请输入字段长度" style="width: 100%" />
            </el-form-item>
            <el-form-item label="小数位数" prop="decimalPlaces" v-if="showDecimalPlaces">
                <el-input-number v-model="fieldForm.decimalPlaces" :min="0" placeholder="请输入小数位数" style="width: 100%" />
            </el-form-item>
            <el-form-item label="数据密级" prop="securityLevel">
                <el-select v-model="fieldForm.securityLevel" placeholder="请选择数据密级" style="width: 100%">
                    <el-option v-for="item in securityLevelOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否唯一" prop="isUnique">
                <el-radio-group v-model="fieldForm.isUnique">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否主键" prop="isPrimaryKey">
                <el-radio-group v-model="fieldForm.isPrimaryKey" @change="handlePrimaryKeyChange">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否可为空" prop="isNullable" v-if="!fieldForm.isPrimaryKey">
                <el-radio-group v-model="fieldForm.isNullable">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否列表显示" prop="isListDisplay" v-if="isBasicInfoTab">
                <el-radio-group v-model="fieldForm.isListDisplay">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleFieldDialogClose">取消</el-button>
                <el-button type="primary" @click="handleSubmitField">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 删除字段确认弹窗 -->
    <el-dialog v-model="deleteConfirmVisible" title="提示" width="400px">
        <p>是否确认删除该字段？</p>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="deleteConfirmVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmDeleteField">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 库表导入对话框 -->
    <el-dialog v-model="tableImportDialogVisible" title="库表导入" width="600px">
        <el-form label-width="120px">
            <el-form-item label="数据源">
                <el-select v-model="tableImportForm.dataSource" placeholder="请选择数据源" style="width: 100%"
                    @change="handleDataSourceChange">
                    <el-option v-for="item in dataSourceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="表名">
                <el-select v-model="tableImportForm.tableName" placeholder="请选择表" style="width: 100%">
                    <el-option v-for="item in tableOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="tableImportDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmImportFromTable">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog v-model="excelImportDialogVisible" title="Excel导入" width="600px">
        <el-upload class="upload-demo" drag action="#" :auto-upload="false" :on-change="handleExcelChange" :limit="1"
            accept=".xlsx,.xls">
            <el-icon class="el-icon--upload">
                <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
                拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
                <div class="el-upload__tip">
                    请上传Excel文件，第一行为字段名称，第二行为字段注释
                </div>
            </template>
        </el-upload>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="excelImportDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmImportFromExcel">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 密级设置对话框 -->
    <el-dialog v-model="securitySettingDialogVisible" title="密级设置" width="400px">
        <el-form :model="securitySettingForm" :rules="securitySettingRules" label-width="100px">
            <el-form-item label="选择密级" prop="securityLevel">
                <el-select v-model="securitySettingForm.securityLevel" placeholder="请选择内容" style="width: 100%">
                    <el-option v-for="item in securityLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="securitySettingDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmSecuritySetting">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 编码与分类对话框 -->
    <el-dialog v-model="codeClassificationDialogVisible" :title="getCodeClassificationTitle()" width="800px">
        <el-form :model="codeClassificationForm" :rules="codeClassificationRules" label-width="140px">
            <!-- 编码设置 -->
            <div class="form-section">
                <h3 class="section-title">编码设置</h3>
                <el-form-item label="主数据编码字段" prop="codeField">
                    <el-select v-model="codeClassificationForm.codeField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="主数据编码规则" prop="codeRule">
                    <el-select v-model="codeClassificationForm.codeRule" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in codeRuleOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>

            <!-- 业务字段设置 -->
            <div class="form-section">
                <h3 class="section-title">业务字段设置</h3>
                <el-form-item label="业务主键字段" prop="businessKeyField">
                    <el-select v-model="codeClassificationForm.businessKeyField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="主数据名称字段" prop="masterDataNameField">
                    <el-select v-model="codeClassificationForm.masterDataNameField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>

            <!-- 分类设置 -->
            <div class="form-section">
                <h3 class="section-title">分类设置</h3>
                <el-form-item label="主数据分类字段" prop="classificationField">
                    <el-select v-model="codeClassificationForm.classificationField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="主数据分类依据" prop="classificationBasis">
                    <el-select v-model="codeClassificationForm.classificationBasis" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in classificationBasisOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>

            <!-- 层级设置（仅分类模型显示） -->
            <div v-if="currentTab === 'category'" class="form-section">
                <h3 class="section-title">层级设置</h3>
                <el-form-item label="根节点ID">
                    <el-input v-model="codeClassificationForm.rootNodeId" placeholder="请输入根节点ID" style="width: 100%" />
                </el-form-item>
                <el-form-item label="*层级ID字段" prop="hierarchyIdField">
                    <el-select v-model="codeClassificationForm.hierarchyIdField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="*层级Name字段" prop="hierarchyNameField">
                    <el-select v-model="codeClassificationForm.hierarchyNameField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="*层级PID字段" prop="hierarchyPidField">
                    <el-select v-model="codeClassificationForm.hierarchyPidField" placeholder="请选择内容" style="width: 100%">
                        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>
        </el-form>

        <!-- 说明信息 -->
        <div class="description-section">
            <h4>【说明】</h4>
            <ul>
                <li v-if="currentTab !== 'classification'">实体模型和BOM模型没有层级设置。</li>
                <li>在选择字段时，字段的展示方式都以 --字段注释 (字段名称)-- 的样式展示，如 人员名称(RYMC)</li>
                <li v-if="currentTab === 'classification'">分类设置中的【主数据分类字段】和层级设置中的【层级ID字段】进行映射匹配</li>
                <li v-if="currentTab === 'classification'">加描述，更加清晰</li>
            </ul>
            
            <div class="field-descriptions">
                <h4>【编码与分类】</h4>
                <p><strong>1、主数据编码字段：</strong>非必填，选择视图中某个字段进行编码，要求新增数据时能自动生成编码，不用手动输入。</p>
                <p><strong>2、主数据编码规则：</strong>绑定编码生成的规则，下拉选择，这个是在主数据编码中创建的。</p>
                <p><strong>5、主数据分类字段：</strong>主数据在分类的时候，选择哪个字段进行分类，下拉选择视图中的字段。</p>
                <p><strong>6、主数据分类依据：</strong>主数据在分类的时候，根据哪个进行分类，下拉选择分类模型。</p>
                <p v-if="currentTab === 'classification'"><strong>7、层级 ID 字段：</strong>下拉选择，分类的树的层级 ID，这个字段一般会选择编码字段。</p>
                <p v-if="currentTab === 'classification'"><strong>8、层级 Name 字段：</strong>下拉选择，分类树的名称。</p>
                <p v-if="currentTab === 'classification'"><strong>9、层级 PID 字段：</strong>下拉选择，分类树层级的上级字段，根据层级来展示分类树。</p>
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="codeClassificationDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmitCodeClassification">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 相似度设置对话框 -->
    <el-dialog v-model="similarityDialogVisible" title="相似度设置" width="800px">
        <div class="similarity-description">
            <p><strong>提示：</strong>相似度设置会用于主数据检测中，超过阈值的数据会被视为相似数据。</p>
        </div>
        
        <el-form ref="similarityFormRef" :model="similarityForm" :rules="similarityRules" label-width="120px">
            <!-- 相似度阈值设置 -->
            <el-form-item label="*相似度阈值" prop="threshold">
                <el-input-number 
                    v-model="similarityForm.threshold" 
                    :min="0" 
                    :max="100" 
                    :precision="0"
                    style="width: 200px" 
                />
                <span style="margin-left: 8px;">%</span>
            </el-form-item>
            
            <!-- 相似度权重设置 -->
            <el-form-item label="*相似度权重">
                <div class="weight-section">
                    <div class="weight-header">
                        <el-button type="primary" icon="Plus" @click="addWeightField">添加字段</el-button>
                        <span class="weight-total">权重总和：{{ getTotalWeight }}%</span>
                    </div>
                    
                    <!-- 权重字段表格 -->
                    <el-table 
                        :data="similarityForm.weightFields" 
                        style="width: 100%; margin-top: 16px;"
                        :show-header="true"
                    >
                        <el-table-column type="index" label="序号" width="60" align="center" />
                        
                        <el-table-column label="字段名称" min-width="200">
                            <template #default="{ row, $index }">
                                <el-select 
                                    v-model="row.fieldName" 
                                    placeholder="请选择字段" 
                                    style="width: 100%"
                                    @change="(value) => handleFieldChange(value, $index)"
                                >
                                    <el-option 
                                        v-for="field in fieldOptions" 
                                        :key="field.value" 
                                        :label="field.label" 
                                        :value="field.value"
                                    />
                                </el-select>
                            </template>
                        </el-table-column>
                        
                        <el-table-column width="180">
                            <template #default="{ row }">
                                <div style="display: flex;align-items: center">
                                    <el-input-number 
                                    v-model="row.weight" 
                                    :min="1" 
                                    :max="100" 
                                    :precision="0"
                                    style="width: 100%"
                                    />
                                    <span style="margin-left: 4px;">%</span>
                                </div>
                            </template>
                            <template #header>
                                <span>权重</span>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="操作" width="80" align="center">
                            <template #default="{ $index }">
                                <el-button 
                                    type="danger" 
                                    icon="Delete" 
                                    size="small" 
                                    @click="removeWeightField($index)"
                                />
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 示例说明 -->
                    <div class="weight-example" v-if="similarityForm.weightFields.length === 0">
                        <p><strong>字段示例：</strong></p>
                        <ul>
                            <li>员工编码（权重10%）</li>
                            <li>员工姓名、性别、户籍地、身份证号、所属部门</li>
                        </ul>
                    </div>
                </div>
            </el-form-item>
        </el-form>
        
        <!-- 相似度计算公式说明 -->
        <div class="similarity-formula">
            <h4>相似度设置规则：</h4>
            <ol>
                <li>相似度阈值：仅能输入大于等于0，小于等于100的正整数。默认展示60；超过以及等于阈值的数据在相似度检测中，会被认为是相似数据。</li>
                <li>相似度权重：默认不展示字段，点击+添加视图中基本信息的字段，点击-号删除字段，不用二次确认。权重在输入的时候限制从1到100的正整数，所有字段的权重相加需等于100才能保存成功。</li>
            </ol>
            <p><strong>相似度计算公式：</strong></p>
            <p>相似度 = 字段1相似度×字段1权重 + 字段2相似度×字段2权重 + ... + 字段n相似度×字段n权重</p>
        </div>
        
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="similarityDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmitSimilarity">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, inject } from 'vue'
import { Plus, Document, Setting, Lock, Delete, Refresh, Search, ArrowUp, ArrowDown, Edit, View, UploadFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 获取当前模型类型
 */
const currentTab = inject('currentTab', ref('entity'))

/**
 * 判断是否显示新增视图按钮（实体模型和分类模型显示，BOM模型隐藏）
 */
const showAddViewButton = computed(() => {
    return currentTab.value !== 'bom'
})

/**
 * 表单引用
 */
const queryFormRef = ref()
const tableRef = ref()

/**
 * 表格数据加载状态
 */
const loading = ref(false)

/**
 * 查询表单数据
 */
const queryForm = reactive({
    viewName: ''
})

/**
 * 表格数据
 */
const tableData = ref([])

/**
 * 分页配置
 */
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100]
})

/**
 * 选中的行数据
 */
const selectedRows = ref([])


/**
 * 库表导入对话框可见性
 */
const tableImportDialogVisible = ref(false)

/**
 * Excel导入对话框可见性
 */
const excelImportDialogVisible = ref(false)

/**
 * 库表导入表单数据
 */
const tableImportForm = reactive({
    dataSource: '',
    tableName: ''
})

/**
 * 数据源选项
 */
const dataSourceOptions = [
    { label: 'MySQL数据源', value: 'mysql' },
    { label: 'Oracle数据源', value: 'oracle' },
    { label: 'SQL Server数据源', value: 'sqlserver' },
    { label: 'PostgreSQL数据源', value: 'postgresql' }
]

/**
 * 表选项数据
 */
const tableOptions = ref([])

/**
 * 上传的Excel文件
 */
const excelFile = ref(null)

/**
 * 编码与分类对话框显示状态
 */
const codeClassificationDialogVisible = ref(false)

/**
 * 相似度设置对话框显示状态
 */
const similarityDialogVisible = ref(false)

/**
 * 编码与分类表单数据
 */
const codeClassificationForm = reactive({
    // 编码设置
    codeField: '',
    codeRule: '',
    // 业务字段设置
    businessKeyField: '',
    masterDataNameField: '',
    // 分类设置
    classificationField: '',
    classificationBasis: '',
    // 层级设置（仅分类模型）
    rootNodeId: '',
    hierarchyIdField: '',
    hierarchyNameField: '',
    hierarchyPidField: ''
})

/**
 * 编码与分类表单验证规则
 */
const codeClassificationRules = {
    codeField: [
        { required: true, message: '请选择编码字段', trigger: 'change' }
    ],
    codeRule: [
        { required: true, message: '请选择编码规则', trigger: 'change' }
    ],
    businessKeyField: [
        { required: true, message: '请选择业务主键字段', trigger: 'change' }
    ],
    masterDataNameField: [
        { required: true, message: '请选择主数据名称字段', trigger: 'change' }
    ],
    classificationField: [
        { required: true, message: '请选择分类字段', trigger: 'change' }
    ],
    classificationBasis: [
        { required: true, message: '请选择分类依据', trigger: 'change' }
    ],
    hierarchyIdField: [
        { required: true, message: '请选择层级ID字段', trigger: 'change' }
    ],
    hierarchyNameField: [
        { required: true, message: '请选择层级Name字段', trigger: 'change' }
    ],
    hierarchyPidField: [
        { required: true, message: '请选择层级PID字段', trigger: 'change' }
    ]
}

/**
 * 相似度设置表单验证规则
 */
const similarityRules = {
    threshold: [
        { required: true, message: '请输入相似度阈值', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '相似度阈值必须在0-100之间', trigger: 'blur' }
    ]
}

/**
 * 编码规则选项
 */
const codeRuleOptions = ref([
    { label: '自动递增编码', value: 'auto_increment' },
    { label: '日期时间编码', value: 'datetime' },
    { label: '自定义编码规则', value: 'custom' }
])

/**
 * 分类依据选项
 */
const classificationBasisOptions = ref([
    { label: '产品分类模型', value: 'product_classification' },
    { label: '设备分类模型', value: 'equipment_classification' },
    { label: '物料分类模型', value: 'material_classification' }
])

/**
 * 相似度设置表单数据
 */
const similarityForm = reactive({
    threshold: 60, // 相似度阈值，默认60%
    weightFields: [] // 权重字段列表
})

/**
 * 相似度设置表单引用
 */
const similarityFormRef = ref()

/**
 * 获取字段选项（格式：字段注释(字段名称)）
 */
const fieldOptions = computed(() => {
    const currentFields = isBasicInfoTab.value ? viewForm.fields : extendViewForm.fields
    return currentFields.map(field => ({
        label: `${field.fieldComment || field.fieldName}(${field.fieldName})`,
        value: field.fieldName
    }))
})

/**
 * 加载表格数据
 */
const loadTableData = () => {
    // 实际应用中这里应该调用API获取数据
    loading.value = true
    setTimeout(() => {
        // 根据模型类型生成不同的模拟数据
        let allData = []
        
        if (currentTab.value === 'bom') {
            // BOM模型默认三个视图
            allData = [
                {
                    id: 1,
                    viewName: '基本信息',
                    viewType: 'basic',
                    creator: '系统',
                    createTime: '2023-01-01 10:00:00',
                    lastModifier: '系统',
                    lastModifyTime: '2023-01-01 10:00:00',
                    isDefault: true
                },
                {
                    id: 2,
                    viewName: '母件信息',
                    viewType: 'parent',
                    creator: '系统',
                    createTime: '2023-01-01 10:00:00',
                    lastModifier: '系统',
                    lastModifyTime: '2023-01-01 10:00:00',
                    isDefault: true
                },
                {
                    id: 3,
                    viewName: '子件清单信息',
                    viewType: 'child',
                    creator: '系统',
                    createTime: '2023-01-01 10:00:00',
                    lastModifier: '系统',
                    lastModifyTime: '2023-01-01 10:00:00',
                    isDefault: true
                }
            ]
        } else {
            // 实体模型和分类模型的数据
            allData = [
                {
                    id: 1,
                    viewName: '产品分类视图',
                    viewType: 'basic',
                    creator: '管理员',
                    createTime: '2023-01-01 10:00:00',
                    lastModifier: '管理员',
                    lastModifyTime: '2023-01-02 15:30:00',
                    isDefault: false
                },
                {
                    id: 2,
                    viewName: '设备分类视图',
                    viewType: 'basic',
                    creator: '管理员',
                    createTime: '2023-01-03 09:15:00',
                    lastModifier: '管理员',
                    lastModifyTime: '2023-01-04 11:20:00',
                    isDefault: false
                },
                {
                    id: 3,
                    viewName: '物料分类视图',
                    viewType: 'basic',
                    creator: '管理员',
                    createTime: '2023-01-05 14:25:00',
                    lastModifier: '管理员',
                    lastModifyTime: '2023-01-06 16:40:00',
                    isDefault: false
                }
            ]
        }

        // 根据查询条件过滤数据
        let filteredData = [...allData]
        if (queryForm.viewName) {
            filteredData = filteredData.filter(item =>
                item.viewName.includes(queryForm.viewName)
            )
        }

        tableData.value = filteredData
        pagination.total = filteredData.length
        loading.value = false
    }, 500)
}

/**
 * 处理查询
 */
const handleQuery = () => {
    pagination.currentPage = 1 // 重置到第一页
    loadTableData()
}

/**
 * 重置查询
 */
const resetQuery = () => {
    if (queryFormRef.value) {
        queryFormRef.value.resetFields()
    }
    pagination.currentPage = 1
    loadTableData()
}

/**
 * 抽屉可见性
 */
const drawerVisible = ref(false)

/**
 * 当前激活的标签页
 */
const activeTab = ref('basicInfo')

/**
 * 表单引用
 */
const viewFormRef = ref()
const extendViewFormRef = ref()
const fieldFormRef = ref()

/**
 * 视图表单数据
 */
const viewForm = reactive({
    viewName: '',
    viewCode: '',
    modelId: '',
    description: '',
    fields: []
})

/**
 * 扩展视图表单数据
 */
const extendViewForm = reactive({
    viewName: '',
    description: '',
    fields: []
})

/**
 * 字段表单数据
 */
const fieldForm = reactive({
    fieldName: '',
    fieldComment: '',
    dataType: '',
    fieldLength: null,
    decimalPlaces: null,
    securityLevel: 'L1',
    isUnique: true,
    isPrimaryKey: false,
    isNullable: true,
    isListDisplay: false,
    isDefault: false
})

/**
 * 视图表单验证规则
 */
const viewRules = {
    viewName: [
        { required: true, message: '请输入视图名称', trigger: 'blur' },
        { max: 50, message: '视图名称不能超过50个字符', trigger: 'blur' }
    ],
    viewCode: [
        { required: true, message: '请输入视图代号', trigger: 'blur' },
        { max: 50, message: '视图代号不能超过50个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9]+$/, message: '视图代号只能包含数字和大小写字母', trigger: 'blur' }
    ],
    modelId: [
        { required: true, message: '请选择所属模型', trigger: 'change' }
    ],
    description: [
        { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
    ]
}



/**
 * 字段表单验证规则
 */
const fieldRules = {
    fieldName: [
        { required: true, message: '请输入字段名称', trigger: 'blur' },
        { max: 100, message: '字段名称不能超过100个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段名称只能包含字母、数字、下划线且必须以字母开头', trigger: 'blur' }
    ],
    fieldComment: [
        { max: 100, message: '字段注释不能超过100个字符', trigger: 'blur' },
        { pattern: /^[^<>"'&]*$/, message: '字段注释不能包含特殊符号', trigger: 'blur' }
    ],
    dataType: [
        { required: true, message: '请选择数据类型', trigger: 'change' }
    ]
}



/**
 * 数据类型选项
 */
const dataTypeOptions = [
    { label: 'tinyint', value: 'tinyint' },
    { label: 'smallint', value: 'smallint' },
    { label: 'integer', value: 'integer' },
    { label: 'bigint', value: 'bigint' },
    { label: 'float', value: 'float' },
    { label: 'double', value: 'double' },
    { label: 'numeric', value: 'numeric' },
    { label: 'char', value: 'char' },
    { label: 'nchar', value: 'nchar' },
    { label: 'varchar', value: 'varchar' },
    { label: 'varchar2', value: 'varchar2' },
    { label: 'date', value: 'date' },
    { label: 'datetime', value: 'datetime' },
    { label: 'timestamp', value: 'timestamp' },
    { label: 'time', value: 'time' },
    { label: 'blob', value: 'blob' },
    { label: 'text', value: 'text' },
    { label: 'int with time zone', value: 'int with time zone' },
    { label: 'time with time zone', value: 'time with time zone' },
    { label: 'timestamp with time zone', value: 'timestamp with time zone' },
    { label: 'datetime with time zone', value: 'datetime with time zone' },
    { label: 'boolean', value: 'boolean' },
    { label: 'jsonb', value: 'jsonb' },
    { label: 'uuid', value: 'uuid' }
]

/**
 * 数据密级选项
 */
const securityLevelOptions = [
    { label: 'L1/内部数据', value: 'L1' },
    { label: 'L2/秘密', value: 'L2' },
    { label: 'L3/机密', value: 'L3' },
    { label: 'L4/绝密', value: 'L4' }
]

/**
 * 字段对话框可见性
 */
const fieldDialogVisible = ref(false)

/**
 * 是否正在编辑字段
 */
const isEditingField = ref(false)

/**
 * 密级设置对话框可见性
 */
const securitySettingDialogVisible = ref(false)

/**
 * 密级设置表单数据
 */
const securitySettingForm = reactive({
    securityLevel: ''
})

/**
 * 密级设置表单验证规则
 */
const securitySettingRules = {
    securityLevel: [
        { required: true, message: '请选择密级', trigger: 'change' }
    ]
}

/**
 * 当前编辑的字段索引
 */
const currentFieldIndex = ref(-1)

/**
 * 是否为基本信息标签页
 */
const isBasicInfoTab = computed(() => activeTab.value === 'basicInfo')

/**
 * 是否显示小数位数字段
 */
const showDecimalPlaces = computed(() => {
    const numericTypes = ['float', 'double', 'numeric']
    return numericTypes.includes(fieldForm.dataType)
})

/**
 * 删除确认对话框可见性
 */
const deleteConfirmVisible = ref(false)

/**
 * 待删除的字段索引
 */
const pendingDeleteIndex = ref(-1)

/**
 * 是否删除扩展视图字段
 */
const isDeleteExtendField = ref(false)

/**
 * 处理新增视图
 */
const handleAddView = () => {
    // 重置表单数据
    resetViewForm()

    // 添加默认字段
    viewForm.fields = [
        {
            fieldName: 'id',
            fieldComment: '主键ID',
            dataType: 'integer',
            fieldLength: null,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: true,
            isNullable: false,
            isListDisplay: true,
            isDefault: true
        },
        {
            fieldName: 'name',
            fieldComment: '名称',
            dataType: 'varchar',
            fieldLength: 100,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: false,
            isPrimaryKey: false,
            isNullable: false,
            isListDisplay: true,
            isDefault: true
        },
        {
            fieldName: 'code',
            fieldComment: '代码',
            dataType: 'varchar',
            fieldLength: 50,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: false,
            isNullable: false,
            isListDisplay: true,
            isDefault: true
        }
    ]

    // 设置为新增模式
    editMode.value = 'add'
    
    // 显示抽屉
    drawerVisible.value = true
}

/**
 * 处理编码与分类
 */
const handleCodeClassification = () => {
    // 重置表单数据
    Object.assign(codeClassificationForm, {
        codeField: '',
        codeRule: '',
        businessKeyField: '',
        masterDataNameField: '',
        classificationField: '',
        classificationBasis: '',
        rootNodeId: '',
        hierarchyIdField: '',
        hierarchyNameField: '',
        hierarchyPidField: ''
    })
    
    // 显示对话框
    codeClassificationDialogVisible.value = true
}

/**
 * 获取编码与分类对话框标题
 */
const getCodeClassificationTitle = () => {
    const tabNames = {
        entity: '实体模型',
        bom: 'BOM模型',
        category: '分类模型'
    }
    return `编码与分类 - ${tabNames[currentTab.value] || '未知模型'}`
}

/**
 * 提交编码与分类
 */
const handleSubmitCodeClassification = () => {
    // 这里可以添加表单验证逻辑
    console.log('编码与分类配置:', codeClassificationForm)
    ElMessage.success('编码与分类配置保存成功')
    codeClassificationDialogVisible.value = false
}

/**
 * 处理相似度设置
 */
const handleSimilaritySetting = () => {
    // 重置表单数据
    similarityForm.threshold = 60
    similarityForm.weightFields = []
    // 显示对话框
    similarityDialogVisible.value = true
}

/**
 * 添加权重字段
 */
const addWeightField = () => {
    similarityForm.weightFields.push({
        fieldName: '',
        fieldComment: '',
        weight: 0
    })
}

/**
 * 删除权重字段
 */
const removeWeightField = (index) => {
    similarityForm.weightFields.splice(index, 1)
}

/**
 * 处理字段选择变化
 */
const handleFieldChange = (value, index) => {
    // 根据选择的字段名称，从fieldOptions中找到对应的字段信息
    const selectedField = fieldOptions.value.find(field => field.value === value)
    if (selectedField) {
        similarityForm.weightFields[index].fieldComment = selectedField.label
    }
}

/**
 * 计算权重总和
 */
const getTotalWeight = computed(() => {
    return similarityForm.weightFields.reduce((sum, field) => sum + (field.weight || 0), 0)
})

/**
 * 提交相似度设置
 */
const handleSubmitSimilarity = () => {
    // 验证权重总和是否为100
    if (getTotalWeight.value !== 100) {
        ElMessage.error('所有字段的权重相加必须等于100')
        return
    }
    
    // 验证表单
    similarityFormRef.value?.validate((valid) => {
        if (valid) {
            console.log('相似度设置:', similarityForm)
            ElMessage.success('相似度设置保存成功')
            similarityDialogVisible.value = false
        }
    })
}

/**
 * 处理密级设置
 */
const handleSecuritySetting = () => {
    // 获取当前模型的密级，如果有的话进行反显
    // 这里模拟获取当前模型密级的逻辑
    const currentModelSecurityLevel = getCurrentModelSecurityLevel()
    if (currentModelSecurityLevel) {
        securitySettingForm.securityLevel = currentModelSecurityLevel
    } else {
        securitySettingForm.securityLevel = ''
    }
    
    securitySettingDialogVisible.value = true
}

/**
 * 获取当前模型的密级
 * 实际应用中应该从API获取
 */
const getCurrentModelSecurityLevel = () => {
    // 模拟返回当前模型的密级，实际应该从后端API获取
    return 'L1' // 示例：返回L1密级
}

/**
 * 获取所有字段的最低密级
 * 用于过滤模型密级选项
 */
const getLowestFieldSecurityLevel = () => {
    if (!viewForm.fields || viewForm.fields.length === 0) {
        return null
    }
    
    const securityLevels = viewForm.fields.map(field => field.securityLevel)
    const levelOrder = ['L4', 'L3', 'L2', 'L1'] // 从高到低
    
    for (const level of levelOrder.reverse()) { // 从低到高查找
        if (securityLevels.includes(level)) {
            return level
        }
    }
    return 'L1'
}

/**
 * 确认密级设置
 */
const confirmSecuritySetting = () => {
    // 验证表单
    if (!securitySettingForm.securityLevel) {
        ElMessage.warning('请选择密级')
        return
    }
    
    // 检查模型密级是否高于字段最低密级
    const lowestFieldLevel = getLowestFieldSecurityLevel()
    if (lowestFieldLevel) {
        const levelOrder = ['L1', 'L2', 'L3', 'L4']
        const selectedLevelIndex = levelOrder.indexOf(securitySettingForm.securityLevel)
        const lowestFieldLevelIndex = levelOrder.indexOf(lowestFieldLevel)
        
        if (selectedLevelIndex > lowestFieldLevelIndex) {
            ElMessage.warning('模型的密级不能高于所有字段的最低密级')
            return
        }
    }
    
    // 实际应用中这里应该调用API保存密级设置
    ElMessage.success('密级设置成功')
    securitySettingDialogVisible.value = false
    
    // 这里可以添加保存到后端的逻辑
    console.log('设置的密级:', securitySettingForm.securityLevel)
}

/**
 * 处理批量删除
 */
const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请选择要删除的数据')
        return
    }

    ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
        '批量删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        ElMessage.success(`删除了${selectedRows.value.length}条数据`)
        // 实际应用中这里应该调用删除API
        loadTableData()
    }).catch(() => {
        // 用户取消删除
    })
}

// 配置相关数据
const hasSelectedSegments = ref(false)

/**
 * 选中的字段数据
 */
const selectedSegments = ref([])

/**
 * 处理表格选择变化
 */
const handleSegmentSelectionChange = (selection) => {
    selectedSegments.value = selection
    hasSelectedSegments.value = selection.length > 0
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
    loadTableData()
}

/**
 * 当前编辑的视图数据
 */
const currentEditView = ref(null)

/**
 * 编辑模式（add: 新增, edit: 编辑, view: 查看）
 */
const editMode = ref('add')

/**
 * 获取抽屉标题
 */
const getDrawerTitle = () => {
    switch (editMode.value) {
        case 'add':
            return '新增视图'
        case 'edit':
            return '编辑视图'
        case 'view':
            return '查看视图'
        default:
            return '新增视图'
    }
}

/**
 * 处理编辑视图
 * @param {Object} row - 行数据
 */
const handleEditView = (row) => {
    currentEditView.value = { ...row }
    editMode.value = 'edit'
    

    handleNormalViewEdit(row)
   
}



/**
 * 处理普通视图编辑
 * @param {Object} row - 行数据
 */
const handleNormalViewEdit = (row) => {
    // 重置表单数据
    resetViewForm()
    
    // 填充表单数据
    viewForm.viewName = row.viewName
    viewForm.description = row.description || ''
    
    // 模拟加载字段数据
    viewForm.fields = getDefaultFields(row.viewType)
    
    // 显示抽屉
    drawerVisible.value = true
    ElMessage.info(`编辑视图: ${row.viewName}`)
}



/**
 * 获取默认字段数据
 * @param {String} viewType - 视图类型
 */
const getDefaultFields = (viewType) => {
    const basicFields = [
        {
            fieldName: 'id',
            fieldComment: '主键ID',
            dataType: 'integer',
            fieldLength: null,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: true,
            isNullable: false,
            isListDisplay: true,
            isDefault: true
        },
        {
            fieldName: 'name',
            fieldComment: '名称',
            dataType: 'varchar',
            fieldLength: 100,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: false,
            isPrimaryKey: false,
            isNullable: false,
            isListDisplay: true,
            isDefault: true
        }
    ]
    
    if (viewType === 'parent') {
        return [
            ...basicFields,
            {
                fieldName: 'parent_code',
                fieldComment: '母件编码',
                dataType: 'varchar',
                fieldLength: 50,
                decimalPlaces: null,
                securityLevel: 'L1',
                isUnique: true,
                isPrimaryKey: false,
                isNullable: false,
                isListDisplay: true,
                isDefault: true
            }
        ]
    } else if (viewType === 'child') {
        return [
            ...basicFields,
            {
                fieldName: 'child_code',
                fieldComment: '子件编码',
                dataType: 'varchar',
                fieldLength: 50,
                decimalPlaces: null,
                securityLevel: 'L1',
                isUnique: false,
                isPrimaryKey: false,
                isNullable: false,
                isListDisplay: true,
                isDefault: true
            },
            {
                fieldName: 'quantity',
                fieldComment: '数量',
                dataType: 'numeric',
                fieldLength: 10,
                decimalPlaces: 2,
                securityLevel: 'L1',
                isUnique: false,
                isPrimaryKey: false,
                isNullable: false,
                isListDisplay: true,
                isDefault: true
            }
        ]
    }
    
    return basicFields
}

/**
 * 母件信息视图 - 拾取操作
 */
const handlePickParent = () => {
    ElMessage.info('拾取母件功能待实现')
}

/**
 * 子件清单信息视图 - 新增字段
 */
const handleAddChildField = () => {
    fieldForm.value = {
        fieldName: '',
        fieldComment: '',
        dataType: 'varchar',
        fieldType: '普通字段',
        fieldLength: null,
        decimalPlaces: null,
        securityLevel: 'L1',
        isUnique: false,
        isPrimaryKey: false,
        isNullable: true,
        isListDisplay: false,
        isDefault: false
    }
    isEditingField.value = false
    fieldDialogVisible.value = true
}

/**
 * 子件清单信息视图 - 引用字段
 */
const handleReferenceField = () => {
    fieldForm.value = {
        fieldName: '',
        fieldComment: '',
        dataType: 'varchar',
        fieldType: '引用字段',
        fieldLength: null,
        decimalPlaces: null,
        securityLevel: 'L1',
        isUnique: false,
        isPrimaryKey: false,
        isNullable: true,
        isListDisplay: false,
        isDefault: false
    }
    isEditingField.value = false
    fieldDialogVisible.value = true
}

/**
 * 处理查看
 * @param {Object} row - 行数据
 */
const handleView = (row) => {
    currentEditView.value = { ...row }
    editMode.value = 'view'
    
    // 重置视图表单
    resetViewForm()
    
    // 填充视图基本信息
    viewForm.viewName = row.viewName
    viewForm.viewCode = row.viewCode || row.viewName
    viewForm.description = row.description || ''
    
    // 加载默认字段（模拟数据）
    viewForm.fields = getDefaultFields(row.viewType || 'basic')
    
    // 显示抽屉
    drawerVisible.value = true
    activeTab.value = 'basicInfo'
}

/**
 * 处理单行删除
 * @param {Object} row - 要删除的行数据
 */
const handleDeleteSingle = (row) => {
    ElMessageBox.confirm(
        `确定要删除视图 "${row.viewName}" 吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        ElMessage.success(`删除视图 "${row.viewName}" 成功`)
        // 实际应用中这里应该调用删除API
        loadTableData()
    }).catch(() => {
        // 用户取消删除
    })
}

/**
 * 处理选择变化
 * @param {Array} selection - 选中的行
 */
const handleSelectionChange = (selection) => {
    selectedRows.value = selection

}

/**
 * 处理每页大小变化
 * @param {Number} size - 每页大小
 */
const handleSizeChange = (size) => {
    pagination.pageSize = size
    loadTableData()
}

/**
 * 处理当前页变化
 * @param {Number} page - 当前页
 */
const handleCurrentChange = (page) => {
    pagination.currentPage = page
    loadTableData()
}

/**
 * 重置视图表单
 */
const resetViewForm = () => {
    // 重置基本信息表单
    viewForm.viewName = ''
    viewForm.viewCode = ''
    viewForm.modelId = ''
    viewForm.description = ''
    viewForm.fields = []

    // 重置扩展视图表单
    extendViewForm.viewName = ''
    extendViewForm.description = ''
    extendViewForm.fields = []

    // 重置表单验证
    if (viewFormRef.value) {
        viewFormRef.value.resetFields()
    }
    if (extendViewFormRef.value) {
        extendViewFormRef.value.resetFields()
    }

    // 重置标签页
    activeTab.value = 'basicInfo'
}

/**
 * 重置字段表单
 */
const resetFieldForm = () => {
    fieldForm.fieldName = ''
    fieldForm.fieldComment = ''
    fieldForm.dataType = ''
    fieldForm.fieldLength = null
    fieldForm.decimalPlaces = null
    fieldForm.securityLevel = 'L1'
    fieldForm.isUnique = true
    fieldForm.isPrimaryKey = false
    fieldForm.isNullable = true
    fieldForm.isListDisplay = false
    fieldForm.isDefault = false

    // 重置表单验证
    if (fieldFormRef.value) {
        fieldFormRef.value.resetFields()
    }
}

/**
 * 处理抽屉关闭
 */
const handleDrawerClose = () => {
    // 如果是查看模式，直接关闭，不需要确认
    if (editMode.value === 'view') {
        drawerVisible.value = false
        resetViewForm()
        return
    }
    
    // 编辑模式需要确认关闭
    ElMessageBox.confirm(
        '确定要关闭吗？未保存的数据将会丢失',
        '关闭确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        drawerVisible.value = false
        resetViewForm()
    }).catch(() => {
        // 用户取消关闭
    })
}

/**
 * 处理提交视图
 */
const handleSubmitView = () => {
    // 根据当前标签页选择要验证的表单
    const formRef = activeTab.value === 'basicInfo' ? viewFormRef.value : extendViewFormRef.value
    const formData = activeTab.value === 'basicInfo' ? viewForm : extendViewForm

    if (!formRef) return

    formRef.validate((valid) => {
        if (valid) {
            // 表单验证通过，提交数据
            ElMessage.success(`成功${activeTab.value === 'basicInfo' ? '创建' : '添加'}视图: ${formData.viewName}`)
            drawerVisible.value = false
            resetViewForm()
            loadTableData() // 刷新表格数据
        } else {
            ElMessage.error('表单验证失败，请检查输入')
            return false
        }
    })
}

/**
 * 处理添加字段
 */
const handleAddField = () => {
    resetFieldForm()
    isEditingField.value = false
    currentFieldIndex.value = -1
    fieldDialogVisible.value = true
}



/**
 * 处理编辑字段
 * @param {Object} row - 字段数据
 * @param {Number} index - 字段索引
 */
const handleEditField = (row, index) => {
    // 复制字段数据到表单
    Object.assign(fieldForm, JSON.parse(JSON.stringify(row)))
    isEditingField.value = true
    currentFieldIndex.value = index
    fieldDialogVisible.value = true
}



/**
 * 处理字段对话框关闭
 */
const handleFieldDialogClose = () => {
    fieldDialogVisible.value = false
    resetFieldForm()
}

/**
 * 处理主键变更
 */
const handlePrimaryKeyChange = (value) => {
    if (value) {
        // 如果设置为主键，则不可为空
        fieldForm.isNullable = false
    }
}

/**
 * 处理提交字段
 */
const handleSubmitField = () => {
    if (!fieldFormRef.value) return

    fieldFormRef.value.validate((valid) => {
        if (valid) {
            // 表单验证通过，添加或更新字段
            const fieldData = JSON.parse(JSON.stringify(fieldForm))

            if (isEditingField.value && currentFieldIndex.value >= 0) {
                // 更新字段
                if (isBasicInfoTab.value) {
                    viewForm.fields.splice(currentFieldIndex.value, 1, fieldData)
                } else {
                    extendViewForm.fields.splice(currentFieldIndex.value, 1, fieldData)
                }
                ElMessage.success('字段更新成功')
            } else {
                // 添加字段
                if (isBasicInfoTab.value) {
                    viewForm.fields.push(fieldData)
                } else {
                    extendViewForm.fields.push(fieldData)
                }
                ElMessage.success('字段添加成功')
            }

            fieldDialogVisible.value = false
            resetFieldForm()
        } else {
            ElMessage.error('表单验证失败，请检查输入')
            return false
        }
    })
}





/**
 * 确认删除字段
 */
const confirmDeleteField = () => {
    if (pendingDeleteIndex.value >= 0) {
        if (isDeleteExtendField.value) {
            extendViewForm.fields.splice(pendingDeleteIndex.value, 1)
        } else {
            viewForm.fields.splice(pendingDeleteIndex.value, 1)
        }
        ElMessage.success('字段删除成功')
    }
    deleteConfirmVisible.value = false
}

/**
 * 处理字段上移
 */
const handleMoveUp = () => {
    if (selectedSegments.value.length === 0) {
        ElMessage.warning('请选择要上移的字段')
        return
    }

    const currentFields = isBasicInfoTab.value ? viewForm.fields : extendViewForm.fields
    const selectedFieldNames = selectedSegments.value.map(item => item.fieldName)
    
    // 获取选中字段的索引
    const selectedIndexes = []
    selectedFieldNames.forEach(fieldName => {
        const index = currentFields.findIndex(field => field.fieldName === fieldName)
        if (index !== -1) {
            selectedIndexes.push(index)
        }
    })
    
    // 按索引排序，确保从前往后处理
    selectedIndexes.sort((a, b) => a - b)
    
    // 检查是否可以上移（第一个选中项不能是第一个字段）
    if (selectedIndexes[0] === 0) {
        ElMessage.warning('选中的字段已经在最顶部，无法上移')
        return
    }
    
    // 执行上移操作
    selectedIndexes.forEach(index => {
        if (index > 0) {
            // 交换当前字段和上一个字段的位置
            const temp = currentFields[index]
            currentFields[index] = currentFields[index - 1]
            currentFields[index - 1] = temp
        }
    })
    
    ElMessage.success('字段上移成功')
}

/**
 * 处理字段下移
 */
const handleMoveDown = () => {
    if (selectedSegments.value.length === 0) {
        ElMessage.warning('请选择要下移的字段')
        return
    }

    const currentFields = isBasicInfoTab.value ? viewForm.fields : extendViewForm.fields
    const selectedFieldNames = selectedSegments.value.map(item => item.fieldName)
    
    // 获取选中字段的索引
    const selectedIndexes = []
    selectedFieldNames.forEach(fieldName => {
        const index = currentFields.findIndex(field => field.fieldName === fieldName)
        if (index !== -1) {
            selectedIndexes.push(index)
        }
    })
    
    // 按索引倒序排序，确保从后往前处理
    selectedIndexes.sort((a, b) => b - a)
    
    // 检查是否可以下移（最后一个选中项不能是最后一个字段）
    if (selectedIndexes[0] === currentFields.length - 1) {
        ElMessage.warning('选中的字段已经在最底部，无法下移')
        return
    }
    
    // 执行下移操作
    selectedIndexes.forEach(index => {
        if (index < currentFields.length - 1) {
            // 交换当前字段和下一个字段的位置
            const temp = currentFields[index]
            currentFields[index] = currentFields[index + 1]
            currentFields[index + 1] = temp
        }
    })
    
    ElMessage.success('字段下移成功')
}

/**
  * 处理删除字段（批量删除选中的字段）
  */
 const handleDeleteField = () => {
     if (selectedSegments.value.length === 0) {
         ElMessage.warning('请选择要删除的字段')
         return
     }
 
     const currentFields = isBasicInfoTab.value ? viewForm.fields : extendViewForm.fields
     const selectedFieldNames = selectedSegments.value.map(item => item.fieldName)
     
     // 检查是否包含默认字段
     const hasDefaultFields = selectedSegments.value.some(field => field.isDefault)
     if (hasDefaultFields) {
         ElMessage.warning('默认字段不能删除')
         return
     }
     
     ElMessageBox.confirm(
         `确定要删除选中的 ${selectedSegments.value.length} 个字段吗？`,
         '删除字段确认',
         {
             confirmButtonText: '确定',
             cancelButtonText: '取消',
             type: 'warning'
         }
     ).then(() => {
         // 执行删除操作
         selectedFieldNames.forEach(fieldName => {
             const index = currentFields.findIndex(field => field.fieldName === fieldName)
             if (index !== -1) {
                 currentFields.splice(index, 1)
             }
         })
         
         // 清空选中状态
         selectedSegments.value = []
         hasSelectedSegments.value = false
         
         ElMessage.success(`成功删除 ${selectedFieldNames.length} 个字段`)
     }).catch(() => {
         // 用户取消删除
     })
 }
 
 /**
  * 处理删除单个字段
  * @param {Object} row - 字段数据
  * @param {Number} index - 字段索引
  */
 const handleDeleteSingleField = (row, index) => {
     if (row.isDefault) {
         ElMessage.warning('默认字段不能删除')
         return
     }
     
     ElMessageBox.confirm(
         `确定要删除字段 "${row.fieldComment || row.fieldName}" 吗？`,
         '删除字段确认',
         {
             confirmButtonText: '确定',
             cancelButtonText: '取消',
             type: 'warning'
         }
     ).then(() => {
         const currentFields = isBasicInfoTab.value ? viewForm.fields : extendViewForm.fields
         currentFields.splice(index, 1)
         ElMessage.success('字段删除成功')
     }).catch(() => {
         // 用户取消删除
     })
 }



/**
 * 处理库表导入
 */
const handleImportFromTable = () => {
    tableImportForm.dataSource = ''
    tableImportForm.tableName = ''
    tableOptions.value = []
    tableImportDialogVisible.value = true
}

/**
 * 监听数据源变化，加载表列表
 * @param {String} value - 数据源值
 */
const handleDataSourceChange = (value) => {
    // 实际应用中应该调用API获取表列表
    setTimeout(() => {
        if (value === 'mysql') {
            tableOptions.value = [
                { label: 'user', value: 'user' },
                { label: 'product', value: 'product' },
                { label: 'order', value: 'order' }
            ]
        } else if (value === 'oracle') {
            tableOptions.value = [
                { label: 'USERS', value: 'USERS' },
                { label: 'PRODUCTS', value: 'PRODUCTS' },
                { label: 'ORDERS', value: 'ORDERS' }
            ]
        } else {
            tableOptions.value = []
        }
    }, 500)
}

/**
 * 确认库表导入
 */
const confirmImportFromTable = () => {
    if (!tableImportForm.dataSource) {
        ElMessage.warning('请选择数据源')
        return
    }
    if (!tableImportForm.tableName) {
        ElMessage.warning('请选择表')
        return
    }

    // 模拟从数据库获取字段信息
    const mockFields = [
        {
            fieldName: 'user_id',
            fieldComment: '用户ID',
            dataType: 'integer',
            fieldLength: null,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: true,
            isNullable: false,
            isListDisplay: true,
            isDefault: false
        },
        {
            fieldName: 'username',
            fieldComment: '用户名',
            dataType: 'varchar',
            fieldLength: 50,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: false,
            isNullable: false,
            isListDisplay: true,
            isDefault: false
        },
        {
            fieldName: 'email',
            fieldComment: '邮箱',
            dataType: 'varchar',
            fieldLength: 100,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: false,
            isNullable: true,
            isListDisplay: true,
            isDefault: false
        }
    ]

    // 添加字段到表单
    mockFields.forEach(field => {
        // 检查字段是否已存在
        const exists = viewForm.fields.some(f => f.fieldName === field.fieldName)
        if (!exists) {
            viewForm.fields.push(field)
        }
    })

    ElMessage.success('成功导入表字段')
    tableImportDialogVisible.value = false
}

/**
 * 处理Excel导入
 */
const handleImportFromExcel = () => {
    excelFile.value = null
    excelImportDialogVisible.value = true
}

/**
 * 处理Excel文件变更
 * @param {Object} file - 文件对象
 */
const handleExcelChange = (file) => {
    excelFile.value = file
}

/**
 * 确认Excel导入
 */
const confirmImportFromExcel = () => {
    if (!excelFile.value) {
        ElMessage.warning('请上传Excel文件')
        return
    }

    // 实际应用中应该解析Excel文件内容
    // 这里模拟从Excel导入的字段
    const mockFields = [
        {
            fieldName: 'product_id',
            fieldComment: '产品ID',
            dataType: 'integer',
            fieldLength: null,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: true,
            isPrimaryKey: true,
            isNullable: false,
            isListDisplay: true,
            isDefault: false
        },
        {
            fieldName: 'product_name',
            fieldComment: '产品名称',
            dataType: 'varchar',
            fieldLength: 100,
            decimalPlaces: null,
            securityLevel: 'L1',
            isUnique: false,
            isPrimaryKey: false,
            isNullable: false,
            isListDisplay: true,
            isDefault: false
        },
        {
            fieldName: 'price',
            fieldComment: '价格',
            dataType: 'numeric',
            fieldLength: 10,
            decimalPlaces: 2,
            securityLevel: 'L1',
            isUnique: false,
            isPrimaryKey: false,
            isNullable: true,
            isListDisplay: true,
            isDefault: false
        }
    ]

    // 添加字段到表单
    mockFields.forEach(field => {
        // 检查字段是否已存在
        const exists = viewForm.fields.some(f => f.fieldName === field.fieldName)
        if (!exists) {
            viewForm.fields.push(field)
        }
    })

    ElMessage.success('成功导入Excel字段')
    excelImportDialogVisible.value = false
}

// 初始加载数据
loadTableData()
</script>

<style lang="scss" scoped>
.classification-model {
    height: 100%;
    display: flex;
    flex-direction: column;
    // background: #fff;

    .search-form {
        margin-bottom: 16px;
        padding: 16px;
        // background: #f8f9fa;
        border-radius: 4px;
    }

    .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .toolbar-left {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 8px;
        }
    }

    .table-container {
        flex: 1;
        overflow: hidden;

        .el-table {
            height: 100%;
        }
    }

    .pagination-container {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
    }
}

/* 字段表格容器样式 */
.field-table-container {
    margin-top: 20px;

    .field-table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
            margin: 0;
            font-size: 16px;
            color: #303133;
        }

        .field-table-actions {
            display: flex;
            gap: 8px;
        }
    }
}

/* 抽屉内容样式 */
:deep(.el-drawer__body) {
    padding: 20px;
    overflow-y: auto;
}

:deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-drawer__footer) {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
}

/* 表单样式 */
:deep(.el-form-item__label) {
    font-weight: 500;
}

/* 标签页样式 */
:deep(.el-tabs__header) {
    margin-bottom: 20px;
}

/* 对话框样式 */
:deep(.el-dialog__body) {
    padding: 20px;
}

:deep(.dialog-footer) {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 编码与分类对话框样式 */
.form-section {
    margin-bottom: 24px;
    padding: 16px;
    // background-color: #fafafa;
    border-radius: 6px;
    // border: 1px solid #e8e8e8;
}

.section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    // border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
}

.description-section {
    margin-top: 24px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;
}

.description-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.description-section ul {
    margin: 0 0 16px 0;
    padding-left: 20px;
}

.description-section li {
    margin-bottom: 8px;
    color: #666;
    line-height: 1.5;
}

.field-descriptions p {
    margin: 8px 0;
    color: #666;
    line-height: 1.6;
}

.field-descriptions strong {
    color: #333;
}

/* 相似度设置对话框样式 */
.similarity-description {
    background-color: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 20px;
}

.similarity-description p {
    margin: 0;
    color: #1e40af;
    font-size: 14px;
}

.weight-section {
    width: 100%;
}

.weight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.weight-total {
    font-weight: bold;
    color: #409eff;
    font-size: 14px;
}

.weight-example {
    margin-top: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #409eff;
}

.weight-example p {
    margin: 0 0 8px 0;
    font-weight: bold;
    color: #333;
}

.weight-example ul {
    margin: 0;
    padding-left: 20px;
}

.weight-example li {
    margin: 4px 0;
    color: #666;
}

.similarity-formula {
    margin-top: 20px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.similarity-formula h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
}

.similarity-formula ol {
    margin: 0 0 16px 0;
    padding-left: 20px;
}

.similarity-formula li {
    margin: 8px 0;
    color: #666;
    line-height: 1.6;
}

.similarity-formula p {
    margin: 8px 0;
    color: #666;
    line-height: 1.6;
}

.similarity-formula strong {
    color: #333;
}
</style>