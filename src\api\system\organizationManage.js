import request from '@/utils/request';

// 组织标签查询
export function getOrganizationTagList(params) {
  return request({
    url: '/system/sysorganizationlabel/listlabel',
    method: 'get',
    params
  });
}

// 组织标签添加
export function addOrganizationTag(data) {
  return request({
    url: '/system/sysorganizationlabel/addlabel',
    method: 'post',
    data
  });
}

// 组织标签修改
export function updateOrganizationTag(data) {
  return request({
    url: '/system/sysorganizationlabel/modifylabel',
    method: 'post',
    data
  });
}

// 组织标签删除
export function deleteOrganizationTag(params) {
  return request({
    url: `/system/sysorganizationlabel/deletelabel`,
    method: 'delete',
    params
  });
}

// 组织列表
export function getOrganizationList(params) {
  return request({
    url: '/system/sysorganization/list',
    method: 'get',
    params
  });
}

// 组织添加
export function addOrganization(data) {
  return request({
    url: '/system/sysorganization/addorganization',
    method: 'post',
    data
  });
}

// 组织修改
export function updateOrganization(data) {
  return request({
    url: '/system/sysorganization/modifyorganization',
    method: 'post',
    data
  });
}

// 组织删除
export function deleteOrganization(params) {
  return request({
    url: '/system/sysorganization/deleteorganization',
    method: 'delete',
    params
  });
}

// 数据源模块获取组织数据
export function getOrganizationData(params) {
  return request({
    url: '/system/sysorganizationlabel/listlabelandorganization',
    method: 'get',
    params
  });
}
