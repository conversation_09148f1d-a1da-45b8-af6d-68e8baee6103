<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" /> -->
    <div
      style="
        margin-bottom: 20px;
        font-weight: 400;
        font-size: 20px;
        height: 20px;
        line-height: 20px;
      "
      >邮箱服务器管理</div
    >
    <div class="partOne">
      <div class="top-container">
        <div class="title">渠道设置</div>
        <div>
          <el-button type="primary" @click="submitForm"
            ><IconSave style="margin-right: 4px" />保存</el-button
          >
        </div>
      </div>
      <div class="email-container box-container">
        <el-form ref="emailForm" :model="form" :rules="rules" label-position="top">
          <div style="margin-bottom: 20px; font-size: 14px; color: #000">邮件设置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮件服务器地址" prop="host">
                <el-input v-model="form.host" placeholder="请输入服务器地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统邮件发送账户" prop="user">
                <el-input v-model="form.user" placeholder="请输入系统邮件发送账户"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮件服务器端口" prop="port">
                <el-input v-model="form.port" placeholder="请输入服务器端口"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统邮件账号密码" prop="pass">
                <el-input
                  v-model="form.pass"
                  type="password"
                  show-password
                  placeholder="请输入账号密码"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row> </el-form
      ></div>
    </div>
    <div class="portTwo">
      <div class="top-container" style="margin-top: 20px">
        <div class="title">运维告警</div>
        <div>
          <el-button type="primary" @click="addMan"
            ><IconAdd style="margin-right: 4px" />添加运维告警人</el-button
          >
        </div>
      </div>
      <div class="list-container box-container">
        <div class="data-container" v-for="data in peopleList" :key="data.id">
          <div class="img-container">
            <div class="card-name-img" :class="`card-name-img-type`">
              {{ data.nickName.slice(0, 1) }}
            </div>
          </div>
          <div class="content-container">
            <div>
              <span
                style="color: #434343; font-size: 14px; font-weight: 600; vertical-align: text-top"
                >{{ data.nickName }}</span
              >
              <span class="line">|</span>
              <span
                style="color: #434343; font-size: 14px; font-weight: 600; vertical-align: text-top"
                >{{ data.userName }}</span
              >
              <span class="line">|</span>
              <span
                style="color: #8c8c8c; font-size: 12px; font-weight: 600; vertical-align: text-top"
                >{{ data.createTime }}</span
              >
            </div>
            <div style="color: #8c8c8c; font-size: 12px; font-weight: 400; margin-top: 6px">
              <span><IconMail style="margin-right: 4px" />{{ data.email }}</span>
            </div>
          </div>
          <div class="btn-container">
            <el-icon @click="handleDelete(data)" style="cursor: pointer; color: #1269ff"
              ><Delete
            /></el-icon>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="open"
      title="添加运维告警人"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelAdd()"
    >
      <el-form>
        <el-form-item label="选择用户">
          <el-select v-model="userId" clearable>
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.info"
              :value="user.userId"
              :disabled="user.email ? false : true"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAdd">取 消</el-button>
          <el-button type="primary" @click="submitFormAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { IconMail, IconSave, IconAdd } from '@arco-iconbox/vue-update-line-icon';
  import HeadTitle from '@/components/HeadTitle';
  import {
    getAllList,
    getConfigInfo,
    saveOrUpdateConfig,
    getAllUser,
    createWarningMan,
    deleteWarningMan,
  } from '@/api/alert/warnConfig';
  import { onMounted } from 'vue';
  const HeadTitleName = ref('告警管理');
  const { proxy } = getCurrentInstance();
  const form = ref({});
  const total = ref(0);
  const queryParams = ref({
    pageSize: 20,
    pageNum: 1,
  });
  const peopleList = ref([]);
  // tabs的初始值
  const activeName = ref('wayConfig');
  const validateNotNumber = (rule, value, callback) => {
    if (!isNaN(value)) {
      callback(new Error('该字段不能为数字'));
    } else {
      callback();
    }
  };
  // 邮件表单验证规则
  const rules = ref({
    host: [
      { required: true, message: '服务器地址不能为空', trigger: 'blur' },
      // 不能为数字
      { validator: validateNotNumber, trigger: 'blur' },
    ],
    port: [
      { required: true, message: '端口不能为空', trigger: 'blur' },
      // 只能是数字不能是负数
      { pattern: /^[0-9]*$/, message: '请输入正确的端口', trigger: 'blur' },
    ],
    user: [
      { required: true, message: '账户不能为空', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    ],
    pass: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
  });
  // 提交邮件设置信息
  const submitForm = () => {
    proxy.$refs.emailForm.validate((valid) => {
      if (valid) {
        // 接口需要的数据
        const obj = { alertConfVal: {} };
        obj.alertType = 1;
        obj.alertConfVal.host = form.value.host;
        obj.alertConfVal.user = form.value.user;
        obj.alertConfVal.pass = form.value.pass;
        obj.alertConfVal.port = form.value.port;
        obj.alertConfVal = JSON.stringify(obj.alertConfVal);
        if (form.value.id) {
          obj.id = form.value.id;
        } else {
          obj.id = null;
        }
        saveOrUpdateConfig(obj).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess('告警配置成功');
          }
        });
      }
    });
  };
  // 回显数据
  onMounted(() => {
    // 获取平台邮箱配置信息
    getConfigInfo().then((res) => {
      if (res.data) {
        form.value.host = res.data.alertConfProperties.host;
        form.value.port = res.data.alertConfProperties.port;
        form.value.user = res.data.alertConfProperties.user;
        form.value.pass = res.data.alertConfProperties.pass;
        form.value.id = res.data.id;
      } else {
        proxy.$modal.msgWarning('邮件设置未配置');
      }
    });
    getList();
  });
  const onClick = ref(true);
  const open = ref(false);
  const userId = ref(null);
  const userList = ref([]);
  // 打开告警人选择弹窗
  const addMan = () => {
    open.value = true;
    // 获取所有用户列表 并展示邮箱和账号
    // 禁止选择邮箱未绑定的用户
    getAllUser().then((res) => {
      if (res.data && res.data.length) {
        res.data.forEach((val) => {
          if (!val.email) {
            val.info = `用户:${val.nickName} 账号:${val.userName} 邮箱:无`;
          } else {
            val.info = `用户:${val.nickName} 账号:${val.userName} 邮箱:${val.email}`;
          }
        });
        userList.value = res.data;
      } else {
        userList.value = [];
      }
    });
  };
  // 新增告警人确定
  const submitFormAdd = () => {
    createWarningMan({ userId: userId.value }).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess('新增成功');
        open.value = false;
        getList();
      }
    });
  };
  // 取消新增
  const cancelAdd = () => {
    userId.value = null;
    open.value = false;
  };
  // // 标签切换到运维告警界面，获取运维列表
  // const handleClick = (data) => {
  //   if (data == 'operationWarn') {
  //     getList();
  //   }
  // };
  // 获取运维列表
  const getList = () => {
    getAllList(queryParams.value).then((res) => {
      total.value = res.total;
      if (res.rows && res.rows.length) {
        peopleList.value = res.rows;
      } else {
        peopleList.value = [];
      }
    });
  };

  const handleDelete = (row) => {
    proxy.$modal
      .confirm(`是否确定删除告警人${row.nickName}?`)
      .then(() => {
        return deleteWarningMan(row.userId);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      });
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;

    .top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      .title {
        color: #000000;
        // font-family: 'PingFang SC';
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        padding-left: 10px;
        border-left: 3px solid #1269ff;
      }
      :deep svg {
        fill: none;
        path {
          stroke: #fff;
        }
      }
    }
    .box-container {
      padding: 20px;
      border-radius: 8px;
      background: #ffffff;
    }
    .portTwo {
      height: calc(100% - 336px);
      .list-container {
        height: calc(100% - 70px);
        overflow: auto;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        .data-container {
          flex-basis: calc(50% - 20px); /* 每栏占50%减去间距 */
          // width: 50%;
          margin: 10px;
          padding: 10px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          height: 68px;
          box-sizing: border-box;
          .img-container {
            position: relative;
            width: 50px;
            height: 100%;
            margin-right: 8px;
            .card-name-img {
              position: absolute;
              top: 10%;
              width: 40px;
              height: 40px;

              line-height: 40px;
              text-align: center;

              border-radius: 20px;
              color: $--base-color-primary;
              background: $--base-color-tag-primary;
            }
          }
          .content-container {
            flex: 1;
            .line {
              display: inline-block;
              width: 2px;
              height: 20px;
              line-height: 20px;
              vertical-align: top;
              margin: 0 6px;
            }
          }
          .btn-container {
            margin-right: 10px;
            cursor: pointer;
            opacity: 0;
          }
          &:hover {
            background: #f1f5fa;
          }
          &:hover .btn-container {
            opacity: 1;
          }
        }
      }
    }
  }
</style>
