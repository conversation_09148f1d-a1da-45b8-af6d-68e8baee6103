<template>
  <el-empty v-if="!form.dataObj.length" description="暂无数据"></el-empty>

  <el-form v-else ref="indexForm" :model="form" label-width="110px" label-position="left">
    <div v-for="(item, index) in form.dataObj" :key="index">
      <section class="dropdown-link">
        <!-- <el-row style="margin-bottom: 10px" @click="isShow"> -->
        <!-- <el-col :span="20"> -->
        <!-- <!~~ 规则{{ index + 1 }} ~~> -->
        <!-- 选择质量规则 -->
        <!-- <el-icon class="icon--right"> -->
        <!-- <arrow-down /> -->
        <!-- </el-icon> -->
        <!-- </el-col> -->
        <!-- <el-col :span="4"> -->
        <!-- <!~~ <el-link type="text" icon="delete" @click="deleteObj(index)" /> ~~> -->
        <!-- </el-col> -->
        <!-- </el-row> -->
        <!--  -->
        <div v-show="activeName === index + 1">
          <!-- <el-form-item label="质量规则" :prop="`dataObj.${index}.ruleId`" :rules="rules.ruleId"> -->
          <el-form-item
            :label="'质量规则'"
            :prop="`dataObj[${index}].ruleId`"
            :rules="rules.ruleId"
          >
            <el-select
              v-model="item.ruleId"
              placeholder="请选择规则类型"
              clearable
              :disabled="!canvasActions"
              @change="changeRuleType"
            >
              <el-option
                v-for="items in ruleTypes"
                :key="items.id"
                :label="items.name"
                :value="items.id"
              />
            </el-select>
          </el-form-item>

          <!-- 动态表单 -->
          <!-- 多表准确性检测 -->
          <div v-if="item.ruleId === 3">
            <el-divider />
            <!-- <mbData -->
            <!-- :data-obj="item.source" -->
            <!-- :index="index" -->
            <!-- :use-type="'source'" -->
            <!-- :rules="rules" -->
            <!-- :src-connector-type="src_connector_type" -->
            <!-- :canvas-actions="canvasActions" -->
            <!-- :prop-path="`dataObj[${index}].source`" -->
            <!-- /> -->
            <filters
              :data-obj="item.source"
              :index="index"
              :use-type="'source'"
              :rules="rules"
              :canvas-actions="canvasActions"
              :prop-path="`dataObj[${index}].source`"
            />
            <el-divider />

            <mbData
              :data-obj="item.target"
              :index="index"
              :use-type="'target'"
              :rules="rules"
              :src-connector-type="src_connector_type"
              :canvas-actions="canvasActions"
              :prop-path="`dataObj[${index}].target`"
            />
            <filters
              :data-obj="item.target"
              :index="index"
              :use-type="'target'"
              :rules="rules"
              :canvas-actions="canvasActions"
              :prop-path="`dataObj[${index}].target`"
            />
            <el-divider />

            <span>ON语句</span>
            <!-- mapping_columns -->
            <template v-for="(syncChange, i) in item.mapping_columns" :key="i">
              <el-form
                ref="syncChangeForm"
                :model="syncChange"
                class="container"
                label-position="top"
              >
                <el-form-item prop="src_field" label="源字段">
                  <el-input
                    v-model="syncChange.src_field"
                    placeholder="源字段"
                    :disabled="!canvasActions"
                  />
                </el-form-item>

                <el-form-item label="操作符">
                  <el-input v-model="syncChange.operator" :disabled="!canvasActions" />
                </el-form-item>
                <el-form-item label="目标字段">
                  <el-input v-model="syncChange.target_field" :disabled="!canvasActions" />
                </el-form-item>

                <component :is="deleteIcon" @click="deleteSyncChange(i)" />
              </el-form>
            </template>
            <component :is="addIcon" @click="addSyncChange" />
          </div>

          <div v-if="item.ruleId !== 3">
            <el-divider />
            <!-- <mbData -->
            <!-- :data-obj="item.source" -->
            <!-- :index="index" -->
            <!-- :use-type="'source'" -->
            <!-- :rules="rules" -->
            <!-- :src-connector-type="src_connector_type" -->
            <!-- :canvas-actions="canvasActions" -->
            <!-- :prop-path="`dataObj[${index}].source`" -->
            <!-- /> -->
            <!-- 空值检测 -->
            <div v-if="item.ruleId === 1">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
            </div>

            <!-- 自定义SQL -->
            <div v-if="item.ruleId === 2">
              <sql
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
            </div>

            <!-- 两表对比 -->
            <div v-if="item.ruleId === 4">
              <!-- <el-divider /> -->
              <sql
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
              <el-divider />
              <mbData
                :data-obj="item.target"
                :index="index"
                :use-type="'target'"
                :rules="rules"
                :src-connector-type="src_connector_type"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].target`"
              />
              <el-divider />
              <sql
                :data-obj="item.target"
                :index="index"
                :use-type="'target'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].target`"
              />
            </div>

            <!-- 字段长度校验 -->
            <div v-if="item.ruleId === 5">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
              <el-divider />

              <el-form-item
                label="逻辑操作符"
                :prop="`dataObj.${index}.logic_operator`"
                :rules="rules.logic_operator"
              >
                <el-select
                  v-model="item.logic_operator"
                  placeholder="请选择校验操作符"
                  clearable
                  :disabled="!canvasActions"
                >
                  <el-option
                    v-for="items in logic_operatorList"
                    :key="items.value"
                    :label="items.label"
                    :value="items.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="字段长度限制"
                :prop="`dataObj.${index}.field_length`"
                :rules="rules.field_length"
              >
                <el-input
                  v-model="item.field_length"
                  placeholder="请输入字段长度限制"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
            </div>

            <!-- 唯一性检测 -->
            <div v-if="item.ruleId === 6">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
            </div>

            <!-- 正则表达式 -->
            <div v-if="item.ruleId === 7">
              <el-form-item
                label="正则表达式"
                :prop="`dataObj.${index}.regexp_pattern`"
                :rules="rules.regexp_pattern"
              >
                <el-input
                  v-model="item.regexp_pattern"
                  placeholder="请输入正则表达式"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
            </div>

            <!-- 及时性检测 -->
            <div v-if="item.ruleId === 8">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
              <el-form-item
                label="起始时间"
                :prop="`dataObj.${index}.begin_time`"
                :rules="rules.begin_time"
              >
                <!-- <el-date-picker -->
                <!-- v-model="item.begin_time" -->
                <!-- type="datetime" -->
                <!-- placeholder="选择日期时间" -->
                <!-- ></el-date-picker> -->
                <el-input
                  v-model="item.begin_time"
                  placeholder="请输入起始时间"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
              <el-form-item
                label="结束时间"
                :prop="`dataObj.${index}.deadline`"
                :rules="rules.deadline"
              >
                <!-- <el-date-picker -->
                <!-- v-model="item.deadline" -->
                <!-- type="datetime" -->
                <!-- placeholder="选择日期时间" -->
                <!-- ></el-date-picker> -->
                <el-input
                  v-model="item.deadline"
                  placeholder="请输入结束时间"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
              <el-form-item
                label="时间格式"
                :prop="`dataObj.${index}.datetime_format`"
                :rules="rules.datetime_format"
              >
                <el-input
                  v-model="item.datetime_format"
                  placeholder="请输入时间格式"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
            </div>

            <!-- 枚举值检测 -->
            <div v-if="item.ruleId === 9">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
              <el-divider />

              <el-form-item
                label="枚举值列表"
                :prop="`dataObj.${index}.enum_list`"
                :rules="rules.enum_list"
              >
                <el-input
                  v-model="item.enum_list"
                  placeholder="请输入枚举值列表"
                  clearable
                  :disabled="!canvasActions"
                />
              </el-form-item>
            </div>

            <!-- 表行数校验 -->
            <div v-if="item.ruleId === 10">
              <filters
                :data-obj="item.source"
                :index="index"
                :use-type="'source'"
                :rules="rules"
                :show-list="false"
                :canvas-actions="canvasActions"
                :prop-path="`dataObj[${index}].source`"
              />
            </div>
          </div>

          <!-- 通用底部 -->
          <div>
            <el-divider />

            <el-form-item
              label="校验方式"
              :prop="`dataObj.${index}.check_type`"
              :rules="rules.check_type"
            >
              <el-select
                v-model="item.check_type"
                placeholder="请选择规则类型"
                clearable
                :disabled="!canvasActions"
              >
                <el-option
                  v-for="items in checkTypeList"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="校验操作符"
              :prop="`dataObj.${index}.operator`"
              :rules="rules.operator"
            >
              <el-select
                v-model="item.operator"
                placeholder="请选择校验操作符"
                clearable
                :disabled="!canvasActions"
              >
                <el-option
                  v-for="items in operatorList"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="阈值"
              :prop="`dataObj.${index}.threshold`"
              :rules="rules.threshold"
            >
              <el-input-number
                v-model="item.threshold"
                placeholder="请输入阈值"
                min="0"
                max="100"
                clearable
                :disabled="!canvasActions"
              />
            </el-form-item>

            <!-- 如果是两表对比 那么不显示期望值类型 -->
            <el-form-item
              v-if="item.ruleId !== 4"
              label="期望值类型"
              :prop="`dataObj.${index}.comparison_type`"
              :rules="rules.comparison_type"
            >
              <el-select
                v-model="item.comparison_type"
                placeholder="请选择期望值类型"
                clearable
                :disabled="!canvasActions"
              >
                <el-option
                  v-for="items in expectedTypes"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
            <!-- 期望值类型 是fix 那么显示固定值 -->
            <el-form-item
              v-if="item.comparison_type === 1"
              label="固定值"
              :prop="`dataObj.${index}.comparison_name`"
              :rules="rules.comparison_name"
            >
              <el-input
                v-model="item.comparison_name"
                placeholder="请输入固定值"
                clearable
                :disabled="!canvasActions"
              />
            </el-form-item>
            <!-- <el-form-item -->
            <!-- v-if="item.ruleId !== 9" -->
            <!-- label="期望值类型" -->
            <!-- :prop="`dataObj.${index}.comparison_type`" -->
            <!-- :rules="rules.comparison_type" -->
            <!-- > -->
            <!-- <el-select v-model="item.comparison_type" placeholder="请选择期望值类型"> -->
            <!-- <el-option -->
            <!-- v-for="items in expectedTypes" -->
            <!-- :key="items.value" -->
            <!-- :label="items.label" -->
            <!-- :value="items.value" -->
            <!-- :disabled="item.disabled" -->
            <!-- /> -->
            <!-- </el-select> -->
            <!-- </el-form-item> -->
            <el-form-item
              label="失败策略"
              :prop="`dataObj.${index}.failure_strategy`"
              :rules="rules.failure_strategy"
            >
              <el-select
                v-model="item.failure_strategy"
                placeholder="请选择规则类型"
                clearable
                :disabled="!canvasActions"
              >
                <el-option
                  v-for="items in failureStrategyList"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </section>
    </div>
  </el-form>
</template>

<script setup>
  import mbData from './mbData.vue';
  import sql from './sql.vue';
  import filters from './filters.vue';
  // 导入转换
  import transform from './transform.js';
  import { getQualityRuleList, getQualityRuleFormCreateJson } from '@/api/dataGovernance';
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  import deleteIcon from '@/assets/icons/delete.svg';
  import addIcon from '@/assets/icons/add.svg';
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const form = qualityRulesStore.$state;
  const { canvasActions } = defineProps({
    canvasActions: {
      type: Boolean,
      required: true,
    },
  });
  const activeName = ref(1);
  const changeRuleType = (value) => {
    // 切换数据清空除了 ruleId 外的所有数据
    clearDataObjFields(value);
    if (value === undefined || value === null || value === '') return;
    getQualityRuleFormCreateJsonUtil(value);
  };
  const isInit = ref(false);
  const clearDataObjFields = (value) => {
    // 判断如果是回显 那么不执行
    if (!isInit.value) return;
    // 切换数据清空除了 ruleId 外的所有数据
    Object.keys(form.dataObj[0]).forEach((key) => {
      if (key !== 'ruleId' && key !== 'source') {
        form.dataObj[0][key] = '';
      }
      //   if (key === 'source') {
      //     form.dataObj[0][key] = {};
      //   }
      if (key === 'target') {
        form.dataObj[0][key] = {};
      }
    });
    qualityRulesStore.$state.dataObj = form.dataObj;
  };

  const rules = ref({
    ruleId: [{ required: true, message: '请选择规则类型', trigger: 'change' }],

    logic_operator: [{ required: true, message: '请选择逻辑操作符', trigger: 'change' }],
    field_length: [{ required: true, message: '请输入字段长度限制', trigger: 'blur' }],
    regexp_pattern: [{ required: true, message: '请输入正则表达式', trigger: 'blur' }],
    begin_time: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
    deadline: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
    datetime_format: [{ required: true, message: '请输入时间格式', trigger: 'blur' }],
    enum_list: [{ required: true, message: '请输入枚举值列表', trigger: 'blur' }],

    dataType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
    dataSources: [{ required: true, message: '请选择数据源', trigger: 'change' }],
    database: [{ required: true, message: '请选择数据库', trigger: 'change' }],
    schema: [{ required: true, message: '请选择模式', trigger: 'change' }],
    dataTable: [{ required: true, message: '请选择表', trigger: 'change' }],
    field: [{ required: true, message: '请选择检测列', trigger: 'change' }],
    filter: [{ required: false, message: '请输入过滤条件', trigger: 'blur' }],

    value_name: [{ required: true, message: '请输入值名', trigger: 'blur' }],
    execute_sql: [{ required: true, message: '请选择计算SQL', trigger: 'change' }],

    check_type: [{ required: true, message: '请选择校验方式', trigger: 'change' }],
    operator: [{ required: true, message: '请选择校验操作符', trigger: 'change' }],
    comparison_type: [{ required: true, message: '请选择期望值类型', trigger: 'change' }],
    // comparison_name
    comparison_name: [{ required: true, message: '请选择固定值', trigger: 'blur' }],
    threshold: [{ required: true, message: '请输入阈值', trigger: 'change' }],
    failure_strategy: [{ required: true, message: '请选择失败策略', trigger: 'change' }],
  });

  const ruleTypes = ref([]);

  const expectedTypes = ref([]);
  const operatorList = ref([]);
  const logic_operatorList = ref([]);
  const checkTypeList = ref([]);
  const failureStrategyList = ref([]);
  const src_connector_type = ref([]);

  const isShow = () => {
    // 点击规则后，显示规则
    activeName.value = activeName.value === 1 ? 2 : 1;
  };

  const { proxy } = getCurrentInstance();

  const checkSubmit = async () => {
    return await proxy.$refs.indexForm.validate((valid) => valid);
  };

  //   const deleteObj = async (index) => {
  //     if (!form.dataObj[index]) return;

  //     const confirm = await proxy.$modal.confirm(`是否确认删除数据项？`);

  //     if (!confirm) return;

  //     nextTick(() => {
  //       form.dataObj.splice(index, 1);
  //       proxy.$modal.msgSuccess('删除成功');
  //     });
  //   };

  defineExpose({
    checkSubmit,
  });

  const getQualityRuleListUtil = async () => {
    const res = await getQualityRuleList();
    if (res.code !== 200) return;

    const { data } = res;

    try {
      // 过滤掉 id 是 3 和 4 的规则
      ruleTypes.value = transformRule(data).filter((item) => item.id !== 2);

      console.log('ruleTypes.value', ruleTypes.value);
    } catch (error) {
      proxy.$modal.msgError('数据转换失败请查看接口数据是否正确');
    }
  };
  const formCreateJson = ref(null);
  const getQualityRuleFormCreateJsonUtil = async (ruleId = 1) => {
    const res = await getQualityRuleFormCreateJson({
      ruleId,
    });

    if (res.code !== 200) return;

    const { data } = res;

    try {
      formCreateJson.value = transformRule(JSON.parse(JSON.parse(data)));
    } catch (error) {
      proxy.$modal.msgError('数据转换失败请查看接口数据是否正确');
    }

    if (!formCreateJson.value) return;

    const labelMap = {
      'Expected - Actual': 'expected_and_actual',
      'Actual - Expected': 'actual_and_expected',
      'Actual / Expected': 'actual_or_expected',
      '(Expected - Actual) / Expected': 'expected_and_actual_or_expected',
    };

    operatorList.value =
      formCreateJson.value.find((item) => item.field === 'operator')?.options || [];

    logic_operatorList.value =
      formCreateJson.value.find((item) => item.field === 'logic_operator')?.options || [];

    expectedTypes.value =
      formCreateJson.value
        .find((item) => item.field === 'comparison_type')
        ?.options.map((item) => ({
          ...item,
          label: `${transform.rule[item.label]}(${item.label})`,
        })) || [];

    checkTypeList.value =
      formCreateJson.value
        .find((item) => item.field === 'check_type')
        ?.options.map((item) => ({
          ...item,
          label: `${transform.task_result[labelMap[item.label]]}(${item.label})`,
        })) || [];

    failureStrategyList.value =
      formCreateJson.value
        .find((item) => item.field === 'failure_strategy')
        ?.options.map((item) => ({
          ...item,
          label: `${transform.rule[item.label]}`,
        })) || [];

    src_connector_type.value =
      formCreateJson.value
        .find((item) => item.field === 'src_connector_type')
        ?.options.filter(
          (item) =>
            item.label !== 'SPARK' &&
            item.label !== 'CLICKHOUSE' &&
            item.label !== 'PRESTO' &&
            item.label !== 'H2' &&
            item.label !== 'REDSHIFT' &&
            item.label !== 'ATHENA',
        ) || [];
    // 过滤掉 SPARK，CLICKHOUSE，PRESTO，H2，REDSHIFT，ATHENA
  };

  /**
   *  转换规则
   * @param data
   */
  const transformRule = (data) => {
    if (!data) return [];

    return data.map((item) => {
      item.name = transform.rule[item.name.replace(/\$t\(/, '').replace(/\)/, '')];
      return item;
    });
  };
  const initializeDataObj = () => {
    form.dataObj = form?.dataObj?.map((item) => ({
      ...item,
      source: { ...item },
      target: { ...item },
    }));
  };

  onMounted(async () => {
    // await initializeDataObj();
    await getQualityRuleListUtil();
    await getQualityRuleFormCreateJsonUtil(form.dataObj[0].ruleId);
    isInit.value = true;
  });

  const addSyncChange = () => {
    //  不是数组转为数组
    if (!Array.isArray(form.dataObj[0]?.mapping_columns)) {
      form.dataObj[0].mapping_columns = [];
    }
    form.dataObj[0].mapping_columns.push({
      src_field: '',
      operator: '',
      target_field: '',
    });
  };
  const deleteSyncChange = (index) => {
    console.log('index', index);
    debugger;
    form.dataObj[0].mapping_columns.splice(index, 1);
  };
</script>

<style lang="scss" scoped>
  .dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
    font-weight: bold;
    padding-bottom: 15px;
    // margin-left: 6%;
    // margin-bottom: 10px;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(3, 1fr);
    grid-template-columns: repeat(3, 1fr) 1fr;
    gap: 10px;
  }
</style>
