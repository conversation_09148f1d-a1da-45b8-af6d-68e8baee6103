import request from '@/utils/request';

// 查询告警信息订阅清单列表
export function listSubscribe(query) {
  return request({
    url: '/alert/subscribe/list',
    method: 'get',
    params: query,
  });
}

// 查询告警信息订阅清单详细
export function getSubscribe(ID) {
  return request({
    url: '/alert/subscribe/' + ID,
    method: 'get',
  });
}

// 新增告警信息订阅清单
export function addSubscribe(data) {
  return request({
    url: '/alert/subscribe',
    method: 'post',
    data,
  });
}

// 修改告警信息订阅清单
export function updateSubscribe(data) {
  return request({
    url: '/alert/subscribe',
    method: 'put',
    data,
  });
}

// 删除告警信息订阅清单
export function delSubscribe(ID) {
  return request({
    url: '/alert/subscribe/' + ID,
    method: 'delete',
  });
}
