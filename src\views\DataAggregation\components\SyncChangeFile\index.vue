<template>
  <b>2.编辑器-F</b>
  <el-col :span="3">
    <el-tooltip placement="top">
      <template #content>
        <div class="box-item">
          <el-button
            icon="DocumentCopy"
            size="small"
            circle
            @click="copyInfo('formworkAPItoToken')"
          />
          <Codemirror v-model="formworkAPItoToken" :disabled-type="true" />
        </div>
      </template>
      <el-tag class="ml-2" type="warning" effect="light" round>文本到表</el-tag>
    </el-tooltip>
  </el-col>
  <el-divider></el-divider>
  <el-col :span="24" style="margin-bottom: 20px">
    <div ref="el">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button
            icon="FullScreen"
            size="small"
            circle
            type="info"
            style="position: relative; bottom: -25px; z-index: 1"
            @click="toggle"
          />
        </el-col>
        <el-col :span="12">
          <!-- <el-tag @click="exit">退出全屏</el-tag> -->
        </el-col>
      </el-row>
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
    </div>
  </el-col>

  <b>3.运行模式</b>
  <el-divider></el-divider>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="运行模式">
        <el-select v-model="modeListType" placeholder="">
          <el-option v-for="item in modeList" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="任务运行最大内存">
        <el-input v-model="modeListTypeNum" placeholder="" suffix="GB">
          <template #suffix>
            <el-icon class="el-input__icon" style="font-style: normal; margin-right: 10px"
              >GB</el-icon
            >
          </template>
        </el-input>
      </el-form-item>
      <!-- <el-select v-model="modeListTypeNum" placeholder=""> -->
      <!-- <el-option v-for="item in modeListNum" :key="item.value" :label="item.label" :value="item.value" /> -->
      <!-- </el-select> -->
    </el-col>
  </el-row>
  <el-col :span="24">
    <el-button
      type="primary"
      plain
      style="margin-top: 20px; margin-bottom: 20px"
      @click="getParentData"
      >确定</el-button
    >
  </el-col>
  <!-- <el-button plain @click="getParentData" style="margin-top: 20px; margin-bottom: 20px;" disabled="true">取消</el-button> -->
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { Base64 } from 'js-base64';
  import { useClipboard, useFullscreen } from '@vueuse/core';
  const el = ref();
  const { isFullscreen, toggle, enter, exit } = useFullscreen(el);
  // 监听键盘F10 如果点击了F10则执行全屏
  // window.onkeydown = function (event) {
  //   if (event.keyCode == 121) {
  //     enter()
  //   }
  // }
  const { copy } = useClipboard();
  const { proxy } = getCurrentInstance();
  const parentData = ref(''); // 创建一个 ref 来存储从子组件获取的数据
  const data = reactive({
    form: {},
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      describe: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
      syncTaskName: [{ required: true, message: '工作流名称不能为空', trigger: 'blur' }],
      cron: [{ required: true, message: 'cron不能为空', trigger: 'blur' }],
    },
  });
  const { form } = toRefs(data);

  const formworkAPItoToken = `
env {
  execution.parallelism = 1
  job.mode = "BATCH"
}

source {
  LocalFile {
  schema {
    fields {
      user_id=int,
      login_name=string,
      nick_name=string,
      login_passwd=string,
      name=string,
      id_num=string,
      phone_num=string,
      email=string,
      address=string,
      hirthday=string,
      gender=string,
      user_level=string,
      create_dt=string,
      operate_dt=string
    }
  }
  path = "/home/<USER>/user_info_202311131633.csv"
  file_format_type = "csv"
  delimiter = ","
  skip_header_row_number = 1
  read_columns = [user_id,login_name,nick_name,login_passwd,name,id_num,phone_num,email,address,hirthday,gender,user_level,create_dt,operate_dt]
}
}

sink {
    jdbc {
        url = "jdbc:mysql://************:30774/test?useUnicode=yes&characterEncoding=UTF-8&allowMultiQueries=true&rewriteBatchedStatements=true"
        driver = "com.mysql.cj.jdbc.Driver"
        user = "root"
        password = "123456"
        generate_sink_sql = true
        database = test
        table = user_info
        enable_upsert = true
        batch_size = 1000
        }
}`;

  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
  });
  const { flowId, nodeName, nodeId, openWorkFlowData, workFlowType } = toRefs(props);

  console.log('nodeId.value', nodeId.value);
  const modeListType = ref('seatunnel-local');
  const modeList = ref([
    {
      id: 'seatunnel-cluster',
      label: 'seatunnel-cluster',
    },
    {
      id: 'seatunnel-local',
      label: 'seatunnel-local',
    },
    {
      id: 'flink-15-cluster',
      label: 'flink-15-cluster',
    },
  ]);
  const modeListTypeNum = ref('2');
  const modeListNum = ref([
    {
      value: '1',
      label: '1',
    },
    {
      value: '2',
      label: '2',
    },
    {
      value: '3',
      label: '3',
    },
    {
      value: '4',
      label: '4',
    },
    {
      value: '5',
      label: '5',
    },
    {
      value: '6',
      label: '6',
    },
    {
      value: '7',
      label: '7',
    },
    {
      value: '8',
      label: '8',
    },
    {
      value: '9',
      label: '9',
    },
    {
      value: '10',
      label: '10',
    },
  ]);
  const copyInfo = (data) => {
    if (data == 'formworkAPItoTable') {
      copy(formworkAPItoTable);
    } else if (data == 'formworkAPItoToken') {
      copy(formworkAPItoToken);
    }
    // 提示用户
    proxy.$modal.msgSuccess('复制成功');
  };
  // 保存
  const getParentData = () => {
    // 判断是否有数据
    if (!parentData.value) {
      proxy.$modal.msgWarning('请先填写数据');
      return;
    }

    // let dataValue = {
    //   "schema": '',
    //   "datasourceType": form.value.sourceDataType,
    //   "datasourceId": form.value.sourceDataSource.id,
    //   "databaseName": form.value.sourceDatabase,
    //   "connectionParams": form.value.sourceDataSource.connectionParams,
    // }
    // const jsonString = JSON.stringify(dataValue).replace(/"/g, '\"');

    const query = {
      id: nodeId.value,
      operatorId: '4uyhrj418bb91e12y74eg345gdk',
      createTime: '',
      createUser: null,
      updateTime: null,
      updateUser: null,
      aliasName: null,
      nodeName: nodeName.value,
      flowId: flowId.value,
      parentFlowId: '',
      parentNodeId: '',
      nodeType: 'SEATUNNEL_ALG',
      parents: null,
      jobId: null,
      outputProperty: null,
      program: 'SEATUNNEL_FILE_ALG',
      operatorName: 'SEATUNNEL_FILE',
      inputProperties: [
        {
          id: '7uhjdr2q4ca1a1sdtv34rdfgh67',
          name: 'config',
          displayName: 'Seatunnel配置',
          operatorId: '4uyhrj418bb91e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          value: Base64.encode(parentData.value),
          inputPropertyId: '7uhjdr2q4ca1a1sdtv34rdfgh67',
          program: 'SEATUNNEL_FILE_ALG',
        },
        {
          id: '15fbB75F2c4bcf1ddd31B6ccfb908830',
          name: 'deployMode',
          displayName: '运行模式',
          operatorId: '4uyhrj418bb91e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          value: modeListType.value,
          inputPropertyId: '15fbB75F2c4bcf1ddd31B6ccfb908830',
          program: 'SEATUNNEL_FILE_ALG',
        },
        {
          id: '265ea14712e8f09ec801fd1ef0b9de70',
          name: 'jvmOpt',
          displayName: 'JVM参数',
          operatorId: '4uyhrj418bb91e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          // 转base64
          value: modeListTypeNum.value,
          inputPropertyId: '265ea14712e8f09ec801fd1ef0b9de70',
          program: 'SEATUNNEL_API_ALG',
        },
      ],
      isDrillDown: false,
      configDatasource: 0,
      isLogOutput: false,
      isReportOutput: false,
    };

    saveNode(query).then((response) => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess('保存成功');
        // router.push('/DataAggregation/SyncTaskManage')
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  };
  if (workFlowType.value == 'edit') {
    //  回显数据
    // 遍历openWorkFlowData.value.inputProperties 对比  如果相同则赋值

    openWorkFlowData.value.inputProperties.forEach((item, index) => {
      if (item.name == 'config') {
        parentData.value = Base64.decode(item.value);
      } else if (item.name == 'deployMode') {
        modeListType.value = item.value;
      } else if (item.name == 'jvmOpt') {
        modeListTypeNum.value = item.value;
      }
    });
  }
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .box-item {
    white-space: pre-line;
    max-width: 700px;
    max-height: 400px;
    overflow-y: auto;

    滚动条样式 &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }
</style>
