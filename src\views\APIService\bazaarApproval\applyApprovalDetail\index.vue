<template>
  <div class="detail-container">
    <el-button type="primary" icon="DArrowLeft" class="call-back-btn" @click="callback"
      >返回</el-button
    >
    <div class="apply-container">
      <detailMsg type="申请列表" :operate-type="operateType"></detailMsg>
    </div>
    <div class="right">
      <el-card class="personBox">
        <div class="pb-top">
          <div class="circle">{{ firstText }}</div>
          <div>
            <div style="font-size: 16px; font-weight: 600">{{ userName }}</div>
            <div>
              <el-tag size="small" type="info" effect="light"> 已撤销 </el-tag>
              {{ status }}
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div>
          <span>审批流：</span>
        </div>
        <div style="margin-top: 20px">
          <span>备注：</span>
        </div>
      </el-card>
      <el-card class="flowBox">
        <div class="flow-top">
          <div>流程</div>
          <div v-if="status == '已撤销'">
            <el-button>再次申请</el-button>
          </div>
        </div>
        <!-- 流程组件 -->
        <div class="flow-node-format">
          <el-scrollbar class="flow-node-list">
            <flow-node-format ref="flowNodeFormatRef"></flow-node-format>
          </el-scrollbar>
          <div class="flow-btn-box">
            <el-button @click="approval(0)">驳回</el-button>
            <el-button type="primary" @click="approval()">审批</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup>
  import detailMsg from './components/DetailMsg';
  import FlowNodeFormat from '@/views/flyflow/flyflow/components/flow/FlowNodeFormatData.vue';
  import router from '@/router';
  const firstText = ref(null);
  const applyUser = ref(null);
  const status = ref(null);
  const operateType = ref('撤销');
  const flowNodeFormatRef = ref();

  // 获取流程
  const getFlowNode = () => {
    flowNodeFormatRef.value.queryData(
      {
        flyflow_261501364504: '',
      },
      'pkk8jg21718175975060',
      '',
      '',
      'start',
    );
  };
  // 审核、驳回事件
  const approval = (type = 1) => {
    if (type === 0) {
      console.log('bohui');
    } else {
      console.log('shenpi');
    }
  };

  const callback = () => {
    router.go(-1);
  };

  const init = () => {
    getFlowNode();
  };

  onMounted(async () => {
    init();
    // const res = await getUserInfo()
    // applyUser.value = res.data.name
    // firstText.value = res.data.name.slice(0,1)
    // status.value = res.data.status
  });
</script>
<style lang="scss" scoped>
  .detail-container {
    border-radius: 8px;
    background: #fcfbff;
    margin: 0 260px;
    display: flex;
    padding: 20px;

    .apply-container {
      width: 1320px;
      margin: 0 -260px;
    }

    .right {
      flex: 1;
      margin-left: 20px;
      .personBox {
        height: 264px;
        position: relative;
        left: 0;
        top: 45px;

        .line {
          margin: 20px 0;
          height: 1px;
          background: #f7f8fb;
        }

        .pb-top {
          display: flex;
          align-items: end;

          .circle {
            width: 42px;
            height: 42px;
            padding: 6px 10px;
            border-radius: 20px;
            color: #1269ff;
            background: #eaf1ff;
            box-sizing: border-box;
            margin-right: 20px;
          }
        }
      }

      .flowBox {
        position: relative;
        left: 0;
        top: 65px;

        .flow-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .flow-node-format {
            height: calc(100% - 20px);
          }
        }
      }
    }
    .call-back-btn {
      z-index: 9;
    }
  }
</style>
