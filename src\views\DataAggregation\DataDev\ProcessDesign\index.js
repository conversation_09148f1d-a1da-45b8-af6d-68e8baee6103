import { Shape } from '@antv/x6';

export const graphOptions = (check = false) => {
  return {
    container: document.getElementById('container'),
    background: { color: '#F2F7FA' },
    // panning: true,//不能和scroller一起使用
    mousewheel: true,
    width: 1400,
    height: 1000, // 设置画布的高度

    // 定制节点和边的交互行为 ==> boolean 节点或边是否可交互
    interacting: check
      ? {
          nodeMovable: true,
          edgeMovable: true,
          magnetConnectable: true,
          vertexDeletable: true,
        }
      : true,

    // 显示网格 // 'dot' | 'fixedDot' | 'mesh'
    grid: {
      size: 10, // 网格大小
      visible: true,
      type: 'mesh',
      args: [
        { color: '#E7E8EA', thickness: 1 },
        { color: '#CBCED3', thickness: 1, factor: 5 },
      ],
    },

    // 滚动
    scroller: {
      enabled: true,
      pageVisible: false, // 是否分页
      pageBreak: false,
      pannable: true, // 是否平移
    },
    // 滚轮缩放 MouseWheel
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: ['ctrl', 'meta'],
      maxScale: 3,
      minScale: 0.3,
    },
    // 连线规则
    connecting: {
      // 路由类型
      router: {
        // 连线类型在此修改
        // 曼哈顿路由 'manhattan' 路由是正交路由 'orth' 的智能版本，该路由由水平或垂直的正交线段组成，并自动避开路径上的其他节点（障碍）。
        name: 'metro',
        args: {
          padding: 1,
        },
      },
      // 圆角连接器，将起点、路由点、终点通过直线按顺序连接，并在线段连接处通过圆弧连接（倒圆角）。
      connector: {
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      // allowReverse: false,
      allowLoop: false, // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点
      allowBlank: false, // 是否允许连接到画布空白位置的点
      allowMulti: false, // 是否允许在相同的起始节点和终止之间创建多条边
      allowEdge: false, // 是否允许边链接到另一个边

      //   snap: true, // 是否开启自动吸附
      // 距离节点或者连接桩 20px 时会触发自动吸附
      snap: {
        radius: 50,
      },
      // 拽出新的边
      createEdge() {
        return new Shape.Edge({
          markup: [
            {
              tagName: 'path',
              selector: 'stroke',
            },
          ],
          //   connector: { name: 'rounded' },
          // connector: { name: 'smooth' },
          // connector形状
          connector: { name: 'jumpover' },

          // 连接线样式
          attrs: {
            stroke: {
              fill: 'none',
              connection: true,
              strokeWidth: 3,
              strokeLinecap: 'round',
              stroke: '#1890ff',
              strokeDasharray: 5,
              targetMarker: 'classic',
              style: {
                animation: 'ant-line 30s infinite linear',
              },
            },
          },
          zIndex: 99999999,
        });
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet;
      },
    },
    // 连线高亮
    highlighting: {
      // 连线过程中，自动吸附到链接桩时被使用。
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            width: 12,
            r: 6,
            magnet: true,
            stroke: '#008CFF',
            strokeWidth: 2,
            fill: '#0F67FF',
          },
        },
      },
    },
    // 鼠标气泡框
    tooltip: {
      visible: true, // 设置默认可见
      marker: true, // 设置提示框的箭头
      offset: 10, // 设置提示框与鼠标的偏移量
    },
    rotating: false, // 不能旋转
    autoResize: true,
    onToolItemCreated({ tool }) {
      const options = tool.options;
      if (options && options.index % 2 === 1) {
        tool.setAttrs({ fill: 'red' });
      }
    },
  };
};

// 链接桩样式
export const portStyle = {
  // width: 12,
  r: 6,
  magnet: true,
  stroke: '#008CFF',
  strokeWidth: 2,
  fill: '#fff',
  zIndex: 1,
  style: {
    visibility: 'hidden',
  },
};

// 链接桩配置
export const ports = {
  // 设置链接桩分组
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          ...portStyle,
        },
      },
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          ...portStyle,
        },
      },
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          ...portStyle,
        },
      },
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          ...portStyle,
        },
      },
    },
    absolute: {
      position: 'absolute',
      attrs: {
        circle: {
          r: 6,
          magnet: true,
          stroke: '#008CFF',
          strokeWidth: 2,
          fill: '#fff',
        },
      },
    },
  },
  // 链接桩
  items: [
    {
      group: 'top',
    },
    {
      group: 'right',
    },
    {
      group: 'bottom',
    },
    {
      group: 'left',
    },
  ],
};

// 自定义节点的名称
export const SETTING_SHAPE_NAME = 'setting-shape';

// 动态计算宽高比
export const transformToPercent = (target, sum, font) => {
  const percent = (target / sum) * 100;
  return `${percent.toFixed(2)}${font ? 'px' : '%'}`;
};

// 自定义对象节点的配置，需要展示更多的节点内容在这里去添加，并更新数据
// https://x6.antv.vision/zh/docs/tutorial/intermediate/custom-node
export const SettingNodeOptions = {
  inherit: 'rect',
  width: 64,
  height: 64,
  markup: [
    {
      tagName: 'rect',
      selector: 'body',
    },
    {
      tagName: 'image',
      selector: 'settingImage',
    },
    {
      tagName: 'text',
      selector: 'settingName',
    },
  ],
  attrs: {
    body: {
      class: SETTING_SHAPE_NAME,
      stroke: '#5F95FF',
      fill: '#5F95FF',
      strokeWidth: 2,
      fillOpacity: 0.75,
    },
    settingImage: {
      refWidth: transformToPercent(32, 64),
      refHeight: transformToPercent(32, 64),
      refX: transformToPercent(16, 64),
      refY: transformToPercent(8, 64),
      textAnchor: 'middle',
      textVerticalAnchor: 'bottom',
      anchor: 'middle',
      verticalAnchor: 'bottom',
    },
    settingName: {
      refWidth: '100%',
      refX: transformToPercent(32, 64),
      refY: transformToPercent(60, 64),
      textAnchor: 'middle',
      textVerticalAnchor: 'bottom',
      fontSize: 12,
      fill: '#262626',
      // 超出换行
      // textWrap: {
      //     width: 64,
      //     breakWord: false, // 是否截断单词
      // },
    },
  },
  ports: { ...ports },
};

export const colors = ['#2AC94F', '#0AAEFF', '#DB5A6B', '#057748', '#2E4E7E', '#845A33'];
