<template>
  <div class="card-box">
    <section
      style="
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        gap: 10px;
        margin-bottom: 20px;
      "
    >
      <div>
        <span>
          <span>客户标识：</span> <span class="customerKey">{{ licenseInfo?.key }}</span>
        </span>
        <el-link
          icon="DocumentCopy"
          style="margin-left: 20px"
          :underline="false"
          @click="copyText"
        />
      </div>
      <div>
        <el-link
          style="margin-right: 12px; color: #1269ff; cursor: pointer"
          :underline="false"
          @click="onClick"
        >
          <IconRefresh />
          &nbsp;更新
        </el-link>
      </div>
    </section>
    <el-row>
      <el-col :span="22">
        <el-tag class="card-type" type="warning">有效期至：{{ licenseInfo?.tme }}</el-tag>
      </el-col>
      <el-col :span="2">
        <el-tag type="success">使用中</el-tag>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { getLicense } from '@/api/login';
  import { ElMessageBox } from 'element-plus';
  import { onMounted } from 'vue';
  import licenseExpired from '~/src/utils/licenseExpired';
  import { IconRefresh } from '@arco-iconbox/vue-update-line-icon';

  const { proxy } = getCurrentInstance();
  const licenseInfo = ref();
  onMounted(async () => {
    const res = await getLicense('123');
    console.log(res);
    if (res.code !== 200) return;
    licenseInfo.value = res.data;
  });

  function copyText() {
    const text = document.getElementsByClassName('customerKey')[0].innerText;
    navigator.clipboard.writeText(text).then(
      () => {
        proxy.$modal.msgSuccess('复制成功');
      },
      () => {
        proxy.$modal.msgError('复制失败');
      },
    );
  }

  function onClick() {
    ElMessageBox.confirm(licenseExpired(licenseInfo.value), '提示', {
      dangerouslyUseHTMLString: true,
      closeOnClickModal: false,
      showClose: true,
      showCancelButton: false,
      showConfirmButton: false,
      customClass: 'custom-class-key',
    });
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .card-box {
    width: 770px;
    padding: 20px;
    background: $--base-color-card-bg2;
    margin: 20px auto;
    border-radius: 8px;
    p {
      margin: 0;
      font-size: 14px;
    }
    .card-type {
      margin-top: 4px;
      border: none;
      background: $--base-btn-yellow-bg;
    }
    .customerKey {
      color: $--base-color-text2;
      font-size: 16px;
    }
  }
</style>
