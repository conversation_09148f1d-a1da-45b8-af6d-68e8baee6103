import { getTenantList } from '@/api/system/user';

export const useWorkFLowStore = defineStore(
  'workFlow',
  () => {
    // localStorage.setItem('workFlow', JSON.stringify({ workSpaceId: 0, tenantId: 0 }))
    const tenantId = ref(
      localStorage.getItem('workFlow')
        ? JSON.parse(localStorage.getItem('workFlow')).tenantId
        : undefined,
    ); // state
    const workSpaceId = ref(
      localStorage.getItem('workFlow')
        ? JSON.parse(localStorage.getItem('workFlow')).workSpaceId
        : undefined,
    );
    const runStatus = ref(false);

    const cells = ref();

    const pare = ref();

    const setWorkSpaceId = (id) => {
      workSpaceId.value = id;
    };
    const getWorkSpaceId = () => {
      return workSpaceId.value;
    };

    const setTenantId = (id) => {
      tenantId.value = id;
    };
    const getTenantId = () => {
      return tenantId.value;
    };

    const setPare = (obj) => {
      pare.value = obj;
    };

    const getRunStatus = () => {
      return runStatus.value;
    };

    const setRunStatus = (obj) => {
      runStatus.value = obj;
    };
    const getCells = () => {
      return cells.value;
    };

    const setCells = (obj) => {
      cells.value = obj;
    };
    const tenantList = ref([]);
    const getTenantInfo = () => {
      return new Promise((resolve, reject) => {
        getTenantList()
          .then((res) => {
            tenantList.value = res.data;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    };
    return {
      getRunStatus,
      setRunStatus,
      setWorkSpaceId,
      getWorkSpaceId,
      setTenantId,
      getTenantId,
      getCells,
      setCells,
      workSpaceId,
      runStatus,
      tenantId,
      pare,
      setPare,
      cells,
      tenantList,
      getTenantInfo,
    };
  },
  {
    persist: true,
  },
);
