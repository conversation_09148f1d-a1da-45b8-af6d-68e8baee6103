import { ref, reactive, nextTick } from 'vue';
import {
  getMetadataAcquisitionList,
  isGather,
  noGather,
  getGatherRecord,
  findTable,
  runCollection,
  getMetadataLog,
  deleteMetadataLog,
  configCollection,
  restartCollection,
  removeMetadataLogBatch,
  deleteMetadataLogBatch,
  getMetadataAcquisitionEdit,
  stopCollection,
} from '@/api/dataGovernance';

import {
  STATUS_TYPE,
  PLAN_TYPE,
} from '@/views/dataGovernance/dataMap/metadataCollection/common/constants';
import { parseTime, tableLabelToValue } from '@/utils/xugu';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useWorkFLowStore } from '@/store/modules/workFlow';
import { useRouteDataStore } from '@/store/modules/dataAssets';
import { useRouteData } from '@/store/modules/dataCollect';
import router from '@/router';
export default function useMetadataCollectionService(props) {
  const { proxy } = getCurrentInstance();

  const store = useWorkFLowStore();

  const storeForRoute = useRouteDataStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const pageType = ref('list');
  const choseType = ref('');
  const cardDatasModel = ref([
    {
      name: 'XUGU',
      type: 'XUGU',
      total: 0,
      collectNum: 0,
      remark: '尚未创建XUGU类型数据源',
    },
    {
      name: 'ORACLE',
      type: 'ORACLE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建ORACLE类型数据源',
    },
    {
      name: 'HIVE',
      type: 'HIVE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建HIVE类型数据源',
    },
    {
      name: 'MYSQL',
      type: 'MYSQL',
      total: 0,
      collectNum: 0,
      remark: '尚未创建MYSQL类型数据源',
    },
    {
      name: 'SQLSERVER',
      type: 'SQLSERVER',
      total: 0,
      collectNum: 0,
      remark: '尚未创建SQLSERVER类型数据源',
    },
    {
      name: 'POSTGRESQL',
      type: 'POSTGRESQL',
      total: 0,
      collectNum: 0,
      remark: '尚未创建POSTGRESQL类型数据源',
    },
    {
      name: 'DAMENG',
      type: 'DAMENG',
      total: 0,
      collectNum: 0,
      remark: '尚未创建DAMENG类型数据源',
    },
    {
      name: 'GREENPLUM',
      type: 'GREENPLUM',
      total: 0,
      collectNum: 0,
      remark: '尚未创建GREENPLUM类型数据源',
    },
    {
      name: 'KINGBASE',
      type: 'KINGBASE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建KINGBASE类型数据源',
    },
    {
      name: 'DB2',
      type: 'DB2',
      total: 0,
      collectNum: 0,
      remark: '尚未创建DB2类型数据源',
    },
    {
      name: 'SYBASE',
      type: 'SYBASE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建SYBASE类型数据源',
    },
    // DWS
    {
      name: 'DWS',
      type: 'DWS',
      total: 0,
      collectNum: 0,
      remark: '尚未创建DWS类型数据源',
    },
    // CLICKHOUSE
    {
      name: 'CLICKHOUSE',
      type: 'CLICKHOUSE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建类型数据源',
    },
    // TDENGINE
    {
      name: 'TDENGINE',
      type: 'TDENGINE',
      total: 0,
      collectNum: 0,
      remark: '尚未创建TDENGINE类型数据源',
    },
    // XUGUTSDB
    {
      name: 'XUGUTSDB',
      type: 'XUGUTSDB',
      total: 0,
      collectNum: 0,
      remark: '尚未创建XUGUTSDB类型数据源',
    },
  ]);
  // 初始化表格、查询框、弹出框
  const databaseInfo = reactive({
    data: [],
  });
  const tableSelect = ref([]);
  const dataType = ref('1');
  const collectTable = [
    // {
    //   prop: 'id',
    //   label: '序号',
    //   width: 100,
    // },
    {
      prop: 'name',
      label: '数据源名称',
      showOverflowTooltip: true,
    },
    {
      prop: 'creator',
      label: '创建人',
      width: 120,
    },
    {
      //   prop: 'collectionPlan',
      prop: 'plan',
      label: '采集计划',
      showOverflowTooltip: true,
    },
    {
      prop: 'remark',
      label: '数据源备注',
      showOverflowTooltip: true,
    },
    {
      prop: 'runTime',
      label: '上次运行时间',
      width: 200,
    },
  ];
  const unCollectTable = [
    // {
    //   prop: 'id',
    //   label: '序号',
    //   width: 100,
    // },
    {
      prop: 'name',
      label: '数据源名称',
      showOverflowTooltip: true,
    },
    {
      prop: 'creator',
      label: '创建人',
      width: 120,
    },
    {
      prop: 'connectionInfoLabel',
      label: '数据源连接信息',
      showOverflowTooltip: true,
    },
    {
      prop: 'remark',
      label: '数据源备注',
      showOverflowTooltip: true,
    },
  ];
  const recordTable = [
    // {
    //   prop: 'id',
    //   label: '序号',
    //   width: 100,
    // },
    {
      prop: 'name',
      label: '数据源名称',
      showOverflowTooltip: true,
    },
    {
      prop: 'creator',
      label: '创建人',
      width: 120,
    },
    {
      prop: 'status',
      label: '运行状态',
      showOverflowTooltip: true,
      width: 100,
    },
    {
      prop: 'type',
      label: '运行类型',
      showOverflowTooltip: true,
    },
    {
      prop: 'startTime',
      label: '开始时间',
      width: 200,
    },
    {
      prop: 'endTime',
      label: '结束时间',
      width: 200,
    },

    {
      prop: 'findTable',
      label: '上次运行发现表',
      showOverflowTooltip: true,
    },
  ];

  const detailInfo = reactive({
    title: 'XUGU',
    searchInfo: {
      searchForm: {
        ruleName: '',
      },
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        maxCount: 10,
        total: 10,
      },
    },
    tableInfo: {
      columns: collectTable,
      tableData: [
        // {
        //   name: '规则名称',
        //   type: '规则类型',
        //   creator: '创建人',
        //   findTable: '10',
        //   status: '0',
        //   statusLabel: '失败',
        //   startTime: '2023-03-15 12:00:00',
        //   endTime: '2023-03-15 12:00:00',
        //   remark: '这是描述',
        // },
        // {
        //   name: '规则名称',
        //   type: '规则类型',
        //   creator: '创建人',
        //   findTable: '10',
        //   status: '1',
        //   statusLabel: '成功',
        //   startTime: '2023-03-15 12:00:00',
        //   endTime: '2023-03-15 12:00:00',
        //   remark: '这是描述',
        // },
        // {
        //   name: '规则名称',
        //   type: '规则类型',
        //   creator: '创建人',
        //   findTable: '10',
        //   status: '2',
        //   statusLabel: '运行中',
        //   startTime: '2023-03-15 12:00:00',
        //   endTime: '2023-03-15 12:00:00',
        //   remark: '这是描述',
        // },
      ],
    },
  });
  const formRules = ref({
    isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
    methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
    plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
  });

  const dialogInfo = reactive({
    dialogVisible: false,
    dialogTitle: '',
    id: '',
    searchInfo: {
      searchForm: {
        ruleName: '',
      },
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        maxCount: 10,
        total: 10,
      },
    },
    data: [],
    columns: [
      {
        prop: 'tableName',
        label: '表名',
        width: 300,
        showOverflowTooltip: true,
      },
      {
        prop: 'schemaName',
        label: '模式名',
      },
      {
        prop: 'dbName',
        label: '库名',
        width: 140,
        showOverflowTooltip: true,
      },
      {
        prop: 'dsName',
        label: '数据源名称',
      },
      {
        prop: 'type',
        label: '类型',
      },
    ],
    planFromInfo: {
      reqFrom: {
        // date: '',
        // plan: '',
        // time: '',
        // minute: '',
        // timeInterval: '',
        // isWaring: '',
        // methodType: '',
        // week: '',
        plan: 0,
      },
      dateInfo: [],
      weekInfo: [
        {
          label: '周一',
          value: 1,
        },
        {
          label: '周二',
          value: 2,
        },
        {
          label: '周三',
          value: 3,
        },
        {
          label: '周四',
          value: 4,
        },
        {
          label: '周五',
          value: 5,
        },
        {
          label: '周六',
          value: 6,
        },
        {
          label: '周日',
          value: 7,
        },
      ],
      timeInfo: [],
    },
    logInfo: [],
    logContent: '',
  });

  // 事件
  const cardListener = reactive({
    detail: () => {},
    add: () => {},
  });
  const listeners = {
    changeDataType: async (res) => {
      detailInfo.tableInfo.columns = [];
      if (res === '1') {
        detailInfo.tableInfo.columns = collectTable;
        await getGatherDetail();
      } else if (res === '2') {
        detailInfo.tableInfo.columns = unCollectTable;
        await getNoGatherDetail();
      } else {
        detailInfo.tableInfo.columns = recordTable;
        await getGatherRecordList();
      }
    },
    turnTo: (url, params) => {
      router.push({
        path: url,
      });
    },
  };

  // 表格对应监听事件
  const tableListener = reactive({
    tableSearch: () => {
      tableSearch();
    },
    refresh: () => {
      detailInfo.searchInfo.searchForm = { name: '' };
      tableSearch();
    },
    showTableDialog: async (scope) => {
      dialogInfo.title = '运行发现表';
      dialogInfo.type = 'findTable';
      dialogInfo.dialogVisible = true;
      dialogInfo.id = scope.row.id;
      await getFindTable(scope.row.id);
    },
    showCollectDialog: (scope) => {
      dialogInfo.title = '配置采集计划';
      dialogInfo.type = 'collectPlan';
      dialogInfo.dialogVisible = true;
      dialogInfo.planFromInfo.reqFrom.isEdit = false;
      dialogInfo.id = scope.row.id;
    },
    showLogDetail: (row) => {
      dialogInfo.title = '日志详情';
      dialogInfo.type = 'log';
      dialogInfo.dialogVisible = true;
      getLogList(row.row);
    },
    selectChange: (res) => {
      tableSelect.value = res;
    },
    deleteItems: () => {
      const message = {};
      message.title = dataType.value === '1' ? '删除选中的元数据采集' : '是否确认删除该执行记录';
      message.content =
        dataType.value === '1'
          ? '删除元数据采集后，将不再采集该数据源下的所有数据。'
          : '删除该执行记录后，将无法恢复';
      if (tableSelect.value.length <= 0) {
        ElMessage.error('请选择要删除的元数据采集');
      } else {
        const deleteFnc = dataType.value === '1' ? removeMetadataLogBatch : deleteMetadataLogBatch;
        ElMessageBox({
          title: '操作确认',
          message: h('p', null, [
            h('p', null, message.title),
            h('span', { style: 'color: teal' }, message.content),
          ]),
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            console.log(tableSelect.value);
            const choseList = tableSelect.value.map((list) => {
              return list.id;
            });
            deleteFnc({ ids: choseList.toString() }).then((resD) => {
              if (resD.code === 200) {
                ElMessage({
                  type: 'success',
                  message: '删除成功',
                });
                // if (dataType.value === '1') {
                //   getGatherDetail();
                // } else {
                //   getGatherRecordList();
                // }
                tableSearch();
              } else {
                ElMessage({
                  type: 'error',
                  message: '删除失败',
                });
              }
            });
          })
          .catch(() => {});
      }
    },
    deleteItem: (scope) => {
      const message = {};
      message.title = dataType.value === '1' ? '删除选中的元数据采集' : '是否确认删除该执行记录';
      message.content =
        dataType.value === '1'
          ? '删除元数据采集后，将不再采集该数据源下的所有数据。'
          : '删除该执行记录后，将无法恢复';
      const deleteFnc = dataType.value === '1' ? removeMetadataLogBatch : deleteMetadataLogBatch;
      ElMessageBox({
        title: '操作确认',
        message: h('p', null, [
          h('p', null, message.title),
          h('span', { style: 'color: teal' }, message.content),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          deleteFnc({ ids: scope.row.id + '' }).then((resD) => {
            if (resD.code === 200) {
              ElMessage({
                type: 'success',
                message: '删除成功',
              });
              //   if (dataType.value === '1') {
              //     getGatherDetail();
              //   } else {
              //     getGatherRecordList();
              //   }
              tableSearch();
            } else {
              ElMessage({
                type: 'error',
                message: '删除失败',
              });
            }
          });
        })
        .catch(() => {});
    },
    run: (scope) => {
      runCollections(scope);
    },
    stopCollection: (scope) => {
      stopCollections(scope.row.id);
    },
    restart: (scope) => {
      restartCollection({
        workSpaceId: workspaceId.value,
        tenantId: tenantId.value,
        id: scope.row.id,
      }).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '设置成功',
          });
          //   getGatherRecordList();
          tableSearch();
        }
      });
    },
    edit: (scope) => {
      getMetadataAcquisitionEdit({ id: Number(scope.row.id) }).then((res) => {
        console.log(res);
        // 处理返回数据
        let methodType = [];
        const timeInterval = res.dateRange?.split(',').map((item) => {
          return new Date(item);
        });
        const plan = tableLabelToValue(PLAN_TYPE, res.planType);
        switch (res.noticeType) {
          case '站内信':
            methodType = [2];
            break;
          case '邮箱':
            methodType = [1];
            break;
          case '全部':
            methodType = [1, 2];
            break;
          default:
            methodType = [];
            break;
        }
        dialogInfo.title = '配置采集计划';
        dialogInfo.isEdit = true;
        dialogInfo.type = 'collectPlan';
        dialogInfo.dialogVisible = true;
        dialogInfo.id = res.gatherId;
        dialogInfo.planFromInfo.reqFrom = {
          isEdit: true,
          isWaring: res.isWaring,
          methodType,
          timeInterval,
          plan,
          week:
            plan === 2
              ? res.planDate?.split(',').map((item) => {
                  return Number(item);
                })
              : [],
          date:
            plan === 1
              ? res.planDate?.split(',').map((item) => {
                  return Number(item);
                })
              : [],
          time: res.planTime
            ? new Date(parseTime(new Date().getTime(), '{y}-{m}-{d}') + ' ' + res.planTime + ':00')
            : '',
        };
      });
    },
  });
  // 详情模块监听事件
  const detailListener = reactive({
    callback: async () => {
      dataType.value = '1';
      detailInfo.tableInfo.columns = collectTable;
      detailInfo.tableInfo.tableData = [];
      pageType.value = 'list';
      choseType.value = {};
      await getMetaDataList();
    },
    showDetail: async (card) => {
      choseType.value = card;
      detailInfo.title = card.name;
      try {
        await getGatherDetail(card.name);
      } catch (error) {
        console.log(error, 'error');
      }

      pageType.value = 'detail';
    },
  });
  // 弹出框内容监听
  const dialogListener = reactive({
    submitSpatial: () => {
      proxy.$refs.collectPlanFrom.validate(async (valid) => {
        if (valid) {
          //   console.log(dialogInfo.planFromInfo.reqFrom);
          //   console.log(parseTime(dialogInfo.planFromInfo.reqFrom.time));
          let noticeType = 0;
          if (dialogInfo?.planFromInfo?.reqFrom?.methodType?.length > 1) {
            noticeType = 3;
          } else if (dialogInfo?.planFromInfo?.reqFrom?.methodType?.length === 1) {
            noticeType = dialogInfo.planFromInfo.reqFrom.methodType[0];
          }
          // 处理时间
          const reqDateRange = dialogInfo.planFromInfo.reqFrom.timeInterval?.map((time) => {
            return parseTime(time);
          });
          // 处理数据
          const req = {
            gatherId: dialogInfo.id,
            isWaring: dialogInfo.planFromInfo.reqFrom.isWaring,
            noticeType,
            planType: dialogInfo.planFromInfo.reqFrom.plan,
            dateRange: reqDateRange?.toString(),
            planDate: '', // '传值日期，1到9不传01这种,明天则不需要传',
            planTime: parseTime(dialogInfo.planFromInfo.reqFrom.time, '{h}:{i}'),
            workSpaceId: workspaceId.value,
            tenantId: tenantId.value,
            isEdit: dialogInfo.planFromInfo.reqFrom.isEdit,
          };
          // 不同类型planDate传不同值
          if (req.planType === 1) {
            req.planDate = dialogInfo.planFromInfo.reqFrom.date?.toString();
          } else if (req.planType === 2) {
            req.planDate = dialogInfo.planFromInfo.reqFrom.week?.toString();
          } else if (req.planType === 3 || req.planType === 0) {
            delete req.planDate;
          }
          const res = await configCollection(req);
          if (res.code === 200) {
            ElMessage({
              type: 'success',
              message: '配置成功',
            });
            dialogInfo.planFromInfo.reqFrom = {
              plan: 0,
            };
            if (dialogInfo.isEdit) {
              getGatherDetail();
            } else {
              getNoGatherDetail();
            }
            dialogInfo.dialogVisible = false;
          } else {
            ElMessage({
              type: 'error',
              message: res.msg,
            });
          }
        }
      });
    },
    cancel: () => {
      dialogInfo.dialogVisible = false;
    },
    tableSearch: () => {
      getFindTable(dialogInfo.id);
    },
    formListener: {
      // 不同的采集计划对应不同的值
      changeDataType: (res) => {
        if (res === 1) {
          formRules.value = {
            isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
            methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
            plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
            time: [{ required: true, message: '请选择时间', trigger: 'change' }],
            date: [{ required: true, message: '请选择日期', trigger: 'change' }],
          };
        } else if (res === 2) {
          formRules.value = {
            isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
            methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
            plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
            time: [{ required: true, message: '请选择时间', trigger: 'change' }],
            week: [{ required: true, message: '请选择日期', trigger: 'change' }],
          };
        } else if (res === 3) {
          formRules.value = {
            isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
            methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
            plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
            time: [{ required: true, message: '请选择时间', trigger: 'change' }],
          };
        } else if (res === 4) {
          formRules.value = {
            isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
            methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
            plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
            minute: [{ required: true, message: '请选择日期', trigger: 'change' }],
          };
        } else {
          formRules.value = {
            isWaring: [{ required: true, message: '请选择是否告警', trigger: 'change' }],
            methodType: [{ required: true, message: '请选择告警通知方式', trigger: 'change' }],
            plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
          };
        }
        setTimeout(() => {
          proxy.$refs.collectPlanFrom.clearValidate();
        }, 0);
      },
    },
    changeLogDetail: (row) => {
      dialogInfo.logContent = row.log || '';
    },
    turnTable: (scope) => {
      toAssets(scope.row);
    },
  });

  const tableSearch = async () => {
    // const params = {
    //   ...detailInfo.searchInfo.queryParams,
    //   ...detailInfo.searchInfo.searchForm,
    // };
    // detailInfo.tableInfo.tableData = [];
    // detailInfo.tableInfo.tableData.push({
    //   name: '规则名称',
    //   type: '规则类型',
    //   creator: '创建人',
    // });
    if (dataType.value === '1') {
      await getGatherDetail();
    } else if (dataType.value === '2') {
      await getNoGatherDetail();
    } else {
      await getGatherRecordList();
    }

    // const res = await proxy.$http.get('/api/rule/list', { params });
  };

  // 获取数据源列表
  const getMetaDataList = async () => {
    const req = {
      workSpaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await getMetadataAcquisitionList(req);
    if (res) {
      for (const item in res) {
        const isCreated = [];
        const createdData = res[item].map((data) => {
          if (data.isGather) {
            isCreated.push(data);
          }
          return data;
        });
        // if (createdData.length) {
        //   isCreated = true;
        // }
        databaseInfo.data.forEach((data) => {
          if (item === data.name || item === data.type) {
            data.collectNum = isCreated.length || 0;
            data.total = res[item].length;
            data.remark =
              res[item].length > 0 ? '已创建采集器的数据源' : `尚未创建${item}类型数据源`;
            data.isCreated = isCreated.length > 0;
          }
        });
        // databaseInfo.data.push({
        //   name: item,
        //   type: item,
        //   collectNum: isCreated.length || 0,
        //   total: res[item].length,
        //   //   remark: isCreated ? '已创建采集器的数据源' : `尚未创建${item}类型数据源`,
        //   remark: res[item].length > 0 ? '已创建采集器的数据源' : `尚未创建${item}类型数据源`,
        //   isCreated: isCreated.length > 0,
        // });
      }
    }
  };
  // 获取已采集
  const getGatherDetail = async (name) => {
    detailInfo.tableInfo.tableData = [];
    const req = {
      pageNum: detailInfo.searchInfo.queryParams.pageNum,
      pageSize: detailInfo.searchInfo.queryParams.pageSize,
      type: choseType.value.type,
      dsname: detailInfo.searchInfo.searchForm.name,
      workSpaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await isGather(req);
    detailInfo.searchInfo.queryParams.total = res.total;
    detailInfo.tableInfo.tableData = res.rows.map((item) => {
      item.name = item.dsName;
      item.creator = item.createBy;
      item.connectionInfo = item.databaseInfo;
      return item;
    });
  };
  // 获取未采集
  const getNoGatherDetail = async (name) => {
    detailInfo.tableInfo.tableData = [];
    const req = {
      pageNum: detailInfo.searchInfo.queryParams.pageNum,
      pageSize: detailInfo.searchInfo.queryParams.pageSize,
      type: choseType.value.type,
      dsname: detailInfo.searchInfo.searchForm.name,
      workSpaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await noGather(req);
    detailInfo.searchInfo.queryParams.total = res.total;
    detailInfo.tableInfo.tableData = res.rows.map((item) => {
      item.name = item.dsName;
      item.creator = item.createBy;
      item.connectionInfo = item.databaseInfo;
      item.connectionInfoLabel = item.databaseInfo.split(',   ').join('<br/>');
      return item;
    });
  };

  // 获取采集记录
  const getGatherRecordList = async (name) => {
    detailInfo.tableInfo.tableData = [];
    const req = {
      pageNum: detailInfo.searchInfo.queryParams.pageNum,
      pageSize: detailInfo.searchInfo.queryParams.pageSize,
      dsname: detailInfo.searchInfo.searchForm.name,
      type: choseType.value.type,
      workSpaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await getGatherRecord(req);
    detailInfo.searchInfo.queryParams.total = res.total;
    detailInfo.tableInfo.tableData = res.rows.map((item) => {
      item.name = item.dsName;
      item.creator = item.createBy;
      item.statusLabel = item.status;
      item.status = tableLabelToValue(STATUS_TYPE, item.status);
      item.type = item.scheduleType;
      item.findTable = item.findTables;
      return item;
    });
  };

  // 获取发现表数据
  const getFindTable = async (id) => {
    const req = {
      pageNum: dialogInfo.searchInfo.queryParams.pageNum,
      pageSize: dialogInfo.searchInfo.queryParams.pageSize,
      id,
      workSpaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await findTable(req);
    dialogInfo.searchInfo.queryParams.total = res.total;
    if (res.code === 200) {
      dialogInfo.data = res.rows;
    }
  };

  // 运行采集
  const runCollections = async (scope) => {
    const req = {
      id: scope.row.id,
      workSpaceId: workspaceId.value,
      //   tenantId: tenantId.value,
    };
    const res = await runCollection(req);
    if (res.code === 200) {
      ElMessage({
        type: 'success',
        message: '运行成功',
      });
      tableSearch();
      //   if (dataType.value === '1') {
      //   getGatherDetail();
      //   } else {
      //     getGatherRecordList();
      //   }
    } else {
      ElMessage({
        type: 'error',
        message: '运行失败' + res.msg ? '-' + res.msg : '',
      });
    }
  };
  // 停止采集
  const stopCollections = async (id) => {
    ElMessageBox({
      title: '操作确认',
      message: h('p', null, [
        h('p', null, '确认停止该采集？'),
        h('span', { style: 'color: teal' }, '停止该采集后本次已采集内容将失效'),
      ]),
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const res = await stopCollection(id);
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '停止成功',
          });
          tableSearch();
        } else {
          ElMessage({
            type: 'error',
            message: '操作失败' + res.msg ? '-' + res.msg : '',
          });
        }
      })
      .catch(() => {});
  };

  // 获取日志列表
  const getLogList = async (row) => {
    // const res = await proxy.$http.get('/api/rule/list', { params });
    const req = {
      id: row.id,
      workSpaceId: workspaceId.value,
    };
    getMetadataLog(req).then((res) => {
      let logInfoContent = '';
      if (!res.code) {
        res.forEach((item) => {
          logInfoContent += item + '\n';
        });
        dialogInfo.logContent = logInfoContent;
      }
    });
  };

  // 跳转数据资产表详情
  // 路由跳转配置
  const router = useRouter();
  const toAssets = (row) => {
    const assetData = {
      catalog: row.dbName,
      datasourceId: row.datasourceId,
      schema: row.schemaName,
      tableName: row.tableName,
      workspaceId: row.workspaceId,
      id: row.assetId,
      activeName: '',
    };
    storeForRoute.setRouteData(assetData);
    router.push({
      name: 'DataAssets',
    });
  };

  watch(workspaceId, (val) => {
    pageType.value = 'list';
    dataType.value = '1';
    init();
  });

  const init = async () => {
    for (let num = 1; num <= 31; num++) {
      dialogInfo.planFromInfo.dateInfo.push({
        label: num > 9 ? num : '0' + num,
        value: num,
      });
    }
    for (let num = 0; num <= 59; num++) {
      dialogInfo.planFromInfo.timeInfo.push({
        label: num > 9 ? num : '0' + num,
        value: num,
      });
    }
    databaseInfo.data = JSON.parse(JSON.stringify(cardDatasModel.value));
    try {
      await getMetaDataList();
    } catch (error) {
      console.log(error);
    }
    // await getMetaDataList();
  };
  const dataForDataCollect = useRouteData();
  // 初始化
  onMounted(async () => {
    await init();
    if (dataForDataCollect.$state.routeData?.type) {
      pageType.value = 'detail';
      detailListener.showDetail({
        name: dataForDataCollect.$state.routeData.type,
        type: dataForDataCollect.$state.routeData.type,
      });
      dataType.value = '3'
      listeners.changeDataType('3')
    }
  });
  watch(pageType, (val) => {
    if (val == 'list') return dataForDataCollect.$reset();
  });
  // 需要关闭详情并清空跳转界面带来的参数
  onBeforeUnmount(() => {
    if (pageType.value == 'detail') {
      pageType.value = 'list';
      dataForDataCollect.$reset();
    }
  });
  return {
    dialogInfo,
    databaseInfo,
    detailInfo,
    tableListener,
    detailListener,
    dialogListener,
    listeners,
    dataType,
    formRules,
    pageType,
    choseType,
  };
}
