<template>
  <div class="app-container">
    <el-container>
      <el-aside class="asideStep">
        <el-scrollbar :min-size="1" :height="util.getPageSize().pageHeight - 120">
          <div effect="dark" style="background-color: white; margin-top: 0px; padding-top: 20px">
            <h3 style="margin-left: 20px; font-weight: 500">组件库</h3>
            <template v-for="(item, index) in oriFormList" :key="index">
              <h5 style="padding-left: 20px">{{ item.name }}</h5>
              <draggable
                v-model="item.formList"
                ghost-class="ghost"
                :force-fallback="true"
                item-key="index"
                :sort="false"
                class="leftItem"
                :animation="300"
                :group="{ name: 'dragFormList', pull: 'clone', put: false }"
                :clone="cloneFunc"
                @end="dragEnd"
                @start="drag = true"
              >
                <template #item="{ element }">
                  <div class="zj">
                    <el-button size="large" style="width: 100%">{{ element.name }} </el-button>
                  </div>
                </template>
              </draggable>
            </template>
          </div>
        </el-scrollbar>
      </el-aside>
      <el-main style="padding: 0px">
        <div style="display: flex; flex-direction: row">
          <div class="f11">
            <pc @set-current-form="setCurrentFormFunc"></pc>
          </div>
          <div v-if="currentForm" class="f22" :class="{ 'f22-edit': flowStore?.edit }">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  {{ currentForm?.typeName }}
                </div>
              </template>

              <el-form label-width="120px" label-position="top">
                <el-form-item label="表单ID">
                  <el-input v-model="currentForm.id" disabled maxlength="50">
                    <template #append>
                      <el-button :icon="DocumentCopy" @click="copyFormId(currentForm.id)" />
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item required label="标题">
                  <el-input v-model="currentForm.name" maxlength="10" />
                </el-form-item>

                <el-form-item label="提示" :required="currentForm.type === 'Description'">
                  <el-input v-model="currentForm.placeholder" maxlength="50" />
                </el-form-item>

                <component
                  :is="getFormConfigWidget(currentForm.type)"
                  :id="currentForm.id"
                  ref="currentFormConfigRef"
                ></component>
                <el-form-item label="是否必填">
                  <!-- <el-checkbox v-model="currentForm.required" label="必填" /> -->
                  <!-- 改为 switch -->
                  <el-switch v-model="currentForm.required" />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue';

  import { formValidateDict } from '../../utils/formValidate';

  import { DocumentCopy } from '@element-plus/icons-vue';
  import { useFlowStore } from '../../stores/flow';
  // 要注意导入
  import draggable from 'vuedraggable';
  import { formGroupConfig } from '../../api/form/data';
  import getFormConfigName from '../../utils/getFormConfigWidget';
  import * as util from '../../utils/objutil';

  import Pc from './components/design/pc.vue';
  const drag = ref(false);

  const currentFormConfigRef = ref();
  const flowStore = useFlowStore();

  const step2List = computed(() => {
    const step2 = flowStore.step2;
    return step2;
  });
  const step2FormList = computed(() => {
    if (!flowStore) {
      return undefined;
    }
    const step2 = flowStore.step2Form;
    return step2;
  });
  // 监听表单变化 渲染手机端和 pc 端
  watch(
    () => step2FormList?.value,
    (v) => {
      flowStore.setStep2Pc(v);
      const arr = [];
      for (const item of v) {
        arr.push(item);
      }
      flowStore.setStep2(arr);
    },
    { deep: true },
  );

  // 定义当前打开的表单
  const currentForm = ref();

  function setCurrentFormFunc(v) {
    currentForm.value = v;
  }

  const cloneFunc = (el) => {
    el.id = util.getRandomId();
    return el;
  };

  const getFormConfigWidget = (name) => {
    // 写的时候，组件的起名一定要与 dragList 中的 element 名字一模一样，不然会映射不上
    return getFormConfigName[name];
  };

  const dragEnd = (a) => {
    drag.value = false;
    oriFormList.value = util.deepCopy(oriFormList.value);
  };
  // 复制表单 idid
  function copyFormId(id) {
    util.copyToBoard(id);
  }

  const oriFormList = ref(formGroupConfig);

  const validate = (f) => {
    const err = [];

    const formList = step2List.value;
    if (formList.length === 0) {
      err.push('表单不能为空');
    }

    let cIndex = 0;
    const indexObj = {};
    for (const form of formList) {
      indexObj[form.id] = cIndex;
      cIndex++;
    }
    for (const form of formList) {
      const formValidateDictElement = formValidateDict[form.type];
      if (formValidateDictElement) {
        const result = formValidateDictElement(form);

        if (!result.valid) {
          err.push(result.msg);
        }
      }

      // 计算每个表单的顺序

      // 检查动态表单配置

      {
        let dynamicFormConfig = form.dynamicFormConfig;
        if (!dynamicFormConfig) {
          dynamicFormConfig = [];
        }
        for (const it of dynamicFormConfig) {
          const list = it.list;
          const conditionList = it?.condition?.conditionList;
          if (conditionList?.length > 0) {
            // 判断条件里的顺序
            for (const c of conditionList) {
              console.log(c);
              for (const t of c.conditionList) {
                const key = t.key;
                const indexObjElement = indexObj[key];
                if (indexObjElement) {
                  if (indexObjElement >= indexObj[form.id]) {
                    err.push(
                      '请检查表单 [' +
                        form.name +
                        '] 的动态表单条件中引用的条件顺序不能在当前表单之下',
                    );
                  }
                }
              }
            }

            // 有条件
            if (list?.length < 1) {
              err.push('请设置表单[' + form.name + ']的动态表单值');
            } else {
              for (const l of list) {
                if (util.isBlank(l.value) || util.isBlank(l.contentConfig)) {
                  err.push('请完善表单[' + form.name + ']的动态表单值');
                }
              }
              const arr = list.map((r) => r.contentConfig);
              if (util.distinct(arr).length !== arr.length) {
                err.push('表单 [' + form.name + '] 的动态表单不能重复配置');
              }
            }
          }
        }
      }
    }

    // 表单唯一名字集合
    const uniqueFormNameList = Array.from(new Set(formList.map((res) => res.name)));

    if (uniqueFormNameList.length < formList.length) {
      for (const formName of uniqueFormNameList) {
        const length = formList.filter((res) => res.name === formName).length;
        if (length > 1) {
          err.push('表单名称【' + formName + '】不能重复');
        }
      }
    }

    f(err.length === 0, err);
  };
  defineExpose({ validate });
</script>
<style scoped lang="less">
  .leftItem {
    padding-left: 0px;
  }

  .zj {
    display: inline-block;
    width: 120px;
    margin: 5px 10px 5px 10px;
  }

  @f22_width: 500px;

  @center_width: 360px;

  .f11 {
    width: calc(100% - @f22_width);
    margin: 10px;

    background-color: white;
    border-radius: 5px;
    padding: 10px;
    // outline: 1px solid #c9c9c9;
    box-shadow: 0 0 10px #c9c9c9;
  }

  .f22 {
    width: @f22_width;

    margin: 10px;
    // background-color: white;
    border-radius: 5px;
    // padding: 10px;
    // outline: 1px solid #c9c9c9;
    box-shadow: 0 0 10px #c9c9c9;
  }
  .f22-edit {
    // pointer-events: none;
    // cursor: not-allowed;
  }
  .asideStep {
    background-color: white;
    border-radius: 5px;
    padding: 10px;
    // outline: 1px solid #c9c9c9;
    box-shadow: 0 0 10px #c9c9c9;
    margin: 10px;
  }
  .app-container {
    min-height: 100%;
    // width: 100%;
    // overflow: auto;
    padding: 1px;
    background: #ffffff;
    border-radius: 4px;
    // margin-top: 20px;
  }
</style>
