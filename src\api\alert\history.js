import request from '@/utils/request';

// 查询告警历史列表
export function listHISTORY(params) {
  return request({
    url: '/alert/history/list',
    method: 'get',
    params,
  });
}

// 查询告警历史详细
// export function getHISTORY(ID) {
//   return request({
//     url: '/alert/history/' + ID,
//     method: 'get'
//   })
// }

// 新增告警历史
export function addHISTORY(data) {
  return request({
    url: '/alert/history',
    method: 'post',
    data,
  });
}

// 修改告警历史
export function updateHISTORY(data) {
  return request({
    url: '/alert/history',
    method: 'put',
    data,
  });
}

// 删除告警历史
export function delHISTORY(ID) {
  return request({
    url: '/alert/history/' + ID,
    method: 'delete',
  });
}
