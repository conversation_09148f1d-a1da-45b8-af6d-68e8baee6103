<template>
  <el-form ref="seatunnelRef" :model="form" :rules="rules" label-width="100px">
    <!-- <!~~ 应用名称~~> -->
    <!-- <el-form-item label="应用名称" prop="operationModel" > -->
    <!-- <el-input v-model="form.operationModel" placeholder="请输入" /> -->
    <!-- </el-form-item> -->
    <!-- 运行模式 -->
    <el-form-item label="运行模式" prop="parallelism">
      <!-- <el-input v-model="form.parallelism" placeholder="请输入" /> -->
      <!-- 下拉框-->
      <el-select
        v-model="form.parallelism"
        placeholder="请选择"
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option
          v-for="dict in parallelismList"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>
    <!-- 部署模式 -->
    <el-form-item v-if="false" label="部署模式" prop="">
      <!-- <el-input v-model="form.deployMode" placeholder="请输入" /> -->
      <!-- 下拉框-->
      <el-select
        v-model="form.deployMode"
        placeholder="请选择"
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option
          v-for="dict in deployModeList"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>
    <advanced-options
      ref="advancedOptionsRef"
      v-model:form="formData"
      v-model:is-show="isShow"
      :NodeData="filteredNodeData"
      :CanvasActions="CanvasActions"
    />
    <!-- 任务执行内存 -->
    <el-form-item v-show="isShow" v-if="form.parallelism == 'local'" prop="typeName">
      <template #label>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="form.taskMemoryTooltip"
          placement="top-start"
        >
          <el-icon>
            <QuestionFilled />
          </el-icon>
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="form.taskMemoryLabel"
          placement="top-start"
          :disabled="form.taskMemoryLabel?.length <= 5"
        >
          <span>
            {{
              form.taskMemoryLabel?.length > 5
                ? form.taskMemoryLabel?.slice(0, 4) + '...'
                : form.taskMemoryLabel
            }}
          </span>
        </el-tooltip>
      </template>
      <el-input
        v-model="form.taskMemory"
        :min="1"
        :max="1000"
        :step="1"
        :disabled="!CanvasActions"
        type="number"
        @change="handleInput('taskMemory')"
      >
        <template #suffix>
          <span class="input-unit">{{ form.taskMemoryUnit }}</span>
        </template>
      </el-input>
    </el-form-item>

    <!-- 配置 -->
    <el-form-item label="配置" prop="config">
      <el-button
        type="text"
        :disabled="!CanvasActions"
        style="margin-bottom: 10px"
        @click="visualConfig"
        >可视化配置</el-button
      >
      <el-input
        v-model="form.config"
        type="textarea"
        placeholder="Please input"
        :disabled="!CanvasActions"
        :rows="4"
      />
    </el-form-item>

    <el-form-item label="参数设置">
      <el-button :disabled="!props.CanvasActions" type="light" icon="Plus" @click="addSyncChange"
        >自定义</el-button
      >
      <el-button :disabled="!props.CanvasActions" type="light" icon="Plus" @click="addVariable"
        >全局变量</el-button
      >
    </el-form-item>

    <div v-if="syncChangeList.length">
      <el-form-item>
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content>
                自定义参数支持时间变量，以下为时间变量格式：<br />
                注意：N代表数字,昨天就是把-N 换成-1：<br />
                后 1 年：$[add_months(yyyyMMdd,12*1)]<br />
                后 N 年：$[add_months(yyyyMMdd,12*N)]<br />
                前 N 年：$[add_months(yyyyMMdd,-12*N)]<br />
                后 N 月：$[add_months(yyyyMMdd,N)]<br />
                前 N 月：$[add_months(yyyyMMdd,-N)]<br />
                本月第一天： $[month_begin(yyyyMMdd,0)]<br />
                本月第三天： $[month_begin(yyyyMMdd,+3)]<br />
                本月最后一天：$[month_end(yyyyMMdd,0)]<br />
                本周一：$[week_begin(yyyyMMdd,0)]<br />
                本周日：$[week_end(yyyyMMdd,0)]<br />
                后 N 周：$[yyyyMMdd+7*N]<br />
                前 N 周：$[yyyyMMdd-7*N]<br />
                后 N 天：$[yyyyMMdd+N]<br />
                前 N 天：$[yyyyMMdd-N]<br />
                后 N 小时：$[HHmmss+N/24] 或者$[yyyyMMdd] $[HHmmss+N/24]<br />
                前 N 小时：$[HHmmss-N/24] 或者 $[yyyyMMdd] $[HHmmss-N/24]<br />
                后 N 分钟：$[HHmmss+N/24/60] 或者 $[yyyyMMdd] $[HHmmss+N/24/60]<br />
                前 N 分钟：$[HHmmss-N/24/60] 或者 $[yyyyMMdd] $[HHmmss-N/24/60]<br />
                当天：$[yyyy-MM-dd]或者$[yyyy-MM-dd HH:mm:ss]或者$[yyyy-MM-dd
                HH:mms:s.SSS]或者iso格式的：$[yyyy-MM-dd]T$[HH:mm:ss]Z
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">自定义</span>
          </span>
        </template>
      </el-form-item>
      <div v-for="(syncChange, index) in syncChangeList" :key="index" style="margin-left: 60px">
        <el-form ref="syncChangeForm" :model="syncChange" class="container">
          <div class="item">
            <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
              <el-input
                v-model="syncChange.prop"
                placeholder="prop"
                :disabled="!CanvasActions"
              ></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.direct" :disabled="!CanvasActions">
                <el-option
                  v-for="data in directList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.type" :disabled="!CanvasActions">
                <el-option
                  v-for="data in customerIdList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-input v-model="syncChange.value" :disabled="!CanvasActions"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-button
              style="background: none"
              :disabled="!props.CanvasActions"
              type="light"
              icon="Delete"
              @click="deleteSyncChange(index)"
            ></el-button>
          </div>
        </el-form>
      </div>
    </div>

    <div v-if="sectionList.length">
      <el-form-item label="变量"></el-form-item>
      <div v-for="(data, index) in sectionList" :key="index" class="sectionList-box">
        <div style="width: 300px">
          <el-select
            v-model="data.variableValue"
            style="width: 100%"
            :disabled="!props.CanvasActions"
            value-key="id"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="list in variableList"
              :key="list.id"
              :label="`${list.name}/${list.code}`"
              :value="list"
            ></el-option>
          </el-select>
        </div>
        <div style="margin-left: 10px">
          <el-button
            style="background: none"
            :disabled="!props.CanvasActions"
            type="light"
            icon="Delete"
            @click="delSectionList(index)"
          ></el-button>
        </div>
      </div>
    </div>
  </el-form>
  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog v-model="dialogVisible" title="可视化配置" width="80%" append-to-body>
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>可视化配置</span>
      </div>
      <el-divider></el-divider>
    </template>

    <div style="margin-top: -60px; text-align: right; padding-right: 25px">
      <!-- <el-button @click="footer">格式化</el-button> -->
      <el-button @click="clear">清空</el-button>
    </div>

    <div style="margin-top: 20px">
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 500px" />
    </div>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getAllListForWorkFlow } from '@/api/datamodel';
  // import GlobalVariable from '../variableForGlobal';
  import advancedOptions from '../components/advancedOptions/index.vue';

  import Codemirror from '@/components/Codemirror'; // 编辑器
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const data = reactive({
    form: {
      parallelism: '',
      deployMode: '',
      taskMemory: '',
      jobMemory: '',
      config: '',
      customProperties: '',
      taskSlots: '',
    },
    rules: {
      parallelism: [{ required: true, message: '请选择运行模式', trigger: 'blur' }],
      deployMode: [{ required: true, message: '请选择部署模式', trigger: 'blur' }],
      taskMemory: [{ required: true, message: '请输入任务执行内存', trigger: 'blur' }],
      jobMemory: [{ required: true, message: '请输入作业执行内存', trigger: 'blur' }],
      config: [{ required: true, message: '请输入配置', trigger: 'blur' }],
      customProperties: [{ required: true, message: '请输入自定义参数', trigger: 'blur' }],
      taskSlots: [{ required: true, message: '请输入算子数', trigger: 'blur' }],
    },
  });

  const { form, rules } = toRefs(data);
  const dialogVisible = ref(false);
  const parallelismList = ref([]);
  const deployModeList = ref([]);
  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const syncChangeList = ref([]);
  const customerIdList = ref([
    // type:VARCHAR,INTEGER,LONG,FLOAT,DOUBLE,DATE,TIME,TIMESTAMP,BOOLEAN,LIST
    { value: 'VARCHAR', label: 'VARCHAR' },
    { value: 'INTEGER', label: 'INTEGER' },
    { value: 'LONG', label: 'LONG' },
    { value: 'FLOAT', label: 'FLOAT' },
    { value: 'DOUBLE', label: 'DOUBLE' },
    { value: 'DATE', label: 'DATE' },
    { value: 'TIME', label: 'TIME' },
    { value: 'TIMESTAMP', label: 'TIMESTAMP' },
    { value: 'BOOLEAN', label: 'BOOLEAN' },
    { value: 'LIST', label: 'LIST' },
  ]);

  const directList = ref([
    { value: 'IN', label: 'IN' },
    { value: 'OUT', label: 'OUT' },
    // { value: 'INOUT', label: 'INOUT' },
  ]);
  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }
  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      direct: '',
      type: '',
      value: '',
    };
    // 生成唯一的 key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  const submitDrawer = async () => {
    const res = await proxy.$refs.seatunnelRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    Object.entries(formData.value).forEach(([key, value]) => {
      const property = NodeData.value.inputProperties.find((item) => item.name === key);
      if (property) {
        property.value = value;
      }
    });
    await emit('submitDrawer', NodeData.value);
  };
  const formData = ref({}); // 表单数据
  const isShow = ref(false);
  const filteredNodeData = computed(() => {
    const targetDisplayNames = ['失败重试次数', '失败重试间隔', '延时执行时间'];
    const inputProperties = NodeData.value.inputProperties || [];
    const filteredProperties = inputProperties.filter((item) =>
      targetDisplayNames.includes(item.displayName),
    );
    return { ...NodeData.value, inputProperties: filteredProperties };
  });

  const taskMemoryProp = NodeData.value?.inputProperties.find(
    (res) => res.name === 'taskExecutorMemory',
  );

  form.value.taskMemoryTooltip = taskMemoryProp.description;
  form.value.taskMemoryLabel = taskMemoryProp.displayName;
  form.value.taskMemoryUnit = taskMemoryProp.unit;
  form.value.taskMemoryPlaceholder = taskMemoryProp.defaultValue;

  const properties = [
    'parallelism',
    'deployMode',
    'taskMemory',
    'jobMemory',
    'config',
    'customProperties',
    'taskSlots',
  ];

  const DataProcessing = async () => {
    properties.forEach((property, index) => {
      NodeData.value.inputProperties[index + 1].value = form.value[property];
    });
    NodeData.value.inputProperties[0].value = NodeData.value.nodeName;
    NodeData.value.inputProperties[6].value = JSON.stringify(syncChangeList.value);
    const data = sectionList.value.map((res) => res.variableValue);
    NodeData.value.inputProperties.forEach((res) => {
      if (res.displayName == '全局变量') {
        res.value = JSON.stringify(data);
      }
    });
  };
  // 声明提示
  const taskMemoryPrompt = ref('');
  const jobMemoryPrompt = ref('');
  const taskSlotsPrompt = ref('');
  const defaultPrompt = '一段提示';

  const taskMemoryPropName = NodeData.value?.inputProperties.find(
    (res) => res.name === 'taskExecutorMemory',
  );
  const jobMemoryPropName = NodeData.value?.inputProperties.find(
    (res) => res.name === '-Djobmanager.memory.process.size',
  );
  const taskSlotsPropName = NodeData.value?.inputProperties.find(
    (res) => res.name === '-Dtaskmanager.numberOfTaskSlots',
  );

  taskMemoryPrompt.value = taskMemoryPropName.description
    ? taskMemoryPropName.description
    : defaultPrompt;
  jobMemoryPrompt.value = jobMemoryPropName.description
    ? jobMemoryPropName.description
    : defaultPrompt;
  taskSlotsPrompt.value = taskSlotsPropName.description
    ? taskSlotsPropName.description
    : defaultPrompt;

  const sectionList = ref([]);
  const addVariable = () => {
    if (sectionList.value == null) {
      sectionList.value = new Array();
    }
    const obj = {
      variableValue: null,
    };
    sectionList.value.push(obj);
  };
  const delSectionList = (index) => {
    sectionList.value.splice(index, 1);
  };
  const variableList = ref([]);
  const init = async () => {
    sectionList.value = [];
    syncChangeList.value = [];
    parallelismList.value = JSON.parse(NodeData.value.inputProperties[1].viewValueOptions);
    deployModeList.value = JSON.parse(NodeData.value.inputProperties[2].viewValueOptions);

    properties.forEach((property, index) => {
      rules.value[property][0].required = NodeData.value.inputProperties[index + 1].required;
    });

    // 回显
    properties.forEach((property, index) => {
      form.value[property] =
        NodeData.value.inputProperties[index + 1].value ||
        NodeData.value.inputProperties[index + 1].defaultValue;
    });
    if (NodeData.value.inputProperties[6].value) {
      syncChangeList.value = JSON.parse(NodeData.value.inputProperties[6].value);
    }
    try {
      const res = await getAllListForWorkFlow({ workspaceId: props.workspaceId });
      variableList.value = res.data;
      const global = NodeData.value.inputProperties.filter(
        (res) => res.displayName == '全局变量',
      )[0].value;
      console.log(global);
      if (global) {
        sectionList.value = JSON.parse(global).map((res) => {
          return { variableValue: res };
        });
      }
    } catch {}
    proxy.$refs.seatunnelRef.clearValidate();
  };

  onMounted(() => {
    init();
  });

  watch(NodeData, () => {
    init();
  });

  const parentData = ref('');
  const visualConfig = () => {
    dialogVisible.value = true;
  };
  const cancel = () => {
    dialogVisible.value = false;
  };
  const clear = () => {
    return proxy.$modal
      .confirm('是否确定清空数据项？')
      .then(() => {
        form.value.config = '';
      })
      .catch(() => {});
  };

  const confirm = () => {
    form.value.config = parentData.value;
    cancel();
  };

  watch(
    () => form.value.config,
    (val) => {
      parentData.value = val;
    },
  );
  const handleInput = (field) => {
    const value = form.value[field];
    if (value > 1000) {
      form.value[field] = 1000;
    } else if (value < 1) {
      form.value[field] = 1;
    }
    console.log('Updated value:', form.value[field]);
  };
  // watch(parentData, (val) => {
  //     if (val === '') {
  //         parentData.value = form.value.config
  //     }
  // })
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(4, 1fr);
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }
  .dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
    font-weight: bold;
    padding-bottom: 15px;
    margin-left: 6%;
  }
  .sectionList-box {
    display: flex;
    margin: 10px 60px;
  }
</style>
