import request from '@/utils/request';

//! ------------- SQL查询对应接口
// 获取分组列表
export function getTree(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScriptGroup/getTree',
    method: 'get',
    params: query,
  });
}

// 添加分组列表
export function addTree(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScriptGroup/add',
    method: 'post',
    data: query,
  });
}

// 编辑分组
/// xugurtp-ad-hoc-query/adHocScriptGroup/update
export function updateTree(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScriptGroup/update',
    method: 'post',
    data: query,
  });
}

// 删除分组
export function deleteTree(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScriptGroup/delete',
    method: 'DELETE',
    params: query,
  });
}
// 删除script
export function deleteScript(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScript/delete',
    method: 'DELETE',
    params: query,
  });
}
// 运行脚本
export function sqlRunning(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScript/running',
    method: 'post',
    data: query,
  });
}
// 新增脚本
export function addScript(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScript/add',
    method: 'post',
    data: query,
  });
}
// 编辑脚本
export function updateScript(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScript/update',
    method: 'post',
    data: query,
  });
}
// 获取分组列表
export function getInfo(query) {
  return request({
    url: '/xugurtp-ad-hoc-query/adHocScript/getInfo',
    method: 'get',
    params: query,
  });
}
