<!-- SplitPanes.vue -->
<template>
    <div class="app-container">
        <splitpanes class="default-theme" :horizontal="horizontal" :push-other-panes="pushOtherPanes">
            <pane :min-size="leftMinSize" :max-size="leftMaxSize" :size="leftSize" class="left">
                <slot name="left"/>
            </pane>
            <pane class="right">
                <slot name="right"/>
            </pane>
        </splitpanes>
    </div>
</template>

<script setup>
import { Pane, Splitpanes } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';

defineProps({
    horizontal: {
        type: <PERSON>olean,
        default: false
    },
    pushOtherPanes: {
        type: Boolean,
        default: true
    },
    leftMinSize: {
        type: Number,
        default: 20
    },
    leftMaxSize: {
        type: Number,
        default: 30
    },
    leftSize: {
        type: Number,
        default: 20
    },
    rightMinSize: {
        type: Number,
        default: 10
    },
    rightMaxSize: {
        type: Number,
        default: 90
    },
    rightSize: {
        type: Number,
        default: 50
    }
});
</script>

<style lang="scss" scoped>
:deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
}

:deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
}

.left,
.right {
    padding: 0px 10px 0px 10px;
}
</style>
