<template>
    <div class="main-content-container">
        <!-- 默认提示 -->
        <div v-if="contentType === 'default'" class="content-default">
            <div class="empty-placeholder">
                <el-icon class="empty-icon">
                    <Document />
                </el-icon>
                <p>请选择左侧目录查看内容</p>
            </div>
        </div>

        <!-- 普通目录内容 -->
        <div v-else-if="contentType === 'folder'" class="content-folder">
            <masterModelManage />
        </div>
        
        <!-- 视图管理 -->
        <div v-else-if="contentType === 'viewManage'" class="content-view-manage">
            <viewManage />
        </div>
        
        <!-- 表单设计 -->
        <div v-else-if="contentType === 'formDesign'" class="content-form-design">
            <formDesign />
        </div>
        
        <!-- 流程审批 -->
        <div v-else-if="contentType === 'approvalProcess'" class="content-approval-process">
            <approvalProcess />
        </div>
    </div>
</template>

<script setup>
import { Document } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'
import masterModelManage from './module/masterModelManage/index.vue'
import viewManage from './module/viewManage/index.vue'
import formDesign from './module/formDesign/index.vue'
import approvalProcess from './module/approvalProcess/index.vue'

/**
 * 响应式数据
 */
const contentType = ref('default') // 默认、folder、viewManage、formDesign、approvalProcess
const currentNode = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

/**
 * 模拟数据
 */
const folderItems = ref([])


/**
 * 根据节点数据更新内容类型
 * @param {Object} data - 节点数据
 */
const updateContentType = (data) => {
    if (!data) {
        contentType.value = 'default'
        currentNode.value = null
        return
    }

    currentNode.value = data

    // 根据节点标签或其他属性判断内容类型
    switch (data.label) {
        case '视图管理':
            contentType.value = 'viewManage'
            break
        case '表单设计':
            contentType.value = 'formDesign'
            break
        case '流程审批':
            contentType.value = 'approvalProcess'
            break
        default:
            contentType.value = 'folder'
            loadFolderData(data) // 加载文件夹数据
            break
    }

    console.log('内容类型已更新:', contentType.value)
}

/**
 * 加载文件夹数据
 * @param {Object} folderData - 文件夹数据
 */
const loadFolderData = (folderData) => {
    // 模拟异步加载数据
    setTimeout(() => {
        // 根据文件夹ID或其他标识加载对应数据
        if (folderData.count > 0) {
            folderItems.value = Array(folderData.count).fill().map((_, index) => ({
                name: `${folderData.label}数据${index + 1}`,
                type: '数据项',
                updateTime: '2023-01-15 14:30:00',
                creator: '管理员'
            }))
        } else {
            folderItems.value = []
        }

        total.value = folderItems.value.length
    }, 300)
}

/**
 * 处理编辑
 * @param {Object} row - 行数据
 */
const handleEdit = (row) => {
    ElMessage.success(`编辑: ${row.name}`)
}

/**
 * 处理删除
 * @param {Object} row - 行数据
 */
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除"${row.name}"吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        ElMessage.success(`删除: ${row.name}`)
        // 实际应用中这里应该调用删除API
    }).catch(() => {
        // 用户取消删除
    })
}

/**
 * 处理每页显示数量变化
 * @param {Number} val - 新的每页显示数量
 */
const handleSizeChange = (val) => {
    pageSize.value = val
    console.log('每页显示数量:', val)
}

/**
 * 处理当前页变化
 * @param {Number} val - 新的当前页
 */
const handleCurrentChange = (val) => {
    currentPage.value = val
    console.log('当前页:', val)
}

// 暴露方法给父组件调用
defineExpose({
    updateContentType
})
</script>

<style lang="scss" scoped>
.main-content-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 16px;

    .content-default {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .empty-placeholder {
            text-align: center;
            color: #909399;

            .empty-icon {
                font-size: 48px;
                margin-bottom: 16px;
            }

            p {
                font-size: 16px;
            }
        }
    }

    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }
    }

    .content-body {
        flex: 1;
        overflow: auto;
    }

    .content-tabs {
        flex: 1;

        .tab-content {
            padding: 16px 0;
        }
    }

    .pagination-container {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>