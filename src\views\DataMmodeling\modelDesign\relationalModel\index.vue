<template>
  <div v-if="divShow">
    <SplitPanes>
      <template #left>
        <themeLeft
          :data-tree="dataTree"
          :tree-props="props"
          @node-click="handleNodeClick"
          @filter-change="onChange"
        />
      </template>
      <template #right>
        <section class="right-box">
          <el-empty v-if="!dataNode" description="请选择" style="height: 100%"></el-empty>
          <el-tabs v-if="dataNode" v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="ODS 层" name="first">
              <div class="pm">
                <el-row>
                  <el-col :span="8">
                    <el-button icon="Plus" type="primary" @click="jumpTo">新增</el-button>
                  </el-col>

                  <el-col :span="16">
                    <div class="operationType">
                      <!-- <el-row> -->
                      <el-input
                        v-model="input3"
                        placeholder="请输入名称"
                        class="input-with-select"
                        size="mini"
                      >
                        <template #prepend>
                          <el-select
                            v-model="selectName"
                            placeholder="Select"
                            style="width: 115px"
                            size="mini"
                          >
                            <el-option
                              v-for="dict in model_search_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </template>
                      </el-input>
                      <!-- </el-row> -->

                      <!-- <el-row style="width: 200px"> -->
                      <el-date-picker
                        v-model="time"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[
                          new Date(2000, 1, 1, 0, 0, 0),
                          new Date(2000, 1, 1, 23, 59, 59),
                        ]"
                        :disabled-date="disablesDate"
                      ></el-date-picker>
                      <!-- </el-row> -->

                      <!-- <el-row> -->
                      <right-toolbar
                        :search="false"
                        :columns="columnsODS"
                        @query-table="reload(dataNode?.id, 'ODS')"
                      ></right-toolbar>
                      <el-button
                        circle
                        icon="Search"
                        @click="getDataModelLogicListUtil(dataNode?.id, 'ODS')"
                      ></el-button>
                      <!-- </el-row> -->
                    </div>
                  </el-col>
                </el-row>
              </div>

              <el-table
                ref="tableRef"
                row-key="date"
                :data="ODStableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  prop="code"
                  label="贴源表名称"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsODS[0].visible"
                  prop="name"
                  label="贴源表注释"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsODS[1].visible"
                  prop="createBy"
                  label="创建人"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsODS[2].visible"
                  prop="catalogName"
                  label="所属主题"
                  width="200 "
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columnsODS[3].visible"
                  prop="remark"
                  label="描述"
                  width="200"
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columnsODS[4].visible"
                  prop="status"
                  label="状态"
                  width="200"
                  :filters="[
                    { text: '草稿', value: '2' },
                    { text: '上线', value: '1' },
                    { text: '下线', value: '0' },
                  ]"
                  :filter-method="filterTag"
                  filter-placement="bottom-end"
                >
                  <template #default="scope">
                    <el-tag
                      :type="filterTagType(scope.row.status)"
                      :disable-transitions="true"
                      round
                      effect="plain"
                    >
                      {{ filterTagTypeText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="columnsODS[5].visible"
                  prop="updateTime"
                  label="更新时间"
                  sortable
                  width="200"
                />

                <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="revamp(scope)">
                      {{ scope.row.status === 2 ? '编辑' : '查看' }}
                    </el-button>
                    <el-button
                      v-if="scope.row.status == 0 || scope.row.status == 2"
                      type="text"
                      @click="updateStatusUtil(scope.row, 1)"
                    >
                      发布
                    </el-button>
                    <el-button
                      v-if="scope.row.status === 1"
                      type="text"
                      :disabled="scope.row.status === 1"
                      @click="updateStatusUtil(scope.row, 0)"
                    >
                      下线
                    </el-button>
                    <el-button
                      type="text"
                      :disabled="scope.row.status === 1"
                      @click="remove(scope.row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div style="margin-bottom: 20px">
                <!-- 分页 -->
                <pagination
                  v-show="total > 0"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  :pager-count="maxCount"
                  :total="total"
                  @pagination="listPage('ODS')"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane label="DWI 层" name="second">
              <div class="pm">
                <el-row>
                  <el-col :span="8">
                    <el-button icon="Plus" type="primary" @click="jumpTo">新增</el-button>
                  </el-col>

                  <el-col :span="16">
                    <div class="operationType">
                      <!-- <el-row> -->
                      <el-input
                        v-model="input3"
                        placeholder="请输入名称"
                        class="input-with-select"
                        size="mini"
                      >
                        <template #prepend>
                          <el-select
                            v-model="selectName"
                            placeholder="Select"
                            style="width: 115px"
                            size="mini"
                          >
                            <el-option
                              v-for="dict in model_search_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </template>
                      </el-input>
                      <!-- </el-row> -->

                      <!-- <el-row style="width: 200px"> -->
                      <el-date-picker
                        v-model="time"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[
                          new Date(2000, 1, 1, 0, 0, 0),
                          new Date(2000, 1, 1, 23, 59, 59),
                        ]"
                        :disabled-date="disablesDate"
                      ></el-date-picker>
                      <!-- </el-row> -->

                      <!-- <el-row> -->
                      <right-toolbar
                        :search="false"
                        :columns="columnsDWI"
                        @query-table="reload(dataNode?.id, 'DWI')"
                      ></right-toolbar>
                      <el-button
                        circle
                        icon="Search"
                        @click="getDataModelLogicListUtil(dataNode?.id, 'DWI')"
                      ></el-button>
                      <!-- </el-row> -->
                    </div>
                  </el-col>
                </el-row>
              </div>

              <el-table
                ref="tableRef"
                row-key="date"
                :data="ODStableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  prop="code"
                  label="整合表名称"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsDWI[0].visible"
                  prop="name"
                  label="整合表注释"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsDWI[1].visible"
                  prop="createBy"
                  label="创建人"
                  width="200 "
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columnsDWI[2].visible"
                  prop="catalogName"
                  label="所属主题"
                  width="200 "
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columnsDWI[3].visible"
                  prop="remark"
                  label="描述"
                  width="200"
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columnsDWI[4].visible"
                  prop="status"
                  label="状态"
                  width="200"
                  :filters="[
                    { text: '草稿', value: '2' },
                    { text: '上线', value: '1' },
                  ]"
                  :filter-method="filterTag"
                  filter-placement="bottom-end"
                >
                  <template #default="scope">
                    <el-tag
                      :type="filterTagType(scope.row.status)"
                      :disable-transitions="true"
                      round
                      effect="plain"
                    >
                      {{ filterTagTypeText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="columnsDWI[5].visible"
                  prop="updateTime"
                  label="更新时间"
                  sortable
                  width="140"
                />

                <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                  <template #default="scope">
                    <el-button type="text" @click="revamp(scope)">
                      {{ scope.row.status === 2 ? '编辑' : '查看' }}
                    </el-button>

                    <el-button
                      v-if="scope.row.status == 0 || scope.row.status == 2"
                      type="text"
                      @click="updateStatusUtil(scope.row, 1)"
                    >
                      发布
                    </el-button>
                    <el-button
                      v-if="scope.row.status === 1"
                      type="text"
                      :disabled="scope.row.status === 1"
                      @click="updateStatusUtil(scope.row, 0)"
                    >
                      下线
                    </el-button>
                    <el-button
                      type="text"
                      :disabled="scope.row.status === 1"
                      @click="remove(scope.row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div style="margin-bottom: 20px">
                <!-- 分页 -->
                <pagination
                  v-show="total > 0"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  :pager-count="maxCount"
                  :total="total"
                  @pagination="listPage('DWI')"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </section>
      </template>
    </SplitPanes>
  </div>
  <DWI
    v-if="DWIshow"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :data-node="dataNode"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  >
  </DWI>
  <ODS
    v-if="ODSshow"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :data-node="dataNode"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  />
</template>

<script setup>
  import {
    deleteDataModelLogic,
    getCatalogTree,
    getDataModelLogicDetail,
    getDataModelLogicList,
    updateDataModelLogic,
    warehouseType,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import SplitPanes from '@/components/SplitPanes/index';
  import themeLeft from '@/views/DataMmodeling/components/themeLeft/index.vue';

  import { nextTick } from 'vue';
  import DWI from '../relationalModel/module/DWI/index';
  import ODS from '../relationalModel/module/ODS/index';

  const expandedList = ref([]);

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const { model_search_type } = proxy.useDict('model_search_type');
  const maxCount = ref(5);
  const total = ref(0);

  const columnsODS = ref([
    { key: 0, label: `表注释`, visible: true },
    { key: 1, label: `创建人`, visible: true },
    { key: 2, label: `所属主题`, visible: true },
    { key: 3, label: `描述`, visible: true },
    { key: 4, label: `状态`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
  ]);
  const columnsDWI = ref([
    { key: 0, label: `表注释`, visible: true },
    { key: 1, label: `创建人`, visible: true },
    { key: 2, label: `所属主题`, visible: true },
    { key: 3, label: `描述`, visible: true },
    { key: 4, label: `状态`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
  ]);
  const listPage = async (level) => {
    console.log(level);
    await getDataModelLogicListUtil(nodeClick.value.data.id, level);
  };

  const updateStatusUtil = async (row, status) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下操作');
      return;
    }
    const query = {
      id: row.id,
      status,
    };
    const res = await updateDataModelLogic(query);
    // 根据 activeName 判断是哪个层级
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      if (activeName.value === 'first') {
        await getDataModelLogicListUtil(dataNode.value.id, 'ODS');
      } else if (activeName.value === 'second') {
        await getDataModelLogicListUtil(dataNode.value.id, 'DWI');
      }
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };
  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '0',
      pid: '',
      database: '',
      datasource: '',
      // 状态
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入描述', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { queryParams } = toRefs(data);

  const tableRef = ref();

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  // 使用计算属性 判断类型 如果是 online 就是 success 如果是下线就是 danger 如果是草稿 是 ' '
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };

  const filterText = ref();
  const onChange = (value) => {
    getCatalogTreeUtil(value);
  };
  const activeName = ref('first');

  const handleClick = (tab) => {
    input3.value = '';
    time.value = '';
    if (tab.props.name == 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else if (tab.props.name == 'second') {
      getDataModelLogicListUtil(dataNode.value.id, 'DWI');
    }
  };
  const treeData = ref();

  const dataTree = ref();
  const dataNode = ref();
  const nodeClick = ref();
  const handleNodeClick = async ({ e, data, node }) => {
    console.log(data);
    dataNode.value = data;
    nodeClick.value = node;

    if (activeName.value === 'first') {
      await getDataModelLogicListUtil(data.id, 'ODS');
    } else {
      await getDataModelLogicListUtil(data.id, 'DWI');
    }
  };

  const getCatalogTreeUtil = async (value) => {
    ODStableData.value = [];
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      searchName: value,
    };
    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  const ODStableData = ref();
  const time = ref();
  const selectName = ref();
  const reload = async (data, level) => {
    input3.value = '';
    time.value = '';
    await getDataModelLogicListUtil(data, level);
  };

  const getDataModelLogicListUtil = async (data, level) => {
    const query = {
      workspaceId: workspaceId.value,
      thmeId: data,
      // type: '0',
      level,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
      [selectName.value]: input3?.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    const res = await getDataModelLogicList(query);
    if (res.code === 200) {
      ODStableData.value = res.rows;
      // maxCount.value = res.total
      total.value = res.total;
    }
  };
  const DWIshow = ref(false);
  const ODSshow = ref(false);
  //
  // 使用计算属性 判断 divShow 是否显示   !ODSshow 或者 !DWIshow
  const divShow = computed(() => {
    return !ODSshow.value && !DWIshow.value;
  });

  watch(divShow, (val) => {
    console.log(val);
    if (val) {
      console.log(val);
      nextTick(() => {
        console.log(nodeClick.value);
        proxy.$refs.treeRef.setCurrentKey(dataNode.value?.id);
        proxy.$refs.treeRef.expandNode(nodeClick?.value?.parent);
        proxy.$refs.treeRef.expandNode(nodeClick?.value?.parent?.parent);
      });
    }
  });

  // 获取当前数据库类型
  const getDatabaseType = async () => {
    let res = '';
    // 这里是异步获取数据库类型的逻辑
    res = await warehouseType();
    return res;
  };

  const jumpTo = async () => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }
    const DatabaseType = await getDatabaseType();
    console.log(DatabaseType.data);
    rowData.value.dataType = DatabaseType.data;
    if (activeName.value === 'first') {
      ODSshow.value = true;
    } else if (activeName.value === 'second') {
      DWIshow.value = true;
    }
  };

  const toBack = async (e) => {
    console.log(e);
    ODSshow.value = false;
    DWIshow.value = false;
    rowData.value = {};
    nextTick(() => {
      expandedList.value = [dataNode.value.id];
    });
    if (e == 'ODS') {
      getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else {
      getDataModelLogicListUtil(dataNode.value.id, 'DWI');
    }
  };
  const fulfill = async (e, level) => {
    await getDataModelLogicListUtil(dataNode.value.id, level);
  };

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref([]);
  const input3 = ref('');
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };
  const revamp = (data) => {
    console.log(data.row);
    // ODSshow.value = true
    getDataModelLogicDetailUtil(data.row.id);
  };

  const rowData = ref({});
  const getDataModelLogicDetailUtil = async (data) => {
    const query = {
      id: data,
    };
    const res = await getDataModelLogicDetail(query);
    if (res.code === 200) {
      console.log(res, 888);
      rowData.value = res.data;
      const DatabaseType = await getDatabaseType();
      rowData.value.dataType = DatabaseType.data;
      if (activeName.value === 'first') {
        ODSshow.value = true;
      } else if (activeName.value === 'second') {
        DWIshow.value = true;
      }
    } else {
      proxy.$modal.msgError(res.message);
    }
  };

  const remove = async (data) => {
    const Vname =
      INames?.value && ids?.value.length > 0 ? INames?.value : treeData?.value?.label || data?.name;
    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');

    if (!res) return;
    await deleteCatalogUtil(data.id);
    if (activeName.value === 'first') {
      await getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else {
      await getDataModelLogicListUtil(dataNode.value.id, 'DWI');
    }
  };

  const deleteCatalogUtil = async (ids) => {
    const query = {
      id: ids,
    };
    const res = await deleteDataModelLogic(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    await getCatalogTreeUtil();
  };

  onMounted(async () => {
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, (val) => {
    console.log(val);
    getCatalogTreeUtil();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .splitpanes__pane {
    height: 100%;
  }

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .tree-box {
    height: 100%;
    background-color: $--base-color-item-light;
    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: 100%;
      background: $--base-color-item-light;
    }
  }
  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    height: 100%;
    overflow: auto;
  }
  .right-box {
    height: 100%;
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    display: inline-block;
    text-align: right;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
    :deep & > div,
    & > button {
      margin-left: 16px;
    }
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
</style>
