kind: Deployment
apiVersion: apps/v1
metadata:
    name: $APP_NAME-$DEPLOY_ENV
    namespace: real-time-data-platform-big-data
    labels:
        app: $APP_NAME-$DEPLOY_ENV
    annotations:
        deployment.kubernetes.io/revision: '3'
        kubesphere.io/creator: xugu
spec:
    replicas: 1
    selector:
        matchLabels:
            app: $APP_NAME-$DEPLOY_ENV
    template:
        metadata:
            creationTimestamp: null
            labels:
                app: $APP_NAME-$DEPLOY_ENV
            annotations:
                cni.projectcalico.org/ipv4pools: '["default-ipv4-ippool"]'
                kubesphere.io/creator: xugu
                kubesphere.io/imagepullsecrets: '{"$APP_NAME-$DEPLOY_ENV":"harbor104"}'
        spec:
            volumes:
                - name: host-time
                  hostPath:
                      path: /etc/localtime
                      type: ''
                - name: $APP_NAME-$DEPLOY_ENV-nginx-conf
                  configMap:
                    name: $APP_NAME-$DEPLOY_ENV-nginx-conf
                    items:
                      - key: nginx.conf
                        path: nginx.conf
                    defaultMode: 420
            containers:
                - name: $APP_NAME-$DEPLOY_ENV
                  image: '$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME-$DEPLOY_ENV:SNAPSHOT-$BUILD_NUMBER'
                  ports:
                      - name: tcp-$SERVER_PORT
                        containerPort: $SERVER_PORT
                        protocol: TCP
                  resources:
                      limits:
                          cpu: 800m
                          memory: 1Gi
                      requests:
                          cpu: 50m
                          memory: 512Mi
                  volumeMounts:
                    - name: host-time
                      readOnly: true
                      mountPath: /etc/localtime
                    - name: $APP_NAME-$DEPLOY_ENV-nginx-conf
                      readOnly: true
                      mountPath: /etc/nginx/nginx.conf
                      subPath: nginx.conf
                  terminationMessagePath: /dev/termination-log
                  terminationMessagePolicy: File
                  imagePullPolicy: IfNotPresent
            restartPolicy: Always
            terminationGracePeriodSeconds: 30
            dnsPolicy: ClusterFirst
            serviceAccountName: default
            serviceAccount: default
            securityContext: {}
            imagePullSecrets:
                - name: harbor104
            schedulerName: default-scheduler
    strategy:
        type: RollingUpdate
        rollingUpdate:
            maxUnavailable: 25%
            maxSurge: 25%
    revisionHistoryLimit: 10
    progressDeadlineSeconds: 600


