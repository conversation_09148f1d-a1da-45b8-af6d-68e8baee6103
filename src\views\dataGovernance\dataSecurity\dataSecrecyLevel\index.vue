<template>
  <div class="page-container">
    <div class="top-title">数据密级</div>
    <div class="table-container">
      <div class="table-box">
        <el-table height="100%" :data="dataList">
          <el-table-column prop="dictValue" label="密级" >
            <template #default="scope">
              <span :class="`status-span status-span-${scope.row.dictValue}`">
                {{ scope.row.dictValue }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="dictLabel" label="密级名称" ></el-table-column>
          <el-table-column prop="createBy" label="创建人" ></el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { listData } from '@/api/system/dict/data';

  const total = ref(0);

  const queryParams = ref({ pageSize: 20, pageNum: 1, dictType: 'security_level_code' });

  const dataList = ref([]);

  const getList = async () => {
    const res = await listData(queryParams.value);
    dataList.value = res.rows.sort((a, b) => {
      return b.dictSort - a.dictSort;
    });
    total.value = res.total;
  };

  onMounted(() => getList());
</script>

<style lang="scss" scoped>
  .page-container {
    height: 100%;
    margin: 20px 20px 26px;
    .top-title {
      height: 32px;
      padding: 6px 0;
      color: #000000;
      font-weight: 600;
      font-size: 20px;
    }

    .table-container {
      height: calc(100% - 72px);

      margin-top: 20px;
      .table-box {
        height: calc(100% - 82px);
      }
      .status-span {
        display: inline-block;
        width: 37px;
        height: 24px;
        text-align: center;
        &.status-span-L0 {
          background: #dce5f5;
          color: #434343;
        }
        &.status-span-L1 {
          background: #eaf1ff;
          color: #1269ff;
        }
        &.status-span-L2 {
          background: #e6fffb;
          color: #36cfc9;
        }
        &.status-span-L3 {
          background: #fff7e6;
          color: #faad14;
        }
        &.status-span-L4 {
          background: #f9f0ff;
          color: #722ed1;
        }
        &.status-span-L5 {
          background: #fff0f6;
          color: #eb2f96;
        }
        &.status-span-L6 {
          background: #fbeae9;
          color: #f84031;
        }
      }
    }
  }
</style>
