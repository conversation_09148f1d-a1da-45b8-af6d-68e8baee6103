<template>
  <el-form-item :label="`${isSource}值名`" :prop="`${propPath}.name`" :rules="rules.value_name">
    <el-input
      v-model="dataObj.name"
      :placeholder="`请输入${isSource}值名`"
      clearable
      :disabled="!canvasActions"
    />
  </el-form-item>
  <el-form-item :prop="`${propPath}.execute_sql`" :rules="rules.execute_sql">
    <el-input
      v-model="dataObj.execute_sql"
      :placeholder="`请输入${isSource}值 SQL`"
      clearable
      :disabled="!canvasActions"
    />
    <template #label>
      <el-tooltip :content="tooltipInfo" effect="dark" placement="top-start">
        <el-icon> <QuestionFilled /> </el-icon>
      </el-tooltip>
      <span>{{ `${isSource}值SQL` }}</span>
    </template>
  </el-form-item>
</template>

<script setup>
  const props = defineProps({
    dataObj: {
      type: Object,
      default: () => ({}),
    },
    rules: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
    useType: {
      type: String,
      default: 'source',
    },
    canvasActions: {
      type: Boolean,
      default: true,
    },
    propPath: {
      type: String,
      required: true,
    },
  });
  const { dataObj, rules, canvasActions } = toRefs(props);
  const { useType } = toRefs(props);
  const isSource = computed(() => {
    return useType.value === 'source' ? '实际' : '期望';
  });
  const tooltipInfo = computed(() => {
    return '该SQL必须为统计SQL,例如统计行数,计算最大值、最小值等 :SQL中表名必须用$ src_table、$target_table替换实际表名, 例如,select max(a) as max_num from $src_table';
  });
</script>

<style lang="scss" scoped></style>
