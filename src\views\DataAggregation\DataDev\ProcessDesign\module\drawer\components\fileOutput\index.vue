<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item label="数据源" prop="dataSources">
      <el-select
        v-model="form.dataSources"
        placeholder="请选择"
        style="width: 100%"
        :disabled="!CanvasActions"
        clearable
        @change="getBucketListUtil"
      >
        <el-option v-for="dict in dataSourcesList" :key="dict.value" v-bind="dict" />
      </el-select>
    </el-form-item>
    <el-form-item label="桶名" prop="bucket">
      <!-- <el-input v-model="form.bucket" placeholder="请输入" :disabled="!CanvasActions" /> -->
      <!-- 下拉框 -->
      <el-select v-model="form.bucket" placeholder="请选择" clearable :disabled="!CanvasActions">
        <el-option
          v-for="items in BucketList"
          :key="items"
          :label="items.label"
          :value="items.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="目录" prop="catalog">
      <el-input v-model="form.catalog" placeholder="请输入" :disabled="!CanvasActions" />
    </el-form-item>
    <el-form-item label="文件类型" prop="fileFormat">
      <!-- 下拉框 -->
      <el-select
        v-model="form.fileFormat"
        placeholder="请选择文件格式"
        clearable
        :disabled="!CanvasActions"
        @change="fileFormatChange"
      >
        <el-option label="TEXT" value="text" />
        <el-option label="CSV" value="csv" />
        <el-option label="Parquet" value="parquet" />
        <el-option label="ORC" value="orc" />
        <el-option label="JSON" value="json" />
        <el-option label="EXCEL" value="excel" />
        <el-option label="XML" value="xml" />
        <el-option label="Binary" value="binary" />
      </el-select>
    </el-form-item>

    <el-form-item label="字符集" prop="encoding">
      <el-select v-model="form.encoding" placeholder="请选择" clearable :disabled="!CanvasActions">
        <el-option
          v-for="items in encodingList"
          :key="items"
          :label="items.label"
          :value="items.value"
        />
      </el-select>
    </el-form-item>
    <!-- xml -->
    <template v-if="changeType(form.fileFormat) === 'xml'">
      <el-form-item label="use_attr_format" prop="xml_use_attr_format">
        <!-- <el-input -->
        <!-- v-model="form.xml_use_attr_format" -->
        <!-- placeholder="请输入" -->
        <!-- :disabled="!CanvasActions" -->
        <!-- ></el-input> -->
        <!-- 下拉框 true false -->
        <el-select v-model="form.xml_use_attr_format" :disabled="!CanvasActions">
          <el-option label="是" value="true"></el-option>
          <el-option label="否" value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="row_tag" prop="xml_row_tag">
        <el-input
          v-model="form.xml_row_tag"
          placeholder="请输入"
          :disabled="!CanvasActions"
        ></el-input>
      </el-form-item>
    </template>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import { listOss } from '@/api/system/oss';
  import { getToken } from '@/utils/auth';
  import { getBucketList } from '@/api/dataSourceManageApi';
  import jsonTree from '@/components/jsonTree/index';
  import {
    getSampleDataFile,
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    getNodeData,
    getTableList,
    schemaForGP,
    listPaging,
    listForPreApi,
    connect,
    getFiledType,
    getDataSourceList,
    getBucketRootObjsList,
  } from '@/api/DataDev';

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    workspaceId: {
      type: Number,
      default: 0,
    },
  });
  const { NodeData, CanvasActions, workspaceId } = toRefs(props);
  const emit = defineEmits();

  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
  const limit = ref(2);
  const fileList = ref([]);
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    workspaceId: workspaceId.value,
  });

  const open = ref(false);
  const testConnection = ref(null);
  const result = ref(null);
  const isResult = ref(false);
  const preParamKey = ref('');
  const otherList = ref([]);
  const expandAll = ref(true);
  const allValues = ref([]);

  // 分隔符
  const separator = ref();
  const updatePath = (e) => {
    console.log('e', e);
    preParamKey.value = e;
  };

  /**
   * 选择文件
   */
  const selectFileList = ref();

  const data = reactive({
    form: {},
    rules: {
      dataSources: [{ required: true, message: '请选择数据源', trigger: 'blur' }],
      catalog: [{ required: true, message: '请输入目录', trigger: 'blur' }],
      fileFormat: [{ required: true, message: '请选择文件格式', trigger: 'blur' }],
      bucket: [{ required: true, message: '请选择桶名', trigger: 'blur' }],
      encoding: [{ required: true, message: '请选择字符集', trigger: 'blur' }],
    },
  });

  const { form, queryParams, rules } = toRefs(data);

  // 使用计算属性 判断  form.selectFile 是否有值
  const isSelectFile = computed(() => {
    return !form.value.selectFile;
  });
  const cancelDrawer = () => {
    form.value = {};
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    NodeData.value.inputProperties.forEach((property, index) => {
      switch (index) {
        // 文件格式
        case 0:
          property.value = form.value.fileFormat;
          break;
        case 4:
          property.value = form.value.bucket;
          break;
        case 8:
          property.value = form.value.xml_row_tag;
          break;
        case 9:
          property.value = form.value.xml_use_attr_format;
          break;
        // 选中类型
        // 数据源  true
        // 本地   false
        case 10:
          property.value = true;
          break;
        // 数据源名称
        case 11:
          property.value = form.value.dataSources;
          break;
        // 目录
        case 12:
          property.value = form.value.catalog;
          break;
        case 14:
          property.value = form.value.encoding;
          break;
        default:
          break;
      }
    });
    emit('submitDrawer', NodeData.value);
  };

  const hasWarned = false; // 添加这个变量来跟踪警告是否已经显示过
  const init = async () => {
    await getDataSourceListUtil();

    form.value.dataSources = Number(NodeData.value.inputProperties[11].value)
      ? Number(NodeData.value.inputProperties[11].value)
      : null;

    form.value.dataSources && (await getBucketListUtil(form.value.dataSources));

    NodeData.value.inputProperties.forEach((property, index) => {
      switch (index) {
        case 0:
          form.value.fileFormat = property.value;
          break;
        case 4:
          form.value.bucket = property.value;
          break;
        case 8:
          form.value.xml_row_tag = property.value;
          break;
        case 9:
          form.value.xml_use_attr_format = property.value;
          break;
        case 10:
          // 忽略,不需要设置
          break;
        // case 11:
        //   form.value.dataSources = Number(property.value) ? Number(property.value) : null;
        //   break;
        case 12:
          form.value.catalog = property.value;
          break;
        case 14:
          form.value.encoding = property.value || property.defaultValue;
          break;
        default:
          break;
      }
    });
  };
  onMounted(async () => {
    await init();
  });

  watch(NodeData, () => {
    init();
  });

  const dataSourcesList = ref([]);
  const getDataSourceListUtil = async () => {
    const res = await getDataSourceList({
      pageNo: 1,
      pageSize: 100000,
      dataSourceType: 'MINIO',
      workSpaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    dataSourcesList.value = res.data.totalList.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  };
  const encodingList = ref([
    {
      label: 'UTF-8',
      value: 'UTF-8',
    },
    {
      label: 'GBK',
      value: 'gbk',
    },
  ]);
  const BucketList = ref([]);
  const getBucketListUtil = async (datasourceId) => {
    const dataSource = dataSourcesList.value.find((item) => item.value === datasourceId);
    // 清空 数据
    form.value.catalog = null;
    form.value.fileFormat = null;
    form.value.bucket = null;
    form.value.encoding = null;
    BucketList.value = [];
    if (!dataSource) return;

    // proxy.$modal.msgWarning('暂无对应数据');
    // const bucketName = dataSource.label;

    try {
      const res = await getBucketList({
        datasourceId,
        // bucketName
      });
      if (res.code !== 200) return proxy.$modal.msgWarning(`获取桶列表失败: ${res.msg}`);
      BucketList.value = res.data.map((item) => ({
        label: item || item.name || '未知标签',
        value: item,
      }));
    } catch (error) {
      console.error('请求桶列表时发生错误:', error);
    }
  };

  const changeType = (fileUrl) => {
    // 获取文件后缀
    const fileExtension = fileUrl?.split('.')?.pop();
    const map = {
      xls: 'excel',
      xlsx: 'excel',
      txt: 'text',
    };
    return map[fileExtension] ? map[fileExtension] : `${fileExtension}`;
  };

  const fileFormatChange = () => {
    // form.value.bucket = null;
    form.value.encoding = null;
    form.value.xml_use_attr_format = null;
    form.value.xml_row_tag = null;
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .per {
    height: 300px;
    overflow: auto;
    // 使用原样展示
    white-space: pre-wrap;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .test-connection {
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
    max-width: 50%;
    min-width: 50%;
    border: 1px solid #ebeef5;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      border-left: 1px solid #ebeef5;
    }
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }
</style>
