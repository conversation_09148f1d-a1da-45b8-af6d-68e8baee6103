<template>
  <div ref="FullRef" class="app-container-box">
    <el-row :gutter="0">
      <splitpanes class="default-theme">
        <!-- tree -->
        <pane min-size="20" max-size="30" size="20">
          <!-- <el-col :span="treeLap ? 4 : 0" class="tree-container"> -->
          <div class="head-container" @contextmenu.prevent="$event.preventDefault()">
            <!-- tree head -->
            <el-row :gutter="20" class="head-title-tree">
              <el-col :span="10">
                <span class="TitleName">工作流设计</span>
              </el-col>
              <el-col :span="14" class="right-btn-box">
                <!-- <Plus
                    :color="storageSetting?.theme ? storageSetting?.theme : '#409EFF'"
                    @click="addTree"
                  /> -->
                <ExportAndImport
                  module-name="Flow"
                  :allow-click="{
                    output: { disabled: false, msg: '' },
                    input: { disabled: false, msg: '' },
                    logs: { disabled: false, msg: '' },
                  }"
                  @reload="getWorkList"
                ></ExportAndImport>
                <el-tooltip content="新增目录" placement="top">
                  <el-button
                    style="width: 28px; height: 28px"
                    class="right-btn-add"
                    icon="Plus"
                    type="primary"
                    @click="addTree"
                  ></el-button>
                </el-tooltip>
              </el-col>
            </el-row>
            <!-- tree head -->

            <el-row :gutter="20" style="margin-bottom: 10px">
              <!-- tree   hander-->
              <el-col :span="24" style="margin-bottom: 5px">
                <!-- <el-select v-model="dataTreeType" placeholder="" @change="filterNameUti">
                  <el-option
                    v-for="dict in dataTreeTypeList"
                    :key="dict.label"
                    :value="dict.value"
                    :label="dict.label"
                  ></el-option>
                </el-select> -->
                <el-radio-group v-model="dataTreeType" @change="filterNameUti">
                  <el-radio-button label="all">全部</el-radio-button>
                  <el-radio-button label="myCreate">我创建的</el-radio-button>
                </el-radio-group>
              </el-col>
              <el-col :span="24">
                <el-input
                  v-model="filterText"
                  prefix-icon="Search"
                  placeholder="流程组名称/流程名称"
                />
              </el-col>
            </el-row>
            <!-- tree  hander -->

            <!-- tree  -->
            <el-tree
              v-if="treeShow"
              ref="treeRef"
              :data="dataTree"
              :props="defaultProps"
              node-key="id"
              :filter-node-method="filterNode"
              class="treeFab"
              :default-expanded-keys="expandedList"
              :default-checked-keys="checkedList"
              highlight-current="true"
              @node-click="handleNodeClick"
              @node-expand="nodeExpand"
              @node-collapse="nodeCollapse"
            >
              <template #default="{ node, data }">
                <div class="tree-item">
                  <div class="tree-item-box">
                    <span v-if="node.level == 1">
                      <el-icon>
                        <FolderOpened />
                      </el-icon>
                      <el-tooltip :content="data.text" placement="top" effect="light">
                        {{ data.text.length > 35 ? data.text.slice(0, 35) + '...' : data.text }}
                      </el-tooltip>
                    </span>
                    <span v-if="node.level == 2">
                      <!-- <el-button @click="putDraw(true)" size="small" type="text" effect="dark" icon="Finished" /> -->
                      <el-icon>
                        <Cpu />
                      </el-icon>
                      <el-tooltip :content="data.text" placement="top" effect="light">
                        {{ data.text.length > 35 ? data.text.slice(0, 35) + '...' : data.text }}
                      </el-tooltip>
                    </span>
                  </div>

                  <div class="tree-btn-box">
                    <span
                      v-if="node.level == 1"
                      class="tree-prop-icon"
                      @click.stop="append($event, data, node)"
                    >
                      <el-tooltip content="新增工作流" placement="top">
                        <el-icon>
                          <Plus />
                        </el-icon>
                      </el-tooltip>
                    </span>
                    <span
                      v-if="node.level == 2"
                      class="tree-prop-icon"
                      @click.stop="coppyTreeItem($event, data, node)"
                    >
                      <el-tooltip content="复制" placement="top">
                        <el-icon>
                          <CopyDocument />
                        </el-icon>
                      </el-tooltip>
                    </span>

                    <el-popover trigger="click" placement="top" :width="160">
                      <div class="popover">
                        <span v-if="node.level == 2" class="tree-prop-icon" @click.stop="moveGroup">
                          <el-icon>
                            <Sort />
                          </el-icon>
                          移动工作流
                        </span>
                        <span class="tree-prop-icon" @click.stop="rename()">
                          <el-icon>
                            <Edit />
                          </el-icon>
                          编辑
                        </span>
                        <span
                          v-if="data.children == null"
                          class="tree-prop-icon"
                          @click.stop="remove"
                        >
                          <el-icon>
                            <Delete />
                          </el-icon>
                          删除
                        </span>
                      </div>
                      <template #reference>
                        <span class="tree-icon" @click.stop="showContextMenu($event, data, node)">
                          <el-icon>
                            <MoreFilled />
                          </el-icon>
                        </span>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </template>
            </el-tree>
            <!-- tree  -->

            <!-- 菜单 -->
            <transition name="slide-fade">
              <div
                v-if="showMenu"
                class="custom-menu"
                :style="{ top: `${menuY}px`, left: `${menuX}px` }"
              >
                <div v-show="menuNode.level < 2 ? true : false">
                  <!-- <a @click="addTree">新建流程组</a> -->
                  <a @click="append">新增工作流</a>
                </div>
                <div v-show="menuNode.level < 2 ? false : true">
                  <a @click="coppyTreeItem">复制</a>
                </div>
                <!-- <a @click="rename" v-show="menuNode.level == 2 ? false : true">重命名</a> -->
                <a @click="rename">编辑</a>
                <a @click="moveGroup">移动工作流</a>
                <a @click="remove">删除</a>
              </div>
            </transition>
          </div>
          <!-- 菜单 -->

          <!-- </el-col> -->
        </pane>
        <!-- <span @click="treeLap = !treeLap" class="tree-lap" :style="treeLap ? '' : 'transform: rotate(180deg); left: 0; '"> -->
        <!-- <svg width="10" height="40" xmlns="http://www.w3.org/2000/svg"> -->
        <!-- <g fill="none" fill-rule="evenodd"> -->
        <!-- <path d="m10 40-7.717-2.567A3.333 3.333 0 0 1 0 34.267V5.733a3.333 3.333 0 0 1 2.283-3.166L10 0v40Z" -->
        <!-- fill="#CCC" /> -->
        <!-- <path -->
        <!-- d="m2.547 19.125 3.928-3.2c.515-.42 1.277-.046 1.277.625v6.4c0 .671-.762 1.044-1.277.625l-3.928-3.2a.81.81 0 0 1 0-1.25Z" -->
        <!-- fill="#FFF" fill-rule="nonzero" /> -->
        <!-- </g> -->
        <!-- </svg> -->
        <!-- </span> -->
        <pane>
          <el-col :span="treeLap ? 20 : 24" style="margin-top: 1px; width: 100%">
            <el-empty
              v-if="flowTabs.length <= 0"
              description="请选择左侧工作流"
              style="height: 100%"
              :image="imgUrlForEmpty"
            ></el-empty>

            <template v-else>
              <!-- 关闭全部 -->

              <el-tabs
                v-model="activeFlow"
                class="custom-tabs"
                closable
                type="card"
                tab-position="top"
                @tab-click="tabToggle"
                @tab-remove="tabRemoveClick"
              >
                <el-tab-pane
                  v-for="item in flowTabs"
                  :key="item.unique"
                  :label="item.text"
                  :name="item.unique"
                >
                  <!-- {{ item.content }} -->
                </el-tab-pane>
              </el-tabs>
              <!-- <el-icon><CloseBold /></el-icon> -->
              <el-tooltip content="关闭全部" placement="top">
                <el-button
                  v-if="flowTabs.length >= 8"
                  type="text"
                  class="closeAllbtn"
                  icon="CloseBold"
                  @click="closeAll"
                />
              </el-tooltip>
            </template>

            <Transition name="bounce">
              <div v-show="flowTabs.length > 0" class="tool-header" style="width: 100%">
                <!-- 工具头 -->
                <NavTools
                  v-if="!syncShow"
                  ref="NavToolsRef"
                  :is-show-button="isShowButton"
                  :run-status="runStatus"
                  :graph="graph"
                  :stopmodel="model"
                  :CanvasActions="CanvasActions"
                  :show-full="false"
                  :node-log-show="nodeLogShow"
                  @toggle-canvas-actions="toggleCanvasActions"
                  @full-cilck="FullCilck"
                  @put-draw="putDraw"
                  @save-work-flow-util="saveWorkFlowUtil(true)"
                  @run-draw="runDrawUtil"
                  @stop-draw="stopDrawUtil"
                  @star-draw="starDraw"
                  @open-warn-dialog="handleSubscribe"
                  @view-or-close-log="viewOrCloseLog"
                />
                <div class="stencil-app">
                  <!-- sync -->
                  <sync
                    v-if="syncShow"
                    class="app-sync"
                    :NodeData="nodeDataOffLine"
                    :workspace-id="workspaceId"
                    :CanvasActions="CanvasActions"
                    @tab-remove-click="tabRemoveClick"
                  />
                  <!-- 侧边 -->
                  <div
                    v-show="CanvasActions"
                    class="app-stencil-box"
                    :style="showStencil ? '' : 'width: 20px'"
                  >
                    <div
                      v-show="showStencil"
                      id="stencil"
                      ref="stencilContainer"
                      class="app-stencil"
                      :class="{ 'animate-opacity': showStencil }"
                    ></div>

                    <el-icon
                      class="stencil-icon"
                      :style="
                        showStencil
                          ? 'top: 46px; right: 20px; z-index: 9;'
                          : 'top: calc(50% + 10px); right: 2px;  z-index: 9;'
                      "
                    >
                      <Operation @click="showStencilClick()" />
                    </el-icon>
                  </div>

                  <!-- 主题 -->
                  <div id="container" ref="container" class="app-content"></div>

                  <nodelog
                    v-show="nodeLogShow"
                    ref="nodelogRef"
                    class="app-nodelog"
                    :class="{ 'all-width': !CanvasActions || !showStencil }"
                    :node-log-data="webScoketMsg"
                    :workspace-id="workspaceId"
                    @view-log="getLogUtil"
                    @refresh-log="refreshDataForLog"
                    @close-log="closeNodeLog"
                    @change-run-status="changeStatus"
                  />
                  <!-- 地图 -->
                  <div v-show="!syncShow" id="minimap" class="app-Map"></div>
                  <!-- 右键菜单 -->
                  <flow-content-menu
                    v-model:visible="menuVisible"
                    :position="menuPosition"
                    :CanvasActions="CanvasActions"
                    @on-menu-click="onMenuClick"
                  />
                </div>
              </div>
            </Transition>
          </el-col>
        </pane>
      </splitpanes>
    </el-row>
    <!--  -->

    <template v-if="drawer">
      <DrawerRight
        ref="drawerRight"
        v-model:drawer="drawer"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="cancelDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>
    <el-dialog
      v-model="dialogTree.open"
      :title="'新增' + dialogTree.title"
      width="40%"
      append-to-body
      :draggable="true"
      @close="cancelDialogTree"
    >
      <el-form
        ref="dataSourceRef"
        :model="dialogTree"
        :rules="rules"
        label-position="right"
        label-width="auto"
        @submit.prevent="submitForm"
      >
        <el-form-item :label="dialogTree.title + '名称'" prop="flowName">
          <el-input v-model="dialogTree.flowName" placeholder="请输入流程名称" />
        </el-form-item>

        <el-form-item v-if="dialogTree.title != '流程组'" label="流程类型" prop="flowType">
          <el-select v-model="dialogTree.flowType" placeholder="请选择流程类型" style="width: 100%">
            <el-option
              v-for="dict in flow_type"
              :key="dict.label"
              :value="dict.value"
              :label="dict.label"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="dialogTree.description"
            placeholder="请输入描述(限100字)"
            type="textarea"
            maxlength="100"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogTree">取 消</el-button>
          <el-button type="primary" :loading="dialogTree.loading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogTreeTwo.open"
      :title="nodeTitle + '编辑'"
      width="650"
      append-to-body
      :draggable="true"
      @close="cancelDialogTreeTwo"
    >
      <div>
        <el-form
          ref="dataSourceTwoRef"
          :rules="rules"
          :model="dialogTreeTwo"
          label-position="right"
          label-width="auto"
          @submit.prevent="submitFormTwo"
        >
          <el-form-item :label="nodeTitle + '名称'" prop="flowName">
            <el-input v-model="dialogTreeTwo.flowName" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="描述" prop="noticeTitle">
            <el-input
              v-model="dialogTreeTwo.description"
              placeholder="请输入描述(限100字)"
              type="textarea"
              maxlength="100"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogTreeTwo">取 消</el-button>
          <el-button type="primary" @click="submitFormTwo">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogAddNode.open"
      title="新增节点"
      width="650"
      append-to-body
      :draggable="true"
      @close="cancelDialogAddNode()"
    >
      <div>
        <el-form-item label="节点名称" prop="noticeTitle">
          <el-input
            v-model="dialogAddNode.flowName"
            maxlength="20"
            show-word-limit
            placeholder="默认自动生成名称"
          />
        </el-form-item>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogAddNode()">取 消</el-button>
          <el-button type="primary" :loading="btnLoading" @click="submitFormAddNode"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogSubDescription.open"
      title="提交流程"
      width="40%"
      append-to-body
      :draggable="true"
      @close="cancelDialogSubDescription"
    >
      <div>
        <el-form-item label="描述" prop="noticeTitle">
          <el-input
            v-model="dialogSubDescription.flowName"
            placeholder="请输入描述(限100字)"
            type="textarea"
            maxlength="100"
          />
        </el-form-item>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogSubDescription">取 消</el-button>
          <el-button type="primary" @click="submitFormSubDescription">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看日志 Drawer -->
    <!-- <el-drawer v-model="nodeLogDrawer" title="查看日志" :append-to-body="true" size="80%">

      <div class="nodeLog">
        <pre>{{ LogData }}</pre>
      </div>
    </el-drawer> -->
    <!-- 日志对话框 -->
    <el-dialog v-model="openLog" :title="LogTitle" width="1200px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="4" style="border-right: 8px solid #eee; overflow: hidden">
          <el-scrollbar height="600px">
            <el-tree
              ref="logTree"
              :data="LogContentList"
              :props="defaultPropsForLog"
              :default-expanded-keys="defaultExpandedKeys"
              node-key="taskLogNo"
              @node-click="handleNodeClickForLog"
            >
              <template #default="{ node }">
                <!-- {{ node }} -->
                <!-- <el-tooltip :content="showTaskStatus(node.data.state)" placement="top">
              </el-tooltip> -->
                <div class="table-status">
                  <span :class="`task-status-content task-status-${node.data.state}`">
                    {{ showTaskStatus(node.data.state) }}
                  </span>
                </div>
                <el-tooltip
                  :disabled="node.label.length < 10"
                  :content="node.label"
                  placement="top"
                >
                  <span>{{
                    node.label.length > 10 ? node.label.slice(0, 10) + '...' : node.label
                  }}</span>
                </el-tooltip>
              </template>
            </el-tree>
          </el-scrollbar>
        </el-col>
        <el-col :span="19">
          <div style="max-height: 600px; overflow-y: auto">
            <Codemirror
              v-model="logStrB"
              :disabled-type="true"
              style="max-height: 600px; overflow-y: auto"
            />
          </div>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="openLogClose">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 告警订阅 -->
    <el-dialog
      v-model="openWarn"
      title="告警订阅"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      z-index="1000"
      @close="cancelWarn"
    >
      <el-form ref="warnFormRef" :model="warnForm" :rules="ruleForWarn">
        <el-form-item label="告警策略" prop="subscribeType">
          <el-radio-group v-model="warnForm.subscribeType" class="radio_group">
            <el-radio label="2">成功通知</el-radio>
            <el-radio label="3">失败通知</el-radio>
            <el-radio label="1">成功失败都通知</el-radio>
            <!-- <el-radio label="0">成功失败都不通知</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知策略" prop="checkList">
          <el-checkbox-group v-model="warnForm.checkList">
            <el-checkbox label="站内信" />
            <el-checkbox :disabled="onCheck" label="邮箱" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="() => (openWarn = false)">取 消</el-button>
          <el-button type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="changeGroupDialog" title="移动工作流" width="30%" :draggable="true">
      <div class="change-form-box">
        <el-form
          ref="changeFormRef"
          :model="changeGroupFrom"
          :rules="changeGroupFromRules"
          label-position="right"
          label-width="auto"
        >
          <el-form-item label="目的分组" prop="flowGroupId">
            <el-tree-select
              v-model="changeGroupFrom.flowGroupId"
              :data="flowGroupOptions"
              :props="{
                value: 'id',
                label: 'text',
                children: 'children',
                disabled: 'disabled',
              }"
              value-key="flowGroupId"
              placeholder="选择目的分组"
              check-strictly
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeChangeGroupDialog">取 消</el-button>
          <el-button type="primary" @click="changeGroupCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 运行方式 -->
    <el-dialog
      v-model="startFlowData.dialogVisible"
      title="运行"
      width="650px"
      :draggable="true"
      @close="closeStartFlowDialog"
    >
      <div class="change-form-box">
        <el-form
          ref="startFlowFormRef"
          :model="startFlowData.flowForm"
          :rules="startFlowData.rules"
          label-position="right"
          label-width="auto"
        >
          <el-form-item label="工作流名称">
            {{ startFlowData.flowForm.flowName || activeFlow }}
          </el-form-item>
          <el-form-item v-if="!isSingleNode" label="节点名称">
            {{ startFlowData.flowForm.nodeName }}
          </el-form-item>
          <!-- <el-form-item v-if="!isSingleNode" label="失败策略" prop="failureStrategy"> -->
          <!-- <el-radio-group v-model="startFlowData.flowForm.failureStrategy"> -->
          <!-- <el-radio label="CONTINUE">继续</el-radio> -->
          <!-- <el-radio label="END">终止</el-radio> -->
          <!-- </el-radio-group> -->
          <!-- </el-form-item> -->
          <el-form-item v-if="!isSingleNode" label="节点执行" prop="taskDependType">
            <el-radio-group v-model="startFlowData.flowForm.taskDependType">
              <!-- 向前 向后 仅当前 -->
              <el-radio label="TASK_PRE">向前</el-radio>
              <el-radio label="TASK_POST">向后</el-radio>
              <el-radio label="TASK_ONLY">仅当前</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="isSingleNode" label="是否补数" prop="execType">
            <!-- switch -->
            <!-- 否 START_PROCESS -->
            <!-- 是 COMPLEMENT_DATA -->
            <el-switch v-model="startFlowData.flowForm.execType" />
          </el-form-item>
          <template v-if="startFlowData.flowForm.execType">
            <el-form-item label="执行方式" prop="runMode">
              <el-radio-group v-model="startFlowData.flowForm.runMode">
                <el-radio label="RUN_MODE_SERIAL">串行补数</el-radio>
                <el-radio label="RUN_MODE_PARALLEL">并行补数</el-radio>
              </el-radio-group>
            </el-form-item>

            <template v-if="startFlowData.flowForm.runMode == 'RUN_MODE_PARALLEL'">
              <el-form-item label="并行度" prop="expectedParallelismNumber">
                <el-input
                  v-model="startFlowData.flowForm.expectedParallelismNumber"
                  min="1"
                  type="number"
                />
              </el-form-item>
            </template>

            <el-form-item label="调度时间" prop="scheduleTime">
              <el-radio-group v-model="startFlowData.flowForm.timeType" @change="timeTypeChange">
                <el-radio label="1">时间选择</el-radio>
                <el-radio label="2">手动输入</el-radio>
              </el-radio-group>
              <!-- 时间选择 -->
              <template v-if="startFlowData.flowForm.timeType == 1">
                <el-date-picker
                  v-model="startFlowData.flowForm.scheduleTime"
                  type="datetimerange"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="截止日期"
                />
              </template>
              <!-- 手动输入 -->
              <template v-if="startFlowData.flowForm.timeType == 2">
                <el-input
                  v-model="startFlowData.flowForm.scheduleTime"
                  placeholder="'格式为:YYYY-MM-DD HH:mm:ss 多个以逗号分割'"
                  type="textarea"
                  show-word-limit
                />
              </template>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeStartFlowDialog">取 消</el-button>
          <el-button type="primary" @click="submitStartFlow">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getTenantList } from '@/api/system/user';
  import { ElMessage } from 'element-plus';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  // import autofit from "autofit.js" // 屏幕适应
  /*
AntV X6
*/
  import { Graph } from '@antv/x6'; // 导入图形库
  import { Clipboard } from '@antv/x6-plugin-clipboard'; // 导入剪切板插件
  import { History } from '@antv/x6-plugin-history'; // 导入历史插件
  import { Keyboard } from '@antv/x6-plugin-keyboard'; // 导入快捷键插件
  import { MiniMap } from '@antv/x6-plugin-minimap'; // 导入缩略图插件
  import { Scroller } from '@antv/x6-plugin-scroller'; // 导入滚动条插件
  import { Selection } from '@antv/x6-plugin-selection'; // 导入选中插件
  import { Snapline } from '@antv/x6-plugin-snapline'; // 导入对齐线插件
  import { Stencil } from '@antv/x6-plugin-stencil'; // 导入侧边插件
  // getLog,
  import { getSingleRunInstance, getTaskList, getTaskInstanceLog } from '@/api/dataSourceManageApi';
  import {
    addWorkFlow,
    buildFlowNode,
    collect,
    delWorkflow,
    delWorkflowGroup,
    getNode,
    getOperatorTree,
    getProjTreeMenu,
    getUUid,
    lockFlow,
    nodeRename,
    openChildWorkFlow,
    openWorkFlow,
    putDrawRes,
    runDraw,
    saveNode,
    saveOrUpdate,
    saveWorkFlow,
    stopDraw,
    toCheck,
    unlockFlow,
    copyFlow,
    moveFlow,
    getGroupOptions,
    startFlow,
  } from '@/api/DataDev';
  import { getInfo } from '@/api/login';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { addSubscribe, updateSubscribe } from '@/api/alert/subscribe';
  import {
    SETTING_SHAPE_NAME,
    graphOptions,
    ports,
  } from '@/views/DataAggregation/DataDev/ProcessDesign'; // 导入配置
  import DrawerRight from '@/views/DataAggregation/DataDev/ProcessDesign/module/drawer/index.vue';
  import { useDebounceFn, useFullscreen } from '@vueuse/core';
  import rawSource from '../../DataDev/ProcessDesign/data.js'; // 导入 mock 数据
  import FlowContentMenu from '../ProcessDesign/components/FlowContentMenu.vue'; // 导入右键组件
  import NavTools from '../ProcessDesign/components/NavTools.vue'; // 导入 NavTools 组件
  import nodelog from '../ProcessDesign/module/nodeLog/index.vue'; // 导入 nodelog 组件
  import sync from './module/drawer/components/offSync/sync.vue';

  import autoTreeIcon from '@/assets/icons/自定义.png';
  import { reactive, ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';

  const route = useRoute();

  // const wsUrl = import.meta.env.VITE_APP_WS_URL;

  const { proxy } = getCurrentInstance();

  const eCopy = ref();
  const NodeData = ref();
  const NNN = ref();
  const syncShow = ref(false);
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  // const currentUrl = window.location.host;
  // const urlProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
  // let webSocket = new WebSocket(`${urlProtocol}://${currentUrl}/socket/operator/websocket`);
  //   const webSocket = new WebSocket(`${wsUrl}/socket/operator/websocket`);
  const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || '';
  console.log(storageSetting.theme);

  const isRun = ref(false);
  const webScoketMsg = ref([]);
  const reconnectAttempts = 0;
  const reconnectTimeout = 2000; // 初始重连间隔为 2 秒
  const maxReconnectTimeout = 30000; // 最大重连间隔为 30 秒
  const runStatus = ref(false);
  //   const webScoketMsgList = ref([]);
  // function reconnect() {
  //   if (reconnectAttempts < 5) {
  //     // 假设最多尝试重连 5 次
  //     setTimeout(function () {
  //       console.log('尝试重新连接...');
  //       connectWebSocket();
  //       reconnectAttempts++;
  //       reconnectTimeout *= 2; // 每次重连失败后增加重连间隔
  //       if (reconnectTimeout > maxReconnectTimeout) {
  //         reconnectTimeout = maxReconnectTimeout; // 重连间隔不超过最大值
  //       }
  //     }, reconnectTimeout);
  //   } else {
  //     console.log('已达到最大重连尝试次数，不再尝试重连');
  //     // 处理无法重连的情况，如提示用户或执行其他逻辑
  //   }
  // }

  // function connectWebSocket() {
  //   webSocket = new WebSocket(`${urlProtocol}://${currentUrl}/socket/operator/websocket`);
  //   webSocket.onopen = function () {
  //     getUUid().then((res) => {
  //       const data = {
  //         id: res.data,
  //         token: '1',
  //         command: 'register',
  //         data: '',
  //       };

  //       webSocket.send(JSON.stringify(data));
  //       setInterval(function () {
  //         if (webSocket.readyState === WebSocket.OPEN) {
  //           webSocket.send(JSON.stringify(data));
  //         } else {
  //           // 连接可能已经关闭，重新连接或采取其他措施
  //         }
  //       }, 30000); // 每 30 秒发送一次心跳
  //     });
  //   };

  //   webSocket.onerror = function () {};
  //   webSocket.onclose = function () {
  //     reconnect();
  //   };
  // }

  // function onWebSocket() {
  //   webSocket.onmessage = function (e) {
  //     const message = JSON.parse(e.data);
  //     if (message.command == 'flow_task_status') {
  //       if (message.data.nodeId) {
  //         // console.log(message)
  //         const tab = flowTabs.value.find((value) => {
  //           if (value.flowId == message.data.flowId) {
  //             return value;
  //           }
  //         });
  //         // console.log(tab);
  //         let found = tab?.logMessage.filter((res) => res.nodeId != message.data.nodeId);
  //         if (!found) return;
  //         if (isRun.value) {
  //           found = [];
  //           isRun.value = false;
  //         }
  //         found.push(message.data);
  //         tab.logMessage = found;
  //         if (message.data.statusCode == '0' || message.data.statusCode == '1') {
  //           tab.runStatus = true;
  //         } else {
  //           tab.runStatus = false;
  //         }
  //         if (tab.flowId == activeFlowItem.value.flowId) {
  //           webScoketMsg.value = found;
  //           if (webScoketMsg.value.length > 0) {
  //             const foundObject = flowTabs.value.find((value) => {
  //               return value.unique == activeFlow.value;
  //             });

  //             const flowId = foundObject?.flowId;
  //             if (flowId == message.data.flowId) {
  //               nodeLogShow.value = true;
  //             }
  //             console.log(message.data.statusCode);

  //             if (message.data.statusCode == '0' || message.data.statusCode == '1') {
  //               runStatus.value = true;
  //             } else {
  //               runStatus.value = false;
  //             }
  //             tab.runStatus = runStatus.value;
  //           }
  //         }
  //       }
  //     } else if (message.command == 'flow_status') {
  //       let found = [];
  //       let tab = [];
  //       message.data.taskStatuses.forEach((taskStatus) => {
  //         if (taskStatus.nodeId) {
  //           tab = flowTabs.value.find((value) => {
  //             if (value.flowId == taskStatus.flowId) {
  //               return value;
  //             }
  //           });
  //           found = tab?.logMessage.filter((res) => res.nodeId != taskStatus.nodeId);
  //         }
  //         if (tab.logMessage.length > 0) return;
  //         found.push(taskStatus);
  //         tab.logMessage = found;
  //         message.data.taskStatuses.forEach((s) => {
  //           if (s.statusCode == '0' || s.statusCode == '1') {
  //             tab.runStatus = true;
  //           } else {
  //             tab.runStatus = false;
  //           }
  //         });
  //         if (tab.flowId == activeFlowItem.value.flowId) {
  //           webScoketMsg.value = found;
  //           if (webScoketMsg.value.length > 0) {
  //             const foundObject = flowTabs.value.find((value) => {
  //               return value.unique == activeFlow.value;
  //             });

  //             const flowId = foundObject?.flowId;
  //             if (flowId == taskStatus.flowId) {
  //               nodeLogShow.value = true;
  //             }
  //           }

  //           message.data.taskStatuses.forEach((s) => {
  //             if (s.statusCode == '0' || s.statusCode == '1') {
  //               runStatus.value = true;
  //             } else {
  //               runStatus.value = false;
  //             }
  //           });
  //           tab.runStatus = runStatus.value;
  //         }
  //       });
  //     }
  //   };
  // }

  // const workspaceId = ref()
  const FullRef = ref();
  const { toggle } = useFullscreen(FullRef);

  /** 表单校验
   *
   */
  const rules = {
    flowName: [
      { required: true, message: '请输入流程名称', trigger: 'blur' },
      { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      // 禁用特殊字符 ……&#￥、“”、空格
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
        message: '禁止输入特殊字符',
        trigger: 'blur',
      },
    ],
    flowType: [{ required: true, message: '请选择流程类型', trigger: 'blur' }],
    describe: [
      { required: true, message: '请输入描述', trigger: 'blur' },
      { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
    ],
  };

  /**
   * 日志 */

  const nodeLogShow = ref(false);
  const nodeLogDrawer = ref(false);
  const LogData = ref([]);
  /*
AntV X6
----------------------------------------
*/

  const props = defineProps({
    // 地图显示
    minimapShow: {
      type: Boolean,
      default: true,
    },
    // 画布可控
    // CanvasActions: {
    //   type: Boolean,
    //   default: false,
    // }
  });

  const CanvasActions = ref(true);

  // mock 节点数据
  const nodes = reactive(rawSource);

  // 画布右键菜单
  const menuVisible = ref(false); // 显示/隐藏
  const menuPosition = ref({}); // 显示位置
  const selector = ref(); // 当前选中值
  const selectorNode = ref(); // 当前选中节点

  // 点击节点展示配置
  const drawer = ref(false);
  const drawerTitle = ref('');
  const drawerType = ref('');
  const nodeData = ref({});
  const flow = ref({});
  const jobLogId = ref('');
  const changeFlow = (data) => {
    flow.value = data;
  };
  const showStencil = ref(true); // 显示/隐藏 侧边栏

  // 侧边内容属性
  const commonAttrs = {
    body: {
      fill: 'rgba(51, 200, 156, 1)', // 背景色
      stroke: 'rgba(51, 200, 156, 0.12)', // 边框颜色
      strokeWidth: '8', // 线条粗细
      rx: 120,
      ry: 120,
    },
  };
  // 工具头 传参
  const graph = ref();
  const model = ref();

  /*
tree
----------------------------------------
*/
  // 下拉选择值
  const dataTreeType = ref('all');
  // 下拉选择 list
  const dataTreeTypeList = ref([
    // 全部 我创建的 我收藏的
    {
      value: 'all',
      label: '全部',
    },
    {
      value: 'myCreate',
      label: '我创建的',
    },
    // {
    //   value: 'myCollect',
    //   label: '我收藏的',
    // }
  ]);
  // 显示隐藏 tree
  const treeLap = ref(true);
  // tree 固定参数
  const defaultProps = {
    children: 'children',
    label: 'text',
  };

  const defaultPropsForLog = {
    children: 'children',
    label: 'name',
  };

  const dataTree = ref([]);
  const deptName = ref(''); // 树节点 name
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);
  // 数据
  const menuData = ref(null);
  const menuNode = ref(null);
  const treeRef = ref();
  const treeData = ref();
  const menuDataCopy = ref();

  // 搜索
  const filterText = ref('');

  // 弹窗
  const dialogTree = reactive({
    title: '',
    open: false,
    flowName: '',
    typeName: '',
    describe: '',
    type: 0,
    flowType: '',
    loading: false,
  });
  const flow_type = ref([
    {
      value: 'OFFLINE',
      label: '离线',
    },
    {
      value: 'REALTIME',
      label: '实时',
    },
  ]);
  const dialogTreeTwo = reactive({
    open: false,
    flowName: '',
  });
  const dialogTreeThree = reactive({
    open: false,
    flowName: '',
  });
  const dialogAddNode = reactive({
    open: false,
    flowName: '',
  });
  const dialogSubDescription = reactive({
    open: false,
    flowName: '',
  });

  // NavTabs

  /**  打开的所有项目流程 */
  const flowTabs = ref([]);
  /** 当前选中的项目流程-name */
  const activeFlow = ref('');

  /** 当前选中的项目流程 - 对象 */
  const activeFlowItem = ref({});

  watch(filterText, (val) => {
    treeRef.value?.filter(val);
  });

  const isShowButton = ref(true);
  /**
   * 上一次的 data
   */
  const previousData = ref('');

  /**
   * 页签切换
   */
  const nodeDataOffLine = ref();
  let foundObject = null;
  const tabToggle = async (data, shouldOpenChildWorkFlow = true) => {
    syncShow.value = false;
    runStatus.value = false;
    menuVisible.value = false;

    // 如果 data.props.name 和 flowTabs.value 数组包含的对象里的 unique 相等 那么返回  flowTabs.value 的 flowType
    foundObject = flowTabs.value.find((value) => {
      if (value.text === data?.props?.label) {
        return value;
      }
      if (value.text === data.text) {
        return value;
      }
      if (value.flowId == data) {
        return value;
      }
    });
    activeFlowItem.value = foundObject;

    let flowType;
    let flowName;
    if (foundObject) {
      flowType = foundObject.flowType;
      flowName = foundObject.operatorName;
    }
    activeFlow.value = data?.props?.name ? data?.props?.name : data;
    if (foundObject?.pNodeId && shouldOpenChildWorkFlow && flowName != '离线同步') {
      await openChildWorkFlowUtil(foundObject);
      syncShow.value = false;
      isShowButton.value = false;
      nodeLogShow.value = false;
    } else if (flowName && flowName === '离线同步') {
      nextTick(() => {
        activeFlow.value = foundObject.unique;
        nodeDataOffLine.value = {
          id: foundObject.unique,
          flowId: foundObject.parentKey,
        };
        syncShow.value = true;
        isShowButton.value = false;
        nodeLogShow.value = false;
        drawer.value = false;
      });
    } else {
      await openDrae(foundObject?.flowId ? foundObject?.flowId : data);
      syncShow.value = false;
      isShowButton.value = true;

      if (
        foundObject.logMessage.length > 0 &&
        foundObject.logMessage.some((res) => res.flowId == foundObject.flowId)
      ) {
        nextTick(() => {
          nodeLogShow.value = true;
          runStatus.value = foundObject.runStatus;
          webScoketMsg.value = foundObject.logMessage;
        });
      } else {
        nodeLogShow.value = false;
      }
    }
    graph.value.cleanSelection && graph.value.cleanSelection();
  };

  /**
   * 页签移除
   */
  const tabRemoveClick = (targetName) => {
    // flowTabs.value = []
    const tabs = flowTabs.value;
    let activeName = activeFlow.value;
    if (activeName === targetName) {
      tabs.forEach((tab, index) => {
        if (tab.unique === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1];
          if (nextTab) {
            activeName = nextTab.unique;
          } else {
            activeName = '';
          }
          activeFlowItem.value = nextTab || {};
        }
      });
    }
    activeFlow.value = activeName;

    // 找到 打开的父页签 和子页签 如果父页签关闭 那么子页签也要关闭
    const childs = tabs.filter((value) => {
      if (value.parentKey === tabs.find((value) => value.text === targetName)?.flowId) {
        return value;
      }
    });

    // 获取所有子页签的 unique 值
    const childUniques = childs.map((child) => child.unique);

    // 过滤掉关闭的页签和所有子页签
    flowTabs.value = tabs.filter(
      (tab) => tab.unique !== targetName && !childUniques.includes(tab.unique),
    );

    // flowTabs.value = tabs.filter((tab) => tab.unique !== targetName);
    previousData.value = '';
    previousNodeData.value = '';

    if (flowTabs.value.length) {
      const query = {
        props: {
          flowType: flowTabs.value[flowTabs.value.length - 1].flowType,
          id: flowTabs.value[flowTabs.value.length - 1].operatorId
            ? flowTabs.value[flowTabs.value.length - 1].operatorId
            : null,
          name: flowTabs.value[flowTabs.value.length - 1].text,
          label: flowTabs.value[flowTabs.value.length - 1].text,
        },
      };
      tabToggle(query);
    } else {
      // graph.value && graph.value.dispose();
      // graph.value = null;
    }
  };

  /**
   * 增加页签
   * @param unique 页签 name 属性，唯一值
   * @param obj
   */
  async function tabAddClick(unique) {
    // if (typeof unique != "object") return
    let obj = '';
    if (typeof unique === 'undefined') {
      obj = {
        unique: NodeData.value.id, // 本身 id
        key: 'flow-' + NodeData.value.id, // 如果是父级别 则用 flow 子级别则用 node
        parentKey: NodeData.value.flowId,
        program: NodeData.value.program,
        text: NodeData.value.id,
        content: 'New Tab content' + NodeData.value.nodeName,
        pNodeId: NodeData.value.id,
        operatorId: NodeData.value.operatorId,
        operatorName: NodeData.value.operatorName,
      };
    } else {
      obj = {
        flowId: unique.id ? unique.id : unique.flowId, // flowid
        flowType: unique.flowType, // 子无
        unique: unique.text, // 本身 id
        key: 'flow-' + unique.id, // 如果是父级别 则用 flow 子级别则用 node
        parentKey: unique.parentKey ? unique.parentKey : null,
        program: 'FLOW',
        text: unique.text,
        content: 'New Tab content' + unique.id,
        pNodeId: unique.pNodeId ? unique.pNodeId : null,
        operatorId: unique.operatorId ? unique.operatorId : null,
        operatorName: unique.operatorName ? unique.operatorName : null,
        logMessage: [],
        runStatus: false,
      };
    }

    const active = flowTabs.value.find((value) => {
      return value.text == obj.text;
    });

    if (!active) {
      flowTabs.value.push(obj);
    }

    // activeFlow.value = obj.text;
    activeFlowItem.value = active || obj;

    // await getOperatorList(query)
    if (obj.parentKey) {
      await tabToggle(obj);
      // 使用 graph.value 查看画布中是否有节点
      store.setCells(graph.value);
    } else {
      await tabToggle(unique.id ? unique.id : unique.flowId);
    }
  }

  const previousNodeData = ref();
  const expandedList = ref([]);
  const checkedList = ref([]);
  const nodeExpand = (data) => {
    expandedList.value.push(data.id);
  };
  const nodeCollapse = (data) => {
    expandedList.value.splice(expandedList.value.indexOf(data.id), 1); // 收起时删除数组里对应选项
  };

  const handleNodeClick = async (data, node, self) => {
    // node;
    // self;

    // data;
    if (node.level == 1) {
      return;
    }
    if (previousNodeData.value === data) {
      return;
    }
    previousNodeData.value = data;
    deptName.value = data.label;

    const active = flowTabs.value.some((value) => {
      return value.text == data?.text;
    });
    const activeTab = flowTabs.value.find((value) => {
      return value.text == data?.text;
    });
    if (active) {
      const qu = {
        props: {
          flowType: activeTab.flowType,
          id: activeTab.operatorId,
          name: activeTab.text,
          label: activeTab.text,
        },
      };
      // await tabToggle(qu)
    }

    await tabAddClick(data);
  };

  function graphInit() {
    graph.value = new Graph(graphOptions());
    //  这两处的真假同时控制画布的权限 initEvent(false)

    // 根据后端数据进行渲染
    // graph.value.fromJSON(openFlowData.value.flowCanvasJson ? JSON.parse(openFlowData.value.flowCanvasJson) : {})

    // 画布居中
    graph.value.zoomTo(1.0);
    graph.value.centerContent();
    initSidebar();

    // 快捷键以及事件
    initEvent(false);

    // 监听画布变化
    const parse = () => {
      // 获取画布内容
      model.value = JSON.stringify(graph.value.toJSON(), null, 2);
    };
    parse();
    // 监听画布变化
    graph.value.on('cell:change:*', parse);
  }

  /**
 初始化侧边栏
 */
  const stencil = ref();

  function initSidebar() {
    operatorList.value.forEach((s) => {
      s.collapsable = true;
    });
    // 初始化侧边栏
    stencil.value = new Stencil({
      title: '', // 侧边栏标题
      target: graph.value, // 注册到 graph 中
      search(cell, keyword) {
        return cell.label.indexOf(keyword) !== -1;
      }, // 搜索
      placeholder: '搜索算子', // 搜索框提示
      notFoundText: 'Not Found', // 没有找到提示
      collapsable: false, // 是否可折叠
      stencilGraphHeight: 0, // 侧边栏高度
      stencilGraphWidth: 260,
      // 侧边栏分组内容
      groups: operatorList.value.map((group) => ({
        ...group,
        // 此次更改icon?
      })),
    });
    // 注册到 页面
    proxy.$refs.stencilContainer.appendChild(stencil.value.container); // 注册到 div 中

    // 侧边模块

    operatorList.value.forEach((item) => {
      const n = [];
      result.value.map((it) => {
        if (it.pid == item.flowId) {
          n.push(graph.value.createNode(it));
        }
      });
      stencil.value.load(n, item.title);
    });
  }

  // 工具与事件
  function initEvent(check = false) {
    // 工具集
    const pluginsToUse = [
      {
        plugin: Scroller,
        config: {
          enabled: true,
          pageVisible: false,
          pageBreak: false,
          pannable: true,
        },
      },
      {
        plugin: MiniMap,
        config: {
          container: document.getElementById('minimap'),
          width: 150,
          height: 150,
          padding: 2,
          X: 100,
          Y: 50,
          graphOptions: {
            createCellView(cell) {
              // 可以返回三种类型数据
              // 1. null: 不渲染
              // 2. undefined: 使用 X6 默认渲染方式
              // 3. CellView: 自定义渲染
              return undefined;
            },
          },
        },
      },
      {
        plugin: Snapline,
        config: {
          enabled: true,
        },
      },
      {
        plugin: History,
        config: {
          enabled: !check,
        },
      },
      {
        plugin: Clipboard,
        config: {
          enabled: !check,
        },
      },
      {
        plugin: Selection,
        config: {
          enabled: true,
          showNodeSelectionBox: true,
          multiple: !check,
          rubberband: false,
        },
      },
      {
        plugin: Keyboard,
        config: {
          enabled: !check,
          // enabled: true,
        },
      },
    ];

    for (const { plugin, config } of pluginsToUse) {
      graph.value.use(new plugin(config));
    }

    graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.value.getSelectedCells();
      if (cells.length) {
        graph.value.copy(cells);
      }
      return false;
    });

    graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.value.isClipboardEmpty()) {
        const cells = graph.value.paste({ offset: 32 });
        graph.value.cleanSelection();
        graph.value.select(cells);
      }
      return false;
    });

    /*
    // 快捷键实例化
    graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.copy(cells)
      }
      return false
    })

    graph.value.bindKey(['meta+x', 'ctrl+x'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.cut(cells)
      }
      return false
    })

    graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.value.isClipboardEmpty()) {
        const cells = graph.value.paste({ offset: 32 })
        graph.value.cleanSelection()
        graph.value.select(cells)
      }
      return false
    })

    graph.value.bindKey(['meta+z', 'ctrl+z'], () => {
      if (graph.value.canUndo()) {
        graph.value.undo()
      }
      return false
    })

    graph.value.bindKey(['backspace', 'delete'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.removeCells(cells)
      }
    })
  */
    let timer = 0;
    const delay = 200;
    let prevent = false;
    // 事件
    // 点击...
    graph.value.on('cell:click', (e) => {
      timer = setTimeout(async () => {
        if (!prevent) {
          menuVisible.value = false;
          const { node } = e;
          const shape = e.node.shape;
          const $select =
            shape === SETTING_SHAPE_NAME
              ? document.querySelector('.x6-node-selected > rect')
              : document.querySelector(`.x6-node-selected > ${shape}`);
          if (!$select) {
            return;
          }
          const position = $select.getBoundingClientRect && $select.getBoundingClientRect();
          if (!position) {
            return;
          }
          await getNodeUtil(node?.store?.data?.data?.id);
          NNN.value = node;
          drawerTitle.value = node.id;
          drawer.value = true;
        }
        prevent = false;
        graph.value.cleanSelection && graph.value.cleanSelection();
      }, delay);
    });

    graph.value.on('cell:dblclick', async (e) => {
      clearTimeout(timer);
      prevent = true;
      const res = await getNode(e.node.store.data.data.id);
      console.log(res.data.isDrillDown);
      if (res.data.isDrillDown) {
        NodeData.value = res.data;
        tabAddClick(undefined);
      }
      clearTimeout(timer);
    });

    /*
      // 双击动态添加链接桩
      // graph.value.on('node:dblclick', e => {
      //   const { e: event, node } = e
      //   const shape = e.node.shape
      //   // 当前选中元素
      //   const $select =
      //     shape === SETTING_SHAPE_NAME
      //       ? document.querySelector('.x6-node-selected > rect')
      //       : document.querySelector(`.x6-node-selected > ${shape}`)
      //   if (!$select) {
      //     return
      //   }
      //   const position = $select.getBoundingClientRect && $select.getBoundingClientRect()
      //   if (!position) {
      //     return
      //   }
      //   // 鼠标位置
      //   const pageX = event.pageX
      //   const pageY = event.pageY
      //   const zoom = graph.value.zoom()
      //   // 相对节点左上角的位置
      //   const x = (pageX - position.x) / zoom
      //   const y = (pageY - position.y) / zoom
      //   node.addPort({
      //     group: 'absolute',
      //     args: {
      //       // 计算链接桩位置
      //       x: Math.round(x),
      //       y: Math.round(y)
      //     },
      //     silent: false
      //   })
      // })
    */

    // 链接线工具
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge() && !check) {
        cell.addTools([
          'vertices',
          'segments',
          {
            name: 'button-remove',
            args: {
              x: '30%',
              y: '50%',
            },
          },
        ]);
      }
    });
    graph.value.on('cell:mouseleave', ({ cell }) => {
      if (cell.isEdge()) {
        cell.removeTool('vertices');
        cell.removeTool('segments');
        cell.removeTool('button-remove');
      }
    });

    // 链接桩控制
    graph.value.on('node:mouseenter', () => {
      showPorts(true);
    });
    graph.value.on('node:mouseleave', () => {
      showPorts(false);
    });

    // 画布右键菜单
    graph.value.on('cell:contextmenu', (e) => {
      const { e: event, cell } = e;
      // 画布节点
      selector.value = cell;
      menuVisible.value = false;
      menuPosition.value = {
        left: event.pageX + 'px',
        top: event.pageY + 'px',
      };

      //   const isDrillDown = getNode(e.node.store.data.data.id).then((res) => {
      //     return res.data.isDrillDown;
      //   });

      RightCicK.value = e;

      menuVisible.value = true;
    });

    // 点击画布空白区域
    graph.value.on('blank:click', () => {
      graph.value.cleanSelection && graph.value.cleanSelection();
      // 关闭右键菜单
      menuVisible.value = false;
    });

    // 当节点被添加到画布时
    graph.value.on('node:added', async (e) => {
      eCopy.value = e;
      const { e: event, cell, node } = e;
      // 节点放大 3 倍
      // const size = cell.size();
      // cell.size(size.width * 1.5, size.height * 1.5);

      // //图标、文字对应放大\位置改变
      // const imgSetting = cell.attrs.image;
      // const labelSetting = cell.attrs.label;
      // imgSetting.X = 16;
      // imgSetting.Y = 12;
      // imgSetting.width = imgSetting.width ? 32 : 0;
      // imgSetting.height = 32;
      // labelSetting.refX = imgSetting.width ? 58 : 26;

      dialogAddNode.open = true;
      selector.value = cell;
      selectorNode.value = node;
    });

    //  连接线触发
    graph.value.on('edge:connected', (e) => {
      const { edge } = e;
      toCheckUtil(e, edge);
    });
    // 移除连接线触发
    graph.value.on('edge:removed', ({ edge, options }) => {
      if (graph.value != null) {
        const json = graph.value.toJSON();
        if (json.cells.length > 0) {
          const flowId = json.cells[0].data.flowId;
          if (flowId == openFlowData.value.id) {
            debouncedSaveWorkFlowUtil();
          }
        }
      }
      /*    if(foundObject.flowId==openFlowData.value.id){
            } */
    });
  }

  // 当前节点移动后是否有过碰撞，碰撞则连线
  function checkCollisionAndConnect(node) {
    const nodes = graph.value.getNodes();
    nodes.forEach((otherNode) => {
      if (otherNode.id !== node.id) {
        const nodeBBox = node.getBBox();
        const otherNodeBBox = otherNode.getBBox();
        if (isCollision(nodeBBox, otherNodeBBox)) {
          connectNodes(node, otherNode);
        }
      }
    });
  }
  // 是否有节点碰撞
  function isCollision(bbox1, bbox2) {
    return (
      bbox1.x < bbox2.x + bbox2.width &&
      bbox1.x + bbox1.width > bbox2.x &&
      bbox1.y < bbox2.y + bbox2.height &&
      bbox1.y + bbox1.height > bbox2.y
    );
  }

  async function connectNodes(node1, node2) {
    // if (!graph.value.hasEdge(node1, node2)) {
    // 连接节点连接柱
    const port1 = node1.getPorts();
    const port2 = node2.getPorts();

    const quety = {
      sourceAlgId: node2.data.operatorId,
      targetAlgId: node1.data.operatorId,
    };
    const check = await toCheck(quety);
    if (check.code === 200) {
      const edge = graph.value.addEdge({
        source: {
          cell: node2,
          port: port2[1].id,
        },
        target: {
          cell: node1,
          port: port1[0].id,
        },
        attrs: {
          line: {
            fill: 'none',
            connection: true,
            strokeWidth: 3,
            strokeLinecap: 'round',
            stroke: '#1890ff',
            strokeDasharray: 5,
            targetMarker: 'classic',
            style: {
              animation: 'ant-line 30s infinite linear',
            },
          },
        },
      });
      await saveWorkFlowUtil();
    }
    // }
  }

  // 连接桩显示/隐藏
  function showPorts(show) {
    const container = document.getElementById('container');
    const ports = container.querySelectorAll('.x6-port-body');
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden';
    }
  }

  /**
   * tree右击显示菜单
   */
  function showContextMenu(event, data, node) {
    closeContextMenu();
    treeData.value = data;
    // showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    menuData.value = data;
    console.log(data);
    menuNode.value = node;
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  // 添加全局点击事件监听器
  const clickHandler = (event) => {
    menuVisible.value = false;

    if (!menuNode.value || !menuNode.value.contains(event.target)) {
      closeContextMenu();
    }
  };
  const defaultAll = ref(true);
  const treeShow = ref(true);

  // 改变分组弹框
  const changeGroupDialog = ref(null);
  const changeGroupFrom = reactive({ flowGroupId: '' });
  const flowGroupOptions = ref([]);
  const changeGroupFromRules = reactive({
    flowGroupId: [{ required: true, message: '请选择目的分组', trigger: 'blur' }],
  });

  // 菜单事件
  const append = (event, data, node) => {
    showContextMenu(event, data, node);
    dialogTree.title = '流程';
    dialogTree.type = 2;
    dialogTree.open = true;
    console.log(menuData.value);
    menuDataCopy.value = menuData.value;
  };

  // 移动工作流
  const moveGroup = async () => {
    const menuNodeValue = menuNode.value;
    changeGroupFrom.flowId = menuNodeValue.data.id;
    changeGroupFrom.flowGroupId = menuNodeValue.parent.data.id;
    changeGroupDialog.value = true;
    const res = await getGroupOptions({
      workspaceId: workspaceId.value,
    });
    flowGroupOptions.value = res.data.map((item) => {
      if (item.id === changeGroupFrom.flowGroupId) {
        item.disabled = true;
      }
      return item;
    });
  };

  // 提交移动工作流
  const changeGroupCommit = async () => {
    const res = await moveFlow({
      flowId: changeGroupFrom.flowId,
      flowGroupId: changeGroupFrom.flowGroupId,
    });
    if (res.code === 200) {
      changeGroupDialog.value = false;
      getWorkList();
      proxy.$modal.msgSuccess('移动成功');
    }
  };
  // 关闭弹出框
  const closeChangeGroupDialog = () => {
    changeGroupDialog.value = false;
  };

  /**
   * 删除工作流列表数据
   */
  const remove = async () => {
    const menuNodeValue = menuNode.value;
    const treeDataValue = treeData.value;
    console.log(treeData.value);
    const res = await proxy.$modal.confirm('是否确定删除" ' + treeDataValue.text + ' "的数据项？');
    if (res) {
      const tabIndex = flowTabs.value.findIndex((tab) => tab.text === menuNodeValue.data.text);

      if (tabIndex !== -1) {
        tabRemoveClick(menuNodeValue.data.text);
      }

      const deleteFunction = menuNodeValue.level == 1 ? delWorkflowGroup : delWorkflow;
      const response = await deleteFunction(menuNodeValue.data.id);
      if (response.code != 200) {
        return proxy.$modal.msgError('删除失败');
      }
      getWorkList();
      proxy.$modal.msgSuccess('删除成功');
    }
  };

  const nodeTitle = ref();
  const menuDataflowName = ref();
  const rename = () => {
    menuDataCopy.value = menuData.value;
    nodeTitle.value = menuDataCopy.value?.pid ? '流程' : '流程组';
    dialogTreeTwo.flowName = menuDataCopy.value?.text;
    menuDataflowName.value = menuDataCopy.value?.text;
    dialogTreeTwo.description = menuDataCopy.value.description;
    dialogTreeTwo.open = true;
  };
  // 复制工作流
  const coppyTreeItem = async (event, data, node) => {
    showContextMenu(event, data, node);
    const menuNodeValue = menuNode.value;
    const response = await copyFlow({ flowId: menuNodeValue.data.id });
    if (response.code === 200) {
      getWorkList();
      ElMessage.success('复制成功');
    }
  };

  // 查找数据
  const filterNode = (value, data) => {
    if (!value) return true;
    return data && data.text ? data.text.includes(filterText.value) : false;
  };
  // 添加主树
  const addTree = () => {
    dialogTree.title = '流程组';
    dialogTree.type = 1;
    dialogTree.open = true;
  };
  /**
   * 关闭弹窗
   * @param
   *  @returns None
   */
  const cancelDrawer = () => {
    drawer.value = false;
  };
  /**
   * 提交弹窗
   * @param
   *  @returns None
   */
  const submitDrawer = (e) => {
    proxy.$refs.drawerRight.$refs.nodeInfoFromRef.validate((valid) => {
      if (valid) {
        saveNodeUtil(e);
      }
    });
  };
  // 清除弹窗
  const cancelDialogTree = () => {
    dialogTree.title = '';
    dialogTree.flowName = '';
    dialogTree.typeName = '';
    dialogTree.describe = '';
    dialogTree.flowType = '';
    dialogTree.type = 0;
    dialogTree.open = false;
    dialogTree.loading = false;
    proxy.$refs.dataSourceRef.resetFields();
  };

  // 保存弹窗
  const submitForm = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);

    if (!res) return;
    dialogTree.loading = true;

    if (dialogTree.type === 1) {
      const quety = {
        mDescribe: dialogTree.description,
        flowGroupName: dialogTree.flowName,
        workspaceId: workspaceId.value,
      };
      try {
        await saveOrUpdate(quety);
        getWorkList();
        cancelDialogTree();
        proxy.$modal.msgSuccess('成功');
        treeShow.value = false;
        defaultAll.value = false;
        nextTick(() => {
          treeShow.value = true;
        });

        dialogTree.loading = false;
      } catch (error) {
        dialogTree.loading = false;
        return;
      }
    }

    try {
      if (dialogTree.type === 2) {
        const form = {
          flowName: dialogTree.flowName,
          flowType: dialogTree.flowType,
          description: dialogTree.description,
          workspaceId: workspaceId.value,
          flowGroupId: menuDataCopy?.value?.id,
        };
        if (!menuDataCopy?.value?.id) {
          dialogTree.loading = false;
          return proxy.$modal.msgError('未获取到流程组ID');
        }
        const res = await addWorkFlowUtil(form);

        const data = {
          flowId: res.id, // flowid
          flowType: res.flowType, // 子无
          unique: res.flowName, // 本身 id
          key: 'flow-' + res.id, // 如果是父级别 则用 flow 子级别则用 node
          parentKey: res.parentKey ? res.parentKey : null,
          parogram: 'FLOW',
          text: res.flowName,
          content: 'New Tab content' + res.id,
          pNodeId: res.pNodeId ? res.pNodeId : null,
          // operatorId: res.flowGroupId ? res.flowGroupId : null,
          operatorId: null,
        };

        await getWorkList({ add: true });
        await cancelDialogTree();
        // await openDrae(res.id);
        await tabAddClick(data);
      }
    } catch (error) {
      dialogTree.loading = false;
    }
  };

  //   const debouncedSubmitForm = useDebounceFn(submitForm, 500);
  const debouncedSaveWorkFlowUtil = useDebounceFn(saveWorkFlowUtil, 500);
  const cancelDialogTreeTwo = () => {
    dialogTreeTwo.open = false;
    dialogTreeTwo.flowName = '';
    menuDataCopy.value = '';
    dialogTree.description = '';
    dialogTreeTwo.description = '';
    proxy.$refs.dataSourceTwoRef.resetFields();
  };

  const submitFormTwo = async () => {
    const res = await proxy.$refs.dataSourceTwoRef.validate((valid) => valid);
    if (!res) return;
    let resConfirm = true;
    if (flowTabs.value.length > 0) {
      resConfirm = await proxy.$modal.confirm('是否确定修改数据项可能导致已打开的流程关闭？');
    }
    if (!resConfirm) return;
    const quety = {
      id: menuDataCopy?.value?.id,
      flowGroupName: dialogTreeTwo.flowName,
      workspaceId: workspaceId.value,
      mDescribe: dialogTreeTwo.description,
    };
    if (menuDataCopy?.value.pid) {
      await nodeRenameUtil();
    } else {
      await saveOrUpdate(quety);
    }
    getWorkList();
    cancelDialogTreeTwo();
    // 提示成功
    proxy.$modal.msgSuccess('成功');
  };
  const nodeRenameUtil = async () => {
    const quety = {
      id: menuDataCopy?.value?.id,
      flowName: dialogTreeTwo.flowName,
      workspaceId: workspaceId.value,
      description: dialogTreeTwo.description,
    };
    await nodeRename(quety);
  };
  // const cancelDialogTreeThree = () => {
  //   dialogTreeThree.open = false
  //   dialogTreeThree.flowName = ''
  //   menuVisible.value = false
  // }
  // const submitFormThree = () => {
  //   selector.value.attr('text/text', dialogTreeThree.flowName)
  //   menuVisible.value = false
  //   // cancelDialogTreeThree()
  // }
  const cancelDialogAddNode = () => {
    dialogAddNode.open = false;
    btnLoading.value = false;
    dialogAddNode.flowName = '';
    // 判断 是否是点击 showClose 关闭的 如果是需要删除节点
    if (IFsub.value === true) {
      graph.value.removeNode(selector.value);
    }
    IFsub.value = true;
  };

  // 获取文字长度
  function getTextWidth(text, font) {
    // 创建一个canvas元素
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // 设置字体样式
    context.font = font;

    // 测量文本的宽度
    const metrics = context.measureText(text);
    return metrics.width;
  }
  const IFsub = ref(true);
  const btnLoading = ref(false);
  const submitFormAddNode = async () => {
    btnLoading.value = true;
    try {
      await buildTheNode();
      //   await saveWorkFlowUtil();
    } catch (error) {
      btnLoading.value = false;
      return;
    }

    const selectorID = selector?.value?.store?.data?.data?.id;
    const selectorNodeName = selector?.value?.store?.data?.data?.nodeName;
    const flowId = selector?.value?.store?.data?.data?.flowId;

    // if (selector.value.store.data.pid == "ETL") {
    const query = {
      flowType: selector.value.store.data.flowType,
      workspaceId: workspaceId.value,
      pNodeId: selectorID,
      flowName: selectorID,
      pid: flowId,
    };
    // 如果是二层画布 不需要调用
    // if (!selector.value.store.data.pid) {
    await addWorkFlowUtil(query);
    // }
    await getNodeUtil(selectorID);
    const textSetting = selector.value.attrs;
    let textEed = selectorNodeName || selectorID;
    const textLength = getTextWidth(textEed);
    let isWrap = false;
    if (textLength > 150 && textEed.length <= 20) {
      //   textEed = textEed.slice(0, 10) + '...';
      textEed = textEed.slice(0, 12) + '\n' + textEed.slice(12, textEed.length);
      isWrap = true;
    } else if (textLength > 150 && textEed.length > 20) {
      textEed = textEed.slice(0, 22) + '\n' + textEed.slice(22, textEed.length);
      isWrap = true;
    }
    // textSetting.text.text = `${textSetting.text.text}\n(${textEed})`;
    textSetting.text.text = `${textSetting.text.text}`;
    textSetting.titleLabel.text = `${textEed}`;
    const size = selector.value.size();

    // //图标、文字对应放大\位置改变
    const imgSetting = selector.value.attrs.image;
    const titleSetting = selector.value.attrs.titleLabel;
    const labelSetting = selector.value.attrs.label;
    imgSetting.X = 12;
    imgSetting.Y = 40;
    imgSetting.width = imgSetting.width ? 18 : 0;
    imgSetting.height = 18;
    labelSetting.refX = imgSetting.width ? 40 : 26;
    labelSetting.refY = 50;
    labelSetting.fill = '#8C8C8C';

    titleSetting.fill = '#000000';
    titleSetting.refY = isWrap ? 24 : 24;
    // titleSetting.fill = '#000000';
    // {
    //   fill: '#000000',
    //   fontSize: 14,
    //   refX: 40,
    //   refY: 16,
    //   textAnchor: 'left',
    //   height: 34,
    //   alignmentBaseline: 'middle',
    // };
    selector.value.size(size.width * 1.5, size.height * 1.5);
    // graph.value.drawCell(selector.value, { deep: true })//new graph 的方式不行，后面优化的时候看看其他对象

    drawer.value = true;
    // }
    // addWorkFlowUtil()

    IFsub.value = false;
    dialogAddNode.open = false;
    dialogAddNode.flowName = '';
    // 保存加入节点文字改变
    await saveWorkFlowUtil();
    checkCollisionAndConnect(selectorNode.value);
  };
  //   const debouncedSubmit = useDebounceFn(submitFormAddNode, 500);
  const cancelDialogSubDescription = () => {
    dialogSubDescription.open = false;
    dialogSubDescription.flowName = '';
  };

  const submitFormSubDescription = () => {
    putDrawUtil(dialogSubDescription.flowName);

    cancelDialogSubDescription();
  };
  const putDraw = async (e) => {
    // 判断画布是否是只读状态
    if (CanvasActions.value == false) return;

    dialogSubDescription.open = true;
  };
  const RightCicK = ref();

  const starDraw = async (e) => {
    console.log(e);

    // 判断画布是否是只读状态
    if (CanvasActions.value == false) return;

    await collectUtil();
    // await cancelCollectUtil()
  };

  const collectUtil = async (e) => {
    // ,flowId,operationType 1 收藏 2 取消收藏
    // 找到当前标签的 flowId
    const foundObject = flowTabs.value.find((value) => {
      return value.unique == activeFlow.value;
    });

    const flowId = foundObject?.flowId;

    const res = await collect({
      user: 'admin',
      flowId,
      operationType: 'collect',
    });
    if (res.code == 200) {
      proxy.$modal.msgSuccess('成功');
    } else {
      proxy.$modal.msgError('失败');
    }
  };
  /**
   * 画布右击显示菜单 与 事件
   */
  function onMenuClick(select) {
    const [option] = select;
    switch (option) {
      case 'deploy': {
        menuVisible.value = false;
        // 打开画布
        drawerTitle.value = RightCicK.value?.node.store?.data?.data?.algorithmName;
        drawer.value = true;
        getNodeUtil(RightCicK.value?.node.store?.data?.data?.id);
        break;
      }

      case 'name': {
        dialogTreeThree.open = true;
        break;
      }
      case 'run': {
        startFlowData.flowForm.flowName = activeFlow.value;
        startFlowData.flowForm.nodeName = RightCicK.value?.node.store?.data?.data.nodeName;
        startFlowData.flowForm.flowId = RightCicK.value?.node.store?.data?.data.flowId;
        startFlowData.flowForm.startNode = RightCicK.value?.node.store?.data?.data.id;

        isSingleNode.value = false;
        menuVisible.value = false;
        startFlowData.dialogVisible = true;
        break;
      }
      case 'remove': {
        graph.value.removeNode(selector.value);
        menuVisible.value = false;
        debouncedSaveWorkFlowUtil();
        break;
      }
    }
  }

  /**
   * 页面刷新或关闭时
   */
  function beforeunload() {
    // 提示用户是否离开
  }

  // beforeRouteLeave() {
  // 提示用户是否离开
  // }
  // 选中的id对应树数据
  const getTreeDataById = (data, targetId) => {
    // let returnData;
    // lists.forEach((list) => {
    //   if (list.id === id) {
    //     returnData = list;
    //     // throw new Error('Break');
    //   } else {
    //     if (list.children && list.children.length > 0) {
    //       const hasData = getTreeDataById(list.children, id);
    //       if (hasData) {
    //         returnData = hasData;
    //       }
    //     }
    //   }
    // });
    // return returnData;
    for (const node of data) {
      if (node.id === targetId) {
        return node;
      }

      if (node.children && node.children.length > 0) {
        const result = getTreeDataById(node.children, targetId);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  onMounted(async () => {
    // connectWebSocket();
    window.addEventListener('beforeunload', beforeunload());
    window.addEventListener('click', clickHandler);
    await getWorkList();
    await graphInit();
    await getinfoUti();
    // await onWebSocket();
    // autofit.init({
    //   designHeight: 1080,  //项目初始的分辨率，一般就是你开发电脑的屏幕分辨率
    //   designWidth: 1920,  //项目初始的分辨率，一般就是你开发电脑的屏幕分辨率
    //   renderDom: ".app-container-box",   //App 最外层的 id 名，一般都为 ap
    //   resize: true        //是否监听 resize 事件，默认是 true
    // }, false)

    // 如果有路由传过来ID，则选中对应的节点
    if (route.query.id) {
      expandedList.value = [];
      checkedList.value = [];
      expandedList.value.push(route.query.flowGroupId);
      checkedList.value.push(route.query.id);
      const choseData = getTreeDataById(dataTree.value, route.query.id);
      handleNodeClick(choseData, { level: 2 });
      proxy.$refs.treeRef.setCurrentKey(route.query.id);
    }
  });

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', beforeunload());
    // graph.value && graph.value.dispose()
    // graph.value = null
    // autofit.off()
  });

  onUnmounted(() => {
    window.removeEventListener('click', clickHandler);
  });

  /**
   * 以显示或隐藏模具
   *
   * @param {type} None
   * @return {type} None
   */
  function showStencilClick(e) {
    showStencil.value = !showStencil.value;
  }

  /**
   * 根据给定事件切换画布操作.
   *
   * @param {Event} e - 触发切换操作的事件.
   * @return {undefined} None
   */
  async function toggleCanvasActions(e) {
    if (e) {
      await unlockFlow({ flowId: previousNodeData.value.id });
      await addPort();
    } else {
      await lockFlow({ flowId: previousNodeData.value.id });
      await removePort();
    }
    CanvasActions.value = e;
    // await graph.value.dispose()
    // await graphInit()
    // initEvent(!CanvasActions.value)
  }

  /**
   * 全屏切换.
   *
   * @param {type} e - 事件对象.
   * @return {type} None
   */
  function FullCilck(e) {
    toggle();
  }

  /**
   * 异步获取工作区列表。
   *
   * @param {Object} e - 事件对象。.
   * @return {Promise<void>} 检索工作区列表时解析的 promise
   */
  // async function getList(e) {
  //   workspaceId.value = e.selectedWorkspaceId
  //   await getWorkList()
  // }

  /**
   * 使用给定的参数检索任务栏表。
   *
   * @param {*}  None
   * @return {Promise<void>} None
   */
  async function getWorkList(options = {}) {
    const { add = false } = options;
    if (workspaceId.value) {
      // 清空数据  关闭已打开画布 画布置空
      //   console.log(dialogTree);
      //   if (dialogTree.type === 0) {
      //     flowTabs.value = flowTabs.value.filter((item) => item.flowId !== menuDataCopy?.value?.id);
      //     tabToggle(flowTabs.value[0]);
      //   }
      // graph.value && graph.value.dispose()
      // graph.value = null

      const res = await getProjTreeMenu({
        workspaceId: workspaceId.value,
        hasFlow: 1,
        user: userName.value,
        filterType: dataTreeType.value,
      });
      dataTree.value = res.data;
      //   if (res.code == 200) {
      //     dataTree.value = res.data;
      //     // 关闭所有打开的 tab
      //     flowTabs.value = [];
      //   }
      !add && closeChildren();
    }
  }
  const closeChildren = (item) => {
    if (flowTabs.value.length <= 0) return;
    const tabs = flowTabs.value;
    const targetName = menuDataflowName.value;

    const chiles = tabs.filter((value) => {
      if (value.parentKey === tabs.find((value) => value.text === targetName)?.flowId) {
        return value;
      }
    });
    // 获取所有子页签的 unique 值
    const childUniques = chiles.map((child) => child.unique);

    // 过滤掉关闭的页签和所有子页签
    flowTabs.value = tabs.filter(
      (tab) => tab.unique !== targetName && !childUniques.includes(tab.unique),
    );

    previousData.value = '';
    previousNodeData.value = '';

    if (flowTabs.value.length) {
      const query = {
        props: {
          flowType: flowTabs.value[flowTabs.value.length - 1].flowType,
          id: flowTabs.value[flowTabs.value.length - 1].operatorId
            ? flowTabs.value[flowTabs.value.length - 1].operatorId
            : null,
          name: flowTabs.value[flowTabs.value.length - 1].text,
          label: flowTabs.value[flowTabs.value.length - 1].text,
        },
      };
      tabToggle(query);
    } else {
      // graph.value && graph.value.dispose();
      // graph.value = null;
    }
    menuDataflowName.value = '';
  };
  /**
   * 获取算子树
   * @param {OBJECT} 流程类型_节点 ID
   * @returns  None
   */
  async function getOperatorList(u) {
    if (u) {
      const res = await getOperatorTree(u);
      res?.data && formatData(res.data);
      initSidebar();
    }
  }

  const operatorList = ref([]);
  const result = ref([]);

  /**
   * 格式化数据
   * @param {Array} data - 待格式化的数据
   * @returns {Array} 格式化后的数据
   */
  function formatData(data) {
    operatorList.value = data?.map((item) => ({
      flowId: item.id,
      title: item.name,
      name: item.name,
    }));

    result.value = data.flatMap((item) => {
      if (item.children) {
        return item.children.map((child) => ({
          inherit: 'rect', // 继承自 Shape.Rect
          width: 115, // 默认宽度
          height: 44, // 默认高度
          markup: [
            {
              tagName: 'rect',
              selector: 'body',
            },
            {
              tagName: 'image',
            },
            {
              tagName: 'text',
              selector: 'titleLabel',
            },
            {
              tagName: 'text',
              selector: 'label',
            },
          ],
          attrs: {
            body: {
              rx: 6, // 圆角矩形
              ry: 6,
              strokeWidth: 1,
              fill: '#F7F8FB',
              stroke: '#c6d3fa',
            },
            // 字体
            label: {
              fill: '#000000',
              fontSize: 10.5,
              //   refX: child.icon ? 40 : 14,
              //   textAnchor: child.icon ? 'left' : 'center',
              refX: 46,
              refY: 24,
              textAnchor: 'left',
            },
            // title 字体
            titleLabel: {
              fill: 'rgba(255,255,255,0)',
              fontSize: 12,
              //   refX: child.icon ? 40 : 14,
              //   textAnchor: child.icon ? 'left' : 'center',
              refX: 12,
              refY: 20,
              textAnchor: 'left',
            },
            image: {
              fill: '#000000',
              //   width: child.icon ? 26 : 0,
              width: 32,
              height: 32,
              X: 8,
              Y: 6,
              'xlink:href': child.icon || autoTreeIcon,
            },
            style: {
              animation: 'ant-line 30s infinite linear',
            },
          },

          ports: {
            ...ports,
            items: [
              {
                group: 'left',
              },
              {
                group: 'right',
              },
            ],
          },
          label: child.name,
          operatorId: child.id,
          pid: child.pid,
          flowType: child.flowType,
        }));
      } else {
        return [];
      }
    });
  }

  /**
   * 构建节点
   * @returns  None
   */
  async function buildTheNode() {
    const re = await getUUid();
    const query = {
      flowId: previousNodeData.value.id,
      operatorId: selector.value.store.data.operatorId,
      nodeName: dialogAddNode.flowName ? dialogAddNode.flowName : re.data,
    };
    const res = await buildFlowNode(query);

    const queryData = {
      algorithmName: res.data.operatorName,
      operatorId: res.data.operatorId,
      flowId: res.data.flowId,
      id: res.data.id,
      isCustom: res.data.isCustom,
      isDrillDown: res.data.isDrillDown,
      isLogOutput: res.data.isLogOutput,
      isReportOutput: res.data.isReportOutput,
      nodeName: res.data.nodeName,
      outputProperty: res.data.outputProperty,
      parentFlowId: res.data.parentFlowId,
      parentNodeId: res.data.parentNodeId,
      program: res.data.program,
    };

    selector.value.store.data.data = queryData;
  }

  /**
   * 保存流程
   * @param {*} None
   * @returns None
   */
  async function saveWorkFlowUtil(flag = false) {
    const openFlowDataCopy = openFlowData.value;
    const query = {
      ...openFlowDataCopy,
      flowCanvasJson: JSON.stringify(graph.value.toJSON()),
    };
    const res = await saveWorkFlow(query);
    if (res.code == 200 && flag) {
      proxy.$modal.msgSuccess('成功');
    }
  }

  const openFlowData = ref('{"cells": []}');

  const projectCodeOfDs = ref(null);
  const processCode = ref(null);
  /**
   * 打开流程
   * @param {*} id
   * @returns None
   */
  async function openDrae(data) {
    if (!data) return;
    processCode.value = null;
    const res = await openWorkFlow(data?.id ? data?.id : data);
    openFlowData.value = res.data;
    previousNodeData.value = openFlowData.value;
    if (openFlowData.value) {
      const lockStatus = openFlowData.value.lockStatus;
      if (lockStatus == 0 || !lockStatus) {
        CanvasActions.value = true;
      } else {
        CanvasActions.value = false;
      }
    }
    await reloadGrap();
    projectCodeOfDs.value = res.data.projectCodeOfDs;
    processCode.value = res.data.processCodeSingle;
    // 告警配置参数
    objForWarn.value.bizId = res.data.id;
    objForWarn.value.userId = userId.value;
    objForWarn.value.workspaceId = workspaceId.value;
    objForWarn.value.createProcessUser = res.data.createUser;
    subScribe.value = res.data.subscribe;

    // 需要重置刷新次数
    reGetTimes.value = 0;
    if (res.data.projectCodeOfDs && res.data.processCodeSingle) {
      getSingleTaskRecord(res.data.processCodeSingle);
    }
  }

  async function reloadGrap() {
    nextTick(async () => {
      activeFlow.value = openFlowData.value.flowName;
      const query = {
        flowType: openFlowData.value.flowType,
        id: activeFlowItem.value?.operatorId,
      };
      await getOperatorList(query);
      graph.value.fromJSON(
        openFlowData.value.flowCanvasJson ? JSON.parse(openFlowData.value.flowCanvasJson) : {},
      );
      if (!CanvasActions.value) {
        removePort();
      }
      await graph.value.centerContent();
    });
  }

  async function addPort() {
    const cells = graph.value.getCells();
    cells.forEach((s) => {
      if (s.shape != 'edge') {
        const portList = s.getPorts();
        if (portList.length > 0) {
          if (portList.length > 1) {
            return;
          }
          const port = portList[0];
          if (port.group == 'left') {
            s.addPort({
              ...ports,
              group: 'right',
            });
          } else {
            s.addPort({
              ...ports,
              group: 'left',
            });
          }
        } else {
          s.addPort({
            ...ports,
            group: 'left',
          });
          s.addPort({
            ...ports,
            group: 'right',
          });
        }
      }
    });

    // 增加移除连接线按钮
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge()) {
        cell.addTools([
          'vertices',
          'segments',
          {
            name: 'button-remove',
            args: {
              x: '30%',
              y: '50%',
            },
          },
        ]);
      }
    });
  }

  async function removePort() {
    graph.value.on('edge:connected', () => false);

    const cells = graph.value.getCells();
    cells.forEach((s) => {
      if (s.shape != 'edge') {
        const portList = s.getPorts();
        if (portList.length) {
          portList.forEach((port) => {
            const canRemove = checkEdges(s, port);
            if (!canRemove) {
              s.removePort(port.id);
            }
          });
        }
      }
    });
    // 移除连接线按钮
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge()) {
        cell.removeTool('vertices');
        cell.removeTool('segments');
        cell.removeTool('button-remove');
      }
    });
  }

  function checkEdges(cell, port) {
    const connectedEdges = graph.value.getConnectedEdges(cell);
    // 遍历端口
    // 判断是否被连接
    const isPortConnected = connectedEdges.some((edge) => {
      const sourcePortId = edge.getSourcePortId();
      const targetPortId = edge.getTargetPortId();
      return sourcePortId === port.id || targetPortId === port.id;
    });
    return isPortConnected;
  }

  /**
   * 保存流程
   * @param {Object} form
   * @returns   Object res - 保存的流程
   */
  async function addWorkFlowUtil(form) {
    const res = await addWorkFlow(form);
    return res.data;
  }

  /**
   * 获取节点实用进程.
   *@param {String} id
   * @return {Promise} None.
   */
  async function getNodeUtil(id) {
    const res = await getNode(id);
    NodeData.value = res.data;
  }

  /**
   * 保存节点实用进程功能。
   *
   * @param {Object} data
   * @return {Promise} None
   */
  async function saveNodeUtil(data) {
    const res = await saveNode(data);
    if (res.code == 200) {
      proxy.$modal.msgSuccess('成功');
      await tabToggle(activeFlowItem.value);
    } else {
      proxy.$modal.msgError('失败');
    }
  }

  /**
   * 执行 openChildWorkFlowUtil 函数的异步操作。
   *
   * @return {Promise<void>} 当函数执行完成时解析的 Promise 对象。
   */
  /* async function openChildWorkFlowUtil(data) {


  let res = ''
  if (NodeData.value.program != 'OFFLINE_ALG') {


    res = await openChildWorkFlow(data ? data.pNodeId : NodeData.value.id)
  }
  else if (NodeData.value.operatorName == '离线同步') {
    res = {
      code: 200,
      data: {
        flowCanvasJson: '{"cells": []}',
        // program: 'OFFLINE_SYNC',
        // flowType: 'OFFLINE_SYNC',
        id: NodeData.value.nodeName,
        pid: NodeData.value.flowId,
        pNodeId: NodeData.value.id,
        // operatorId: NodeData?.value?.operatorId,
        // operatorName: NodeData?.value?.operatorName

      }
    }
  }
  else {
    res = {
      code: 200,
      data: {
        flowCanvasJson: '{"cells": []}',
        flowName: '离线算法',
        id: 'OFFLINE_ALG',
        lockStatus: 0,
        pid: 'ETL',
        program: 'OFFLINE_ALG',
      }
    }
  }

  openFlowData.value = res.data
  previousNodeData.value = openFlowData.value

  let query = {
    flowId: previousNodeData.value.id,
    //text: NodeData?.value?.nodeName ? NodeData?.value?.nodeName : data.text,
    text: data?.text ? data?.text: NodeData?.value?.nodeName,

    flowType: res.data.flowType,
    parentKey: previousNodeData.value.pid,
    pNodeId: previousNodeData.value.pNodeId,
    //operatorId: NodeData?.value?.operatorId ? NodeData?.value?.operatorId : data.operatorId,
    //operatorName: NodeData?.value?.operatorName ? NodeData?.value?.operatorName : data.operatorName
    operatorId: data?.operatorId?  data?.operatorId:NodeData?.value?.operatorId,
    operatorName: data?.operatorName? data?.operatorName:NodeData?.value?.operatorName

  }

  // 判断 flowTabs.value 中是否 有 NodeData.value.flowid 已经存在 如果已经纯在说明是第二次 执行切换
  let active = flowTabs.value.some((value) => {
    return value.flowId == openFlowData.value.id;
  });
  let activeTab = flowTabs.value.find((value) => {
    return value.flowId == openFlowData.value.id;
  });

  if (active) {
    let qu = {
      props: {
        flowType: activeTab.flowType,
        id: activeTab.operatorId,
        name: activeTab.text,
        label: activeTab.text
      }
    }
    if (activeFlow.value != activeTab.text) {
      activeFlow.value = activeTab.text;
    }

  }


  await tabAddClick(query)
  cancelDrawer()

  if (query.operatorName == '离线同步') {
    syncShow.value = true
  }
  reloadGrap();
  isShowButton.value = false
} */
  async function openChildWorkFlowUtil(data) {
    const res = await openChildWorkFlow(data ? data.pNodeId : NodeData.value.id);
    openFlowData.value = res.data;
    previousNodeData.value = openFlowData.value;
    drawer.value = false;
    await reloadGrap();
  }

  /**
   * 检查数据
   * @param {Object} data
   * @return {Promise} None
   */
  async function toCheckUtil(e, edge) {
    try {
      const quety = {
        sourceAlgId: e.view.sourceView.cell.store.data.data.operatorId,
        targetAlgId: e.view.targetView.cell.store.data.data.operatorId,
      };
      await toCheck(quety);
      await saveWorkFlowUtil();
    } catch (error) {
      graph.value.removeEdge(edge.id);
    }
  }

  async function stopDrawUtil() {
    if (!webScoketMsg.value.length) return;
    runStatus.value = false;
    proxy.$refs.nodelogRef.stopTask(webScoketMsg.value[0].id);
    // try {
    //   const res = await stopDraw({ flowId: previousNodeData.value.id });
    //   if (res.code == 200) {
    //     proxy.$modal.msgSuccess('成功');
    //     // webScoketMsg.value = '' ? nodeLogShow.value = false : nodeLogShow.value = true
    //   } else {
    //     proxy.$modal.msgError('失败');
    //   }
    // } catch (error) {
    //   runStatus.value = true;
    //   //  移除连接线
    //   // proxy.$modal.msgError('失败')
    // }
  }

  async function runDrawUtil() {
    startFlowData.dialogVisible = true;
    isSingleNode.value = true;
    isRun.value = true;

    // const res = await runDraw({ flowId: previousNodeData.value.id })
    // if (res.code == 200) {
    //   proxy.$modal.msgSuccess('成功')
    //   webScoketMsg.value = '' ? nodeLogShow.value = false : nodeLogShow.value = true

    // } else {
    //   proxy.$modal.msgError('失败')
    // }
  }

  const reGetTimes = ref(0);

  const getSingleTaskRecord = async (code = null) => {
    const query = {
      workSpaceId: workspaceId.value,
      processDefineCode: code,
    };
    const res = await getSingleRunInstance(query);
    // 如果2s没获取到日志或者运行状态为运行中则需要定时器，不限制次数，直至状态不为运行中
    if (!res.data) {
      if (reGetTimes.value < 3) {
        setTimeout(() => {
          getSingleTaskRecord(code);
          reGetTimes.value++;
        }, 2000);
      } else {
        runStatus.value = false;
        webScoketMsg.value = [];
        // !stateType && proxy.$modal.msgError('任务实例不存在，请检查工作流配置');
      }
    }
    if (res.data) {
      const foundObject = flowTabs.value.find((value) => {
        return value.unique == activeFlow.value;
      });
      // 当前界面上的工作流标签名称
      const unique = foundObject?.unique;
      // 接口中获取当前工作流的名称
      const name = res.data.name.split('-single-')[0];
      // 在当前标签下才更新显示日志数据
      if (unique == name) {
        nodeLogShow.value = true;
        webScoketMsg.value = [res.data];
      }
      // nodeLogShow.value = true;
    }
  };

  async function putDrawUtil(data) {
    const res = await putDrawRes({
      flowId: previousNodeData.value.id,
      note: data,
    });
    res?.code == 200 ? proxy.$modal.msgSuccess(res.msg) : proxy.$modal.msgError(res.msg);
  }

  // async function getLogUtil(e) {
  //   const res = await getLog({ taskInstanceId: e });
  //   if (res.code == 200) {
  //     nodeLogDrawer.value = true;
  //     LogData.value = res.data;
  //   }
  // }

  const closeNodeLog = () => {
    nodeLogShow.value = false;
  };

  const LogTitle = ref('');
  const openLog = ref(false);
  const LogContentList = ref('');
  const logStrB = ref();
  const defaultExpandedKeys = [];

  const refreshDataForLog = async (processDefinitionCode) => {
    getSingleTaskRecord(processDefinitionCode);
  };

  const viewOrCloseLog = () => {
    webScoketMsg.value = [];
    if (!nodeLogShow.value) {
      nodeLogShow.value = true;
      if (!processCode.value) return;
      getSingleTaskRecord(processCode.value);
    } else {
      nodeLogShow.value = false;
    }
  };

  // 查看日志弹窗
  const getLogUtil = (id) => {
    LogTitle.value = '查看日志';
    openLog.value = true;
    const query = {
      workSpaceId: workspaceId.value,
      id,
    };
    getWorkflowILog(query);
  };

  // 获取日志
  async function getWorkflowILog(params) {
    await getTaskList(params).then((res) => {
      LogContentList.value = res.data;
    });
  }

  /**
   * 关闭日志
   */
  const openLogClose = () => {
    openLog.value = false;
    logStrB.value = '';
  };
  // 日志改变
  const handleNodeClickForLog = (r) => {
    logStrB.value = '';
    processTaskInstance(r);
  };

  // 展开日志第一个
  watch(
    () => LogContentList.value,
    () => {
      if (LogContentList.value.length > 0) {
        console.log('LogContentList.value[0]', LogContentList.value[0]);
        defaultExpandedKeys.value = [LogContentList.value[0].lineNum];
        handleNodeClickForLog(LogContentList.value[0]);
        nextTick(() => {
          proxy.$refs.logTree.setCurrentKey(LogContentList.value[0].taskLogNo);
        });
      }
    },
  );

  /**
   * 日志限制增量
   * @type {number}
   */
  const LOG_LIMIT_INCREMENT = 1000;

  /**
   * 任务实例日志数组
   * @type {Array}
   */
  const taskInstanceLogs = [
    {
      lineTotal: 0,
      message: '',
    },
  ];

  /**
   * 创建日志映射
   * @param {number} lineTotal - 总行数
   * @param {string} message - 消息
   * @returns {Object} 日志映射
   */
  function createLogMap(lineTotal, message) {
    return { lineTotal, message };
  }

  /**
   * 判断是否没有更多日志
   * @param {number} lineNum - 行数
   * @param {string} logStr - 日志字符串
   * @returns {boolean} 是否没有更多日志
   */
  function isNoMoreLog(lineNum, logStr) {
    return (
      (lineNum == 1 && logStr == '$日志终点$') || logStr.includes('Roll view log error: connect to')
    );
  }

  /**
   * 处理任务实例
   * @param {Object} taskInstance - 任务实例
   */
  async function processTaskInstance(taskInstance) {
    console.log('taskInstance', taskInstance);

    if (!taskInstance.host) {
      logStrB.value = '获取日志失败，请检查自定义参数配置';
      taskInstanceLogs.push(createLogMap(0, '错误的日志'));
      return;
    }

    const taskId = taskInstance.id;
    let limit = 0;
    let skipLineNum = 0;
    let lineNum = 0;
    let taskInstanceLog = {};
    let messageInfo = '';
    do {
      limit += LOG_LIMIT_INCREMENT;
      skipLineNum += lineNum;

      try {
        taskInstanceLog = await getTaskInstanceLog(taskId, limit, skipLineNum);
        lineNum = taskInstanceLog.data.lineNum;
        logStrB.value += taskInstanceLog.data.message;
        messageInfo = taskInstanceLog.data.message;
      } catch (error) {
        console.error(error);
      }
      console.log(`已查到工作流实例 [] 下任务实例 [${taskId}]，本次行数：${lineNum}`);
    } while (!isNoMoreLog(lineNum, messageInfo));

    taskInstanceLogs.push(createLogMap(skipLineNum, logStrB.value));
  }
  const filterNameUti = async () => {
    // // workspaceId, user, filterType
    // const query = {
    //   workspaceId: workspaceId.value,
    //   user: userName.value,
    //   filterType: dataTreeType.value,
    // };
    // const res = await filterName(query);

    // if (res.code == 200) {
    //   dataTree.value = res.data;
    //   // 关闭所有打开的 tab
    // }
    getWorkList();
  };

  const userName = ref();
  const tenantId = ref(null);
  const userId = ref(null);
  const tenantList = ref([]);
  const isAdmin = ref(true);
  const getinfoUti = async () => {
    const res = await getInfo();
    userName.value = res.data.user.userName;
    userId.value = res.data.user.userId;
    email.value = res.data.user.email;
    tenantId.value = res.data.user.tenantId;

    if (res.data.user.userType !== 'sys_user') {
      isAdmin.value = false;
    } else {
      isAdmin.value = true;
      const tenantRes = await getTenantList();
      tenantList.value = tenantRes.data;
    }
    console.log('userName.value', userName.value);
  };
  // watch 监听 workspaceId 的变化 重新获取数据
  watch(workspaceId, () => {
    getWorkList();
    // 关闭页签
    flowTabs.value = [];
    // 抽屉关闭
    drawer.value = false;
  });

  const closeAll = () => {
    flowTabs.value = [];
    activeFlow.value = '';
  };

  // 告警订阅功能模块
  const warnForm = ref({
    subscribeType: '1',
    checkList: ['站内信'],
  });
  const onCheck = ref(false);
  const openWarn = ref(false);
  const email = ref(null);
  const objForWarn = ref({});
  const subScribe = ref(null);
  const ruleForWarn = ref({
    subscribeType: [{ required: true, message: '请选择告警策略', trigger: 'change' }],
    checkList: [{ required: true, message: '请选择通知策略', trigger: 'change' }],
  });

  const handleSubscribe = async () => {
    await classify();
    if (!email.value) {
      onCheck.value = true;
      // proxy.$modal.msgWarning('请前往个人用户中心绑定邮箱')
      ElMessage({
        message: '未绑定邮箱，无法使用邮箱通知',
        type: 'warning',
        customClass: 'messageIndex',
      });
    } else {
      onCheck.value = false;
    }
    if (subScribe.value && subScribe.value.alertType == '1') {
      warnForm.value.checkList = ['邮箱'];
    }
    if (subScribe.value && subScribe.value.alertType == '3') {
      warnForm.value.checkList = ['站内信'];
    }
    if (subScribe.value && subScribe.value.alertType == '1,3') {
      warnForm.value.checkList = ['站内信', '邮箱'];
    }
    warnForm.value.subscribeType = subScribe.value ? `${subScribe.value.subscribeType}` : '1';

    openWarn.value = true;
  };

  const classify = async () => {
    await setTimeout(async () => {
      await nextTick(async () => {
        await document
          .querySelectorAll(
            '.app-wrapper .el-radio-group > label > span, .el-overlay .el-radio-group > label > span',
          )
          .forEach((element) => {
            element.style.background = 'none';
          });
      });
    }, 100);
  };

  // 提交设置
  const submitFormWarn = async () => {
    const valid = await proxy.$refs.warnFormRef.validate((valid) => valid);
    if (!valid) return;
    if (warnForm.value.checkList.length === 2) {
      objForWarn.value.alertType = '1,3';
    } else if (warnForm.value.checkList.length === 1) {
      if (warnForm.value.checkList.indexOf('站内信') !== -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    }
    objForWarn.value.subscribeType = warnForm.value.subscribeType;
    if (subScribe.value) {
      objForWarn.value.id = subScribe.value.id;
      updateSubscribe(objForWarn.value).then((res) => {
        subScribe.value.subscribeType = objForWarn.value.subscribeType;
        subScribe.value.alertType = objForWarn.value.alertType;
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('更新订阅成功');
        }
      });
    } else {
      addSubscribe(objForWarn.value).then((res) => {
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('订阅成功');
        }
      });
    }
  };
  // 取消设置
  const cancelWarn = () => {
    warnForm.value = {
      subscribeType: '1',
      checkList: ['站内信'],
    };
    proxy.resetForm('warnFormRef');
    // openWarn.value = false;
  };

  const showTaskStatus = (type) => {
    return taskStatus.find((item) => item.value === type)?.label;
  };

  const taskStatus = [
    { label: '停止', value: 'STOP' },
    {
      label: '运行中',
      value: 'RUNNING_EXECUTION',
    },
    {
      label: '成功',
      value: 'SUCCESS',
    },
    {
      label: '失败',
      value: 'FAILURE',
    },
    {
      label: '终止',
      value: 'KILL',
    },
  ];
  const changeStatus = (status) => {
    runStatus.value = status;
  };
  // ------------------------------------------------startFlow
  // #region
  const currentTime = ref(new Date().toISOString().slice(0, 19).replace('T', ' '));

  // 判断是否是 单节点 还是工作流
  const isSingleNode = ref(false);
  const startFlowData = reactive({
    dialogVisible: false,
    flowForm: {
      failureStrategy: 'CONTINUE',
      taskDependType: 'TASK_POST',
      execType: false,
      runMode: 'RUN_MODE_SERIAL',
      timeType: '2',
      scheduleTime: currentTime.value,
    },
    rules: {
      failureStrategy: [{ required: false, message: '请选择失败策略', trigger: 'change' }],
      taskDependType: [{ required: false, message: '请选择节点执行', trigger: 'change' }],
      execType: [{ required: false, message: '请选择补数类型', trigger: 'change' }],
      runMode: [{ required: false, message: '请选择执行方式', trigger: 'change' }],
      scheduleTime: [{ required: true, message: '请选择调度时间', trigger: 'change' }],
      expectedParallelismNumber: [{ required: true, message: '请选择并行度', trigger: 'change' }],
    },
  });

  const closeStartFlowDialog = () => {
    startFlowData.dialogVisible = false;
    startFlowData.flowForm = {
      failureStrategy: 'CONTINUE',
      taskDependType: 'TASK_POST',
      execType: false,
      runMode: 'RUN_MODE_SERIAL',
      timeType: '2',
      scheduleTime: currentTime.value,
    };
    proxy.$refs.startFlowFormRef?.resetFields();
  };
  const submitStartFlow = async () => {
    const valid = await proxy.$refs.startFlowFormRef.validate((valid) => valid);
    if (!valid) return;
    startFlowUtil();
  };
  const startFlowUtil = async () => {
    //   if (!startFlowData.flowForm.scheduleTime || startFlowData.flowForm.scheduleTime.length === 0)
    //     return proxy.$modal.msgError('请选择调度时间');
    runStatus.value = true;

    const scheduleTime =
      startFlowData.flowForm.timeType === '1'
        ? {
            complementStartDate: startFlowData.flowForm.scheduleTime[0]?.trim() || '',
            complementEndDate: startFlowData.flowForm.scheduleTime[1]?.trim() || '',
          }
        : {
            complementScheduleDateList: Array.isArray(startFlowData.flowForm.scheduleTime)
              ? startFlowData.flowForm.scheduleTime.join(',').trim()
              : (startFlowData.flowForm.scheduleTime || '').trim(),
          };

    if (startFlowData.flowForm.timeType !== '1') {
      const value = scheduleTime.complementScheduleDateList;
      if (value) {
        if (
          !/(((19|20)[0-9]{2})-((0[1-9])|(1[0-2]))-((0[1-9])|((1|2)[0-9])|(3[0-1]))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]))(,(((19|20)[0-9]{2})-((0[1-9])|(1[0-2]))-((0[1-9])|((1|2)[0-9])|(3[0-1]))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])))*$/.test(
            value,
          )
        ) {
          return proxy.$modal.msgWarning('日期格式错误');
        }
        const dates = value.split(',');
        if (dates.length > 100) {
          return proxy.$modal.msgWarning('日期数量超过100个');
        }
      }
    }
    let query = {
      failureStrategy: !isSingleNode.value ? startFlowData.flowForm.failureStrategy : null,
      taskDependType: !isSingleNode.value ? startFlowData.flowForm.taskDependType : null,
      execType: startFlowData.flowForm.execType ? 'COMPLEMENT_DATA' : 'START_PROCESS',

      expectedParallelismNumber: checkTypeExecType(
        startFlowData.flowForm.expectedParallelismNumber,
      ),
      runMode: checkTypeExecType(startFlowData.flowForm.runMode),
      scheduleTime: checkTypeExecType(JSON.stringify(scheduleTime)),

      //   dryRun: startFlowData.flowForm.dryRun,
      //   expectedParallelismNumber: startFlowData.flowForm,
      //   startParams: '{}',
      //   processInstancePriority: startFlowData.flowForm,

      flowId: startFlowData.flowForm.flowId || previousNodeData.value.id,
      startNode: startFlowData.flowForm.startNode,
    };
    try {
      query = Object.fromEntries(Object.entries(query).filter(([_, v]) => v != null));
      const res = await startFlow(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
      if (res.code === 200) {
        // proxy.$modal.msgSuccess('成功');
        projectCodeOfDs.value = res.data.projectCode;
        nodeLogShow.value = true;
        // 每次运行后，需要重置刷新次数
        reGetTimes.value = 0;
        setTimeout(() => {
          getSingleTaskRecord(res.data.processCode);
        }, 1000);
      } else {
        proxy.$modal.msgError('失败');
        runStatus.value = false;
      }
    } catch (error) {
      runStatus.value = false;
      //  移除连接线
      // proxy.$modal.msgError('失败')
    }

    startFlowData.dialogVisible = false;
  };

  const timeTypeChange = () => {
    startFlowData.flowForm.scheduleTime = [];
  };

  const checkTypeExecType = (v) => {
    return startFlowData.flowForm.execType ? v : null;
  };

  // #endregion
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  $white: #ffffff;
  $base-primary: #2468f1;
  $base-bg-color: #f9fafc;
  $base-text-color-regular: #4e5370;
  $base-text-color: #172241; // 主要文字颜色
  $base-text-color-regular: #4e5370; // 常规文字颜色
  $base-text-color-secondary: #9ea0b1; // 次要文字颜色
  $base-text-color-placeholder: #c3c7cc; // 占位符颜色/提示性文字
  $border-color-lighter: #ebecee;

  .closeAllbtn {
    position: absolute;
    right: 20px;
    top: 6px;
    z-index: 10;
  }

  /* 修改 element 组件【Tabs 标签页】默认样式
        ---------------------------------------------------*/
  :deep .el-tabs.custom-tabs {
    background-color: $base-bg-color;

    .el-tabs__item {
      margin-top: 5.9px;
      height: 30px;
      font-size: 12px;
      padding: 0 28px !important;
      line-height: 28px;
      border-top: 2px solid transparent;
      color: $base-text-color-regular;
      border-bottom: 2px solid transparent;
      outline: none;
      transition:
        color 0.3s ease-in,
        border-color 0.5s ease-in-out;

      .el-icon-close {
        color: $base-text-color-secondary;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 7px;
      }

      &.is-active {
        color: $base-text-color;
        border-bottom: 1px solid $base-primary;
        font-weight: 600;
        transition:
          color 0.5s ease,
          border-color 0.3s ease-in-out;
      }

      &:last-child {
        padding-right: 28px;
      }
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 32px;
    }
  }

  :deep .el-tabs--card.custom-tabs > .el-tabs__header {
    border-bottom: 1px solid $border-color-lighter;
    margin-bottom: 0;

    .el-tabs__item {
      border-radius: 5px;

      &:last-child {
        padding-right: 28px;
      }

      &.is-active.is-closable,
      &.is-closable:hover {
        padding-left: 28px;
        padding-right: 28px;
      }
    }

    .el-tabs__nav {
      border: none;
      background-color: $white;
      border-radius: 0;
      border-radius: 5px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
      opacity: 0.9;
    }

    .el-tabs__nav-wrap {
      margin-bottom: 0;
    }
  }

  :deep .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: $border-color-lighter;
    border-radius: 10px;
  }

  .tree-container {
    position: relative;
    // overflow: hidden;
    max-width: 220px;
    max-height: 95vh;
  }

  .tree-lap {
    position: absolute;
    cursor: pointer;
    top: 10;
    bottom: 0;
    z-index: 100;
    left: 210px;
    transition: left 0.3s;
  }

  .treeFab {
    // max-height: calc(100vh - 240px);
    height: calc(100% - 150px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  #container {
    min-width: 100vw;
    // min-height: 100%;//会引起画布较小的情况下下面撑开滚动条的情况
    position: relative;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 10px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .head-title-tree {
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    // background: #f7f7fa;
    padding: 5px;
  }

  .head-container {
    //display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    width: 100%;
    height: 100%;
    // border-right: 1px solid #dfe3e8;

    // select 宽度
    :deep .el-select {
      width: 100%;
    }
    .right-btn-box {
      text-align: right;
      .right-btn-add {
        width: 28px;
        height: 28px;
      }
    }
    .export-and-import {
      display: inline-block;
      margin-right: 10px;
    }
  }

  // .tree-lap {
  //   display: inline-block;
  //   right: 180px;
  //   margin-top: 89vh;
  //   margin-left: -35px;
  //   cursor: pointer;
  //   // 增加过渡效果
  //   // transition: all 0.1s ease-in-out;
  //   z-index: 1;
  // }

  .stencil-app {
    width: 100%;
    display: flex;
    height: calc(100% - 100px);
    position: relative;

    .app-stencil {
      position: relative;
      width: 265px;
      height: 100%;
      transition: all 0.3s ease;
    }

    .animate-opacity {
      animation: opacity 1s ease-in-out;
      position: relative;
    }

    @keyframes opacity {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }

    .app-sync {
      position: absolute;
      // bottom: 0;
      z-index: 100 !important;
      display: block;
      top: 0px;
    }

    .app-Map {
      position: absolute;
      // 左下角
      bottom: 80px;
      right: 30px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 0 10px 1px #e9e9e9;
      width: 150px;
      height: 150px;
      z-index: 99;
    }

    #container {
      min-width: 100vw;
      // min-height: 100%;//会引起画布较小的情况下下面撑开滚动条的情况
      position: relative;
    }

    .app-nodelog {
      width: calc(100% - 265px) !important;
      position: absolute;
      // bottom: 5%;
      bottom: 0;
      right: 0;
      &.all-width {
        width: 100% !important;
      }
    }
  }

  .app-stencil-box {
    width: 265px;
    // height: calc(100% - 78px);
    height: 100%;
    margin-top: 8px;
    position: relative;
  }

  #stencil {
    border-right: 1px solid #dfe3e8;
    border-right: none;
    border-radius: 0px 8px 8px 0px;
  }

  .stencil-icon {
    position: static;
    font-size: 20px;
    cursor: pointer;
    // margin-left: 15%;
    z-index: 9;
    opacity: 0.5;
    transition: opacity 0.3s ease-in-out;
    position: absolute;
    top: 46px;
    right: 20px;
  }

  :deep .x6-widget-stencil {
    background-color: #ffffff;
    border-radius: 0px 8px 8px 0px;
  }

  :deep .x6-widget-stencil-title {
    background-color: #fff;
  }

  :deep .x6-widget-stencil-group-title {
    background-color: #fff !important;
  }

  :deep .x6-widget-transform {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }

  :deep .x6-widget-transform > div {
    border: 1px solid #239edd;
  }

  :deep .x6-widget-transform > div:hover {
    background-color: #3dafe4;
  }

  :deep .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }

  :deep .x6-widget-transform-resize {
    border-radius: 0;
  }

  :deep .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }

  :deep .x6-widget-selection-box {
    opacity: 0;
  }

  .app-container-box {
    overflow-x: hidden;
    overflow-y: hidden;
    width: 100%;
    height: 100%;
    // background: #ffffff;
    overflow: hidden;
    border-radius: 10px;
    // outline: 10px solid #c51212;
    // margin-top: 8px;
    padding: 20px;

    & > .el-row {
      height: 100%;

      ::v-deep .splitpanes__pane {
        height: 100%;

        & > .el-col {
          height: 100%;
        }
      }
    }

    .tool-header {
      height: calc(100% - 9px);
    }
    .TitleName {
      // border-left: 3px solid #409eff;
      padding-left: 10px;
      font-size: 17px;
      height: 32px;
      line-height: 32px;
      position: relative;
      display: inline-block;
      font-weight: 400;
      &::before {
        content: '';
        width: 3px;
        height: 16px;
        border-radius: 4px;
        background: $--base-color-primary;
        position: absolute;
        left: 0;
        top: 8px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /*
        :deep .el-button {
          width: 80px;
          height: 25px;
        }*/

  /*:
        deep .el-button--primary {
          background: #1F78EF;
        }*/

  :deep .el-drawer__header {
    margin-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
    padding: 10px 20px;
  }

  :deep .el-drawer__title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .container {
    display: flex;
  }

  .resizable {
    flex-grow: 1;
  }

  :deep .el-overlay {
    position: absolute;
    height: calc(100vh - 110px);
  }

  .bounce-enter-active {
    animation: bounce-in 0.5s;
  }

  .bounce-leave-active {
    animation: bounce-in 0.1s reverse;
  }

  @keyframes bounce-in {
    0% {
      transform: scale(0);
    }

    50% {
      transform: scale(1);
    }

    100% {
      transform: scale(1);
    }
  }

  // .slide-fade-enter-active {
  //   transition: all 0.3s ease-out;
  // }

  // .slide-fade-leave-active {
  //   transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  // }

  // .slide-fade-enter-from,
  // .slide-fade-leave-to {
  //   transform: translateX(20px);
  //   opacity: 0;
  // }
  // .splitpanes {
  //   background: none;
  // }

  // .splitpanes__pane {
  //   box-shadow: 0 0 5px rgba(0, 0, 0, .2) inset;
  //   justify-content: center;
  //   align-items: center;
  //   display: flex;
  // }

  // .splitpanes--vertical>.splitpanes__splitter {
  //   min-width: 6px;
  //   background: linear-gradient(90deg, #ccc, #111);
  // }

  // .splitpanes--horizontal>.splitpanes__splitter {
  //   min-height: 6px;
  //   background: linear-gradient(0deg, #ccc, #111);
  // }
  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
    // outline: 10px solid #c51212;
  }

  ::v-deep .splitpanes__pane {
    .el-tabs .el-tabs__header .el-tabs__nav .el-tabs__item,
    .el-overlay .el-tabs .el-tabs__header .el-tabs__nav .el-tabs__item {
      padding: 2px 40px !important;
    }
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .collapsed {
    width: 0;
    /* 或者设置为你期望的宽度，或使用其他样式来隐藏侧边栏 */
    transition: width 0.3s ease;
    /* 添加过渡效果，可选 */
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin: 0px 10px;
  }

  .table-status {
    position: relative;
    left: -5px;
    // padding-left: 18px;
    height: 20px;

    // width: 48px;
    // height: 24px;
    & > span {
      height: 20px;
      line-height: 1;
      color: $--base-color-green;
      background-color: $--base-color-green-disable;
      display: inline-block;
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;

      &.task-status-KILL {
        color: #faad14;
        background: #fff7e6;

        &::before {
          border: 3px solid #faad14;
        }
      }

      &.task-status-RUNNING_EXECUTION {
        color: $--base-color-primary;
        background-color: $--base-color-tag-primary;

        &::before {
          border: 3px solid $--base-color-primary;
        }
      }

      &.task-status-SUCCESS {
        color: $--base-color-green;
      }

      &.task-status-FAILURE {
        color: $--base-btn-red-text;
        background-color: $--base-btn-red-bg;

        &::before {
          border: 3px solid $--base-btn-red-text;
        }
      }

      &::before {
        content: '';
        width: 12px;
        height: 12px;
        border: 3px solid $--base-color-green;
        border-radius: 6px;
        position: absolute;
        top: calc(50% - 7px);
        left: -15px;
      }
    }
  }

  .popover {
    display: flex;
    flex-direction: column;
  }
  .tree-prop-icon {
    // width: 100%;
    // height: 32px;
    // line-height: 32px;
    // display: block;
    cursor: pointer;
  }
  .tree-item {
  }
  .tree-item-box {
  }

  .tree-btn-box {
    display: flex;
    gap: 10px;
    margin-right: 20px;
  }
</style>

<style>
  @keyframes ant-line {
    to {
      stroke-dashoffset: -1000;
    }
  }
</style>
