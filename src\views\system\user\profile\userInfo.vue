<template>
  <div v-if="props?.type == 'user'">
    <div class="TitleName">
      <el-row :gutter="20">
        <el-col :span="20">基本信息</el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="Edit" @click="submit">保存</el-button>
        </el-col>
        <!-- <el-col :span="1.5"> -->
        <!-- <el-button icon="Edit" @click="reset">重置</el-button> -->
        <!-- </el-col> -->
      </el-row>
    </div>

    <div style="padding: 0 25%">
      <el-form
        ref="userRef"
        :model="userInfo"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="userInfo.nickName" maxlength="30" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="userInfo.phonenumber" maxlength="11" />
        </el-form-item>
        <el-form-item label="邮箱" prop="emaila">
          <el-input v-model="userInfo.email" maxlength="50" />
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="userInfo.sex">
            <!-- <el-radio label="-1">未知</el-radio> -->
            <el-radio label="0">男</el-radio>
            <el-radio label="1">女</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>

  <div v-else>
    <div v-show="user?.admin">
      <div class="TitleName">
        <el-row :gutter="20">
          <el-col :span="20">品牌信息</el-col>
          <el-col :span="1.5">
            <el-button icon="Edit" type="primary" @click="editCompanyInfoUtil">保存</el-button>
          </el-col>
          <!-- <el-col :span="1.5"> -->
          <!-- <el-button icon="Edit" @click="reset">重置</el-button> -->
          <!-- </el-col> -->
        </el-row>
      </div>

      <div style="padding: 0 25%">
        <el-form
          ref="otherRef"
          :model="otherInfo"
          :rules="otherRules"
          label-width="120px"
          label-position="top"
        >
          <el-form-item label="公司中文名称" prop="companyName">
            <el-input v-model="otherInfo.companyName" maxlength="15" show-word-limit />
          </el-form-item>

          <el-form-item label="公司英文名称" prop="companyCode">
            <el-input v-model="otherInfo.companyCode" maxlength="15" show-word-limit />
          </el-form-item>

          <el-form-item label="平台中文名称" prop="productName">
            <el-input v-model="otherInfo.productName" maxlength="15" show-word-limit />
          </el-form-item>

          <el-form-item label="平台英文名称" prop="productCode">
            <el-input v-model="otherInfo.productCode" maxlength="15" show-word-limit />
          </el-form-item>

          <el-form-item label="ICON 上传">
            <span class="text-info"
              >用于浏览器窗口标识,建议图片为方形尺寸最小为 32*32 支持大小不超过
              5MB格式为PNG/JPG/JPEG</span
            >
            <div class="Item-flex">
              <ImagePreview
                v-if="!icon"
                :width="148"
                :height="148"
                :src="otherInfo.icon"
                :preview-src-list="[otherInfo.icon]"
              />
              <imageUpload v-model="icon" :workspace-id="1" :limit="1" />
            </div>
          </el-form-item>

          <el-form-item label="平台 LOGO 上传">
            <span class="text-info">
              用于系统内部标识,建议图片为方形尺寸最小为 80*40 支持大小不超过
              5MB格式为PNG/JPG/JPEG</span
            >

            <div class="Item-flex">
              <ImagePreview
                v-if="!logoOfSystem"
                :width="148"
                :height="148"
                :src="otherInfo.logoOfSystem"
                :preview-src-list="[otherInfo.logoOfSystem]"
              />
              <imageUpload v-model="logoOfSystem" :workspace-id="1" :limit="1" />
            </div>
          </el-form-item>

          <el-form-item label="平台登录背景上传">
            <span class="text-info"
              >用于登陆页背景 建议图片为方形尺寸最小为 1920*1080 支持大小不超过
              5MB格式为PNG/JPG/JPEG</span
            >

            <div class="Item-flex">
              <ImagePreview
                v-if="!backgroundImgLogin"
                :width="148"
                :height="148"
                :src="otherInfo.backgroundImgLogin"
                :preview-src-list="[otherInfo.backgroundImgLogin]"
              />
              <imageUpload v-model="backgroundImgLogin" :workspace-id="1" :limit="1" />
            </div>
          </el-form-item>

          <el-form-item label="集市登录背景上传">
            <span class="text-info"
              >用于集市登陆页背景 建议图片为方形尺寸为 750*1080 支持大小不超过
              5MB格式为PNG/JPG/JPEG</span
            >

            <div class="Item-flex">
              <ImagePreview
                v-if="!apiLoginImg"
                :width="148"
                :height="148"
                :src="otherInfo.apiLoginImg"
                :preview-src-list="[otherInfo.apiLoginImg]"
              />
              <imageUpload v-model="apiLoginImg" :workspace-id="1" :limit="1" />
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { editCompanyInfo, getCompanyInfo, uploadImg } from '@/api/login';

  import { updateUserProfile } from '@/api/system/user';
  import { getCurrentInstance, ref } from 'vue';

  const props = defineProps({
    user: {
      type: Object,
    },
    other: {
      type: Object,
      default: () => {
        return {
          companyCode: 'companyCode',
          companyName: 'companyName',
          productName: 'productName',
          productCode: 'productCode',
        };
      },
    },
    type: {
      type: String,
      default: 'user',
    },
  });

  const { proxy } = getCurrentInstance();
  const userInfo = ref({});
  const otherInfo = ref({});
  onMounted(() => {
    userInfo.value = { ...props.user };
    otherInfo.value = { ...props.other[0] };
  });

  const rules = ref({
    nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
    email: [
      { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] },
    ],
    phonenumber: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
  });

  const otherRules = ref({
    companyCode: [{ required: true, message: '公司中文名称不能为空', trigger: 'blur' }],
    companyName: [{ required: true, message: '公司英文名称不能为空', trigger: 'blur' }],
    productName: [{ required: true, message: '平台中文名称不能为空', trigger: 'blur' }],
    productCode: [{ required: true, message: '平台英文名称不能为空', trigger: 'blur' }],
  });

  /** 提交按钮 */
  function submit() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        updateUserProfile(userInfo.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
        });
      }
    });
  }

  const uploadImgUtil = async (value, type) => {
    const res = await uploadImg({ ossId: value, imageType: type });

    if (res.code !== 200) return;

    const re = await getCompanyInfo();
    if (re.code !== 200) return;
    sessionStorage.setItem('CompanyInfo', JSON.stringify(re.data));

    if (type === 'icon') {
      setFavicon(re.data.icon);
    }
  };

  function setFavicon(url) {
    // 添加时间戳以防止缓存
    const timestamp = new Date().getTime();
    const newUrl = `${url}?v=${timestamp}`;

    // 查找现有的 favicon 链接标签
    let link = document.querySelector("link[rel*='icon']");

    if (link) {
      // 如果存在，则更新 href
      link.href = newUrl;
    } else {
      // 如果不存在，创建新的 favicon 链接标签
      link = document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = newUrl;
      document.head.appendChild(link);
    }
  }

  const logoOfSystem = ref();
  const icon = ref();
  const backgroundImgLogin = ref();
  const apiLoginImg = ref();

  const editCompanyInfoUtil = async () => {
    const re = await proxy.$refs.otherRef?.validate((valid) => valid);
    if (!re) return;

    const ref = await editCompanyInfo(otherInfo.value);
    if (ref.code !== 200) return;

    const res = await getCompanyInfo();
    if (res.code !== 200) return;

    sessionStorage.setItem('CompanyInfo', JSON.stringify(res.data));
    proxy.$modal.msgSuccess(res.msg);
  };

  editCompanyInfoUtil();
  watch(
    () => icon.value,
    (newValue) => {
      if (newValue) {
        uploadImgUtil(newValue, `icon`);
      }
    },
  );

  watch(
    () => logoOfSystem.value,
    (newValue) => {
      if (newValue) {
        // autocorrect: false
        uploadImgUtil(newValue, `平台Logo`);
      }
    },
  );
  watch(
    () => backgroundImgLogin.value,
    (newValue) => {
      if (newValue) {
        // autocorrect: false
        uploadImgUtil(newValue, `backgroundImgLogin`);
      }
    },
  );
  watch(
    () => apiLoginImg.value,
    (newValue) => {
      if (newValue) {
        // autocorrect: false
        uploadImgUtil(newValue, `apiLoginImg`);
      }
    },
  );
</script>

<style scoped lang="scss">
  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
    & > .el-row {
      justify-content: space-between;
    }
  }

  .Item-flex {
    display: flex;
    align-items: flex-start;
  }
  .text-info {
    color: #909399;
    font-size: 12px;
    // 定位到左上角
    position: absolute;
    right: 100px;
    top: -35px;
  }
</style>
