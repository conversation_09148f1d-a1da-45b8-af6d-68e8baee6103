<template>
  <div
    :class="{ 'json-reader-tree-subtree': !root, 'json-reader-tree-root': root }"
    class="json-reader-tree"
  >
    <div
      class="json-reader-tree-property"
      :class="{ 'json-reader-tree-property-selected': selected }"
      @click="clicked"
    >
      <div v-if="isArrOrObj && (isOpen || expandAll)" class="json-reader-tree-property-arrow"
        >&#9660;
      </div>
      <div v-if="isArrOrObj && !isOpen && !expandAll" class="json-reader-tree-property-arrow"
        >&#9654;
      </div>
      <div>{{ keyName }}:</div>
      <div v-if="!isArrOrObj" class="json-reader-tree-property-value">{{ item }}</div>
    </div>
    <div v-show="isOpen || expandAll" v-if="isArrOrObj">
      <div v-for="(childItem, key) in item">
        <json-tree
          :item="childItem"
          :key-name="key"
          :root="false"
          :original-json="originalJson"
          :parent-json="item"
          :expand-all="expandAll"
          @update="sendUpdate"
        >
        </json-tree>
      </div>
    </div>
  </div>
</template>

<script setup>
  import jsonTree from '@/components/jsonTree/index';
  import { computed, defineEmits, defineProps, ref } from 'vue';
  const { item, keyName, root, originalJson, parentJson, expandAll } = defineProps([
    'item',
    'keyName',
    'root',
    'originalJson',
    'parentJson',
    'expandAll',
  ]);

  console.log('item', item);
  const emit = defineEmits();

  const isOpen = ref(false);
  const selected = ref(false);
  const justClicked = ref(false);
  const path = ref('');

  const isArrOrObj = computed(() => item instanceof Object);

  const clicked = () => {
    if (app.expandAll) app.expandAll = !app.expandAll;
    if (isArrOrObj.value) isOpen.value = !isOpen.value;
    selected.value = true;

    justClicked.value = true;

    if (!app.isResult) {
      path.value = searchObject(originalJson, parentJson, keyName);
      console.log('path.value', path.value);
      emit('update', path.value);
    }
  };

  const searchObject = (oldObj, newObj, key, path = '$') => {
    let output = '';
    let newPath;

    for (const item in oldObj) {
      if (item.toString() === key.toString() && oldObj === newObj) {
        if (Array.isArray(newObj)) return path + '[' + item + ']';
        else if (/[^A-Za-z0-9_$]/.test(item)) return path + '["' + item + '"]';
        else return path + '.' + item;
      } else if (typeof oldObj[item] === 'object') {
        newPath = Array.isArray(oldObj)
          ? path + '[' + item + ']'
          : /[^A-Za-z0-9_$]/.test(item)
          ? path + '["' + item + '"]'
          : path + '.' + item;

        output = searchObject(oldObj[item], newObj, key, newPath) || output;
      }
    }

    return output;
  };

  const sendUpdate = (path) => emit('update', path);
</script>

<style scoped>
  #json-reader-error {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.9em;
    height: 200px;
    margin: 0 0 0 10px;
    width: 90%;
  }

  .json-reader-error-part {
    margin: 10px 0 0 0;
  }

  #json-reader-error-message {
    color: #b91400;
  }

  .json-tree {
    display: none;
  }

  .json-reader-tree-root {
    margin: 0 0 0 10px;
    min-width: 400px;
  }

  .json-reader-tree-subtree {
    margin: 0 0 0 15px;
    min-width: 400px;
  }

  .json-reader-tree-property {
    align-items: center;
    background-color: white;
    border-bottom: 1px solid #ddd;
    color: #0277bd;
    display: flex;
    overflow: auto;
    padding: 10px 4px;
    white-space: pre;
  }

  .json-reader-tree-property:hover {
    cursor: pointer;
  }

  .json-reader-tree-property-arrow {
    font-size: 0.5em;
    margin: 0 5px 0 0;
  }

  .json-reader-tree-property-value {
    color: #455a64;
    padding: 0 10px 0 5px;
  }

  /* .json-reader-tree-property-selected { */
  /* background-color: #c1d5e0; */
  /* } */
</style>
