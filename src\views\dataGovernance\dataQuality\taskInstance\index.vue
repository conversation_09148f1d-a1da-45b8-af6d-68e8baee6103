<template>
  <div class="task-instance">
    <div v-if="!showDetail" class="task-instance-box">
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
      <div class="form-box">
        <el-form
          ref=""
          v-model="searchInfo.searchForm"
          label-position="left"
          inline
          label-width="auto"
        >
          <el-form-item label="实例名称" prop="searchVal">
            <el-input
              v-model="searchInfo.searchForm.searchVal"
              placeholder="请输入实例名称"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="运行状态" prop="stateType">
            <el-select
              v-model="searchInfo.searchForm.stateType"
              placeholder="请选择运行状态"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="(option, index) in options.statusOptions"
                :key="index"
                :label="option.label"
                :value="option.string"
              />
            </el-select>
          </el-form-item>
          <el-button
            type="primary"
            icon="Search"
            :disabled="false"
            @click="tableListener.tableSearch"
          >
            <!-- @keyup.enter="handleQuery" -->
            查询
          </el-button>
        </el-form>
      </div>

      <div class="table-box">
        <el-table
          ref="tableRef"
          :data="tableInfo.tableData"
          height="100%"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in tableInfo.columns" :key="index" v-bind="item">
            <template v-if="item.prop === 'status'" #default="scope">
              <div class="table-status">
                <span :class="`status-${scope.row.status}`">{{ scope.row.statusLabel }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <!-- <el-button type="text" size="small" @click="tableListener.rerun(scope)">
                重跑
              </el-button> -->
              <el-button
                :disabled="scope.row.state !== 7"
                type="text"
                size="small"
                @click="tableListener.showResults(scope)"
              >
                结果
              </el-button>
              <el-button type="text" size="small" @click="tableListener.handleLog(scope)">
                日志
              </el-button>
              <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- </el-tab-pane> -->

      <div style="margin-bottom: 20px">
        <!-- 分页 -->
        <pagination
          v-show="searchInfo.queryParams.total > 0"
          v-model:page="searchInfo.queryParams.pageNum"
          v-model:limit="searchInfo.queryParams.pageSize"
          :pager-count="searchInfo.queryParams.maxCount"
          :total="searchInfo.queryParams.total"
          @pagination="tableListener.tableSearch('ODS')"
        />
      </div>
      <!-- </el-tabs> -->

      <el-dialog v-model="dialogInfo.dialogVisible" title="规则详情" width="40%" :draggable="true">
        <div class="dialog-table-box">
          <el-table
            ref="tableRef"
            :data="dialogInfo.data"
            height="100%"
            :header-cell-class-name="addHeaderCellClassName"
            row-class-name="rowClass"
            empty-text="暂无数据"
          >
            <el-table-column
              v-for="(item, index) in dialogInfo.columns"
              :key="index"
              v-bind="item"
            />
          </el-table>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 日志对话框 -->
      <el-dialog v-model="logInfo.openLog" :title="logInfo.LogTitle" width="1200px" append-to-body>
        <el-row :gutter="20">
          <el-col :span="4" style="border-right: 8px solid #eee; overflow: hidden">
            <el-scrollbar height="600px">
              <el-tree
                ref="logTree"
                :data="logInfo.LogContentList"
                :props="logInfo.defaultProps"
                :default-expanded-keys="logInfo.defaultExpandedKeys"
                node-key="taskLogNo"
                @node-click="logListener.handleNodeClick"
              >
                <template #default="{ node }">
                  <!-- {{ node }} -->
                  <el-tooltip
                    :disabled="node.label.length < 10"
                    :content="node.label"
                    placement="top"
                  >
                    <span>{{
                      node.label.length > 10 ? node.label.slice(0, 10) + '...' : node.label
                    }}</span>
                  </el-tooltip>
                </template>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="20">
            <div style="height: 600px; overflow-y: auto">
              <Codemirror
                v-model="logInfo.logStrB"
                :disabled-type="true"
                style="height: 600px; overflow-y: auto"
              />
            </div>
          </el-col>
        </el-row>

        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="logListener.openLogClose">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <workDetail
      v-else
      :data="detailsInfo.data"
      :work-id="detailsInfo.id"
      @callback="detailsListener.callback"
    ></workDetail>
  </div>
</template>

<script setup>
  import useTaskInstanceService from '@/views/dataGovernance/dataQuality/taskInstance/useTaskInstanceService';
  import workDetail from '@/views/dataGovernance/dataQuality/taskInstance/components/workDetail';
  import Codemirror from '@/components/Codemirror'; // 编辑器

  const {
    tableInfo,
    searchInfo,
    dialogInfo,
    tableListener,
    dialogListener,
    options,
    detailsInfo,
    showDetail,
    detailsListener,
    logInfo,
    logListener,
  } = useTaskInstanceService();
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .task-instance {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: auto;
    .task-instance-box {
      width: 100%;
      height: 100%;
      .form-box {
        // margin-top: 20px;
        ::v-deep .el-form {
          text-align: right;
        }
      }
      .table-box {
        height: calc(100% - 112px);
        background: $--base-color-item-light;
        padding: 10px;
        .table-status {
          position: relative;
          padding-left: 18px;
          height: 24px;
          & > span {
            height: 20px;
            line-height: 1;
            color: $--base-color-green;
            background-color: $--base-color-green-disable;
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            &.status-0 {
              color: $--base-btn-red-text;
              background-color: $--base-btn-red-bg;
              &::before {
                border: 3px solid $--base-btn-red-text;
              }
            }
            &.status-2 {
              color: $--base-color-primary;
              background-color: $--base-color-tag-primary;
              &::before {
                border: 3px solid $--base-color-primary;
              }
            }
            &.status-3 {
              color: $--base-color-yellow;
              background-color: $--base-color-tag-orange;
              &::before {
                border: 3px solid $--base-color-yellow;
              }
            }
            &::before {
              content: '';
              width: 12px;
              height: 12px;
              border: 3px solid $--base-color-green;
              border-radius: 6px;
              position: absolute;
              top: calc(50% - 6px);
              left: 0;
            }
          }
        }
      }
      .pagination-container {
        margin: 0;
        padding: 10px 20px;
      }
      .dialog-table-box {
        height: 500px;
      }
    }
  }
</style>
