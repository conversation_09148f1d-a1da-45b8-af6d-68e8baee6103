<template>
  <div class="grouping-form-box">
    <el-form
      ref="formRef"
      :model="form"
      :rules="groupingRules"
      label-position="left"
      label-width="auto"
    >
      <el-form-item v-if="activeName === 'first'" label="分组名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入" />
      </el-form-item>
      <el-form-item v-else label="主题名称" prop="categoryName">
        <el-input v-model="form.categoryName" placeholder="请输入" />
      </el-form-item>

      <el-form-item v-if="activeName === 'first'" label="父级分组" prop="parentId">
        <el-tree-select
          v-model="form.parentId"
          :cache-data="form"
          :data="options"
          :props="{
            value: 'groupId',
            label: 'groupName',
            children: 'children',
            disabled: 'disabled',
          }"
          value-key="groupId"
          placeholder="选择上级菜单"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item v-else label="主题说明" prop="categoryDesc">
        <el-input
          v-model="form.categoryDesc"
          placeholder="请输入"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 10 }"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item v-if="activeName === 'first'" label="分组说明" prop="groupDesc">
        <el-input
          v-model="form.groupDesc"
          placeholder="请输入"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 10 }"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
  import useAPIAddGroupService from '@/views/APIService/components/APIAddGroup/useAPIAddGroupService';

  const props = defineProps({
    activeName: {
      type: String,
      default: 'first',
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    type: {
      type: String,
      default: '',
    },
  });
  const { proxy } = getCurrentInstance();
  console.log(props.formData, 2233);
  const confirm = async () => {
    return await proxy.$refs.formRef.validate((valid) => valid);
  };
  const { form, groupingRules, options, addForm, editForm } = useAPIAddGroupService(props);
  console.log(form, 'hahah');
  defineExpose({ addForm, editForm, confirm });
</script>
<style lang="scss" scoped></style>
