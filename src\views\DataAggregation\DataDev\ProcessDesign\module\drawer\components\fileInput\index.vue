<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item prop="type">
      <!-- radio -->
      <el-radio-group v-model="form.type" :disabled="!CanvasActions" @change="operChangeType">
        <el-radio :label="'1'"> 数据源 </el-radio>
        <el-radio :label="'0'"> 本地上传 </el-radio>
      </el-radio-group>
      <template v-if="form.type == '0'">
        <el-upload
          v-model:file-list="fileList"
          :limit="limit"
          class="upload-demo"
          :action="uploadFileUrl"
          multiple
          list-type="text"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :on-exceed="handleExceed"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :headers="headers"
          :show-file-list="true"
          :accept="allowedFileExtensions"
          :on-error="uploadOnError"
        >
          <template #default>
            <div class="upload-container">
              <el-button
                type="primary"
                class="btn-item"
                plain
                :disabled="!CanvasActions"
                @click="handleButtonClick"
              >
                <IconUpload /> 上传
              </el-button>
            </div>
          </template>
        </el-upload>
        <!-- <el-select -->
        <!-- v-model="form.selectFile" -->
        <!-- placeholder="请选择" -->
        <!-- style="width: 100%" -->
        <!-- :disabled="!CanvasActions" -->
        <!-- @change="changeSelectFile" -->
        <!-- > -->
        <!-- <el-option -->
        <!-- v-for="dict in selectFileList" -->
        <!-- :key="dict.label" -->
        <!-- :value="dict.value" -->
        <!-- :label="dict.label" -->
        <!-- /> -->
        <!-- </el-select> -->
      </template>
      <template v-else>
        <!-- <el-form-item  label="数据源" prop="dataSources"> -->
        <el-select
          v-model="form.dataSources"
          placeholder="请选择"
          style="width: 100%"
          :disabled="!CanvasActions"
          clearable
          @change="getBucketListUtil"
        >
          <el-option v-for="dict in dataSourcesList" :key="dict.value" v-bind="dict" />
        </el-select>
        <!-- </el-form-item> -->
      </template>
      <template #label>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="tooltipContent()"
          placement="top-start"
        >
          <el-icon class="label-icon">
            <QuestionFilled />
          </el-icon>
        </el-tooltip>
        <span>数据来源</span>
      </template>
    </el-form-item>
    <!-- 文件路径 -->
    <el-form-item v-if="form.type != '0'" label="文件" prop="selectFile">
      <!-- <el-input v-model="form.selectFile" :disabled="!CanvasActions" placeholder="请输入" /> -->
      <el-select
        v-model="form.selectFile"
        placeholder="请选择"
        style="width: 100%"
        :disabled="!CanvasActions"
        clearable
        @change="changeSelectFile"
      >
        <el-option v-for="dict in bucketRootObjsList" :key="dict.value" v-bind="dict" />
      </el-select>
    </el-form-item>

    <el-form-item label="" prop="typeName">
      <!-- <el-checkbox v-model="form.operationModel" label="第一行是否为表头" size="large"
                :disabled="!CanvasActions" /> -->
      <el-radio-group v-model="form.operationModel" :disabled="!CanvasActions" @change="operChange">
        <!--  -->
        <!-- <el-radio :label="'1'"></el-radio> -->
        <!-- <el-radio :label="'0'"></el-radio> -->
        <!-- 增加 tooltip -->
        <el-radio :label="'1'">
          第一行是表头
          <el-tooltip
            class="box-item"
            effect="dark"
            content="数据的第一行是表头(字段名)"
            placement="top-start"
          >
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </el-radio>
        <el-radio :label="'0'">
          第一行非表头
          <el-tooltip
            class="box-item"
            effect="dark"
            content="数据的第一行不是表头(字段名)"
            placement="top-start"
          >
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- {{ changeType(form.selectFile) }} -->
    <!-- excel xls/xlsx -->
    <template v-if="['xls', 'xlsx', 'excel'].includes(changeType(form.selectFile))">
      <el-form-item label="sheet_name" prop="sheet_name">
        <el-input
          v-model="form.sheet_name"
          placeholder="请输入"
          :disabled="!CanvasActions"
        ></el-input>
      </el-form-item>
    </template>

    <!-- txt csv -->
    <!-- <template v-if="['txt', 'csv', 'text'].includes(changeType(form.selectFile))"> -->
    <!-- <el-form-item label="分隔符" prop="separator"> -->
    <!-- <el-input -->
    <!-- v-model="form.separator" -->
    <!-- placeholder="请输入" -->
    <!-- :disabled="!CanvasActions" -->
    <!-- ></el-input> -->
    <!-- </el-form-item> -->
    <!-- </template> -->

    <!-- xml -->
    <template v-if="changeType(form.selectFile) === 'xml'">
      <el-form-item label="use_attr_format" prop="xml_use_attr_format">
        <!-- <el-input -->
        <!-- v-model="form.xml_use_attr_format" -->
        <!-- placeholder="请输入" -->
        <!-- :disabled="!CanvasActions" -->
        <!-- ></el-input> -->
        <!-- 下拉框 true false -->
        <el-select v-model="form.xml_use_attr_format" :disabled="!CanvasActions">
          <el-option label="是" value="true"></el-option>
          <el-option label="否" value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="row_tag" prop="xml_row_tag">
        <el-input
          v-model="form.xml_row_tag"
          placeholder="请输入"
          :disabled="!CanvasActions"
        ></el-input>
      </el-form-item>
    </template>

    <template v-if="form.type != '0'">
      <el-form-item label="桶名" prop="bucket">
        <el-select v-model="form.bucket" placeholder="请选择" clearable :disabled="!CanvasActions">
          <el-option
            v-for="items in BucketList"
            :key="items"
            :label="items.label"
            :value="items.value"
          />
        </el-select>
      </el-form-item>
    </template>
    <el-form-item label="选择字符集" prop="encoding">
      <el-select v-model="form.encoding" placeholder="请选择" clearable :disabled="!CanvasActions">
        <el-option
          v-for="items in encodingList"
          :key="items"
          :label="items.label"
          :value="items.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="结果表别名" prop="tableAliases">
      <el-input
        v-model="form.tableAliases"
        placeholder="请输入"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>

    <el-form-item label="数据视图" prop="typeName">
      <el-button type="text" plain :disabled="isSelectFile" @click="openDeploy"> 配置</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog
    v-model="open"
    title="采样配置"
    :close-on-click-modal="false"
    append-to-body
    @close="cancelEditField()"
  >
    <el-form label-position="top" label-width="auto">
      <template v-if="changeType(form.selectFile) !== 'json'">
        <template v-if="!hiddenFormData && !hideDataView">
          <el-form-item label="样本数据" prop="typeName">
            <el-input
              v-model="testConnection"
              placeholder=""
              type="textarea"
              show-word-limit
              :disabled="!CanvasActions"
            />
          </el-form-item>
          <template v-if="['txt', 'csv', 'text'].includes(changeType(form.selectFile))">
            <el-form-item label="分隔符" prop="separator">
              <el-input v-model="form.separator" placeholder="请输入" :disabled="!CanvasActions" />
            </el-form-item>
          </template>
          <el-form-item label="">
            <el-button plain :disabled="!CanvasActions" @click="sampleAnl"> 样本解析 </el-button>
          </el-form-item>
        </template>
      </template>
      <el-form-item v-else label="样本数据">
        <div class="exampleData">
          <div class="copy-text" @click="copyText">
            <el-tooltip class="box-item" content="复制" effect="light" placement="top-start">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-tooltip>
          </div>

          <div
            v-if="testConnection != null"
            class="test-connection"
            :class="{ 'disabled-tree': !CanvasActions }"
          >
            <div v-for="(item, key) in testConnection" :key="key">
              <treeJson
                :item="item"
                :key-name="key"
                :root="true"
                :original-json="testConnection"
                :parent-json="testConnection"
                :expand-all="expandAll"
                @update="updatePath"
              >
              </treeJson>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="字段映射" prop="typeName">
        <el-button
          type="primary"
          plain
          :disabled="!CanvasActions"
          style="margin-left: 95%; margin-bottom: 1%"
          @click="addSyncChange"
        >
          添加
        </el-button>
        <!--  -->
        <el-table
          ref="request"
          :data="syncChangeList"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="240"
        >
          <el-table-column label="序号" type="index" width="60" />

          <el-table-column label="返回字段名" prop="jsonPath">
            <template #default="scope">
              <el-input v-model="scope.row.jsonPath" placeholder="" :disabled="!CanvasActions" />
            </template>
          </el-table-column>

          <el-table-column label="字段类型" prop="fieldType">
            <template #default="scope">
              <el-row style="width: 100%">
                <el-col :span="12">
                  <el-select v-model="scope.row.fieldType" :disabled="!CanvasActions">
                    <el-option
                      v-for="items in customerIdList"
                      :key="items.id"
                      :label="items.label"
                      :value="items.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-if="scope.row.fieldType == 'decimal'"
                    v-model="scope.row.decimal"
                    placeholder="例(10,2)"
                  />
                </el-col>
              </el-row>
            </template>
          </el-table-column>

          <el-table-column prop="fieldType">
            <template #header>
              <el-tooltip
                class="item"
                effect="dark"
                content="不填写标准变量名时,该字段将不会向下传递或作为字段映射的数据源"
                placement="top-start"
              >
                <span>
                  字段标准名
                  <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                    <WarningFilled />
                  </el-icon>
                </span>
              </el-tooltip>
            </template>
            <template #default="scope">
              <el-input v-model="scope.row.fieldName" placeholder="" :disabled="!CanvasActions" />
            </template>
          </el-table-column>

          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button icon="Delete" @click="deleteSyncChange(scope.$index)" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField()">取 消</el-button>
        <el-button type="primary" :disabled="!CanvasActions" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { listOss } from '@/api/system/oss';
  import { getToken } from '@/utils/auth';
  import { computed } from 'vue';
  import { getBucketList } from '@/api/dataSourceManageApi';

  import {
    getSampleDataFile,
    getFiledType,
    getDataSourceList,
    getBucketRootObjsList,
  } from '@/api/DataDev';
  import treeJson from '@/components/treeJson/index';
  import { JsonViewer } from 'vue3-json-viewer';
  import 'vue3-json-viewer/dist/index.css';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { IconUpload } from '@arco-iconbox/vue-update-line-icon';
  const store = useWorkFLowStore();

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    workspaceId: {
      type: Number,
      default: 0,
    },
  });
  const { NodeData, CanvasActions, workspaceId } = toRefs(props);
  const emit = defineEmits();

  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
  const limit = ref(1);
  const fileList = ref([]);
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    workspaceId: workspaceId.value,
  });

  const open = ref(false);

  const syncChangeList = ref([
    // {
    //   prop: '',
    //   paramPosition: null,
    //   reqParameterLineType: null,
    //   val: '',
    // },
  ]);

  const customerIdList = ref([
    {
      id: 'id',
      label: 'Id',
    },
    {
      id: 'String',
      label: 'String',
    },
  ]);
  const requestQueryList = ref([
    {
      id: 'Custom',
      label: '自定义',
    },
    {
      id: 'Sign',
      label: '签名串',
    },
    {
      id: 'Precondition',
      label: '前置条件',
    },
  ]);

  const testConnection = ref(null);
  const result = ref(null);
  const isResult = ref(false);
  const preParamKey = ref('');
  const otherList = ref([]);
  const allValues = ref([]);

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    syncChangeList.value = syncChangeList.value ? syncChangeList.value : [];
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    // 生成唯一的key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 运行模式
   */
  const operationModelList = ref([
    { label: 'seatunnel-cluster', value: 'remote' },
    { label: 'seatunnel-local', value: 'yarn-per-job' },
    { label: 'fink-15-cluster', value: 'local' },
  ]);
  /**
   * 文件格式
   */
  const fileFormatList = ref([
    { label: 'text', value: 'text' },
    { label: 'csv', value: 'csv' },
    { label: 'json', value: 'json' },
    { label: 'xls', value: 'xls' },
    { label: 'xlsx', value: 'xlsx' },
    { label: 'excel', value: 'excel' },
    { label: 'xml', value: 'xml' },
    { label: 'txt', value: 'txt' },
  ]);

  // 显示文件格式
  const showFileFormat = ref([
    { label: 'TEXT', value: 'text,txt' },
    { label: 'CSV', value: 'csv' },
    { label: 'Parquet', value: 'parquet' },
    { label: 'ORC', value: 'orc' },
    { label: 'JSON', value: 'json' },
    { label: 'EXCEL', value: 'xls,xlsx,excel' },
    { label: 'XML', value: 'xml' },
    { label: 'Binary', value: 'binary' },
  ]);
  const allowedFileExtensions = computed(() => {
    return showFileFormat.value
      .flatMap((format) => format.value.split(','))
      .map((ext) => `.${ext}`)
      .join(',');
  });
  const hiddenFormData = computed(() => {
    const hiddenFormats = ['orc', 'parquet'];

    if (typeof form.value.selectFile === 'string') {
      const fileExtension = form.value.selectFile?.split('.').pop().toLowerCase();
      return hiddenFormats.includes(fileExtension);
    }
    return false;
  });

  // 计算属性：判断是否隐藏数据视图表单项
  const hideDataView = computed(() => {
    // const hiddenFormats = ['orc', 'parquet'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];

    if (typeof form.value.selectFile === 'string') {
      const fileExtension = form.value.selectFile?.split('.').pop().toLowerCase();
      return (
        // hiddenFormats.includes(form.value.fileFormat) ||
        imageExtensions.includes(fileExtension)
      );
    }

    return false;
  });

  /**
   * 选择文件
   */
  const selectFileList = ref();
  const tooltipContent = () => {
    return '仅支持以下文件格式：' + showFileFormat.value.map((format) => format.label).join(', ');
  };
  const validateFileFormat = (rule, value, callback) => {
    const allowedFormats = showFileFormat.value.flatMap((format) => format.value.split(','));
    const fileExtension = value.split('.').pop().toLowerCase();
    if (!allowedFormats.includes(fileExtension)) {
      callback(new Error('文件格式不支持'));
    } else {
      callback();
    }
  };
  const data = reactive({
    form: {
      // 选择文件
      selectFile: '',
      // 文件格式
      fileFormat: '',
      // 运行模式
      operationModel: '',
      // 运行最大内存
      taskExecutionMemory: '',
      encoding: '',
      sheet_name: '',
      xml_use_attr_format: '',
      xml_row_tag: '',
      type: '',
      dataSources: '',
      bucket: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: null,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
      workspaceId: workspaceId.value,
    },
    rules: {
      selectFile: [
        { required: true, message: '请选择文件', trigger: 'blur' },
        { validator: validateFileFormat, trigger: 'blur' }, // Add custom validator
      ],
      fileFormat: [{ required: true, message: '请选择文件格式', trigger: 'blur' }],
      tableAliases: [{ required: true, message: '请选择输入', trigger: 'blur' }],
      separator: [{ required: true, message: '请输入分隔符', trigger: 'blur' }],
      bucket: [{ required: true, message: '请选择桶名', trigger: 'blur' }],
      encoding: [{ required: true, message: '请选择字符集', trigger: 'blur' }],
      sheet_name: [{ required: true, message: '请输入sheet_name', trigger: 'blur' }],
    },
  });

  const { form, queryParams, rules } = toRefs(data);

  // 使用计算属性 判断  form.selectFile 是否有值
  const isSelectFile = computed(() => {
    return !form.value.selectFile;
  });
  const cancelDrawer = () => {
    form.value = {
      // 选择文件
      selectFile: '',
      // 文件格式
      fileFormat: '',
      // 运行模式
      operationModel: '',
      // 运行最大内存
      taskExecutionMemory: '',
    };
    emit('closeDrawer', false);
  };

  const changeType = (fileUrl) => {
    console.log('fileUrl', fileUrl);
    if (!fileUrl || typeof fileUrl !== 'string' || fileUrl.length <= 0) return false;

    // 获取文件后缀
    const fileExtension = fileUrl?.split('.')?.pop();
    const map = {
      xls: 'excel',
      xlsx: 'excel',
      txt: 'text',
    };
    return map[fileExtension] ? map[fileExtension] : `${fileExtension}`;
  };
  // 校验是否是对应格式 如果不是 就返回NUll
  const getFileFormat = () => {
    return fileFormatList.value.find((item) => item.value === changeType(form.value.selectFile));
  };

  const checkFileFormat = (v) => {
    const fileFormat = getFileFormat();
    return fileFormat ? v : 'null';
  };
  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;

    const fileFormat = getFileFormat();
    if (fileFormat && !syncChangeList?.value?.length)
      return proxy.$modal.msgWarning('请配置数据视图');

    NodeData.value.inputProperties.forEach((property, index) => {
      switch (index) {
        // 使用 文件后缀 如果是 xls 或者是 xlsx 转为 excel
        case 0:
          property.value = hideDataView.value ? 'binary' : changeType(form.value.selectFile);
          break;
        // case 1:
        //     property.value = form.value.selectFile;
        //     break;
        case 2:
          property.value = checkFileFormat(form.value.separator);
          break;
        case 3:
          property.value = form.value.operationModel;
          break;
        case 6:
          property.value = form.value.tableAliases;
          break;
        case 4:
        case 5:
          property.value = form.value.bucket;
          break;
        case 7:
          property.value = property.defaultValue;
          break;
        case 8:
          property.value = JSON.stringify(syncChangeList.value);
          break;
        case 9:
          property.value = form.value.xml_row_tag;
          break;
        case 10:
          property.value = form.value.xml_use_attr_format;
          break;
        case 11:
          property.value = Number(form.value.type) === 1 ? Number(form.value.type) : null;
          break;
        case 12:
          property.value = Number(form.value.type) === 1 ? form.value.dataSources : '';
          break;
        case 13:
          property.value = form.value.selectFile;
          break;
        case 14:
          property.value = form.value.sheet_name ? form.value.sheet_name : '';
          break;
        case 15:
          property.value = form.value.encoding ? form.value.encoding : '';
          break;
        default:
          break;
      }
    });
    if (form.value.selectFile.includes(',')) {
      const FileList = form.value?.selectFile?.split(',');
      NodeData.value.inputProperties[1].value = FileList[1];
    } else {
      NodeData.value.inputProperties[1].value = form.value.selectFile;
    }
    emit('submitDrawer', NodeData.value);
  };

  let hasWarned = false; // 添加这个变量来跟踪警告是否已经显示过
  const init = async () => {
    /**
     *  设置输入属性值 应当在初始化时调用 使用方法如下
     * setInputPropertyValue(0); // 设置第一个输入属性的值 会改变form.value.fileFormat的值
     *
     */
    await getDataSourceListUtil();

    const setInputPropertyValue = (index) => {
      const property = NodeData.value.inputProperties[index];
      console.log('property', property);
      const value = property.value ? property.value : property.defaultValue;
      form.value[getPropertyKey(index)] = value;
      console.log('form.value[getPropertyKey(index)]', form.value[getPropertyKey(index)]);
    };

    const getPropertyKey = (index) => {
      switch (index) {
        case 0:
          return 'fileFormat';
        // case 3:
        //   return 'operationModel';
        case 5:
          return 'bucket';
        case 6:
          return 'tableAliases';
        case 10:
          return 'xml_use_attr_format';
        case 9:
          return 'xml_row_tag';
        // case 11:
        //   return 'type';
        // case 12:
        //   return 'dataSources';
        case 13:
          return 'selectFile';
        case 14:
          return 'sheet_name';
        case 15:
          return 'encoding';
        default:
          return '';
      }
    };
    form.value.operationModel = NodeData.value.inputProperties[3].value
      ? NodeData.value.inputProperties[3].value
      : '1';
    const type = NodeData.value.inputProperties[11].value === '1';
    form.value.type = type ? '1' : '0';
    form.value.dataSources = Number(NodeData.value.inputProperties[12].value);
    form.value.dataSources && (await getBucketListUtil(form.value.dataSources));
    setInputPropertyValue(0);
    setInputPropertyValue(3);
    setInputPropertyValue(5);
    setInputPropertyValue(6);
    setInputPropertyValue(10);
    setInputPropertyValue(9);
    setInputPropertyValue(12);

    setInputPropertyValue(13);
    setInputPropertyValue(14);
    // setInputPropertyValue(15);
    form.value.encoding = NodeData.value.inputProperties[15].value || 'UTF-8';
    if (!type) {
      if (!form.value?.selectFile) return;
      const [name, url] = form.value?.selectFile?.split(',');
      fileList.value = [{ name, url }];
    }

    // form.value.selectFile = NodeData.value.inputProperties[1].value ? NodeData.value.inputProperties[1].value : NodeData.value.inputProperties[1].defaultValue; 改为判断
    if (NodeData.value.inputProperties[1].value) {
      if (NodeData.value.inputProperties[1].value == 'ERR:原文件已被删除') {
        form.value.selectFile = NodeData.value.inputProperties[1].defaultValue;

        // 只提示一次
        if (!hasWarned) {
          proxy.$modal.msgWarning('原文件已被删除');
          hasWarned = true; // 显示警告后，将变量设置为 true
        }
      } else {
        form.value.selectFile = NodeData.value.inputProperties[1].value;
      }
    } else {
      form.value.selectFile = NodeData.value.inputProperties[1].defaultValue;
    }

    form.value.separator = NodeData.value.inputProperties[2].value
      ? NodeData.value.inputProperties[2].value
      : NodeData.value.inputProperties[2].defaultValue;

    syncChangeList.value = JSON.parse(NodeData.value.inputProperties[8].value)
      ? JSON.parse(NodeData.value.inputProperties[8].value)
      : NodeData.value.inputProperties[8].defaultValue;

    await nextTick(() => {
      proxy.$refs.dataSourceRef.clearValidate();
    });
  };

  const getListFile = async () => {
    const res = await listOss(proxy.addDateRange(queryParams.value));
    console.log('res.data', res.rows);
    if (res.code === 200) {
      const data = res.rows;
      const allowedFormats = showFileFormat.value.flatMap((format) => format.value.split(','));
      const list = data
        .filter((item) => {
          const fileExtension = item.url.split('.').pop().toLowerCase();
          return allowedFormats.includes(fileExtension);
        })
        .map((item) => ({
          label: item.originalName + item.ossId,
          value: item.url,
        }));
      selectFileList.value = list;
    }
  };

  const operChange = () => {};

  const openDeploy = async () => {
    open.value = true;
    getFiledTypeUtil();
    if (changeType(form.value.selectFile) !== 'xml') {
      if (!hiddenFormData.value && !hideDataView.value) {
        await getSampleDataFileFileUtil();
      }
      await getFiledTypeUtil();
    } else {
      await getFileSampleDataUtil();
    }
  };
  const cancelEditField = () => {
    open.value = false;
  };
  const submit = () => {
    if (!syncChangeList.value?.length) {
      proxy.$modal.msgWarning('需要配置字段映射');
      return;
    }
    const arrForType = syncChangeList.value.map((item) => item.fieldType);
    if (arrForType.some((res) => !res)) {
      proxy.$modal.msgWarning('字段类型不能为空');
      return;
    }
    const arr = syncChangeList.value.map((item) => item.fieldName);
    const set = new Set(arr);
    // 判断是否有空的
    if ([...set].some((res) => !res)) {
      proxy.$modal.msgWarning('字段标准名不能为空');
      return;
    }
    // 判断值是否有重复的 如果有 提示用户
    if (arr.length !== set.size) {
      proxy.$modal.msgWarning('字段标准名不能重复');
      return;
    }
    const isNull = syncChangeList.value.some(
      (item) => item.fieldName === null || item.fieldName === '',
    );
    if (isNull) {
      proxy.$modal.msgWarning('字段标准名不能为空');
      return;
    }
    // 不能有中文
    const isChinese = syncChangeList.value.some((item) => {
      const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
      return reg.test(item.fieldName);
    });
    if (isChinese) {
      proxy.$modal.msgWarning('字段标准名不能有中文');
      return;
    }
    // 不能有空格
    const isSpace = syncChangeList.value.some((item) => {
      const reg = new RegExp('\\s', 'g');
      return reg.test(item.fieldName);
    });
    if (isSpace) {
      proxy.$modal.msgWarning('字段标准名不能有空格');
      return;
    }
    open.value = false;
  };
  const upload = () => {
    // getListFile()
  };

  const handleRemove = (file, uploadFiles) => {
    form.value.selectFile = uploadFiles;
    fileList.value = [{ name: uploadFiles.name, url: uploadFiles.url }]; // 设置 fileList
    changeSelectFile();
  };

  const handlePreview = (uploadFile) => {
    console.log(uploadFile);
  };

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgWarning(`上传文件数量不能超过 ${limit.value} 个!`);
  }

  const beforeRemove = (uploadFile, uploadFiles) => {};
  // doc/xls/ppt/txt/pdf/kjb/zip/csv
  const fileType = ref(['doc', 'xls', 'ppt', 'txt', 'pdf', 'kjb', 'zip', 'csv']);
  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    // if (fileType.value.length) {
    //   const fileName = file.name.split('.');
    //   const fileExt = fileName[fileName.length - 1];
    //   const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
    //   if (!isTypeOk) {
    //     proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
    //     return false;
    //   }
    // }
    return true;
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      // 获取文件列表
      // getListFile();
      console.log(res);
      // 把上传成功的值赋值到 输入框
      form.value.selectFile = res.data.fileName + ',' + res.data.url;
      fileList.value = [{ name: res.data.fileName, url: res.data.url }]; // 设置 fileList

      changeSelectFile();
      console.log(form.value.selectFile);

      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  }

  const uploadOnError = () => {
    proxy.$modal.msgError('上传失败');
  };
  const getFileSampleDataUtil = async () => {
    if (!form.value.selectFile) return proxy.$modal.msgWarning('未获取到 [文件]');
    if (!form.value.xml_row_tag) return proxy.$modal.msgWarning('请填写 [xml_row_tag]');

    if (!form.value.selectFile) return proxy.$modal.msgWarning('未获取到 [文件]');
    const [name, url] = form.value.selectFile.split(',');
    console.log('name', name);
    const query = {
      filePath: url || form.value.selectFile,
      fileFormat: changeType(form.value.selectFile),
      xmltag: form.value.xml_row_tag,
    };

    const res = await getSampleDataFile(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    testConnection.value = res.data;
  };

  const getSampleDataFileFileUtil = async () => {
    if (form.value.type != '1') {
      if (form.value.selectFile.includes(',')) {
        const FileList = form.value.selectFile.split(',');
        const query = {
          filePath: FileList[1],
          fileFormat: changeType(form.value.selectFile),
        };
        const res = await getSampleDataFile(query);
        if (changeType(form.value.selectFile) === 'json') {
          const patternMsg = JSON.parse(res.data);
          testConnection.value = patternMsg;
        } else {
          testConnection.value = res.data;
        }
      } else {
        const query = {
          filePath: form.value.selectFile,
          fileFormat: changeType(form.value.selectFile),
        };
        const res = await getSampleDataFile(query);
        if (changeType(form.value.selectFile) === 'json') {
          const patternMsg = JSON.parse(res.data);
          testConnection.value = patternMsg;
        } else {
          testConnection.value = res.data;
        }
      }
    } else {
      const dataSource = dataSourcesList.value.find((item) => item.id === form.value.dataSources);
      if (!dataSource) return proxy.$modal.msgWarning('暂无查找到对应数据');

      const filePath = JSON.parse(dataSource?.connectionParams);
      if (!filePath) return proxy.$modal.msgWarning('暂无查找到对应数据');

      if (!filePath.endpoint) return proxy.$modal.msgWarning('未匹配到 [endpoint]');
      if (!form.value.bucket) return proxy.$modal.msgWarning('请填写 [bucket]');
      if (!form.value.selectFile) return proxy.$modal.msgWarning('未获取到 [文件]');

      const fullPath = `${filePath.https ? 'https' : 'http'}://${filePath.endpoint}/${form.value.bucket}${form.value.selectFile}`;
      const fileFormat = changeType(form.value.selectFile);

      if (!fileFormat) return proxy.$modal.msgWarning('数据格式未获取完整');

      const query = {
        filePath: fullPath,
        fileFormat,
      };
      const res = await getSampleDataFile(query);
      // 如果是json 则 解析为json
      if (changeType(form.value.selectFile) === 'json') {
        const patternMsg = JSON.parse(res.data);
        testConnection.value = patternMsg;
      } else {
        testConnection.value = res.data;
      }
    }
    if (changeType(form.value.selectFile) === 'json') {
      try {
        const jsonStrings = testConnection.value.split('\r\n').filter(Boolean);
        testConnection.value = JSON.parse(jsonStrings);
      } catch (error) {
        console.error('Error parsing JSON:', error);
      }
    }
  };

  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'flink',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  // 样本解析
  const sampleAnl = () => {
    if (form.value.type != '1') {
      if (['xls', 'xlsx', 'excel'].includes(changeType(form.value.selectFile))) {
        form.value.separator = ',';
      }
    } else {
      // 检测类型 如果是 'xls', 'xlsx', 'excel'  则 把 分割符 设置为 ,
      if (['xls', 'xlsx', 'excel'].includes(form.value.fileFormat)) {
        form.value.separator = ',';
      }
    }

    if (changeType(form.value.selectFile) === 'xml') {
      form.value.separator = form.value.xml_row_tag;
      const regex = new RegExp(`<${form.value.separator}([^>]*)/>`, 'g');
      const matches = testConnection.value.match(regex);

      const uniqueData = new Set();
      if (matches) {
        // Handle the case where data is in attributes
        matches.forEach((match) => {
          const attributes = match.match(/(\w+)="([^"]*)"/g);
          if (!attributes) return;
          attributes.forEach((attr) => {
            const [key, value] = attr.split('=');
            const entry = JSON.stringify({
              jsonPath: key,
              fieldType: null,
              fieldName: value.replace(/"/g, ''),
            });
            uniqueData.add(entry);
          });
        });
      } else {
        // Existing logic for nested elements
        const regexNested = new RegExp(
          `<${form.value.separator}>(.*?)</${form.value.separator}>`,
          'gs',
        );
        const matchesNested = testConnection.value.match(regexNested);
        if (!matchesNested) return proxy.$modal.msgWarning('未正确匹配到数据');
        matchesNested.forEach((match) => {
          const content = match.replace(new RegExp(`</?${form.value.separator}>`, 'g'), '').trim();
          const pairs = content
            .split('\n')
            .map((line) => {
              const trimmedLine = line.trim();
              // Match standard tags with content: <tag>value</tag>
              const standardMatch = trimmedLine.match(/<(\w+)>(.*?)<\/\1>/);
              if (standardMatch) {
                return { key: standardMatch[1], value: standardMatch[2] };
              }

              // Match self-closing tags: <tag/>
              const selfClosingMatch = trimmedLine.match(/<(\w+)\/>/);
              if (selfClosingMatch) {
                return { key: selfClosingMatch[1], value: '' };
              }

              // If neither matches, return null to exclude from the results
              return null;
            })
            .filter((item) => item !== null); // Remove null entries
          pairs.forEach((item) => {
            const entry = JSON.stringify({
              jsonPath: item.key,
              fieldType: null,
              fieldName: item.value,
            });
            uniqueData.add(entry);
          });
        });
      }

      // Convert Set back to array and parse JSON strings
      syncChangeList.value = [...uniqueData].map((entry) => JSON.parse(entry));
    } else {
      // 根据传递的分割符 进行解析 testConnection.value
      const res = testConnection.value.split(form.value.separator);
      // 把解析的 数据放入到 syncChangeList.value 中
      syncChangeList.value = res.map((item) => {
        return {
          jsonPath: item,
          fieldType: 'string',
          fieldName: null,
        };
      });
      console.log('syncChangeList.value', syncChangeList.value);
    }
  };

  onMounted(async () => {
    await getListFile();
    await init();
  });

  watch(NodeData, () => {
    init();
  });

  const operChangeType = async () => {
    form.value.selectFile = null;
    form.value.dataSources = null;
    form.value.operationModel = '1';
    form.value.tableAliases = null;
    syncChangeList.value = [];
    form.value.xml_use_attr_format = null;
    form.value.xml_row_tag = null;
    form.value.sheet_name = null;
    form.value.fileFormat = null;
    form.value.separator = null;
    form.value.bucket = null;
    form.value.encoding = 'UTF-8';
    await nextTick(() => {
      proxy.$refs.dataSourceRef.clearValidate();
      // proxy.$refs.dataSourceRef.clearValidate(['encoding', 'bucket']);
      // proxy.$refs.dataSourceRef.validateField('encoding');
    });
  };

  const changeSelectFile = async () => {
    form.value.tableAliases = '';
    syncChangeList.value = [];
    form.value.xml_use_attr_format = '';
    form.value.xml_row_tag = '';
    form.value.sheet_name = '';
    form.value.fileFormat = '';
    form.value.separator = '';
    testConnection.value = '';

    form.value.operationModel = '1';
    form.value.bucket = null;
    form.value.encoding = 'UTF-8';
    form.value.tableAliases = null;
    await nextTick(() => {
      // proxy.$refs.dataSourceRef.clearValidate();
      proxy.$refs.dataSourceRef.clearValidate(['encoding', 'bucket']);
      // proxy.$refs.dataSourceRef.validateField('encoding');
    });
  };

  const dataSourcesList = ref([]);
  const getDataSourceListUtil = async () => {
    const res = await getDataSourceList({
      pageNo: 1,
      pageSize: 100000,
      dataSourceType: 'MINIO',
      workSpaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    dataSourcesList.value = res.data.totalList.map((item) => {
      return {
        ...item,
        label: item.name,
        value: item.id,
      };
    });
  };
  const encodingList = ref([
    {
      label: 'UTF-8',
      value: 'UTF-8',
    },
    {
      label: 'GBK',
      value: 'gbk',
    },
  ]);
  const BucketList = ref([]);
  const cleanData = () => {
    form.value.operationModel = '1';
    form.value.tableAliases = '';
    syncChangeList.value = [];
    form.value.xml_use_attr_format = '';
    form.value.xml_row_tag = '';
    form.value.sheet_name = '';
    form.value.fileFormat = '';
    form.value.separator = '';
    form.value.bucket = '';
    form.value.encoding = '';
    form.value.selectFile = null;
  };
  const getBucketListUtil = async (datasourceId) => {
    await cleanData();
    await getBucketRootObjsListUtil(datasourceId);
    // 切换
    const dataSource = dataSourcesList.value.find((item) => item.value === datasourceId);
    if (!dataSource) return proxy.$modal.msgWarning('暂无对应数据');
    // const bucketName = dataSource.label;

    try {
      const res = await getBucketList({
        datasourceId,
        // bucketName
      });
      if (res.code !== 200) return proxy.$modal.msgWarning(`获取桶列表失败: ${res.msg}`);
      BucketList.value = res.data.map((item) => ({
        label: item || item.name || '未知标签',
        value: item,
      }));
    } catch (error) {
      console.error('请求桶列表时发生错误:', error);
    }
  };

  const expandAll = ref(false);
  const updatePath = () => {
    const { path, isChecked } = store.pare;

    // 如果 path 里有 [数字]，则将 [数字] 替换为 [*]
    const reg = /\[\d+\]/g;
    const newPath = path.replace(reg, '[*]');
    // 处理 newPath 只保留 . 后面的数据
    const index = newPath.lastIndexOf('.');
    const newNewPath = newPath.substring(index + 1);
    const val = {
      jsonPath: newPath,
      jsonField: newNewPath,
      fieldType: 'string',
      fieldName: newNewPath,
      isParam: false,
      isOutputParam: NodeData.value.program === 'HTTP_ALG_BEFORE_API',
      isCustom: false,
    };

    if (!isChecked) {
      // 追加之前，先检查 fieldMappings.value 中是否已经存在该元素，如果存在，则不追加
      if (syncChangeList.value.findIndex((item) => item.jsonPath === newPath) === -1) {
        syncChangeList.value.push(val);
      } else {
        // 提示用户
        proxy.$modal.msgError('不能添加重复的字段');
      }
    } else {
      // fieldMappings.value = fieldMappings.value.filter(item => item.jsonPath != path)
      syncChangeList.value = syncChangeList.value.filter((item) => item.jsonPath !== newPath);
    }
  };
  const bucketRootObjsList = ref([]);
  const getBucketRootObjsListUtil = async (datasourceId) => {
    const res = await getBucketRootObjsList({ datasourceId });
    if (res.code !== 200) return proxy.$modal.msgWarning(`获取列表失败: ${res.msg}`);
    bucketRootObjsList.value = res.data.map((item) => {
      return {
        ...item,
        value: item.path,
        label: item.objectName,
      };
    });
    console.log('bucketRootObjsList.value', bucketRootObjsList.value);
  };
  const copyText = () => {
    const textarea = document.createElement('textarea');
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = JSON.stringify(testConnection.value);
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    proxy.$modal.msgSuccess('复制成功');
  };
  watch(
    () => form.value.type,
    (newVal) => {
      nextTick(async () => {
        await proxy.$refs.dataSourceRef.clearValidate(['encoding', 'bucket']);
      });
    },
    { immediate: true, deep: true },
  );
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .per {
    height: 300px;
    overflow: auto;
    // 使用原样展示
    white-space: pre-wrap;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .test-connection {
    max-height: 260px;
    min-height: 260px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      border-left: 1px solid #ebeef5;
    }
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }

  .btn-item {
    width: 55px;
    height: 28px;

    &.is-plain {
      color: $--base-color-primary;

      :deep svg {
        fill: $--base-btn-primary-plain;

        path {
          stroke: $--base-color-primary;
        }
      }
    }

    &.is-disabled {
      opacity: 0.7;
    }
  }

  .upload-demo {
    width: 400px;
    position: relative;
  }

  .upload-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    .btn-item {
      width: 100%;
      height: 40px;
      position: relative;
      z-index: 10;
    }
  }

  .exampleData {
    width: 100%;
  }

  .copy-text {
    display: flex;
    justify-content: flex-end;
  }
</style>
