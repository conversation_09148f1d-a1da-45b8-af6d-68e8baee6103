<template>
  <div class="app-container">
    <el-button icon="back" class="container-callback" type="primary" @click="close">返回</el-button>

    <!-- <el-tabs v-model="activeTab" class="tabs">
      <el-tab-pane label="基本信息" name="userinfo">
        <userInfo :user="state.user" :other="other" :type="'user'" />
      </el-tab-pane>
      <el-tab-pane label="品牌信息" name="brandInfo">
        <userInfo :user="state.user" :other="other" :type="'brand'" />
      </el-tab-pane>
      <el-tab-pane label="安全设置" name="resetPwd">
        <resetPwd />
      </el-tab-pane>
      <el-tab-pane label="许可证" name="license">
        <license />
      </el-tab-pane>
    </el-tabs> -->
    <TabSwitch class="tab-container" :title-list="tab" @change="getData"></TabSwitch>
    <div v-if="activeTab == 'userInfo'">
      <userInfo :user="state.user" :other="other" :type="'user'" />
    </div>
    <div v-else-if="activeTab == 'brand'">
      <userInfo :user="state.user" :other="other" :type="'brand'" />
    </div>
    <div v-else-if="activeTab == 'resetPwd'">
      <resetPwd />
    </div>
    <div v-else-if="activeTab == 'license'">
      <license />
    </div>
  </div>
</template>

<script setup name="Profile">
  import TabSwitch from '../../../APIService/bazaarApproval/applyApprovalDetail/components/TabSwitch.vue';
  import { getCompanyInfo } from '@/api/login';
  import { getUserProfile } from '@/api/system/user';
  import router from '@/router';
  import resetPwd from './resetPwd';
  import userInfo from './userInfo';
  import license from './license';

  const activeTab = ref(null);
  const state = reactive({
    user: {},
    roleGroup: {},
    postGroup: {},
  });

  function getUser() {
    getUserProfile().then((response) => {
      state.user = response.data.user;
      state.roleGroup = response.data.roleGroup;
      state.postGroup = response.data.postGroup;
      activeTab.value = 'userInfo';
    });
  }

  const tab = ref([
    { value: 'userInfo', label: '基本信息', isActive: true },
    { value: 'brand', label: '品牌信息', isActive: false },
    { value: 'resetPwd', label: '安全设置', isActive: false },
    { value: 'license', label: '许可证', isActive: false },
  ]);

  const getData = (data) => {
    if (!data) return false;
    tab.value = tab.value.map((res) => {
      res.isActive = false;
      if (res.value === data) {
        res.isActive = true;
      }
      return res;
    });
    activeTab.value = data;
  };

  const other = ref({});
  const url = new URL(window.location.origin);

  const getCompanyInfoUtil = async () => {
    try {
      const res = await getCompanyInfo();
      if (Array.isArray(res.data)) {
        other.value = res.data.map((item) => {
          const processPath = (path) => {
            const extension = path.substring(path.lastIndexOf('.') + 1);
            if (/[A-Z]$/.test(extension)) {
              const newExtension = extension.toLowerCase();
              return path.substring(0, path.lastIndexOf('.')) + '.' + newExtension;
            } else {
              return path;
            }
          };
          return {
            ...item,
            logoOfSystem: url + 'xugurtp-file/' + processPath(item.logoOfSystem),
            icon: url + 'xugurtp-file/' + processPath(item.icon),
            apiLoginImg: url + 'xugurtp-file/' + processPath(item.apiLoginImg),
          };
        });
      } else {
        other.value = [];
      }
    } catch (error) {
      other.value = [];
    }
  };
  onMounted(() => {
    getCompanyInfoUtil();
    getUser();
  });
  /** 关闭按钮 */
  function close() {
    //  回到上一次的页面
    router.go(-1);
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .tab-container {
    width: 760px;
    margin: 0 auto;
  }
  .app-container {
    min-height: 100%;
    width: 100%;
    overflow: hidden;
    padding: 20px;
    background: $--base-color-box-bg;
    border-radius: 4px;
    position: relative;
    // margin-top: 20px;
    .container-callback {
      position: absolute;
      top: 20px;
      left: 20px;
    }
  }

  :deep .update-line-icon-icon-save {
    & > path {
      &:nth-child(2) {
        stroke: $--base-color-primary;
      }
    }
  }
  :deep path {
    stroke: currentColor;
  }
</style>
