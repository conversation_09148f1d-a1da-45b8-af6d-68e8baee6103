<template>
  <div class="container">
    <div
      v-for="title in titleList"
      :key="title.value"
      :class="[
        'title',
        title.isActive ? 'select' : 'notAT',
        title.disabled ? 'disabled' : ''
      ]"
      :style="{ borderRadius: border }"
      @click="handleClick(title)"
    >
      <span :class="title.isActive ? 'content current' : 'content'">{{ title.label }}</span>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    titleList: {
      type: Array,
      default: () => [],
    },
    width: {
      type: String,
      default: '500px',
    },
    height: {
      type: String,
      default: '46px',
    },
    border: {
      type: String,
      default: '4px',
    },
  });
  const emit = defineEmits('change');
  const handleClick = (data) => {
    // 禁用状态不允许点击
    if (data.disabled) return;
    // 已展示的模块则不需要进行切换逻辑
    if (data.isActive) return emit('change', false);
    props.titleList.forEach((val) => {
      if (val.value === data.value) {
        val.isActive = true;
      } else {
        val.isActive = false;
      }
    });
    emit('change', data.value);
  };
</script>

<style scoped lang="scss">
  $bg-color: #f2f4f8;
  $bg-click-color: #fff;
  $text-color: #000;
  $text-click-color: #1269ff;

  .container {
    height: 100%;
    max-height: 34px;
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    // padding: 6px;
    background: $bg-color;
    // border-radius: 4px;
    box-sizing: border-box;
    border-radius: 4px;

    .select {
      background: $bg-click-color;
      // border-radius: 4px;
      border: 1px solid #fff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
    }

    .notAT:hover .content {
      color: $text-click-color;
    }

    .disabled {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover .content {
        color: $text-color !important;
      }
    }

    .title {
      flex: 1;
      height: 100%;
      line-height: 26px;
      padding: 0;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      box-sizing: border-box;
      cursor: pointer;

      .content {
        display: inline-block;
        // width: 64px;
        height: 22px;
        color: $text-color;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .current {
        color: $text-click-color;
      }
    }
  }
</style>
