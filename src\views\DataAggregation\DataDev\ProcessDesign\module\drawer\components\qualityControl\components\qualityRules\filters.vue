<template>
  <el-form-item
    v-if="isShow"
    :label="`${isSource}表检测列`"
    :prop="`${propPath}.field`"
    :rules="rules.field"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.field"
      :placeholder="`请选择${isSource}表检测列`"
      clearable
      :disabled="!canvasActions"
    >
      <el-option
        v-for="item in dataFieldList"
        :key="item.columnName"
        :label="item.columnName"
        :value="item.columnName"
      ></el-option>
    </el-select>
  </el-form-item>
  <el-form-item :label="`${isSource}表过滤条件`" :prop="`${propPath}.filter`" :rules="rules.filter">
    <!-- 输入框 -->
    <el-input
      v-model="dataObj.filter"
      :placeholder="`请选择${isSource}表过滤条件`"
      :disabled="!canvasActions"
    />
  </el-form-item>
</template>

<script setup>
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const form = qualityRulesStore.$state;
  const props = defineProps({
    dataObj: {
      type: Object,
      default: () => ({}),
    },
    rules: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
    useType: {
      type: String,
      default: 'source',
    },
    canvasActions: {
      type: Boolean,
      default: true,
    },
    propPath: {
      type: String,
      required: true,
    },
  });
  const { dataObj, rules, index, useType, canvasActions } = toRefs(props);
  const isShow = computed(() => {
    return (
      form.dataObj[index.value].ruleId !== 2 &&
      form.dataObj[index.value].ruleId !== 3 &&
      form.dataObj[index.value].ruleId !== 10
    );
  });

  const isSource = computed(() => {
    return useType.value === 'source' ? '源' : '目标';
  });

  const dataFieldList = computed(() => {
    return form.dataObj[index.value][useType.value]?.dataFieldList;
  });
</script>

<style lang="scss" scoped></style>
