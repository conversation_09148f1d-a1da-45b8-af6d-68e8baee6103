<template>
  <div class="two-pane">
    <div class="two-pane-left" :class="leftClass">
      <slot name="pane-left"></slot>
    </div>
    <div class="two-pane-right" :class="rightClass">
      <slot name="pane-right"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TwoPane',
    props: {
      // 方向，默认horizontal,左右两部分
      split: {
        validator(value) {
          return ['vertical', 'horizontal'].indexOf(value) >= 0;
        },
        default: 'horizontal',
      },
      // 左侧样式
      leftClass: {
        default: '',
      },
      // 右侧样式
      rightClass: {
        default: '',
      },
    },
  };
</script>

<style lang="scss" scoped>
  .two-pane {
    height: 100%;
    box-sizing: border-box;
    display: flex;

    &-left {
      width: 280px;
      // background-color: $white;
      background-color: white;
    }

    &-right {
      margin-left: 12px;
      width: calc(100% - 280px - 12px);
      flex: 1;
      // background-color: $white;
      background-color: white;
    }
  }
</style>
