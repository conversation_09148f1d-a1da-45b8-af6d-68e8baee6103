<template>
  <!-- <el-page-header @back="goBack"></el-page-header> -->
  <div class="app-container">
    <el-button plain icon="ArrowLeft" type="primary" @click="goBack">返回上一层</el-button>
    <section class="head-title">
      <el-row>
        <el-col :span="8">
          <span>数据源名称:</span>
          <span>{{ form.name }}</span>
        </el-col>
        <el-col :span="5">
          <span>类型:</span>
          <span>{{ form.type }}</span>
        </el-col>
        <el-col :span="5">
          <span>用户名: </span>
          <span> {{ form.user }}</span>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <span style="color: #409eff">JDB_CURL :</span>
          <el-tooltip class="box-item" effect="dark" :content="form.jdbcUrl" placement="bottom">
            <span
              style="
                display: inline-block;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ form.jdbcUrl }}
            </span>
          </el-tooltip>
        </el-col>
        <el-col :span="5">
          <span>IP主机名:</span>
          <span>{{ form.host }}</span>
        </el-col>
        <el-col :span="5">
          <span>描述:</span>
          <span>{{ form.note }}</span>
        </el-col>
      </el-row>
    </section>
    <el-divider></el-divider>

    <el-row v-if="form.type != 'API'" :gutter="20">
      <el-col :span="7" :xs="24">
        <div class="head-container" style="min-height: 300px; max-height: 400px; overflow-y: auto">
          <el-tree
            ref="menuTree"
            v-loading="loadingForTree"
            style="padding-left: 18px"
            :data="dataTree"
            :props="defaultProps"
            :expand-on-click-node="false"
            node-key="label"
            :default-expand-all="false"
            @node-click="handleNodeClick"
          >
            <template #default="{ data, node }">
              <!-- 未展开时的图标 -->
              <el-icon
                v-show="data?.children?.length"
                v-if="!node.expanded"
                class="data-icon icon"
                @click.stop="expandNode(data, node)"
              >
                <caretRight />
              </el-icon>
              <!-- 展开时的图标 -->
              <el-icon
                v-show="data?.children?.length"
                v-else
                class="data-icon icon"
                @click.stop="collapseNode(data, node)"
              >
                <caretBottom />
              </el-icon>
              <span v-show="node.level == '1'" class="data-icon"
                ><img src="@/assets/icons/源.png"
              /></span>
              <span v-show="node.level == '2'" class="data-icon"
                ><img src="@/assets/icons/库.png"
              /></span>
              <span v-show="node.level == '3'" class="data-icon"
                ><img src="@/assets/icons/表.png"
              /></span>
              <span> {{ data.label }}</span>
              <!-- <span v-if="node.level == 2">
                🌗
                {{ data.label }}
              </span>
              <span v-if="node.level == 3">
                🌘
                {{ data.label }}
              </span> -->
            </template>
          </el-tree>
        </div>
      </el-col>

      <el-col :span="17" :xs="24">
        <!-- <h3>{{ 表格名称 }}</h3> -->
        <h3>{{ titleName }}</h3>
        <el-table
          v-if="showInfo"
          border
          :show-header="false"
          :cell-style="columnStyle"
          :data="dataForTable"
        >
          <el-table-column width="120px" prop="name" />
          <el-table-column prop="value" />
        </el-table>

        <el-table v-if="showTable" v-loading="loading" height="450" :data="dataInfo">
          <el-table-column align="center" label="序号" width="50px" type="index"> </el-table-column>
          <el-table-column align="center" label="表名" prop="tableName" />
        </el-table>

        <el-table v-if="showColumn" v-loading="loading" height="450" :data="columnInfo">
          <el-table-column align="center" label="序号" width="50px" type="index"> </el-table-column>
          <el-table-column align="center" label="字段名称" prop="columnName" />
          <el-table-column align="center" label="字段类型" prop="columnType" />
          <el-table-column align="center" label="字段备注" prop="comment" />
        </el-table>

        <!-- 字段 -->
        <el-table v-if="showTopic" v-loading="loading" height="450" :data="topicInfo">
          <el-table-column align="center" label="序号" width="50px" type="index"> </el-table-column>
          <el-table-column align="center" label="topic" prop="tableName" />
        </el-table>

        <el-divider></el-divider>

        <!-- <div v-if="false">
          <h3>用户列表</h3>
          <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="探索字段" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="探索指标" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          </el-row>
           <el-table :data="userList" @selection-change="handleSelectionChange">
          <el-table-column label="字段名称" key="userName" prop="userName" v-if="columns[1].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="字段类型" key="nickName" prop="nickName" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
           </el-table>
        </div> -->
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  // import DataTree from "@/components/treePlus/src/tree.vue";
  import {
    getDatabaseList,
    getFieldList,
    getInfoById,
    getTableList,
    updateUi,
    apiDetail,
  } from '@/api/dataSourceManageApi';
  import { onMounted, toRefs } from 'vue';
  import { useRoute } from 'vue-router';
  // import { isLeaf } from "element-plus/es/utils";
  // import { tr } from "element-plus/es/locale";
  // import { defineProps } from 'vue';
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '字段名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '字段名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '字段类型不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const route = useRoute();
  const DatasourceId = route.query.DatasourceId;
  const loadingForTree = ref(false);
  // 页面加载时获取数据源的详情
  onMounted(() => {
    if (route.query.type != 'API') {
      getInfoById(DatasourceId).then((res) => {
        form.value = res.data;
        form.value.jdbcUrl = JSON.parse(res.data.connectionParams).jdbcUrl;
        form.value.host = JSON.parse(res.data.connectionParams).host;
        dataTree.value[0].label = res.data.name;
        // if(res.data.type == 'KAFKA') {
        //   dataTree.value[0].label = res.data.name
        // }
        form.value.user = JSON.parse(res.data.connectionParams).user;
      });
    } else {
      apiDetail(DatasourceId).then((res) => {
        console.log('res', res.data);
        form.value.name = res.data.name;
        form.value.note = res.data.desc;
        form.value.type = res.data.dataSourceType;
        form.value.jdbcUrl = res.data.url;
      });
    }
  });
  const router = useRouter();
  // 组件接收传参
  const props = defineProps({
    title: {
      type: String,
      default: () => '',
    },
  });

  const defaultProps = {
    children: 'children',
    label: 'label',
  };

  const dataTree = ref([
    {
      label: '',
      children: [],
    },
  ]);

  const loading = ref(false);
  const showInfo = ref(false);
  const showTable = ref(false);
  const showColumn = ref(false);
  const showTopic = ref(false);
  const topicInfo = ref([]);
  const detailInfo = ref({});
  const dataInfo = ref([]);
  const titleName = ref(null);
  const columnInfo = ref([]);
  // 获取详细信息和子级列表
  const handleNodeClick = (data, node, obj) => {
    let tableObj = null;
    if (form.value.type == 'ORACLE') {
      tableObj = { schema: data.label, datasourceId: DatasourceId };
    } else {
      tableObj = { databaseName: data.label, datasourceId: DatasourceId };
    }
    if (data?.children?.length) {
      if (node.level == 1) {
        showTopic.value = false;
        showTable.value = false;
        titleName.value = data.label;
        showColumn.value = false;
        loading.value = true;
        updateUi({ id: DatasourceId }).then((res) => {
          showInfo.value = true;
          detailInfo.value = { ...res.data };
          loading.value = false;
        });
        node.expanded = !node.expanded;
      }
      showInfo.value = false;
      if (data.key) {
        showTopic.value = false;
        titleName.value = data.label;
        showInfo.value = false;
        showTable.value = true;
        loading.value = true;
        showColumn.value = false;
        getTableList(tableObj).then((res) => {
          if (res.data.length) {
            dataInfo.value = res.data;
          } else {
            dataInfo.value = [];
            showTable.value = false;
          }
          loading.value = false;
          node.expanded = !node.expanded;
        });
      }
      // showTable.value = false
    } else {
      expandNode(data, node);
      if (node.level == 1) {
        showColumn.value = false;
        titleName.value = data.label;
        updateUi({ id: DatasourceId }).then((res) => {
          detailInfo.value = { ...res.data };
          showInfo.value = true;
        });
        node.expanded = !node.expanded;
      }
      showInfo.value = false;
      if (form.value.type != 'KAFKA') {
        if (data.key) {
          showTopic.value = false;
          titleName.value = data.label;
          loading.value = true;
          showColumn.value = false;
          getTableList(tableObj)
            .then((res) => {
              if (res.data.length) {
                showTable.value = true;
                dataInfo.value = res.data;
              } else {
                dataInfo.value = [];
                showTable.value = false;
              }
              loading.value = false;
              // node.expanded = !node.expanded
            })
            .catch((error) => (loading.value = false));
        }
        showTable.value = false;

        if (data.table) {
          titleName.value = data.label;
          showTopic.value = false;
          showColumn.value = true;
          loading.value = true;
          if (form.value.type != 'XUGU' && form.value.type != 'ORACLE') {
            getFieldList({
              tableName: data.label,
              databaseName: node.parent.data.label,
              datasourceId: DatasourceId,
            })
              .then((res) => {
                if (res.data.length) {
                  columnInfo.value = res.data;
                } else {
                  columnInfo.value = [];
                }
                loading.value = false;
              })
              .catch((error) => (loading.value = false));
          } else {
            getFieldList({
              tableName: data.label,
              schema: node.parent.data.label,
              datasourceId: DatasourceId,
            })
              .then((res) => {
                if (res.data.length) {
                  columnInfo.value = res.data;
                } else {
                  columnInfo.value = [];
                }
                loading.value = false;
              })
              .catch((error) => (loading.value = false));
          }
        }
      } else {
        if (data.key) {
          showTopic.value = true;
          loading.value = true;
          getTableList(tableObj)
            .then((res) => {
              topicInfo.value = res.data;
              loading.value = false;
              loadingForTree.value = false;
            })
            .catch((error) => {
              loading.value = false;
              loadingForTree.value = false;
            });
        }
      }
    }
  };

  // 表格竖着展示
  const dataForTable = computed(() => {
    return [
      {
        id: 1,
        name: '数据源编号',
        value: detailInfo.value.id,
      },
      {
        id: 2,
        name: '连接名称',
        value: detailInfo.value.name,
      },
      {
        id: 3,
        name: '类型',
        value: detailInfo.value.type,
      },
      {
        id: 4,
        name: '主机',
        value: detailInfo.value.host,
      },
      {
        id: 5,
        name: '登录用户名',
        value: detailInfo.value.userName,
      },
      {
        id: 6,
        name: '创建时间',
        value: detailInfo.value.createTime,
      },
    ];
  });

  // 自定义列背景色
  const columnStyle = (row, column, columnIndex, rowIndex) => {
    // 设置第一列的样式
    if (row.columnIndex === 0) {
      return { 'background-color': '#f3f6fc' };
    }
    return { 'background-color': '#ffffff' };
  };

  // 展开节点并请求相应子级数据列表
  const expandNode = (data, node) => {
    let tableObj = null;
    loadingForTree.value = true;
    if (form.value.type == 'ORACLE') {
      tableObj = { schema: data.label, datasourceId: DatasourceId };
    } else {
      tableObj = { databaseName: data.label, datasourceId: DatasourceId };
    }
    if (data?.children?.length) {
      loadingForTree.value = false;
      node.expanded = true;
      return;
    }
    if (node.level == 1) {
      titleName.value = data.label;
      getDatabaseList({ datasourceId: DatasourceId })
        .then((res) => {
          if (res.data.length) {
            res.data.map((i) => {
              data.children.push({ label: i, children: [], key: '表' });
              return data.children;
            });
          } else {
            data.children = [];
          }
          loadingForTree.value = false;
          node.expanded = true;
        })
        .catch((error) => (loadingForTree.value = false));
    }
    if (form.value.type != 'KAFKA') {
      if (data.key) {
        getTableList(tableObj)
          .then((res) => {
            if (res.data.length) {
              res.data.map((i) => {
                data.children.push({ label: i.tableName, table: '1' });
                return data.children;
              });
            } else {
              data.children = [];
            }
            loadingForTree.value = false;
            node.expanded = true;
          })
          .catch((error) => (loadingForTree.value = false));
      }
    }
    if (data.table) {
      if (form.value.type != 'XUGU' && form.value.type != 'ORACLE') {
        getFieldList({
          tableName: data.label,
          databaseName: node.parent.data.label,
          datasourceId: DatasourceId,
        })
          .then((res) => {
            if (res.data.length) {
              dataInfo.value = res.data;
            } else {
              dataInfo.value = [];
            }
            loadingForTree.value = false;
          })
          .catch((error) => (loadingForTree.value = false));
      } else {
        getFieldList({
          tableName: data.label,
          schema: node.parent.data.label,
          datasourceId: DatasourceId,
        })
          .then((res) => {
            if (res.data.length) {
              dataInfo.value = res.data;
            } else {
              dataInfo.value = [];
            }
            loadingForTree.value = false;
          })
          .catch((error) => (loadingForTree.value = false));
      }
    }
  };

  // 关闭节点
  const collapseNode = (data, node) => {
    node.expanded = false;
  };

  const deptName = ref('');
  // let handleNodeClick = (data,node,obj) => {

  // console.log(data);

  // if(node.level ==1) {
  //   list(data.label).then(res =>
  //   {
  //       res.data.map(i => {
  //       data.children.push({label:i.name,id:i.id,children:[]})
  //       return data.children
  //   })
  //   })
  // } else if (node.level == 2) {
  //    getDatabaseList(data.id).then(res => {
  //     res.data.map(i => {
  //       data.children.push({label:i,children:[]})
  //       return data.children
  //     })

  // })
  // } else if (node.level == 3) {
  //    getTableList({databaseName:data.label,datasourceId:node.parent.data.id}).then(res=>{
  //     if(res.length) {
  //       console.log(123);
  //     }
  //    })
  // } else {

  // }
  // };

  //
  const userList = ref([]);
  const showSearch = ref(true);
  const columns = ref([
    { key: 0, label: '  序号', visible: true },
    { key: 1, label: '字段名称', visible: true },
    { key: 2, label: '字段类型', visible: true },
    { key: 3, label: '备注', visible: true },
    { key: 4, label: '手机号码', visible: true },
    { key: 5, label: '状态', visible: true },
    { key: 6, label: '创建时间', visible: true },
  ]);
  const dateRange = ref([]);
  const initPassword = ref(undefined);

  const multipleSelection = ref([]);
  const selectRows = ref([]);

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  };

  const handleSelectionChange = (val) => {
    multipleSelection.value = val;
  };

  const goBack = () => {
    // console.log('go back')
    router.push('/centralAdmin/dataSourcemanage');
  };
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 16px;
    margin-top: 20px;
  }

  .head-title > .el-row {
    margin-bottom: 10px;

    //  每个span 左右间距
    span {
      margin-right: 10px;
    }
  }

  .icon {
    margin-left: -18px;
  }

  .data-icon {
    margin-right: 5px;
    font-size: 12px;

    img {
      position: relative;
      top: 3px;
      left: 0;
      width: 15px;
      height: 15px;
    }
  }

  :deep(.el-tree-node__content > .el-tree-node__expand-icon) {
    position: absolute;
    opacity: 0;
  }

  :deep(.el-tree-node__content) {
    position: relative;
  }
</style>
