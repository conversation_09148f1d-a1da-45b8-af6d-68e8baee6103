<template>
  <!-- table  -->
  <div style="padding-top: 20px">
    <el-table :data="tableData" height="520">
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :label="column.label"
        :prop="column.prop"
        v-bind="column"
      />
      <!-- 操作 -->
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="handleDetail(row)">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.4987 3.16666H5.16536C4.42898 3.16666 3.83203 3.76361 3.83203 4.49999V11.5C3.83203 12.2364 4.42899 12.8333 5.16536 12.8333H10.832C11.5684 12.8333 12.1654 12.2364 12.1654 11.5V6.83332M8.4987 3.16666V5.49999C8.4987 6.23637 9.09565 6.83332 9.83203 6.83332H12.1654M8.4987 3.16666L12.1654 6.83332"
                stroke="#1269FF"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.83203 10.5H10.1654"
                stroke="#1269FF"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.83203 8.5H7.4987"
                stroke="#1269FF"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 分页 -->
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :pager-count="maxCount"
    :total="total"
    @pagination="getWorkFlowsUtil"
  />
</template>

<script setup>
  import { getWorkFlows } from '~/src/api/dataGovernance';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    assetData: {
      type: String,
      required: true,
    },
  });
  const { assetData } = toRefs(props);
  // mock 数据
  const tableData = ref([]);
  const columns = ref([
    { label: '工作流名称', prop: 'flowName', showOverflowTooltip: true, width: 200 },
    { label: '所属流程组', prop: 'flowGroupName', showOverflowTooltip: true, width: 200 },
    {
      label: '节点名称',
      prop: 'nodeName',
      showOverflowTooltip: true,
      width: 200,
    },
    { label: '创建时间', prop: 'createTime' },
    { label: '创建人', prop: 'createUser' },
  ]);

  const total = ref(0);
  const maxCount = ref(7);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const getWorkFlowsUtil = async () => {
    const query = {
      ...queryParams,
      ...assetData.value,
    };

     // 判断 params 的  schema 是否为空  -  为空则删除该字段
     if (query.schema === '-') {
      delete query.schema;
    }
    
    const res = await getWorkFlows(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.rows.length) return proxy.$modal.msgWarning('暂无数据');
    tableData.value = res.rows;
    total.value = res.total;
  };
  const router = useRouter();

  const handleDetail = (row) => {
    router.push({
      path: '/DataAggregation/lookVersions',
      //   path: '/DataAggregation/ProcessDesign',
      query: { id: row.id, name: row.flowName, type: 'dataGovernance' },
    });
  };
  onMounted(() => {
    getWorkFlowsUtil();
  });
</script>

<style lang="scss" scoped></style>
