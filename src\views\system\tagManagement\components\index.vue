<template>
  <div class="work-detail">
    <el-button type="primary" icon="DArrowLeft" class="call-back-btn" @click="callback"
      >返回
    </el-button>
    <span class="detail-title">标签详情</span>

    <div class="detail-card-box">
      <div class="card-title">
        <div class="card-title-icon">
          <el-icon>
            <UploadFilled />
          </el-icon>
        </div>
        <span class="card-title-text">
          <span class="card-title-name">{{ detailInfo.name || '标签名称' }}</span>
        </span>
      </div>
      <div class="card-content">
        <el-descriptions>
          <el-descriptions-item
            v-for="(des, desIndex) in descriptions"
            :key="desIndex"
            :label="des.label"
          >
            <span v-if="des.label === '执行状态：'" :class="`card-item card-item-${des.value}`">
              {{ des.valueLabel }}
            </span>
            <span v-else :class="`card-item`">{{ des.value }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="other-btn-box">
      <div class="btn-box-table">
        <el-radio-group v-model="dataType" @change="changeDataSource">
          <el-radio-button label="1">标签值</el-radio-button>
          <el-radio-button label="2">关联资产</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="table-box">
      <el-form
        v-if="dataType !== '1'"
        ref="queryRef"
        v-model="tableListInfo"
        :inline="true"
        label-width="100px"
        class="search-box"
      >
        <el-form-item label="资产名称" prop="field">
          <el-input
            v-model="tableListInfo.searchInfo.assetName"
            placeholder="请选择字段"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-form-item label="资产类型" prop="field">
          <!-- 下拉框 -->
          <el-select
            v-model="tableListInfo.searchInfo.type"
            placeholder="请选择字段"
            style="width: 250px"
            clearable
          >
            <el-option label="表" value="table" />
            <el-option label="API" value="api" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签值" prop="field">
          <el-input
            v-model="tableListInfo.searchInfo.labelvalue"
            placeholder="请选择字段"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" />
          <el-button icon="Refresh" @click="resetQuery" />
        </el-form-item>
      </el-form>
      <el-row :gutter="10" style="margin-bottom: 20px">
        <el-col v-if="dataType === '1'" :span="1.5">
          <el-button
            v-hasPermi="['system:role:add']"
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            >新增标签值</el-button
          >
        </el-col>

        <el-col v-if="dataType === '1'" :span="1.5">
          <el-button
            v-hasPermi="['system:role:remove']"
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="tableListener.deleteItem"
            >批量删除</el-button
          >
        </el-col>

        <el-col v-else :span="1.5">
          <!-- 批量解绑 -->
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="tableListener.unbind"
            >批量解绑</el-button
          >
        </el-col>
        <right-toolbar
          v-model:show-search="showSearch"
          :columns="tableInfo.columns"
          @query-table="changeDataSource"
        />
      </el-row>
      <el-table
        ref="tableRef"
        :data="tableInfo.tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="tableListener.selectChange"
      >
        <el-table-column type="selection" width="55" />
        <template v-for="(item, index) in tableInfo.columns" :key="index">
          <!-- <el-table-column v-if="item.visible && item.props === 'labels'">
            <TagsEdit
              v-if="item.prop === 'tags'"
              :baseInfo="deelTagsInfo(item.labels)"
              :assetId="item.id"
              :hasEdit="true"
              type="api"
            ></TagsEdit>
          </el-table-column> -->
          <el-table-column v-if="item.visible" v-bind="item">
            <template v-if="item.prop === 'labels'" #default="scope">
              <TagsEdit
                :base-info="scope.row.labelsData"
                :asset-id="scope.row.id"
                :asset-name="scope.row.assetName"
                :has-edit="false"
                type="api"
                @after-edit="() => {}"
                @set-edit-tag="() => {}"
              ></TagsEdit>
            </template>
            <template v-else #default="scope">{{ scope.row[item.prop] }}</template>
          </el-table-column>
        </template>
        <el-table-column label="操作" fixed="right" min-width="200" width="350">
          <template #default="scope">
            <template v-if="dataType == 1">
              <el-button type="text" size="small" @click="tableListener.show(scope)">
                查看资产
              </el-button>
              <el-button type="text" size="small" @click="tableListener.insert(scope)">
                绑定资产
              </el-button>
              <el-button
                type="text"
                size="small"
                :disabled="scope.row.assetNums > 0"
                @click="tableListener.deleteItem(scope)"
              >
                删除
              </el-button>
            </template>
            <el-button
              v-if="dataType == 2"
              type="text"
              size="small"
              @click="tableListener.unbind(scope)"
            >
              解绑资产
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="margin-bottom: 20px">
      <!-- 分页 -->
      <pagination
        v-show="searchInfo.queryParams.total > 0"
        v-model:page="searchInfo.queryParams.pageNum"
        v-model:limit="searchInfo.queryParams.pageSize"
        :pager-count="searchInfo.queryParams.maxCount"
        :total="searchInfo.queryParams.total"
        @pagination="changeDataSource"
      />
    </div>
  </div>
  <!-- 添加或修改 -->
  <el-dialog v-model="open" :title="title" width="650px" @close="cancel">
    <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="标签键" prop="labelKeyName">
        <el-input v-model="form.labelKeyName" placeholder="请输入标签键" :disabled="true" />
      </el-form-item>
      <el-form-item label="标签值" prop="labelValue">
        <el-button icon="Plus" class="add-tag-value-btn" @click="addTagValue" />
        <div class="tag-value-box">
          <div v-if="form.labelValue?.length > 0" class="input-box">
            <div v-for="(value, index) in form.labelValue" :key="index" class="tag-value-input">
              <el-input
                v-model="value.name"
                placeholder="空值"
                show-word-limit
                maxlength="30"
                :disabled="value.id && labelValueList[index] !== null"
              />
              <el-button
                v-if="!value.id"
                type="danger"
                icon="Delete"
                @click="removeTagValue(index)"
              />
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入内容"
          show-word-limit
          maxlength="100"
          :disabled="true"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 绑定资产 -->
  <el-dialog
    v-model="dialogInfo.dialogVisible"
    title="绑定资产"
    width="60%"
    top="100px"
    :draggable="true"
    @close="dialogListener.closeDialog"
  >
    <div class="detail-header">
      <div>
        <b>标签</b>
        |
        <el-tooltip
          :disabled="dialogInfo.labelChose?.length <= 35"
          :content="dialogInfo.labelChose"
          placement="bottom"
          effect="light"
        >
          <span class="labelS">
            {{
              dialogInfo.labelChose?.length > 35
                ? dialogInfo.labelChose?.slice(0, 35) + '...'
                : dialogInfo.labelChose
            }}
          </span>
        </el-tooltip>
        <p>
          <spam>将为选择的资产打上该标签</spam>
        </p>
      </div>
    </div>

    <div class="form-box">
      <el-form
        ref=""
        v-model="dialogInfo.searchForm"
        label-position="left"
        inline
        label-width="auto"
      >
        <el-form-item label="资产类型" prop="type">
          <el-select
            v-model="dialogInfo.searchForm.type"
            placeholder="请选择"
            style="width: 250px"
            clearable
            @change="changeType"
          >
            <el-option label="表" value="1" />
            <el-option label="API" value="2" />
          </el-select>
        </el-form-item>
        <template v-if="dialogInfo.searchForm.type === '1'">
          <el-form-item label="源类型" prop="datasourceType">
            <el-select
              v-model="dialogInfo.searchForm.datasourceType"
              placeholder="请选择"
              style="width: 250px"
              clearable
              @change="dialogListener.changeDataSource"
            >
              <el-option
                v-for="(option, index) in constants.databaseType"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="datasourceId">
            <el-select
              v-model="dialogInfo.searchForm.datasourceId"
              placeholder="请选择"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="(option, index) in options.databaseOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="库名" prop="databaseName">
            <el-input
              v-model="dialogInfo.searchForm.databaseName"
              placeholder="请输入库名"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="模式名" prop="schemaName">
            <el-input
              v-model="dialogInfo.searchForm.schemaName"
              placeholder="请输入模式名"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="表名称" prop="tableName">
            <el-input
              v-model="dialogInfo.searchForm.tableName"
              placeholder="请输入表名称"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
        </template>
        <template v-if="dialogInfo.searchForm.type === '2'">
          <el-form-item label="API名称" prop="apiName">
            <!-- 输入框 -->
            <el-input
              v-model="dialogInfo.searchForm.apiName"
              placeholder="请输入API名称"
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item label="API类型" prop="apiType">
            <!-- 下拉框 -->
            <el-select
              v-model="dialogInfo.searchForm.apiType"
              placeholder="请选择"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="标签" prop="label">
          <!-- 级联选择器  -->
          <el-cascader
            v-model="dialogInfo.searchForm.label"
            :options="cascaderOptions"
            placeholder="请选择"
            :props="propsForCas"
            collapse-tags
            collapse-tags-tooltip
            clearable
            class="search-label"
          />
        </el-form-item>
        <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
          <el-button
            type="primary"
            icon="Search"
            class="icon-btn"
            @click="dialogListener.tableSearch"
          ></el-button>
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="dialogListener.reset"></el-button>
        </el-tooltip>
      </el-form>
    </div>
    <div class="dialog-table-box">
      <div class="table-box">
        <el-table
          ref="dialogTableRef"
          :data="dialogInfo.data"
          height="260px"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
          @selection-change="dialogListener.selectChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column v-for="(item, index) in dialogInfo.columns" :key="index" v-bind="item">
            <template v-if="item.prop === 'labels'" #default="scope">
              <TagsEdit
                :base-info="scope.row.labelsData"
                :asset-id="scope.row.id"
                :asset-name="scope.row.assetName"
                :has-edit="false"
                type="api"
                @after-edit="() => {}"
                @set-edit-tag="() => {}"
              ></TagsEdit>
            </template>
            <template v-else #default="scope">{{ scope.row[item.prop] }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div style="margin-bottom: 20px">
        <!-- 分页 -->
        <pagination
          v-show="dialogInfo.total > 0"
          v-model:page="dialogInfo.queryParams.pageNum"
          v-model:limit="dialogInfo.queryParams.pageSize"
          :pager-count="dialogInfo.maxCount"
          :total="dialogInfo.total"
          @pagination="dialogListener.tableSearch()"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="" @click="dialogListener.closeDialog">取 消</el-button>
        <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref } from 'vue';
  import { getDatasources } from '@/api/dataGovernance';
  import {
    bindAsset,
    deleteLabelValue,
    getApiList,
    getAssetsInDatasource,
    getLabelKey,
    getLabelValue,
    labelInfo,
    listRelationAsset,
    modifyLabel,
    unbindAsset,
  } from '@/api/system/tagManagement';
  import TagsEdit from '@/components/TagsEdit';

  const emits = defineEmits(['callback']);
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    workspaceId: {
      type: String,
      default: '1',
    },
    rowData: {
      type: Object,
      default: () => {
        return {
          id: 0,
        };
      },
    },
  });
  // 自定义的tag输入验证
  const customValidator = (rule, value, callback) => {
    let isValid = true;

    if (rule.field === 'labelValue') {
      value.forEach((item) => {
        if (item.name && (item.name.indexOf(':') >= 0 || item.name.indexOf(',') >= 0)) {
          isValid = false;
        }
      });
    } else {
      if (value && (value.indexOf(':') >= 0 || value.indexOf(',') >= 0)) {
        isValid = false;
      }
    }

    if (!isValid) {
      callback(
        new Error(
          rule.field === 'labelValue' ? "标签值不能包含':'和','" : "标签键不能包含':'和','",
        ),
      );
    } else {
      callback();
    }
  };
  const rules = {
    labelValue: [{ validator: customValidator, trigger: 'blur' }],
  };

  const isLabelValueDisabled = (labelValue, index) => {
    if (labelValue === '空值') {
      return false;
    }
    return (
      title.value === '新增标签' &&
      //   labelValueList.value?.some((item, i) => {
      //     if (item === labelValue && labelValue) return true;
      //     return false;
      //   })
      labelValueList.value[index] &&
      labelValueList.value[index] === labelValue
    );
  };
  const isLabelValueDelete = (labelValue, index) => {
    return (
      title.value === '新增标签' &&
      (labelValueList.value[index] || labelValueList.value[index] === null) &&
      labelValueList.value[index] === labelValue
    );
  };

  const callback = () => {
    emits('callback');
  };

  const tableListInfo = reactive({
    type: '1',
    searchInfo: {
      options: [],
      searchForm: {},
    },
  });

  // 详情信息
  const descriptions = reactive([
    {
      value: props.rowData.row.labelKeyName,
      label: '标签键',
    },

    {
      value: props.rowData.row.updateTime,
      label: '修改时间',
    },
    {
      value: props.rowData.row.createBy,
      label: '创建人',
    },
    {
      value: props.rowData.row.description,
      label: '备注',
    },
  ]);

  const detailInfo = reactive({
    name: props.rowData.row.labelKeyName,
  });

  const tableInfo = reactive({
    columns: [
      { key: 0, label: `标签值`, prop: 'name', visible: true },
      { key: 2, label: `绑定资产数`, prop: 'assetNums', visible: true },
      { key: 3, label: `创建人`, prop: 'createBy', visible: true },
    ],
    tableData: [],
  });

  const searchInfo = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      maxCount: 10,
      total: 10,
    },
  });

  const dataType = ref('1');

  const changeDataSource = (v) => {
    switch (v) {
      case '1':
        dataType.value = '1';
        break;
      case '2':
        dataType.value = '2';
        break;
      default:
        dataType.value = JSON.parse(JSON.stringify(dataType.value));
        break;
    }

    tableInfo.tableData = [];

    if (dataType.value === 1 || dataType.value === '1') {
      tableListInfo.searchInfo.labelvalue = '';
      getDetailsUtils();
    } else {
      assetListUtil();
    }
  };

  const ids = ref([]);
  const single = ref(false);
  const multiple = ref(true);
  const InsertData = ref();
  // 事件
  const tableListener = {
    show: (scope) => {
      console.log('scope', scope);
      const { row } = scope;
      dataType.value = '2';
      tableListInfo.searchInfo.labelvalue = row.name;
      assetListUtil();
      InsertData.value = row;
    },

    insert: (scope) => {
      console.log('scope', scope);
      const { row } = scope;
      InsertData.value = row;
      dialogInfo.labelChose = (detailInfo.name || '标签名称') + ':' + InsertData.value.name;
      dialogInfo.dialogVisible = true;
      dialogInfo.searchForm.type = '1';
    },

    deleteItem: async ({ row }) => {
      const Ids = row?.id || ids.value;
      if (Ids.length <= 0) return proxy.$modal.msgWarning('请选择标签值');
      const respond = await proxy.$modal.confirm('是否确定删除该标签值');
      if (!respond) return;
      const res = await deleteLabelValue(Ids);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
      getDetailsUtils();
    },

    selectChange: (selection) => {
      ids.value = selection.map((item) => item.id);
      single.value = selection.length !== 1;
      multiple.value = !selection.length;
    },
    unbind: async ({ row }) => {
      const Ids = row?.id || ids.value;
      if (Ids.length <= 0) return proxy.$modal.msgWarning('请选择标签值');
      const respond = await proxy.$modal.confirm('是否确定删除该标签值');
      if (!respond) return;
      const res = await unbindAsset(Ids);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
      assetListUtil();
    },
  };

  const reset = () => {};
  const title = ref();
  const open = ref(false);
  const form = reactive({ labelValue: [] });
  const labelValueList = ref();
  const itemList = ref([]);

  const handleAdd = async () => {
    reset();
    title.value = '新增标签';
    const req = {
      id: props.rowData.row.id,
      workspaceId: props.workspaceId,
      pageSize: 10000,
      pageNum: 1,
    };
    const res = await labelInfo(req);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    // proxy.$modal.msgSuccess(res.msg);
    form.labelKeyName = props.rowData.row.labelKeyName;
    form.description = props.rowData.row.description;
    form.labelValue = [];
    labelValueList.value = [];
    // form.labelValue = res.rows.map((item) => (item.name === '空值' ? null : item.name));
    // labelValueList.value = res.rows.map((item) => (item.name === '空值' ? null : item.name));
    res.rows.forEach((item) => {
      const resDataName = item.name === '空值' ? null : item.name;
      const resData = JSON.parse(JSON.stringify(item));
      resData.name = resData.name === '空值' ? null : resData.name;
      form.labelValue.push(resData);
      labelValueList.value.push(resDataName);
      //   itemList.value.push(item);
    });
    open.value = true;
  };

  const cancel = () => {
    open.value = false;
    reset();
  };

  const submitForm = async () => {
    const isValid = await proxy.$refs.roleRef.validate((valid) => valid);
    if (!isValid) return;
    if (!checkDataLabelValue()) return;
    form.id = props.rowData.row.id;
    form.labelKeyName = props.rowData.row.labelKeyName;
    form.description = props.rowData.row.description;
    form.workspaceId = props.workspaceId;
    const { addedValues, hasChangeValues } = compareData();
    // const labelIds = tableInfo.tableData;

    // 处理成两个数组，一个id一个name
    const changeIds = [];
    const changeLabel = [];
    hasChangeValues.forEach((item) => {
      changeIds.push(item.id || null);
      changeLabel.push(item.name || null);
    });

    const reqForm = {
      ...form,
      labelValue: changeLabel,
      labelValueId: changeIds,

      //   removedValues: form.value.id !== undefined ? removedValues : [],
    };
    console.log('form', form);

    const res = await modifyLabel(reqForm);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getDetailsUtils();
    open.value = false;
  };

  const compareData = () => {
    const formLabelValues = new Set(form.labelValue);
    const existingLabelValues = new Set(labelValueList.value);

    const addedValues = form.labelValue.filter((value) => !existingLabelValues.has(value));
    const removedValues = labelValueList.value.filter((value) => !formLabelValues.has(value));

    const hasChangeValues = form.labelValue.filter((value, index) => {
      return value.name !== labelValueList.value[index];
    });

    return {
      addedValues,
      removedValues,
      hasChangeValues,
    };
  };

  const addTagValue = () => {
    if (!form.labelValue) {
      form.labelValue = [];
    }
    form.labelValue.push({ name: '' });
  };

  const removeTagValue = (index) => {
    form.labelValue.splice(index, 1);
  };

  // 获取详情
  const getDetailsUtils = async () => {
    const req = {
      id: props.rowData.row.id,
      workspaceId: props.workspaceId,
      pageSize: searchInfo.queryParams.pageSize,
      pageNum: searchInfo.queryParams.pageNum,
    };
    const res = await labelInfo(req);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableInfo.tableData = res.rows;
    tableInfo.columns = [
      { key: 0, label: `标签值`, prop: 'name', visible: true },
      { key: 2, label: `绑定资产数`, prop: 'assetNums', visible: true },
      { key: 3, label: `创建人`, prop: 'createBy', visible: true },
    ];
    searchInfo.queryParams.total = res.total;
    // proxy.$modal.msgSuccess(res.msg);
  };
  const assetListUtil = async () => {
    const query = {
      pageNum: searchInfo.queryParams.pageNum,
      pageSize: searchInfo.queryParams.pageSize,
      labelvalue: tableListInfo.searchInfo.labelvalue,
      assetName: tableListInfo.searchInfo.assetName,
      type: tableListInfo.searchInfo.type,
      workspaceId: props.workspaceId,
      id: props.rowData.row.id,
    };
    const res = await listRelationAsset(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableInfo.tableData = res.rows.map((data) => {
      data.labelsData = deelTagsInfo(data.labels);
      return data;
    });
    tableInfo.columns = [
      { key: 0, label: `资产名称`, prop: 'assetName', visible: true },
      {
        key: 1,
        label: `资产类型`,
        prop: 'type',
        visible: true,
        formatter: (row) => {
          return row.type === 'table' ? '表' : 'API';
        },
      },
      { key: 2, label: `标签值`, prop: 'labelValueName', visible: true },
      { key: 3, label: `绑定时间`, prop: 'bindTime', visible: true },
      { key: 4, label: `标签`, prop: 'labels', visible: true },
    ];
    searchInfo.queryParams.total = res.total;
    proxy.$modal.msgSuccess(res.msg);
  };

  // 处理表格标签显示数据
  const deelTagsInfo = (labels) => {
    let thisTags = labels?.split(',');
    let returnData = {};
    if (thisTags && thisTags[thisTags.length - 1] === '') {
      thisTags = thisTags.slice(0, thisTags.length - 1);
    }
    returnData = {
      tags: thisTags,
    };
    return returnData;
  };

  const init = () => {
    getDetailsUtils();
  };

  onMounted(async () => {
    init();
  });

  const handleQuery = () => {
    assetListUtil();
  };
  const resetQuery = () => {
    tableListInfo.searchInfo.assetName = '';
    tableListInfo.searchInfo.type = '';
    tableListInfo.searchInfo.labelvalue = '';
    assetListUtil();
  };
  const dialogTableSelect = ref();
  const dialogInfo = reactive({
    dialogVisible: false,
    searchForm: {},
    data: [],
    columns: [
      { key: 0, label: `表名`, prop: 'tableName', visible: true },
      { key: 2, label: `数据源类型/数据源名称/数据库名称`, prop: 'databaseName', visible: true },
      { key: 3, label: `模式`, prop: 'schemaName', visible: true },
      { key: 4, label: `标签`, prop: 'labels', visible: true },
    ],
    total: 0,
    maxCount: 10,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });
  const dialogListener = reactive({
    reset: () => {
      dialogInfo.searchForm = {
        type: dialogInfo.searchForm.type,
      };
    },
    tableSearch: () => {
      dialogTableSearch();
    },

    selectChange: (selection) => {
      dialogTableSelect.value = selection;
    },
    changeDataSource: () => {
      getDatasourcesData();
    },
    closeDialog: () => {
      // 清空 dialogInfo.searchForm 除了 type 外的所有数据
      Object.keys(dialogInfo.searchForm).forEach((key) => {
        if (key !== 'type') {
          dialogInfo.searchForm[key] = '';
        }
      });

      dialogInfo.data = [];
      dialogInfo.total = 0;
      dialogInfo.queryParams.pageNum = 1;
      dialogInfo.queryParams.pageSize = 10;
      dialogInfo.queryParams.maxCount = null;
      dialogInfo.dialogVisible = false;
    },
    submitSpatial: async () => {
      await bindAssetUtil();
      await getDetailsUtils();
    },
  });

  const dialogTableSearch = async () => {
    if (dialogInfo.searchForm.type === '1') {
      const req = {
        // 新增接口参数
        labelKeyId: props.rowData.row.id,
        datasourceType: dialogInfo.searchForm.datasourceType,
        datasourceId: dialogInfo.searchForm.datasourceId,
        databaseName: dialogInfo.searchForm.databaseName,
        schemaName: dialogInfo.searchForm.schemaName,
        tableName: dialogInfo.searchForm.tableName,
        label: getSelectedLabels(dialogInfo.searchForm.label),
        workspaceId: props.workspaceId,
        pageNum: dialogInfo.queryParams.pageNum,
        pageSize: dialogInfo.queryParams.pageSize,
      };

      if (!dialogInfo.searchForm.datasourceId || dialogInfo.searchForm.datasourceId === '')
        return proxy.$modal.msgWarning('请选择数据源');

      const res = await getAssetsInDatasource(req);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      dialogInfo.total = res.total;
      dialogInfo.data = res.rows.map((item) => {
        item.labelsData = deelTagsInfo(item.labels);
        return item;
      });
      dialogInfo.columns = [
        { key: 0, label: `表名`, prop: 'tableName', visible: true },
        { key: 2, label: `数据源类型/数据源名称/数据库名称`, prop: 'databaseName', visible: true },
        { key: 3, label: `模式`, prop: 'schemaName', visible: true },
        { key: 4, label: `标签`, prop: 'labels', visible: true },
      ];
    } else {
      const req = {
        // 新增接口参数
        labelKeyId: props.rowData.row.id,
        apiName: dialogInfo.searchForm.apiName,
        apiType: dialogInfo.searchForm.apiType,
        label: getSelectedLabels(dialogInfo.searchForm.label),
        workspaceId: props.workspaceId,
        pageNum: dialogInfo.queryParams.pageNum,
        pageSize: dialogInfo.queryParams.pageSize,
      };
      const res = await getApiList(req);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      dialogInfo.total = res.total;
      dialogInfo.columns = [
        { key: 0, label: `API名称`, prop: 'apiName', visible: true },
        { key: 2, label: `API类型`, prop: 'apiType', visible: true },
        { key: 4, label: `标签`, prop: 'labels', visible: true },
      ];
      dialogInfo.data = res.rows.map((item) => {
        item.labelsData = deelTagsInfo(item.labels);
        return item;
      });
      debugger;
    }
  };

  const changeType = (newVal) => {
    dialogInfo.data = [];
    // 清空 dialogInfo.searchForm 除了 type 外的所有数据
    Object.keys(dialogInfo.searchForm).forEach((key) => {
      if (key !== 'type') {
        dialogInfo.searchForm[key] = '';
      }
    });
    // 如果 type 为 1，
    if (newVal === '1') {
      dialogInfo.columns = [
        { key: 0, label: `表名`, prop: 'name', visible: true },
        { key: 2, label: `数据源类型/数据源名称/数据库名称`, prop: 'assetNums', visible: true },
        { key: 3, label: `模式`, prop: 'createBy', visible: true },
        { key: 4, label: `标签`, prop: 'createBy', visible: true },
      ];
    }

    // 如果 type 为 2，
    if (newVal === '2') {
      dialogInfo.columns = [
        { key: 0, label: `API名称`, prop: 'name', visible: true },
        { key: 2, label: `API类型`, prop: 'assetNums', visible: true },
        { key: 4, label: `标签`, prop: 'createBy', visible: true },
      ];
    }
  };

  const constants = {
    databaseType: [
      {
        value: 'MYSQL',
        label: 'MYSQL',
      },
      {
        value: 'POSTGRESQL',
        label: 'POSTGRESQL',
      },
      {
        value: 'HIVE',
        label: 'HIVE',
      },
      {
        value: 'ORACLE',
        label: 'ORACLE',
      },
      {
        value: 'SQLSERVER',
        label: 'SQLSERVER',
      },
      {
        value: 'DAMENG',
        label: 'DAMENG',
      },
      {
        value: 'XUGU',
        label: 'XUGU',
      },
      {
        value: 'SYBASE',
        label: 'SYBASE',
      },
      {
        value: 'DB2',
        label: 'DB2',
      },
      {
        value: 'KINGBASE',
        label: 'KINGBASE',
      },
      {
        value: 'GREENPLUM',
        label: 'GREENPLUM',
      },
    ],
  };

  const options = reactive({
    dataTypeOptions: [],
    databaseOptions: [],
    tableOptions: [],
  });
  const checkDataLabelValue = () => {
    const labelValueSet = new Set();
    for (const labelValue of form.labelValue) {
      if (labelValueSet.has(labelValue)) {
        proxy.$modal.msgWarning('标签值不能重复');
        return false;
      }
      labelValueSet.add(labelValue);
    }
    return true;
  };
  const getDatasourcesData = async () => {
    const reqData = {
      workspaceId: props.workspaceId,
      datasourceType: dialogInfo.searchForm.datasourceType,
    };
    const res = await getDatasources(reqData);
    dialogInfo.datasourceData = res.data;
    options.databaseOptions = res.data.map((item) => {
      item.value = item.datasourceId;
      item.label = item.datasourceName;
      return item;
    });
    dialogInfo.searchForm.datasourceId = '';
    console.log(res, 212);
  };
  const cascaderOptions = ref([]);
  const getLabelKeyUtil = async () => {
    const query = {
      workspaceId: props.workspaceId,
      type: 'table',
    };
    const res = await getLabelKey(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    cascaderOptions.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.labelKeyName,
        children: item.labelValue,
        leaf: false, // 指定非叶子节点
      };
    });
  };
  getLabelKeyUtil();

  const getLabelValueUtil = async (id) => {
    const res = await getLabelValue({ id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    return res.data;
  };

  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      // 第一级已经加载，不需要再次加载
      return resolve([]);
    }

    // 获取子节点数据
    const children = await getLabelValueUtil(node.value);
    // 处理子节点数据
    resolve(
      children.map((child) => ({
        value: child.name,
        label: child.name,
        leaf: true, // 子节点为叶子节点
      })),
    );
  };

  const bindAssetUtil = async () => {
    const query = {
      labelKeyId: props.rowData.row.id,
      labelKey: props.rowData.row.labelKeyName,
      labelValueId: InsertData.value.id,
      labelValue: InsertData.value.name,
      assetId: dialogTableSelect.value.map((items) => items.id || items.apiId),
      name: dialogTableSelect.value.map((items) => items.tableName || items.apiName),
      type: dialogInfo.searchForm.type === '1' ? 'table' : 'api',
      workspaceId: props.workspaceId,
    };
    const res = await bindAsset(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    //
    dialogInfo.dialogVisible = false;
  };

  const typeOptions = [
    { value: '', label: '全部' },
    { value: 'SQL', label: '自有接口' },
    { value: 'API', label: '转发接口' },
    { value: 'SHARE', label: '库表共享' },
  ];

  const getSelectedLabels = (selectedValues) => {
    if (!selectedValues || selectedValues.length === 0) return '';

    const selectedLabels = [];
    for (const value of selectedValues) {
      const labelKey = cascaderOptions.value.find((option) => option.value === value[0]);
      if (labelKey.value === value[0]);
      selectedLabels.push(`${labelKey.label}:${value[1]}`);
    }

    return selectedLabels.join(', ');
  };
  const propsForCas = ref({
    multiple: true,
    lazy: true,
    lazyLoad: loadNode,
    value: 'value',
    label: 'label',
    children: 'children',
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .work-detail {
    width: 100%;
    height: 100%;
    position: relative;

    .call-back-btn {
      margin-right: 10px;
    }

    .detail-title {
      font-size: 20px;
      line-height: 32px;
      display: inline-block;
      vertical-align: top;
      color: $--base-color-title1;
    }

    .detail-card-box {
      margin-top: 20px;
      padding: 10px;
      background-color: $--base-color-item-light;
      border-radius: 8px;

      .card-title {
        width: 100%;
        height: 56px;
        padding: 0px 0px 10px 0px;
        border-bottom: 1px solid $--base-color-box-bg;

        .card-title-icon {
          width: 46px;
          height: 46px;
          background-color: $--base-color-tag-bg;
          border-radius: 8px;
          text-align: center;
          display: inline-block;

          .el-icon {
            width: 24px;
            height: 24px;
            margin-top: 11px;
          }
        }

        .card-title-text {
          font-size: 20px;
          color: $--base-color-title1;
          line-height: 46px;
          font-weight: bold;

          .card-title-id {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;

            &::after {
              content: '';
              width: 2px;
              height: 14px;
              border-radius: 2px;
              background: $--base-color-text2;
              position: absolute;
              right: -1px;
              top: 16px;
            }
          }

          .card-title-name {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;
          }
        }
      }

      .card-content {
        padding: 10px 0px;

        .card-item {
          height: 20px;
          line-height: 1;
          display: inline-block;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;

          &.card-item-0 {
            color: $--base-btn-red-text;
            background-color: $--base-btn-red-bg;
          }

          &.card-item-1 {
            color: $--base-color-green;
            background-color: $--base-color-green-disable;
          }

          &.card-item-2 {
            color: $--base-color-primary;
            background-color: $--base-color-tag-primary;
          }
        }
      }
    }

    .table-title {
      font-size: 20px;
      color: $--base-color-title1;
      line-height: 60px;
      font-weight: bold;
    }

    .other-btn-box {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      .btn-box-table {
        width: 400px;
      }
    }

    .table-box {
      height: calc(100% - 420px);
      background: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      .search-box {
        display: flex;
        justify-content: flex-end;
      }
      ::v-deep .el-table {
        .el-input-number {
          width: 100%;

          .el-input__wrapper {
            .el-input__inner {
              text-align: left;
            }
          }
        }
      }
    }

    .pagination-container {
      margin: 0;
      padding: 10px 20px;
    }
  }

  .add-tag-value-btn {
    margin-bottom: 10px;
    margin-left: 90%;
  }

  .tag-value-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;

    .input-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      background-color: #f6f8fa;
      border-radius: 5px;
      padding: 10px;
    }
    .tag-value-input {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      .el-input {
        // margin-right: 10px;
      }
      & > .el-button {
        margin-left: 10px;
      }
    }
  }

  .detail-header {
    background-color: #f1f5fa;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    height: 70px;
    margin-bottom: 10px;
    .labelS {
      color: #3093e6;
      background-color: #e7f7fd;
      padding: 0px 6px;
      line-height: 22px;
      border-radius: 33px;
    }
  }
  :deep .search-label {
    width: 250px;
  }

  :deep(.el-input__wrapper) {
    // 高度设置
    min-height: 30px;
  }
  //绑定资产样式
  //   .dialog-table-box {
  //     height: calc(100% - 300px);
  //   }
</style>
