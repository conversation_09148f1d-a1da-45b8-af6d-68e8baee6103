<template>
  <HeadTitle :title="HeadTitleName" />
  <div class="app-container">
    <div
      v-loading="g_loading"
      style="border: #efefef solid 1px; height: calc(100vh - 100px); width: 100%"
    >
      <relation-graph ref="graphRef$" :options="options" />
    </div>
  </div>
</template>

<script setup name="User">
  import RelationGraph from 'relation-graph/vue3';

  import HeadTitle from '@/components/HeadTitle';
  const HeadTitleName = ref('概述demo');

  const graphRef$ = ref();
  const g_loading = ref(true);
  const options = {
    backgroundImage: '', // 背景图
    backgroundImageNoRepeat: true, // 背景不重复
    disableDragNode: true, // 禁止拖动节点
    zoomToFitWhenRefresh: false, // 缩放时刷新
    defaultFocusRootNode: false, // 默认焦点根节点

    layouts: [
      {
        label: '布局',
        layoutName: 'tree',
        layoutClassName: 'seeks-layout-center',
        useLayoutStyleOptions: true,
        hideNodeContentByZoom: true,
        from: 'left',
        defaultNodeWidth: '100',
        defaultNodeHeight: '30',
        defaultJunctionPoint: 'lr',
        defaultNodeShape: 1,
        defaultLineShape: 6,
        defaultNodeBorderWidth: 0,
        defaultLineColor: '#c0c0c0',
        defaultNodeColor: '#ffa00b',
        min_per_width: 200,
        max_per_width: 400,
        min_per_height: 40,
        max_per_height: 70,
        defaultExpandHolderPosition: 'right',
        defaultLineWidth: 3,
      },
    ],

    defaultJunctionPoint: 'border',
  };

  onMounted(() => {
    const jsonData = {
      rootId: 'a',
      nodes: [
        // { 'text': '新建同步任务', 'id': 'newSync', nodeShape: 0, width: 120, height: 120, offset_y: 50, offset_x: -50, },
        { text: '新增同步任务', id: 'newSync', nodeShape: 0, width: 120, height: 120 },

        { text: '选择来源数据源', id: 'sourceData', nodeShape: 1, width: 230, height: 50 },
        { text: '选择去向数据源', id: 'aimData', nodeShape: 1, width: 230, height: 50 },

        { text: '选择来源库表', id: 'selectSourceData', nodeShape: 1, width: 130, height: 50 },

        // { 'text': '映射目标库表', 'id': 'mapAimTable', nodeShape: 1, width: 130, height: 40, color: '#34A0CE', fixed: true, x: 1030, y: -200 },
        { text: '映射目标库表', id: 'mapAimTable', nodeShape: 1, width: 130, height: 50 },

        { text: '配置字段映射规则', id: 'setRuleOne', nodeShape: 1, width: 130, height: 50 },

        { text: '配置字段映射规则', id: 'setRuleTwo', nodeShape: 1, width: 130, height: 50 },

        { text: '选择高级同步参数', id: 'seniorSync', nodeShape: 1, width: 130, height: 35 },
        { text: '提交同步任务至列表', id: 'submitList', nodeShape: 1, width: 130, height: 35 },

        { text: '单次运行', id: 'singleRun', nodeShape: 1, width: 130, height: 35 },
        { text: '运行周期', id: 'runCycle', nodeShape: 1, width: 130, height: 35 },
        // { 'text': '最后响应:4分钟前', 'id': 'exe-04', nodeShape: 1, width: 130, height: 35, color: '#34A0CE' },
        // { 'text': '最后响应:2分钟前', 'id': 'exe-05', nodeShape: 1, width: 130, height: 35, color: '#34A0CE' },
        // { 'text': '最后响应:3秒前', 'id': 'exe-06', nodeShape: 1, width: 130, height: 35 },
        // { 'text': '最后响应:3秒前', 'id': 'exe-07', nodeShape: 1, width: 130, height: 35 },
        // { 'text': '最后响应:17天前', 'id': 'exe-08', nodeShape: 1, width: 130, height: 35, color: '#F56C6C' },
        // { 'text': '最后响应:3秒前', 'id': 'exe-09', nodeShape: 1, width: 130, height: 35 },
        // { 'text': '最后响应:3秒前', 'id': 'exe-10', nodeShape: 1, width: 130, height: 35 }
      ],
      lines: [
        { from: 'newSync', to: 'sourceData', text: null, lineShape: 4, animation: 2 },
        { from: 'newSync', to: 'aimData', text: null, lineShape: 4 },

        { from: 'sourceData', to: 'selectSourceData', text: null, lineShape: 4 },
        { from: 'aimData', to: 'selectSourceData', text: null, lineShape: 4 },

        { from: 'selectSourceData', to: 'mapAimTable', text: null, lineShape: 4 },

        { from: 'mapAimTable', to: 'setRuleOne', text: null, lineShape: 4 },
        { from: 'setRuleOne', to: 'setRuleTwo', text: null, lineShape: 4 },
        { from: 'setRuleTwo', to: 'seniorSync', text: null, lineShape: 4 },
        { from: 'seniorSync', to: 'submitList', text: null, lineShape: 4 },
        { from: 'submitList', to: 'singleRun', text: null, lineShape: 4 },
        { from: 'submitList', to: 'runCycle', text: null, lineShape: 4 },
        { from: 'mapAimTable', to: 'exe-07', text: null, lineShape: 4 },
        { from: 'mapAimTable', to: 'exe-08', text: null, lineShape: 4 },
        { from: 'mapAimTable', to: 'exe-09', text: null, lineShape: 4 },
        { from: 'mapAimTable', to: 'exe-10', text: null, lineShape: 4 },
      ],
    };
    setTimeout(() => {
      g_loading.value = false;
      graphRef$.value.setJsonData(jsonData, (graphInstance) => {
        const nodes = graphInstance.getNodes();
        nodes.forEach((node) => {
          if (jsonData.nodes.some((n) => n.fixed && n.id === node.id)) {
            node.x = graphInstance.graphData.rootNode.x + node.x;
            node.y = graphInstance.graphData.rootNode.y + node.y;
          }
        });
      });
    }, 1000);
  });
</script>
<style></style>
