<template>
    <div class="approval-process-container">
        <!-- 搜索表单 -->
        <div class="search-form">
            <el-form :model="data.queryParams" ref="queryForm" :inline="true">
                <el-form-item label="流程名称" prop="name">
                    <el-input
                        v-model="data.queryParams.name"
                        placeholder="请输入流程名称"
                        clearable
                        style="width: 200px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="业务模块" prop="groupName">
                    <el-input
                        v-model="data.queryParams.groupName"
                        placeholder="请输入业务模块"
                        clearable
                        style="width: 200px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        
        <!-- 操作按钮 -->
        <div class="toolbar">
            <el-button type="primary" :icon="Plus">新增流程</el-button>
        </div>
        
        <!-- 数据表格 -->
        <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
        >
            <el-table-column prop="groupName" label="业务模块" min-width="120" />
            <el-table-column prop="name" label="流程名称" min-width="150" show-overflow-tooltip />
            <el-table-column prop="remark" label="备注" min-width="180" show-overflow-tooltip />
            <el-table-column prop="updated" label="更新时间" min-width="150" />
            <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                    <el-button
                        type="primary"
                        link
                        :icon="View"
                        @click="viewFlow(scope)"
                    >查看流程</el-button>
                    <el-button
                        type="danger"
                        link
                        :icon="Delete"
                        @click="deleteFlow(scope)"
                    >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <div class="pagination-container">
            <el-pagination
                v-model:current-page="data.queryParams.pageNum"
                v-model:page-size="data.queryParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, nextTick, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { Plus, Edit, View, Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { approvalProcessData } from '../../../mockData.js';

const { proxy } = getCurrentInstance();

// 列显隐信息
const columns = ref([
    { key: 0, label: `业务模块`, visible: true, prop: 'groupName' },
    { key: 1, label: `流程名称`, visible: true, prop: 'name' },
    { key: 2, label: `备注`, visible: true, prop: 'remark' },
    { key: 3, label: `更新时间`, visible: true, prop: 'updated' },
]);

const maxCount = ref(5);
const total = ref(0);
const filterText = ref();
const props = {
    value: 'id',
    label: 'groupName',
    children: 'children',
};
const dataTree = ref();

onMounted(async () => {
    await getCatalogTreeUtil();
    await handleQuery();
    nextTick(() => {
        if (dataTree.value[0]) {
            document.querySelector('.el-tree-node__content').click();
        }
    });
});

const tableRef = ref();
const tableData = ref([]);
const loading = ref(false);
// 表单数据
const data = reactive({
    form: {},
    rules: {
        name: [
            { required: true, message: '请输入名称', trigger: 'blur' },
            { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
            // 汉字 和字母 数字 支持下划线及括号
            {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
                message: '只能输入汉字、字母、数字、下划线、括号',
                trigger: 'blur',
            },
        ],
        remark: [
            { required: false, message: '请输入描述', trigger: 'blur' },
            { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        ],
    },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        groupName: ''
    },
});

const { queryParams } = toRefs(data);

const getCatalogTreeUtil = async () => {
    // const res = await queryGroupList();
    // if (res.code !== 200) return;
    // dataTree.value = res.data;
};

const handleSelectionChange = (selection) => { };

const handleNodeClick = async (data, e) => {
    if (!e) return proxy.$message.error('请选择一个节点');

    tableData.value = findFlowById(e.key)?.items.map((it) => ({
        ...it,
        groupName: findFlowById(e.key).name,
    }));
};

const findFlowById = (flowId) => {
    if (!flowId) return;
    return typeTableData.value.find((item) => item.id === flowId);
};
const router = useRouter();

const edit = (data) => {
    if (!data.row) return;
    const to = '/flow/create?id=' + data.row.uniqueId + '&flowId=' + data.row.flowId;
    router.push(to);
};
const typeTableData = ref([]);
/**
 * 查询流程审批数据
 */
const handleQuery = async () => {
    loading.value = true;
    // 使用模拟数据
    setTimeout(() => {
        // 根据查询条件过滤
        const filteredData = approvalProcessData.filter(item => {
            const nameMatch = !data.queryParams.name || item.name.includes(data.queryParams.name);
            const groupMatch = !data.queryParams.groupName || item.groupName.includes(data.queryParams.groupName);
            return nameMatch && groupMatch;
        });
        
        // 计算分页
        const start = (data.queryParams.pageNum - 1) * data.queryParams.pageSize;
        const end = start + data.queryParams.pageSize;
        tableData.value = filteredData.slice(start, end);
        total.value = filteredData.length;
        loading.value = false;
    }, 300);
};

watch(filterText, (val) => {
    proxy.$refs.treeRef.filter(val);
});

const filterMethod = (value, data) => {
    if (!value) return true;
    return data.groupName.includes(value); // 使用节点的数据进行比较
};

/**
 * 重置查询条件
 */
const resetQuery = () => {
    data.queryParams.name = '';
    data.queryParams.groupName = '';
    data.queryParams.pageNum = 1;
    handleQuery();
};

/**
 * 处理每页显示数量变化
 * @param {Number} val - 新的每页显示数量
 */
const handleSizeChange = (val) => {
    data.queryParams.pageSize = val;
    handleQuery();
};

/**
 * 处理当前页变化
 * @param {Number} val - 新的当前页
 */
const handleCurrentChange = (val) => {
    data.queryParams.pageNum = val;
    handleQuery();
};

/**
 * 查看流程详情
 * @param {Object} scope - 行数据
 */
const viewFlow = (scope) => {
    ElMessage.info(`查看流程: ${scope.row.name}`);
    // 实际应用中这里应该跳转到流程详情页或打开流程详情对话框
};

/**
 * 删除流程
 * @param {Object} scope - 行数据
 */
const deleteFlow = (scope) => {
    ElMessageBox.confirm(
        `确定要删除"${scope.row.name}"吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        ElMessage.success(`删除成功: ${scope.row.name}`);
        // 实际应用中这里应该调用删除API
        handleQuery(); // 刷新列表
    }).catch(() => {
        // 用户取消删除
    });
};
</script>

<style lang="scss" scoped>
.approval-process-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-form {
    margin-bottom: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
  }
  
  .toolbar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
  
  .el-table {
    flex: 1;
    margin-bottom: 16px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 10px 0;
  }
}

.app-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    padding: 1px;
    background: #ffffff;
    border-radius: 4px;
    // margin-top: 20px;
}

:deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
}

.head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
}

.App-theme {
    // margin-top: 20px;
    // // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    overflow: auto;
}

.TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
}

.info {
    padding: 10px;
    border-radius: 4px;
}

.pm {
    padding: 2px;
    margin: 10px;
}

:deep .el-form .el-form-item__label {
    font-weight: normal;
}

:deep .el-col-20 {
    max-width: 100%;
}

.dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
}

.operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
}

.custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
        -apple-system,
        BlinkMacSystemFont,
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;

    color: #333;

    &>div>a {
        display: block;
        margin-bottom: 10px;
        cursor: pointer;
        color: #333;
        text-decoration: none;

        &:hover {
            background: #e9edfc9f;
        }
    }

    &>a {
        display: block;
        margin-bottom: 10px;
        cursor: pointer;
        color: #333;
        text-decoration: none;

        &:hover {
            background: #e9edfc9f;
        }
    }
}

.el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
}

.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

:deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}
</style>
