<template>
  <div class="container">
    <el-row>
      <el-col :span="24">
        <el-button type="text" @click="toBack"> {{ '<' }} 返回上一层</el-button>
      </el-col>
    </el-row>
    <div class="demo-tabs">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="基本配置 修饰" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
          >
            <el-form-item label="修饰限定名称" prop="code">
              <el-input v-model="form.code" placeholder="请输入修饰限定名称"></el-input>
            </el-form-item>

            <el-form-item label="限定中文名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入限定名称"></el-input>
            </el-form-item>

            <el-form-item label="限定条件" prop="timeUnit">
              <template
                v-for="(syncChange, index) in restrictCondition"
                :key="index"
                style="margin-bottom: 150px"
              >
                <el-form ref="syncChangeForm" :model="syncChange" class="containerData">
                  <div class="item">
                    <el-form-item prop="prop">
                      <!-- 下拉框 -->
                      <el-select
                        v-model="syncChange.type"
                        placeholder="请选择类型"
                        @change="onChangeType(syncChange)"
                      >
                        <el-option
                          v-for="dict in typeList"
                          :key="dict.label"
                          :value="dict.value"
                          :label="dict.label"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="item">
                    <el-form-item prop="prop">
                      <el-select
                        v-model="syncChange.valueCompareType"
                        placeholder="请选择"
                        @change="onChangeTypeTwo(syncChange)"
                      >
                        <el-option
                          v-for="dict in valueCompareTypeListValue"
                          v-if="syncChange.type == 'VALUE' || syncChange.type == 'STRING'"
                          :key="dict.label"
                          :value="dict.value"
                          :label="dict.label"
                        ></el-option>
                        <el-option
                          v-for="dict in valueCompareTypeListDate"
                          v-if="syncChange.type == 'DATE'"
                          :key="dict.label"
                          :value="dict.value"
                          :label="dict.label"
                        ></el-option>

                        <el-option
                          v-for="dict in valueCompareTypeListValueNumber"
                          v-if="syncChange.type == 'NUMBER'"
                          :key="dict.label"
                          :value="dict.value"
                          :label="dict.label"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="item">
                    <el-form-item
                      v-if="
                        syncChange.valueCompareType != 'IsNull' &&
                        syncChange.valueCompareType != 'IsNotNull'
                      "
                      prop="prop"
                    >
                      <el-input
                        v-if="syncChange.type == 'VALUE' || syncChange.type == 'STRING'"
                        v-model="syncChange.constantValue"
                        placeholder="常量值"
                      />
                      <el-input
                        v-if="
                          syncChange.type == 'NUMBER' &&
                          syncChange.valueCompareType !== 'Contain' &&
                          syncChange.valueCompareType !== 'NotContain'
                        "
                        v-model="syncChange.constantValue"
                        placeholder=""
                        type="number"
                      />
                      <el-input
                        v-if="
                          syncChange.type == 'NUMBER' &&
                          (syncChange.valueCompareType === 'Contain' ||
                            syncChange.valueCompareType === 'NotContain')
                        "
                        v-model="syncChange.constantValue"
                        placeholder=""
                      />

                      <!-- 日期选择框 -->

                      <el-date-picker
                        v-if="syncChange.type == 'DATE' && syncChange.valueCompareType != 'Between'"
                        v-model="syncChange.constantValue"
                        type="date"
                        placeholder="请选择日期"
                        value-format="YYYY-MM-DD"
                      />

                      <el-date-picker
                        v-else-if="
                          syncChange.type == 'DATE' && syncChange.valueCompareType == 'Between'
                        "
                        v-model="syncChange.constantValue"
                        value-format="YYYY-MM-DD"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[
                          new Date(2000, 1, 1, 0, 0, 0),
                          new Date(2000, 1, 1, 23, 59, 59),
                        ]"
                        :disabled-date="disablesDate"
                      />
                    </el-form-item>
                  </div>

                  <!-- <div class="item"> -->
                  <!-- <el-button link @click="deleteSyncChange(index)"> -->
                  <!-- <svg t="1699442953096" class="icon" viewBox="0 0 1024 1024" version="1.1" -->
                  <!-- xmlns="http://www.w3.org/2000/svg" p-id="6096" width="20" height="20"> -->
                  <!-- <path -->
                  <!-- d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z" -->
                  <!-- fill="#d81e06" p-id="6097"></path> -->
                  <!-- </svg> -->
                  <!-- </el-button> -->
                  <!-- </div> -->
                </el-form>
              </template>
              <!-- <el-button link @click="addSyncChange"> -->
              <!-- <svg t="1699442878434" class="icon" viewBox="0 0 1024 1024" version="1.1" -->
              <!-- xmlns="http://www.w3.org/2000/svg" p-id="4897" width="20" height="20"> -->
              <!-- <path -->
              <!-- d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z" -->
              <!-- p-id="4898" fill="#1296db"></path> -->
              <!-- </svg> -->
              <!-- </el-button> -->
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model="form.description" type="textarea" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <div class="bont">
          <!-- 分割线 -->
          <el-divider></el-divider>

          <el-button type="primary" @click="fulfill">保 存</el-button>
          <el-button plain @click="toBack">关闭</el-button>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { rowData } = toRefs(props);
  const { proxy } = getCurrentInstance();
  const emit = defineEmits();

  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],

      code: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
        // 校验
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      store: [{ required: true, message: '请选择储存库', trigger: 'blur' }],
      isExternal: [{ required: true, message: '请选择建模方式', trigger: 'blur' }],
      type: [{ required: true, message: '请选择存储类型', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
  });

  const { form, rules } = toRefs(data);
  const toBack = () => {
    emit('toBack', true);
  };

  const activeName = ref('first');
  const tableData = ref([]);

  const beforeleave = (tab, oldTab) => {
    console.log(tab, oldTab);
    return new Promise((resolve) => {
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.name &&
        form.value.code &&
        form.value.dwDatabase &&
        form.value.isExternal &&
        form.value.isFromDatasource
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const fulfill = async () => {
    const res = await proxy.$refs.formRef.validate((valid) => valid);
    if (!res) return;

    // 校验
    for (const item of restrictCondition.value) {
      if (item.valueCompareType != 'IsNull' && item.valueCompareType != 'IsNotNull') {
        if (!item.type || !item.valueCompareType || !item.constantValue) {
          proxy.$modal.msgError('请完善限制条件');
          return;
        }
      } else {
        if (!item.type || !item.valueCompareType) {
          proxy.$modal.msgError('请完善限制条件');
          return;
        }
      }
    }

    const data = {
      name: form.value.name,
      code: form.value.code,
      description: form.value.description,
      restrictCondition: restrictCondition.value,
    };

    if (rowData.value && rowData.value.name) {
      emit('fulfill', data, 'modifyLimitEdit');
      console.log(data);
    } else {
      emit('fulfill', data, 'modifyLimit');
    }

    // // 清空数据
    // form.value = {

    // }
    // // 清空数据
    // restrictCondition.value = []
    // activeName.value = 'first'
    // toBack()
  };

  const cascaderOptions = ref([]);
  // watch form.datasheet
  watch(
    () => tableData.value,
    (val) => {
      cascaderOptions.value = tableData.value.map((item) => {
        return {
          value: item.columnName,
          label: item.columnName,
        };
      });
    },
  );

  const restrictCondition = ref([
    {
      type: '',
      valueCompareType: '',
      constantValue: '',
    },
  ]);
  const typeList = [
    {
      value: 'DATE',
      label: '日期类型',
    },
    // {
    //     value: 'VALUE',
    //     label: '值类型'
    // },
    {
      value: 'STRING',
      label: '字符串类型',
    },
    {
      value: 'NUMBER',
      label: '数字类型',
    },
  ];
  const valueCompareTypeListDate = ref([
    {
      value: 'Equal',
      label: '等于',
    },
    {
      value: 'NotEqual',
      label: '不等于',
    },
    {
      value: 'Before',
      label: '早于',
    },
    {
      value: 'NotBefore',
      label: '不早于',
    },
    {
      value: 'Behind',
      label: '晚于',
    },
    {
      value: 'NotBehind',
      label: '不晚于',
    },
    {
      value: 'IsNull',
      label: '为空',
    },
    {
      value: 'IsNotNull',
      label: '不为空',
    },
    {
      value: 'Between',
      label: '介于',
    },
  ]);
  const valueCompareTypeListValue = ref([
    {
      value: 'Contain',
      label: '包含',
    },
    {
      value: 'NotContain',
      label: '不包含',
    },
    {
      value: 'Equal',
      label: '等于',
    },
    {
      value: 'NotEqual',
      label: '不等于',
    },
    {
      value: 'IsNull',
      label: '为空',
    },
    {
      value: 'IsNotNull',
      label: '不为空',
    },
  ]);
  const valueCompareTypeListValueNumber = ref([
    {
      value: 'Contain',
      label: '包含',
    },
    {
      value: 'NotContain',
      label: '不包含',
    },
    // 大于 大于等于 小于 小于等于
    {
      value: 'Greater',
      label: '大于',
    },
    {
      value: 'GreaterEqual',
      label: '大于等于',
    },
    {
      value: 'Less',
      label: '小于',
    },
    {
      value: 'LessEqual',
      label: '小于等于',
    },
    {
      value: 'Equal',
      label: '等于',
    },
    {
      value: 'NotEqual',
      label: '不等于',
    },
    {
      value: 'IsNull',
      label: '为空',
    },
    {
      value: 'IsNotNull',
      label: '不为空',
    },
  ]);
  function deleteSyncChange(index) {
    restrictCondition.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      type: '',
      valueCompareType: '',
      constantValue: '',
    };

    restrictCondition.value.push(newSyncChange);
  }

  const onChangeType = (row) => {
    if (row.type == 'DATE' && row.valueCompareType == 'Between') {
      row.valueCompareType = null;
      row.constantValue = ['', ''];
    } else if (row.type == 'DATE' && row.valueCompareType != 'Between') {
      row.valueCompareType = null;
      row.constantValue = '';
    } else {
      row.valueCompareType = null;
      row.constantValue = null;
    }
    console.log(row.constantValue);
  };
  const onChangeTypeTwo = (row) => {
    console.log(row);
    if (row.type == 'DATE' && row.valueCompareType == 'Between') {
      row.constantValue = ['', ''];
    } else if (row.type == 'DATE' && row.valueCompareType != 'Between') {
      row.constantValue = '';
    } else {
      row.constantValue = null;
    }

    console.log(row.constantValue);
  };

  onMounted(async () => {
    if (rowData.value && rowData.value.name) {
      form.value.name = rowData.value.name;
      form.value.code = rowData.value.code;
      form.value.description = rowData.value.description;
      await nextTick();
      await (async () => {
        restrictCondition.value = rowData.value.restrictCondition;
      })();
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 20px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    margin: 5px;
  }

  .demo-tabs {
    padding: 20px 0;
    :deep .el-tabs__content {
      padding: 20px;
      background: #ffffff;
      border-radius: 8px;
    }
  }

  .bont {
    text-align: right;
    padding: 10px 0;
  }

  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .codeCs {
    padding: 10px;
    white-space: pre;
    font-family: 'Courier New', monospace;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .rgroup {
    // border-top: 1px solid #0400ff3f;
    // border-left: 1px solid #E6E6E6;
    // border-right: 1px solid #E6E6E6;
    // border-bottom: 1px solid #E6E6E6;
    padding: 10px;
  }

  .containerData {
    width: 100%;
    // border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      //   border-left: 1px solid #ebeef5;
    }
  }
</style>
