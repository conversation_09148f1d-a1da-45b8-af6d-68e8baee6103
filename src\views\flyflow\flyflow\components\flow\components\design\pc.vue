<script setup>
  import { computed, ref } from 'vue';
  import draggable from 'vuedraggable';
  import { useFlowStore } from '../../../../stores/flow';
  import getFormName from '../../../../utils/getFormWidget';
  import * as util from '../../../../utils/objutil';

  const getFormWidget = (name) => {
    // 写的时候，组件的起名一定要与 dragList 中的 element 名字一模一样，不然会映射不上
    return getFormName[name];
  };

  const flowStore = useFlowStore();
  const targetList = computed({
    get: () => {
      const value = step2List.value;
      if (value?.length === 0) {
        return [
          {
            type: 'Empty',
            name: '',
          },
        ];
      }
      return value;
    },
    set: (v) => {
      const value = v.filter((res) => res.type !== 'Empty');
      flowStore.setStep2Form(util.deepCopy(value));
    },
  });

  const deleteForm = (id) => {
    flowStore.setStep2Form(step2List.value.filter((res) => res.id !== id));

    if (currentFormCom.value && currentFormCom.value?.id === id) {
      currentFormCom.value = undefined;
    }
  };

  const emit = defineEmits(['setCurrentForm']);

  const step2Object = computed(() => {
    const obj = {};

    step2List.value.forEach((res) => (obj[res.id] = res));

    return obj;
  });
  const step2List = computed(() => {
    const step2 = flowStore.step2Form;
    return step2;
  });

  // 定义当前打开的表单
  const currentForm = ref();
  // 判断是否选中当前表单显示边框
  const isCurrentForm = (fid) => {
    if (!currentFormCom.value) {
      return false;
    }
    if (currentFormCom.value.id === fid) {
      return true;
    }

    return false;
  };
  const showCurrentPageConfigPanel = (id) => {
    currentFormCom.value = step2List.value.filter((res) => res.id === id)[0];
  };
  const showPanel = (form) => {
    currentFormCom.value = form;
  };

  var currentFormCom = computed({
    get() {
      return currentForm.value;
    },
    set(v) {
      currentForm.value = v;
      emit('setCurrentForm', v);
    },
  });
</script>

<template>
  <h3 style="margin-left: 10px; font-weight: 500">表单配置</h3>
  <div class="drag-content">
    <div class="drag-content-inner">
      <el-form :label-position="'top'" label-width="100px">
        <draggable
          v-model="targetList"
          disabled
          style="min-height: 600px; background-color: var(--el-bg-color-page)"
          item-key="index"
          :sort="true"
          effect="dark"
          :group="{ name: 'dragFormList', pull: true, put: true }"
        >
          <template #item="{ element, index }">
            <div
              class="okcomponent border line"
              effect="dark"
              :class="{ 'active-component': isCurrentForm(element.id) }"
              @click.stop="showCurrentPageConfigPanel(element.id)"
            >
              <el-form-item
                :label="step2Object[element.id]?.name"
                :style="{ marginBottom: element.type === 'Empty' ? '0px' : '18px' }"
                :required="step2Object[element.id]?.required"
              >
                <section class="drag-item">
                  <component
                    :is="getFormWidget(element.type)"
                    :id="element.id"
                    v-model:form="step2Object[element.id]"
                    style="width: 70%"
                    :index="index"
                    :from="1"
                    @show-panel="showPanel"
                  ></component>

                  <!--  按钮 -->
                  <!-- :disabled="flowStore?.edit" 目前没找到对应edit字段，所以打开删除功能只能先删除这个限制，不知道这个edit是怎么加进去的-->
                  <el-button
                    v-if="element.type != 'Empty'"
                    type="primary"
                    icon="Delete"
                    class="deleteIcon"
                    size="small"
                    @click.stop="deleteForm(element.id)"
                  >
                    删除
                  </el-button>
                  <!-- <span>拖动排序</span> -->
                </section>
              </el-form-item>
            </div>
          </template>
        </draggable>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="less">
  .leftItem {
    padding-left: 0px;
  }

  .zj {
    display: inline-block;
    width: 140px;
    margin: 5px;
  }

  @f22_width: 400px;

  @center_width: 360px;
  .drag-content {
    min-height: 640px;

    // padding: 30px 10px;
    background-color: white;
  }

  .drag-content-inner {
    background-color: var(--el-bg-color-page);
    border-radius: var(--el-border-radius-base);

    padding: 5px;
  }

  .f11 {
    width: calc(100% - @f22_width);
  }

  .f22 {
    width: @f22_width;
  }

  .okcomponent {
    padding: 5px;
    border-radius: 5px;
    margin-bottom: 10px;
    background-color: white;
    border: 1px solid white;
    position: relative;
  }

  .active-component {
    border: 1px solid var(--el-color-primary);
  }

  .deleteIcon {
    position: absolute;
    right: 20px;
    z-index: 20;
    color: var(--el-color-danger);
  }

  .deleteIcon:hover {
    cursor: pointer;
    color: palevioletred;
  }

  .drag-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
</style>
