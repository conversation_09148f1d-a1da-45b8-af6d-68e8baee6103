$--base-color-primary: #1269ff;
$--base-color-primary-disable: rgba(18, 105, 255, 0.5);
$--base-color-orange: #ff7a45;
$--base-color-yellow: #ffc53d;
$--base-color-yellow-disable: rgba(255, 197, 61, 0.5);
$--base-color-purple: #9254de;
$--base-color-green: #52c41a;
$--base-color-green-disable: #d9f7be;
$--base-color-red: #ff4d4f;
$--base-color-red-disable: rgba(255, 77, 79, 0.5);

$--base-color-text1: #434343;
$--base-color-text2: #8c8c8c;
$--base-color-text3: #ffffff;
$--base-color-text4: #2f54eb;
$--base-color-text5: #595959;
$--base-color-text6: #1890ff;
$--base-color-title1: #000000;

$--base-color-tag: rgba(18, 105, 255, 0.1);
$--base-color-tag-bg: #eaeff5;
$--base-color-tag-bg2: #f0f5ff;
$--base-color-tag-bg3: #e6f7ff;
$--base-color-tag-primary: #eaf1ff;
$--base-color-tag-orange: #fff2e8;
$--base-color-tag-yellow: #fff1b8;
$--base-color-tag-purple: #efdbff;
$--base-color-tag-disable: #f0f4f9;

$--base-color-bg: #fcfbff;
$--base-color-border: #e3e7ed;
$--base-color-box-bg: #f7f8fb;
$--base-color-card-bg: #f7f8fb;
$--base-color-card-bg2: #ffffff;
$--base-color-item-light: #ffffff;

$--base-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);

$--base-input-bg: #fbfcff;
$--base-input-bd: #eaeff5;
$--base-input-text: #434343;

$--base-radio-bg: #f2f4f8;

$--base-btn-bg: #dce5f5;
$--base-btn-red-bg: #fbeae9;
$--base-btn-red-bg-disable: rgba(251, 234, 233, 0.5);
$--base-btn-red-text: #f84031;
$--base-btn-text-disabled: #ffffff;
$--base-btn-yellow-bg: #fff7e6;
$--base-btn-text: #434343;
$--base-btn-primary-plain: #eaf1ff;

$--base-tree-chose: #eaeff5;

$--base-table-th: #f0f5ff;

$--base-dialog-header: #f7f8fb;
$--base-dialog-bottom: #e3e7ed;
$--base-dialog-header-text: #000000;

//input/类input样式
@mixin allInput() {
  border-radius: 8px;
  //   border: 1px solid $--base-input-bd;
  border: none;
  background: $--base-input-bg !important;
  color: $--base-input-text !important;
}
//radio/类radio样式通用
@mixin allRadio() {
  width: 100% !important;
  min-height: 32px !important;
  padding: 4px !important;
  background-color: $--base-radio-bg !important;
  border-radius: 4px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  & > label {
    flex: 1 !important;
    height: 24px !important;
    text-align: center !important;
    background-color: transparent !important;
    & > span {
      width: 100% !important;
      height: 100% !important;
      padding: 2px 20px !important;
      background: $--base-radio-bg !important;
      color: $--base-color-title1 !important;
      line-height: 20px;
      border: none !important;
      border-radius: 4px !important;
    }
    &.is-active {
      background-color: $--base-color-item-light !important;
      color: $--base-color-primary !important;
      border-radius: 4px !important;
      & > span {
        color: $--base-color-primary !important;
        background-color: $--base-color-item-light !important;
        box-shadow: none !important;
        border: none !important;
        border-radius: 4px !important;
      }
    }
    &:hover {
      color: $--base-color-primary !important;
      & > span {
        color: $--base-color-primary !important;
      }
    }
  }
}
