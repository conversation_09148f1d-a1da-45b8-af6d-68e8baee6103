<template @contextmenu.prevent="$event.preventDefault()">
  <div ref="FullRef" class="app-container-box">
    <el-row :gutter="0">
      <splitpanes class="default-theme">
        <pane>
          <el-col :span="treeLap ? 20 : 24" style="margin-top: 1px; width: 100vw">
            <Transition name="slide-fade">
              <el-empty
                v-if="flowTabs.length <= 0"
                description="请选择"
                style="height: 100%"
              ></el-empty>
              <template v-else>
                <el-tabs
                  v-model="activeFlow"
                  class="custom-tabs"
                  closable
                  type="card"
                  @tab-click="tabToggle"
                  @tab-remove="tabRemoveClick"
                >
                  <el-tab-pane
                    v-for="item in flowTabs"
                    :key="item.unique"
                    :label="item.text"
                    :name="item.unique"
                  >
                    <!-- {{ item.content }} -->
                  </el-tab-pane>
                </el-tabs>
              </template>
            </Transition>

            <Transition name="slide-fade">
              <div v-show="flowTabs.length > 0" style="width: 100vw">
                <!-- 工具头 -->
                <!-- <NavTools :isShowButton="isShowButton" :runStatus="runStatus" :graph="graph" -->
                <!-- @toggleCanvasActions="toggleCanvasActions" @FullCilck='FullCilck' @putDraw='putDraw' -->
                <!-- :stopmodel="model" @saveWorkFlowUtil="saveWorkFlowUtil(true)" @runDraw="runDrawUtil" -->
                <!-- @stopDraw="stopDrawUtil" ref="NavToolsRef" :CanvasActions="CanvasActions" /> -->
                <div class="stencil-app">
                  <!-- sync -->
                  <sync
                    v-if="syncShow"
                    class="app-sync"
                    :NodeData="nodeDataOffLine"
                    :workspace-id="workspaceId"
                    :CanvasActions="CanvasActions"
                  />
                  <!-- 侧边 -->
                  <div v-show="CanvasActions">
                    <div
                      v-show="showStencil"
                      id="stencil"
                      ref="stencilContainer"
                      class="app-stencil"
                      :class="{ 'animate-opacity': showStencil }"
                    >
                    </div>

                    <el-icon
                      class="stencil-icon"
                      :style="
                        showStencil
                          ? 'transform: translate(22vh, -150%);   z-index: 1000;'
                          : 'transform: translate(0vh, 10%); z-index: 1000000;'
                      "
                    >
                      <Operation @click="showStencilClick()" />
                    </el-icon>
                  </div>

                  <!-- 主题 -->
                  <div id="container" ref="container" class="app-content"></div>

                  <nodelog
                    v-show="nodeLogShow"
                    ref="nodelogRef"
                    class="app-nodelog"
                    :node-log-data="webScoketMsg"
                    @view-log="getLogUtil"
                  />
                  <!-- 地图 -->
                  <div v-show="minimapShow" id="minimap" class="app-Map"></div>
                  <!-- 右键菜单 -->
                  <flow-content-menu
                    v-model:visible="menuVisible"
                    :position="menuPosition"
                    :CanvasActions="CanvasActions"
                    @on-menu-click="onMenuClick"
                  />
                </div>
              </div>
            </Transition>
          </el-col>
        </pane>
      </splitpanes>
    </el-row>
    <!--  -->
    <template v-if="drawer">
      <DrawerRight
        ref="drawerRight"
        v-model:drawer="drawer"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="cancelDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>
    <el-dialog
      v-model="dialogTree.open"
      :title="'新增' + dialogTree.title"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <el-form
        ref="dataSourceRef"
        :model="dialogTree"
        :rules="rules"
        label-position="right"
        label-width="auto"
        @keydown.enter.prevent
      >
        <el-form-item :label="dialogTree.title + '名称'" prop="flowName">
          <el-input v-model="dialogTree.flowName" placeholder="请输入流程名称" />
        </el-form-item>

        <el-form-item v-if="dialogTree.title != '流程组'" label="流程类型" prop="flowType">
          <el-select v-model="dialogTree.flowType" placeholder="请选择流程类型" style="width: 100%">
            <el-option
              v-for="dict in flow_type"
              :key="dict.label"
              :value="dict.value"
              :label="dict.label"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="dialogTree.title != '流程组'" label="描述" prop="describee">
          <el-input
            v-model="dialogTree.describe"
            placeholder="请输入描述(限100字)"
            type="textarea"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogTree">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogTreeTwo.open"
      title="节点重命名"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <div>
        <el-form-item label="节点名称" prop="noticeTitle">
          <el-input v-model="dialogTreeTwo.flowName" placeholder="请输入流程名称" />
        </el-form-item>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogTreeTwo">取 消</el-button>
          <el-button type="primary" @click="submitFormTwo">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogAddNode.open"
      title="新增节点"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <div>
        <el-form-item label="节点名称" prop="noticeTitle">
          <el-input v-model="dialogAddNode.flowName" placeholder="默认自动生成名称" />
        </el-form-item>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogAddNode">取 消</el-button>
          <el-button type="primary" @click="submitFormAddNode">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogSubDescription.open"
      title="提交流程"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <div>
        <el-form-item label="描述" prop="noticeTitle">
          <el-input
            v-model="dialogSubDescription.flowName"
            placeholder="请输入描述(限100字)"
            type="textarea"
            maxlength="100"
          />
        </el-form-item>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialogSubDescription">取 消</el-button>
          <el-button type="primary" @click="submitFormSubDescription">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看日志Drawer -->
    <el-drawer v-model="nodeLogDrawer" title="查看日志" :append-to-body="true" size="80%">
      <!-- 使用css 原样展示LogData -->
      <div class="nodeLog">
        <pre>{{ LogData }}</pre>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  // import autofit from "autofit.js" // 屏幕适应
  /*
AntV X6
*/
  import { Graph } from '@antv/x6'; // 导入图形库
  import { Clipboard } from '@antv/x6-plugin-clipboard'; // 导入剪切板插件
  import { History } from '@antv/x6-plugin-history'; // 导入历史插件
  import { Keyboard } from '@antv/x6-plugin-keyboard'; // 导入快捷键插件
  import { MiniMap } from '@antv/x6-plugin-minimap'; // 导入缩略图插件
  import { Scroller } from '@antv/x6-plugin-scroller'; // 导入滚动条插件
  import { Selection } from '@antv/x6-plugin-selection'; // 导入选中插件
  import { Snapline } from '@antv/x6-plugin-snapline'; // 导入对齐线插件
  import { Stencil } from '@antv/x6-plugin-stencil'; // 导入侧边插件

  import {
    addWorkFlow,
    buildFlowNode,
    delWorkflow,
    delWorkflowGroup,
    getLog,
    getNodeVersion,
    // getNode,
    getOperatorTree,
    getProjTreeMenu,
    getUUid,
    lockFlow,
    openChildWorkFlowVersion,
    putDrawRes,
    runDraw,
    saveNode,
    saveOrUpdate,
    saveWorkFlow,
    stopDraw,
    toCheck,
    unlockFlow,
    versionOpen,
    openWorkFlow,
    openChildWorkFlow,
    getNode,
  } from '@/api/DataDev';

  import {
    graphOptions,
    ports,
    SETTING_SHAPE_NAME,
  } from '@/views/DataAggregation/DataDev/ProcessDesign'; // 导入配置
  import DrawerRight from '@/views/DataAggregation/DataDev/ProcessDesign/module/drawer/index.vue';
  import { useFullscreen } from '@vueuse/core';
  import rawSource from '../../DataDev/ProcessDesign/data.js'; // 导入mock 数据
  import FlowContentMenu from '../ProcessDesign/components/FlowContentMenu.vue'; // 导入右键组件
  import sync from '../ProcessDesign/module/drawer/components/offSync/sync.vue';
  import nodelog from '../ProcessDesign/module/nodeLog/index.vue'; // 导入 nodelog 组件

  import { useWorkFLowStore } from '@/store/modules/workFlow';

  const { proxy } = getCurrentInstance();

  const eCopy = ref();
  const NodeData = ref();
  const NNN = ref();
  const syncShow = ref(false);

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const currentUrl = window.location.host;
  const urlProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
  const webSocket = new WebSocket(`${urlProtocol}://${currentUrl}/socket/operator/websocket`);

  const webScoketMsg = ref();
  const webScoketMsgList = ref([]);
  webSocket.onopen = function () {
    getUUid().then((res) => {
      const data = {
        id: res.data,
        token: '1',
        command: 'register',
        data: '',
      };
      setInterval(function () {
        if (webSocket.readyState === WebSocket.OPEN) {
          webSocket.send(JSON.stringify(data));
        } else {
          // 连接可能已经关闭，重新连接或采取其他措施
        }
      }, 30000); // 每 30 秒发送一次心跳

      webSocket.send(JSON.stringify(data));
    });
  };
  const runStatus = ref(false);
  webSocket.onmessage = function (e) {
    if (JSON.parse(e.data).command == 'flow_task_status') {
      webScoketMsgList?.value?.push(JSON.parse(e.data));

      // 如果当前的标签和 最新的相等 那么就显示
      if (activeFlow.value === JSON.parse(e.data).data.flowName) {
        webScoketMsg.value = JSON.parse(e.data);
        if (
          webScoketMsg.value.data.statusCode == '0' ||
          webScoketMsg.value.data.statusCode == '1'
        ) {
          runStatus.value = true;
        } else {
          runStatus.value = false;
        }
      }
    } else if (JSON.parse(e.data).command == 'flow_status') {
      const taskStatuses = JSON.parse(e.data).data.taskStatuses;

      taskStatuses.forEach((res) => {
        const quety = {
          code: JSON.parse(e.data).code,
          command: JSON.parse(e.data).command,
          id: res.id,
          data: res,
          message: JSON.parse(e.data).message,
        };
        webScoketMsgList?.value?.push(quety);
      });

      if (activeFlow.value === JSON.parse(e.data).data.taskStatuses[0].flowName) {
        const queey = {
          code: JSON.parse(e.data).code,
          command: JSON.parse(e.data).command,
          id: JSON.parse(e.data).data.taskStatuses[0].id,
          data: JSON.parse(e.data).data.taskStatuses[0],
          message: JSON.parse(e.data).message,
        };

        webScoketMsg.value = queey;

        nodeLogShow.value = true;
        JSON.parse(e.data).data.taskStatuses.forEach((s) => {
          if (s.statusCode == '0' || s.statusCode == '1') {
            runStatus.value = true;
          }
        });
      }
    }
  };

  webSocket.onerror = function () {
    console.log('连接失败');
  };
  webSocket.onclose = function () {
    console.log('连接关闭');
  };

  // const workspaceId = ref()
  const FullRef = ref();
  const { toggle } = useFullscreen(FullRef);

  /** 表单校验
   *
   */
  const rules = {
    flowName: [
      { required: true, message: '请输入流程名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
      // 禁用特殊字符 ……&#￥、“”、空格
      { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: '禁止输入特殊字符', trigger: 'blur' },
    ],
    flowType: [{ required: true, message: '请选择流程类型', trigger: 'blur' }],
    describe: [
      { required: true, message: '请输入描述', trigger: 'blur' },
      { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
    ],
  };

  /**
   * 日志 */

  const nodeLogShow = ref(false);
  const nodeLogDrawer = ref(false);
  const LogData = ref([]);
  /*
AntV X6
----------------------------------------
*/

  const props = defineProps({
    // 地图显示
    minimapShow: {
      type: Boolean,
      default: true,
    },
    // 画布可控
    // CanvasActions: {
    //   type: Boolean,
    //   default: false,
    // }
  });

  const CanvasActions = ref(false);

  // mock 节点数据
  const nodes = reactive(rawSource);

  // 画布右键菜单
  const menuVisible = ref(false); // 显示/隐藏
  const menuPosition = ref({}); // 显示位置
  const selector = ref(); // 当前选中值

  // 点击节点展示配置
  const drawer = ref(false);
  const drawerTitle = ref('');
  const drawerType = ref('');
  const nodeData = ref({});
  const flow = ref({});
  const jobLogId = ref('');
  const changeFlow = (data) => {
    flow.value = data;
  };
  const showStencil = ref(true); // 显示/隐藏 侧边栏

  // 侧边内容属性
  const commonAttrs = {
    body: {
      fill: 'rgba(51, 200, 156, 1)', // 背景色
      stroke: 'rgba(51, 200, 156, 0.12)', // 边框颜色
      strokeWidth: '8', // 线条粗细
      rx: 120,
      ry: 120,
    },
  };
  // 工具头 传参
  const graph = ref();
  const model = ref();

  /*
tree
----------------------------------------
*/
  // 下拉选择值
  const dataTreeType = ref('ALL');
  // 下拉选择list
  const dataTreeTypeList = ref([
    // 全部 我创建的 我收藏的
    {
      value: 'ALL',
      label: '全部',
    },
    {
      value: 'MY_CREATE',
      label: '我创建的',
    },
    {
      value: 'MY_COLLECT',
      label: '我收藏的',
    },
  ]);
  // 显示隐藏 tree
  const treeLap = ref(true);
  // tree 固定参数
  const defaultProps = {
    children: 'children',
    label: 'text',
  };

  const dataTree = ref([]);
  const deptName = ref(''); // 树节点name
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);
  // 数据
  const menuData = ref(null);
  const menuNode = ref(null);
  const treeRef = ref();
  const treeData = ref();
  const menuDataCopy = ref();

  // 搜索
  const filterText = ref('');

  // 弹窗
  const dialogTree = reactive({
    title: '',
    open: false,
    flowName: '',
    typeName: '',
    describe: '',
    type: 0,
    flowType: '',
  });
  const flow_type = ref([
    {
      value: 'OFFLINE',
      label: '离线',
    },
    {
      value: 'REALTIME',
      label: '实时',
    },
  ]);
  const dialogTreeTwo = reactive({
    open: false,
    flowName: '',
  });
  const dialogTreeThree = reactive({
    open: false,
    flowName: '',
  });
  const dialogAddNode = reactive({
    open: false,
    flowName: '',
  });
  const dialogSubDescription = reactive({
    open: false,
    flowName: '',
  });

  // NavTabs

  /**  打开的所有项目流程 */
  const flowTabs = ref([]);
  /** 当前选中的项目流程-name */
  const activeFlow = ref('');

  /** 当前选中的项目流程-对象 */
  const activeFlowItem = ref({});

  watch(filterText, (val) => {
    treeRef.value?.filter(val);
  });

  const isShowButton = ref(true);
  /**
   * 上一次的 data
   */
  const previousData = ref('');
  /**
   * 页签切换
   */
  const nodeDataOffLine = ref();
  let foundObject = null;
  const tabToggle = async (data, shouldOpenChildWorkFlow = true) => {
    syncShow.value = false;
    runStatus.value = false;

    console.log('标签切换了', graph.value);

    // 如果 data.props.name 和 flowTabs.value 数组包含的对象里的 unique 相等 那么返回  flowTabs.value 的flowType
    console.log(data);
    console.log(flowTabs.value);
    foundObject = flowTabs.value.find((value) => {
      if (value.text === data?.props?.label) {
        return value;
      }
      if (value.text === data.text) {
        return value;
      }
    });
    console.log('flowTabs.value', foundObject);
    activeFlowItem.value = foundObject;

    let flowType;
    let flowName;
    if (foundObject) {
      flowType = foundObject.flowType;
      flowName = foundObject.operatorName;
    }
    activeFlow.value = data?.props?.name ? data?.props?.name : data;
    if (foundObject?.pNodeId && shouldOpenChildWorkFlow && flowName != '离线同步') {
      await openChildWorkFlowUtil(foundObject);
      syncShow.value = false;
      isShowButton.value = false;
      nodeLogShow.value = false;
    } else if (flowName) {
      if (flowName === '离线同步') {
        nextTick(() => {
          activeFlow.value = foundObject.unique;
          nodeDataOffLine.value = {
            id: foundObject.unique,
            flowId: foundObject.parentKey,
          };
          syncShow.value = true;
          isShowButton.value = false;
          nodeLogShow.value = false;
          drawer.value = false;
        });
      }
    } else {
      // await openDrae(foundObject?.flowId ? foundObject.flowId : data)

      await openDrae(foundObject?.flowId ? foundObject.flowId : data);
      syncShow.value = false;
      isShowButton.value = true;
      // 判断 webScoketMsgList.value 里是否有 和 activeFlow.value 相等的 说明是运行过的日志 有就显示 没有就不显示
      if (webScoketMsgList.value.some((res) => res.data.flowName === activeFlow.value)) {
        nodeLogShow.value = true;

        // 用activeFlow.value 取 webScoketMsgList.value 的最后一个 并且不是 .data?.msg == '流程运行完成'
        // 如果是 .data?.msg == '流程运行完成' 那么就取 倒数第二个
        for (let i = webScoketMsgList.value.length - 1; i >= 0; i--) {
          const res = webScoketMsgList.value[i];
          if (res.data.flowName === activeFlow.value) {
            if (res.data.msg === '流程运行完成' && i > 0) {
              webScoketMsg.value = webScoketMsgList.value[i - 1];
            } else {
              webScoketMsg.value = res;
            }
            break;
          }
        }
      } else {
        nodeLogShow.value = false;
      }
    }
  };

  /**
   * 页签移除
   */
  const tabRemoveClick = (targetName) => {
    // flowTabs.value = []
    const tabs = flowTabs.value;
    let activeName = activeFlow.value;
    if (activeName === targetName) {
      tabs.forEach((tab, index) => {
        if (tab.unique === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1];
          if (nextTab) {
            activeName = nextTab.unique;
          } else {
            activeName = '';
          }
          activeFlowItem.value = nextTab || {};
        }
      });
    }
    activeFlow.value = activeName;

    // 找到 打开的父页签 和子页签 如果父页签关闭 那么子页签也要关闭
    const childs = tabs.filter((value) => {
      if (value.parentKey === tabs.find((value) => value.text === targetName)?.flowId) {
        return value;
      }
    });

    // 获取所有子页签的unique值
    const childUniques = childs.map((child) => child.unique);

    // 过滤掉关闭的页签和所有子页签
    flowTabs.value = tabs.filter(
      (tab) => tab.unique !== targetName && !childUniques.includes(tab.unique),
    );

    // flowTabs.value = tabs.filter((tab) => tab.unique !== targetName);
    previousData.value = '';
    previousNodeData.value = '';
    console.log('标签关闭了');

    if (flowTabs.value.length) {
      const query = {
        props: {
          flowType: flowTabs.value[flowTabs.value.length - 1].flowType,
          id: flowTabs.value[flowTabs.value.length - 1].operatorId
            ? flowTabs.value[flowTabs.value.length - 1].operatorId
            : null,
          name: flowTabs.value[flowTabs.value.length - 1].text,
          label: flowTabs.value[flowTabs.value.length - 1].text,
        },
      };
      tabToggle(query);
    } else {
      // graph.value && graph.value.dispose();
      // graph.value = null;
    }
  };

  /**
   * 增加页签
   * @param unique 页签name属性，唯一值
   * @param obj
   */
  async function tabAddClick(unique) {
    console.log('页签增加了', unique);
    console.log('页签增加了', NodeData.value);
    // if (typeof unique != "object") return
    let obj = '';
    if (typeof unique === 'undefined') {
      console.log('undefined');
      obj = {
        unique: NodeData.value.id, // 本身id
        key: 'flow-' + NodeData.value.id, // 如果是父级别 则用 flow 子级别则用node
        parentKey: NodeData.value.flowId,
        program: NodeData.value.program,
        text: NodeData.value.id,
        content: 'New Tab content' + NodeData.value.nodeName,
        pNodeId: NodeData.value.id,
        operatorId: NodeData.value.operatorId,
        operatorName: NodeData.value.operatorName,
      };
    } else {
      obj = {
        flowId: unique.id ? unique.id : unique.flowId, // flowid
        flowType: unique.flowType, // 子无
        unique: unique.text, // 本身id
        key: 'flow-' + unique.id, // 如果是父级别 则用 flow 子级别则用node
        parentKey: unique.parentKey ? unique.parentKey : null,
        program: 'FLOW',
        text: unique.text,
        content: 'New Tab content' + unique.id,
        pNodeId: unique.pNodeId ? unique.pNodeId : null,
        operatorId: unique.operatorId ? unique.operatorId : null,
        operatorName: unique.operatorName ? unique.operatorName : null,
      };
    }

    const active = flowTabs.value.find((value) => {
      return value.text == obj.text;
    });

    if (!active) {
      flowTabs.value.push(obj);
    }

    // activeFlow.value = obj.text;
    activeFlowItem.value = active || obj;

    // await getOperatorList(query)
    if (obj.parentKey) {
      await tabToggle(obj);
    } else {
      await tabToggle(unique.id ? unique.id : unique.flowId);
    }
  }

  const previousNodeData = ref();
  // ??
  const handleNodeClick = async (data, node, self) => {
    node;
    console.log('%c [ node ]-520', 'font-size:13px; background:pink; color:#bf2c9f;', node);
    self;
    console.log('%c [ self ]-522', 'font-size:13px; background:pink; color:#bf2c9f;', self);

    data;
    console.log('%c [ data ]-596', 'font-size:13px; background:pink; color:#bf2c9f;', data);
    if (node?.level == 1) {
      return;
    }
    if (previousNodeData.value === data) {
      return;
    }
    previousNodeData.value = data;
    deptName.value = data.label;

    const active = flowTabs.value.some((value) => {
      return value.text == data?.text;
    });
    const activeTab = flowTabs.value.find((value) => {
      return value.text == data?.text;
    });
    if (active) {
      const qu = {
        props: {
          flowType: activeTab.flowType,
          id: activeTab.operatorId,
          name: activeTab.text,
          label: activeTab.text,
        },
      };
      // await tabToggle(qu)
    }

    await tabAddClick(data);
  };

  /**
 AntV X6
 */

  function graphInit() {
    graph.value = new Graph(graphOptions());
    //  这两处的真假同时控制画布的权限 initEvent(false)

    // 根据后端数据进行渲染
    // graph.value.fromJSON(openFlowData.value.flowCanvasJson ? JSON.parse(openFlowData.value.flowCanvasJson) : {})
    console.log('初始化画布了', graph.value.toJSON());

    // 画布居中
    graph.value.zoomTo(1.0);
    graph.value.centerContent();
    initSidebar();

    // 快捷键以及事件
    initEvent(false);

    // 监听画布变化
    const parse = () => {
      // 获取画布内容
      model.value = JSON.stringify(graph.value.toJSON(), null, 2);
      // console.log('innerText', model.value)
    };
    parse();
    // 监听画布变化
    graph.value.on('cell:change:*', parse);
    graph.value.centerContent();
  }

  /**
 初始化侧边栏
 */
  const stencil = ref();

  function initSidebar() {
    operatorList.value.forEach((s) => {
      s.collapsable = true;
    });
    // 初始化侧边栏
    stencil.value = new Stencil({
      title: '', // 侧边栏标题
      target: graph.value, // 注册到graph中
      search(cell, keyword) {
        return cell.shape.indexOf(keyword) !== -1;
      }, // 搜索
      placeholder: '搜索算子', // 搜索框提示
      notFoundText: 'Not Found', // 没有找到提示
      collapsable: false, // 是否可折叠
      stencilGraphHeight: 0, // 侧边栏高度
      stencilGraphWidth: 220,
      // 侧边栏分组内容
      groups: operatorList.value,
    });
    // 注册到 页面
    proxy.$refs.stencilContainer.appendChild(stencil.value.container); // 注册到div中

    // 侧边模块

    operatorList.value.forEach((item) => {
      const n = [];
      result.value.map((it) => {
        if (it.pid == item.flowId) {
          n.push(graph.value.createNode(it));
        }
      });
      stencil.value.load(n, item.title);
    });
  }

  // 工具与事件
  function initEvent(check = false) {
    // 工具集
    const pluginsToUse = [
      {
        plugin: Scroller,
        config: {
          enabled: true,
          pageVisible: false,
          pageBreak: false,
          pannable: false,
        },
      },
      {
        plugin: MiniMap,
        config: {
          container: document.getElementById('minimap'),
          width: 150,
          height: 150,
        },
      },
      {
        plugin: Snapline,
        config: {
          enabled: true,
        },
      },
      {
        plugin: History,
        config: {
          enabled: !check,
        },
      },
      {
        plugin: Clipboard,
        config: {
          enabled: !check,
        },
      },
      {
        plugin: Selection,
        config: {
          enabled: true,
          showNodeSelectionBox: true,
          multiple: !check,
          rubberband: false,
        },
      },
      {
        plugin: Keyboard,
        config: {
          enabled: !check,
          // enabled: true,
        },
      },
    ];

    for (const { plugin, config } of pluginsToUse) {
      graph.value.use(new plugin(config));
    }

    graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.value.getSelectedCells();
      if (cells.length) {
        graph.value.copy(cells);
      }
      return false;
    });

    graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.value.isClipboardEmpty()) {
        const cells = graph.value.paste({ offset: 32 });
        graph.value.cleanSelection();
        graph.value.select(cells);
      }
      return false;
    });

    /*
    // 快捷键实例化
    graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.copy(cells)
      }
      return false
    })
 
    graph.value.bindKey(['meta+x', 'ctrl+x'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.cut(cells)
      }
      return false
    })
 
    graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.value.isClipboardEmpty()) {
        const cells = graph.value.paste({ offset: 32 })
        graph.value.cleanSelection()
        graph.value.select(cells)
      }
      return false
    })
 
    graph.value.bindKey(['meta+z', 'ctrl+z'], () => {
      if (graph.value.canUndo()) {
        graph.value.undo()
      }
      return false
    })
 
    graph.value.bindKey(['backspace', 'delete'], () => {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.removeCells(cells)
      }
    })
  */
    let timer = 0;
    const delay = 200;
    let prevent = false;
    // 事件
    // 点击...
    graph.value.on('cell:click', (e) => {
      console.log(e);
      timer = setTimeout(() => {
        if (!prevent) {
          console.log('click event');

          // return
          menuVisible.value = false;
          const { e: event, node } = e;
          const shape = e.node.shape;
          // 当前选中元素
          const $select =
            shape === SETTING_SHAPE_NAME
              ? document.querySelector('.x6-node-selected > rect')
              : document.querySelector(`.x6-node-selected > ${shape}`);
          if (!$select) {
            return;
          }
          const position = $select.getBoundingClientRect && $select.getBoundingClientRect();
          if (!position) {
            return;
          }
          NNN.value = node;
          drawerTitle.value = node.id;
          drawer.value = true;

          console.log('previousNodeData.value', previousNodeData.value);

          getNodeUtil(node?.store?.data?.data?.id, previousNodeData.value.id);

          // 执行单击事件的处理逻辑
        }
        prevent = false;
      }, delay);
    });

    graph.value.on('cell:dblclick', (e) => {
      // console.log(e)
      clearTimeout(timer);
      prevent = true;
      console.log('dblclick event');
      // console.log('e.node.store.data.data.id', e.node.store.data.data.id)
      // 执行双击事件的处理逻辑
      const isDrillDown = getNode(e.node.store.data.data.id).then((res) => {
        NodeData.value = res.data;
        console.log(NodeData.value);
        return res.data.isDrillDown;
      });
      if (isDrillDown) {
        // console.log(e.node.store.data.data)
        // let node = e.node.store.data.data
        // let query = {
        //   flowId: node.flowId,
        //   flowType: node.flowType,
        //   unique: node.nodeName,
        //   key: 'flow-de9db082082d3ffc61d4ec4c52bb3482',
        //   parentKey: null,
        //   parogram: 'FLOW',
        //   text: node.nodeName,
        //   content: 'New Tab contentde9db082082d3ffc61d4ec4c52bb3482',
        //   pNodeId: null,
        //   operatorId: null,
        //   operatorName: null
        // }
        // openChildWorkFlowUtil(NodeData.value)
        // syncShow.value = false
        // isShowButton.value = false
        // nodeLogShow.value = false
      }
    });

    /*
      // 双击动态添加链接桩
      // graph.value.on('node:dblclick', e => {
      //   const { e: event, node } = e
      //   const shape = e.node.shape
      //   // 当前选中元素
      //   const $select =
      //     shape === SETTING_SHAPE_NAME
      //       ? document.querySelector('.x6-node-selected > rect')
      //       : document.querySelector(`.x6-node-selected > ${shape}`)
      //   if (!$select) {
      //     return
      //   }
      //   const position = $select.getBoundingClientRect && $select.getBoundingClientRect()
      //   if (!position) {
      //     return
      //   }
      //   // 鼠标位置
      //   const pageX = event.pageX
      //   const pageY = event.pageY
      //   const zoom = graph.value.zoom()
      //   // 相对节点左上角的位置
      //   const x = (pageX - position.x) / zoom
      //   const y = (pageY - position.y) / zoom
      //   node.addPort({
      //     group: 'absolute',
      //     args: {
      //       // 计算链接桩位置
      //       x: Math.round(x),
      //       y: Math.round(y)
      //     },
      //     silent: false
      //   })
      // })
    */

    // 链接线工具
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge() && !check) {
        cell.addTools([
          'vertices',
          'segments',
          {
            name: 'button-remove',
            args: {
              x: '30%',
              y: '50%',
            },
          },
        ]);
      }
    });
    graph.value.on('cell:mouseleave', ({ cell }) => {
      if (cell.isEdge()) {
        cell.removeTool('vertices');
        cell.removeTool('segments');
        cell.removeTool('button-remove');
      }
    });

    // 链接桩控制
    graph.value.on('node:mouseenter', () => {
      showPorts(true);
    });
    graph.value.on('node:mouseleave', () => {
      showPorts(false);
    });

    // 画布右键菜单
    graph.value.on('cell:contextmenu', (e) => {
      console.log('e', e);
      const { e: event, cell } = e;
      // 画布节点
      selector.value = cell;
      menuVisible.value = false;
      menuPosition.value = {
        left: event.pageX + 'px',
        top: event.pageY + 'px',
      };

      // console.log('e.node.store.data.data.id', e.node.store.data.data.id)
      const isDrillDown = getNode(e.node.store.data.data.id).then((res) => {
        return res.data.isDrillDown;
      });
      // console.log('isDrillDown', isDrillDown)

      RightCicK.value = e;

      menuVisible.value = true;
    });

    // 点击画布空白区域
    graph.value.on('blank:click', () => {
      graph.value.cleanSelection && graph.value.cleanSelection();
      // 关闭右键菜单
      menuVisible.value = false;
    });

    // 当节点被添加到画布时
    graph.value.on('node:added', async (e) => {
      eCopy.value = e;
      const { e: event, cell } = e;
      // 节点放大3倍
      const size = cell.size();
      cell.size(size.width * 1.5, size.height * 1.5);

      dialogAddNode.open = true;
      selector.value = cell;
    });

    //  连接线触发
    graph.value.on('edge:connected', (e) => {
      const quety = {
        sourceAlgId: e.view.sourceView.cell.store.data.data.operatorId,
        targetAlgId: e.view.targetView.cell.store.data.data.operatorId,
      };
      toCheckUtil(quety);
    });
    // 移除连接线触发
    graph.value.on('edge:removed', ({ edge, options }) => {
      console.log('移除连接线', graph.value);
      if (graph.value != null) {
        const json = graph.value.toJSON();
        if (json.length > 0) {
          const flowId = json[0].data.flowId;
          if (flowId == openFlowData.value.id) {
            saveWorkFlowUtil();
          }
        }
        console.log('json', json);
      }
      /*    if(foundObject.flowId==openFlowData.value.id){
            } */
    });
  }

  // 连接桩显示/隐藏
  function showPorts(show) {
    const container = document.getElementById('container');
    const ports = container.querySelectorAll('.x6-port-body');
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden';
    }
  }

  /**
   * tree右击显示菜单
   */
  function showContextMenu(event, data, node) {
    treeData.value = data;
    console.log('data', data.id);
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    const menuDataCopy = data;
    menuData.value = data;
    menuNode.value = node;
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  // 添加全局点击事件监听器
  const clickHandler = (event) => {
    if (!menuNode.value || !menuNode.value.contains(event.target)) {
      closeContextMenu();
    }
  };

  // 菜单事件
  const append = () => {
    dialogTree.title = '流程';
    dialogTree.type = 2;
    dialogTree.open = true;
    menuDataCopy.value = menuData.value;
  };

  /**
   * 删除工作流列表数据
   */
  const remove = async () => {
    const menuNodeValue = menuNode.value;
    const treeDataValue = treeData;
    const res = await proxy.$modal.confirm(
      '是否确定删除" ' + treeDataValue.value._rawValue.text + ' "的数据项?',
    );
    if (res) {
      const tabIndex = flowTabs.value.findIndex((tab) => tab.text === menuNodeValue.data.text);

      if (tabIndex !== -1) {
        tabRemoveClick(menuNodeValue.data.text);
      }

      const deleteFunction = menuNodeValue.level == 1 ? delWorkflowGroup : delWorkflow;
      const response = await deleteFunction(menuNodeValue.data.id);
      if (response.code != 200) {
        return proxy.$modal.msgError('删除失败');
      }
      getWorkList();
      proxy.$modal.msgSuccess('删除成功');
    }
  };

  const rename = () => {
    dialogTreeTwo.open = true;
    menuDataCopy.value = menuData.value;
  };

  // 查找数据
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.includes(filterText.value);
  };
  // 添加主树
  const addTree = () => {
    dialogTree.title = '流程组';
    dialogTree.type = 1;
    dialogTree.open = true;
  };
  /**
   * 关闭弹窗
   * @param
   *  @returns None
   */
  const cancelDrawer = () => {
    drawer.value = false;
  };
  /**
   * 提交弹窗
   * @param
   *  @returns None
   */
  const submitDrawer = (e) => {
    saveNodeUtil(e);
  };
  // 清除弹窗
  const cancelDialogTree = () => {
    dialogTree.title = '';
    dialogTree.flowName = '';
    dialogTree.typeName = '';
    dialogTree.describe = '';
    dialogTree.flowType = '';
    dialogTree.type = 0;
    dialogTree.open = false;
  };

  // 保存弹窗
  const submitForm = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);

    if (!res) return;

    if (dialogTree.type == 1) {
      const quety = {
        mDescribe: dialogTree.title,
        flowGroupName: dialogTree.flowName,
        workspaceId: workspaceId.value,
      };
      saveOrUpdate(quety).then((res) => {
        if (res.code == 200) {
          cancelDialogTree();
          getWorkList();
          proxy.$modal.msgSuccess('成功');
        } else {
          // proxy.$modal.msgError('失败')
        }
      });
    }
    if (dialogTree.type == 2) {
      const form = {
        flowName: dialogTree.flowName,
        flowType: dialogTree.flowType,
        description: dialogTree.describe,
        workspaceId: workspaceId.value,
        flowGroupId: menuDataCopy.value.id,
      };
      const res = await addWorkFlowUtil(form);
      console.log('res.flowType', res.flowType);

      const data = {
        flowId: res.id, // flowid
        flowType: res.flowType, // 子无
        unique: res.flowName, // 本身id
        key: 'flow-' + res.id, // 如果是父级别 则用 flow 子级别则用node
        parentKey: res.parentKey ? res.parentKey : null,
        parogram: 'FLOW',
        text: res.flowName,
        content: 'New Tab content' + res.id,
        pNodeId: res.pNodeId ? res.pNodeId : null,
        // operatorId: res.flowGroupId ? res.flowGroupId : null,
        operatorId: null,
      };

      await cancelDialogTree();
      await getWorkList();
      await openDrae(res.id);
      await tabAddClick(data);
    }
  };

  const cancelDialogTreeTwo = () => {
    dialogTreeTwo.open = false;
    dialogTreeTwo.flowName = '';
    menuDataCopy.value = '';
  };

  const submitFormTwo = async () => {
    const quety = {
      id: menuDataCopy?.value?.id,
      flowGroupName: dialogTreeTwo.flowName,
      workspaceId: workspaceId.value,
    };
    await saveOrUpdate(quety);
    cancelDialogTreeTwo();
    getWorkList();
  };

  // const cancelDialogTreeThree = () => {
  //   dialogTreeThree.open = false
  //   dialogTreeThree.flowName = ''
  //   menuVisible.value = false
  // }
  // const submitFormThree = () => {
  //   selector.value.attr('text/text', dialogTreeThree.flowName)
  //   menuVisible.value = false
  //   // cancelDialogTreeThree()
  // }
  const cancelDialogAddNode = () => {
    dialogAddNode.open = false;
    dialogAddNode.flowName = '';
    // 移除节点
    graph.value.removeNode(selector.value);
  };
  const submitFormAddNode = async () => {
    await buildTheNode();
    await saveWorkFlowUtil();

    console.log('previousNodeData.value', previousNodeData.value);
    console.log('selector.value.store.data', selector.value.store.data.data);

    const selectorID = selector?.value?.store?.data?.data?.id;
    const flowId = selector?.value?.store?.data?.data?.flowId;

    // if (selector.value.store.data.pid == "ETL") {
    const query = {
      flowType: selector.value.store.data.flowType,
      workspaceId: workspaceId.value,
      pNodeId: selectorID,
      flowName: selectorID,
      pid: flowId,
    };
    await addWorkFlowUtil(query);
    await getNodeUtil(selectorID);
    drawer.value = true;
    // }
    // addWorkFlowUtil()

    dialogAddNode.open = false;
    dialogAddNode.flowName = '';
  };

  const cancelDialogSubDescription = () => {
    dialogSubDescription.open = false;
    dialogSubDescription.flowName = '';
  };

  const submitFormSubDescription = () => {
    putDrawUtil(dialogSubDescription.flowName);

    cancelDialogSubDescription();
  };
  const putDraw = async (e) => {
    // 判断画布是否是只读状态
    if (CanvasActions.value == false) return;

    dialogSubDescription.open = true;
  };
  const RightCicK = ref();

  /**
   * 画布右击显示菜单 与 事件
   */
  function onMenuClick(select) {
    const [option] = select;
    console.log('option', option);
    switch (option) {
      case 'deploy': {
        menuVisible.value = false;
        // 打开画布
        drawerTitle.value = RightCicK.value?.node.store?.data?.data?.algorithmName;
        drawer.value = true;
        getNodeUtil(RightCicK.value?.node.store?.data?.data?.id);
        break;
      }

      case 'name': {
        dialogTreeThree.open = true;
        break;
      }
      // case 'color': {
      //   selector.value.attr('body/fill', colors[Math.floor((Math.random() * 100) % 6)])
      //   selector.value.attr('body/stroke', colors[Math.floor((Math.random() * 100) % 6)])
      //   menuVisible.value = false
      //   break
      // }
      case 'remove': {
        graph.value.removeNode(selector.value);
        menuVisible.value = false;
        saveWorkFlowUtil();
        break;
      }
    }
  }

  /**
   * 页面刷新或关闭时
   */
  function beforeunload() {
    // 提示用户是否离开
  }

  // beforeRouteLeave() {
  // 提示用户是否离开
  // }
  const versionId = ref();
  const useType = ref();
  onMounted(() => {
    window.addEventListener('beforeunload', beforeunload());
    window.addEventListener('click', clickHandler);
    getWorkList();
    graphInit();
    // 接收路由query 传参
    const { id, name, type } = proxy.$route.query;
    useType.value = type;
    versionId.value = id;
    const query = {
      id,
      text: name,
    };
    handleNodeClick(query);
    // autofit.init({
    //   designHeight: 1080,  //项目初始的分辨率，一般就是你开发电脑的屏幕分辨率
    //   designWidth: 1920,  //项目初始的分辨率，一般就是你开发电脑的屏幕分辨率
    //   renderDom: ".app-container-box",   //App最外层的id名，一般都为ap
    //   resize: true        //是否监听resize事件，默认是 true
    // }, false)
  });

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', beforeunload());
    // graph.value && graph.value.dispose()
    // graph.value = null
    // autofit.off()
  });

  onUnmounted(() => {
    window.removeEventListener('click', clickHandler);
  });

  /**
   * 以显示或隐藏模具
   *
   * @param {type} None
   * @return {type} None
   */
  function showStencilClick(e) {
    showStencil.value = !showStencil.value;
  }

  /**
   * 根据给定事件切换画布操作.
   *
   * @param {Event} e - 触发切换操作的事件.
   * @return {undefined} None
   */
  async function toggleCanvasActions(e) {
    console.log('e', e);
    if (e) {
      console.log('1290----', nodeData.value);
      await unlockFlow({ flowId: previousNodeData.value.id });
      await addPort();
    } else {
      await lockFlow({ flowId: previousNodeData.value.id });
      await removePort();
    }
    CanvasActions.value = e;
  }

  /**
   * 全屏切换.
   *
   * @param {type} e - 事件对象.
   * @return {type} None
   */
  function FullCilck(e) {
    toggle();
  }

  /**
   * 异步获取工作区列表。
   *
   * @param {Object} e - 事件对象。.
   * @return {Promise<void>} 检索工作区列表时解析的 promise
   */
  // async function getList(e) {
  //   workspaceId.value = e.selectedWorkspaceId
  //   await getWorkList()
  // }

  /**
   * 使用给定的参数检索任务栏表。
   *
   * @param {*}  None
   * @return {Promise<void>} None
   */
  async function getWorkList() {
    if (workspaceId.value) {
      // 清空数据  关闭已打开画布 画布置空
      flowTabs.value = [];
      // graph.value && graph.value.dispose()
      // graph.value = null

      const res = await getProjTreeMenu({
        workspaceId: workspaceId.value,
        hasFlow: 1,
      });
      // console.log('res', res.data)
      dataTree.value = res.data;
    }
  }

  /**
   * 获取算子树
   * @param {OBJECT} 流程类型_节点ID
   * @returns  None
   */
  async function getOperatorList(u) {
    console.log('进入算字树', u);
    if (u) {
      const res = await getOperatorTree(u);
      res?.data && formatData(res.data);
      initSidebar();
    }
  }

  const operatorList = ref([]);
  const result = ref([]);

  /**
   * 格式化数据
   * @param {Array} data - 待格式化的数据
   * @returns {Array} 格式化后的数据
   */
  function formatData(data) {
    operatorList.value = data?.map((item) => ({
      flowId: item.id,
      title: item.name,
      name: item.name,
    }));
    console.log('operatorList.value', operatorList.value);

    result.value = data.flatMap((item) => {
      if (item.children) {
        return item.children.map((child) => ({
          inherit: 'rect', // 继承自 Shape.Rect
          width: 80, // 默认宽度
          height: 40, // 默认高度
          attrs: {
            body: {
              rx: 10, // 圆角矩形
              ry: 10,
              strokeWidth: 1,
              // fill: '#5755a1',
              stroke: '#5755a1',
            },
            // 字体
            label: {
              fill: '#000000',
              fontSize: 10.5,
            },
            style: {
              animation: 'ant-line 30s infinite linear',
            },
          },

          ports: {
            ...ports,
            items: [
              {
                group: 'left',
              },
              {
                group: 'right',
              },
            ],
          },
          label: child.name,
          operatorId: child.id,
          pid: child.pid,
          flowType: child.flowType,
        }));
      } else {
        return [];
      }
    });
    console.log('result', result.value);
  }

  /**
   * 构建节点
   * @returns  None
   */
  async function buildTheNode() {
    const re = await getUUid();
    console.log(' ', dialogAddNode.flowName);
    console.log(previousNodeData.value);
    const query = {
      flowId: previousNodeData.value.id,
      operatorId: selector.value.store.data.operatorId,
      nodeName: dialogAddNode.flowName ? dialogAddNode.flowName : re.data,
    };
    const res = await buildFlowNode(query);

    const queryData = {
      algorithmName: res.data.operatorName,
      operatorId: res.data.operatorId,
      flowId: res.data.flowId,
      id: res.data.id,
      isCustom: res.data.isCustom,
      isDrillDown: res.data.isDrillDown,
      isLogOutput: res.data.isLogOutput,
      isReportOutput: res.data.isReportOutput,
      nodeName: res.data.nodeName,
      outputProperty: res.data.outputProperty,
      parentFlowId: res.data.parentFlowId,
      parentNodeId: res.data.parentNodeId,
      program: res.data.program,
    };
    console.log('queryData', queryData);

    selector.value.store.data.data = queryData;
  }

  /**
   * 保存流程
   * @param {*} None
   * @returns None
   */
  async function saveWorkFlowUtil(flag = false) {
    const openFlowDataCopy = openFlowData.value;
    const query = {
      ...openFlowDataCopy,
      flowCanvasJson: JSON.stringify(graph.value.toJSON()),
    };
    const res = await saveWorkFlow(query);
    console.log('%c [ res ]-1303', 'font-size:13px; background:pink; color:#bf2c9f;', res);
    if (res.code == 200 && flag) {
      proxy.$modal.msgSuccess('成功');
    }
  }

  const openFlowData = ref('{"cells": []}');

  /**
   * 打开流程
   * @param {*} id
   * @returns None
   */
  async function openDrae(data) {
    let res = {};

    if (useType.value == 'dataGovernance') {
      res = await openWorkFlow(versionId.value);
    } else {
      res = await versionOpen({ id: data?.id ? data?.id : data });
    }

    openFlowData.value = res.data;
    previousNodeData.value = openFlowData.value;
    if (openFlowData.value) {
      console.log('graph=-====', graph.value);
      const lockStatus = openFlowData.value.lockStatus;
      if (lockStatus == 0 || !lockStatus) {
        CanvasActions.value = true;
      } else {
        CanvasActions.value = false;
      }
    }
    reloadGrap();
    CanvasActions.value = false;
  }

  async function reloadGrap() {
    // initEvent(!CanvasActions.value)

    nextTick(() => {
      console.log(openFlowData.value);

      activeFlow.value = openFlowData.value.flowName;
      const query = {
        flowType: openFlowData.value.flowType,
        id: activeFlowItem.value?.operatorId,
      };
      getOperatorList(query);
      graph.value.fromJSON(
        openFlowData.value.flowCanvasJson ? JSON.parse(openFlowData.value.flowCanvasJson) : {},
      );
      if (!CanvasActions.value) {
        removePort();
      }
    });

    nextTick(() => {
      graph.value.centerContent();
    });
  }

  async function addPort() {
    const cells = graph.value.getCells();
    cells.forEach((s) => {
      console.log('s.shape', s.shape);
      if (s.shape != 'edge') {
        const portList = s.getPorts();
        if (portList.length > 0) {
          if (portList.length > 1) {
            return;
          }
          const port = portList[0];
          if (port.group == 'left') {
            s.addPort({
              ...ports,
              group: 'right',
            });
          } else {
            s.addPort({
              ...ports,
              group: 'left',
            });
          }
        } else {
          s.addPort({
            ...ports,
            group: 'left',
          });
          s.addPort({
            ...ports,
            group: 'right',
          });
        }
      }
    });

    // 增加移除连接线按钮
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge()) {
        cell.addTools([
          'vertices',
          'segments',
          {
            name: 'button-remove',
            args: {
              x: '30%',
              y: '50%',
            },
          },
        ]);
      }
    });
  }

  async function removePort() {
    graph.value.on('edge:connected', () => false);

    const cells = graph.value.getCells();
    console.log('cells', cells);
    cells.forEach((s) => {
      if (s.shape != 'edge') {
        const portList = s.getPorts();
        if (portList.length) {
          portList.forEach((port) => {
            const canRemove = checkEdges(s, port);
            if (!canRemove) {
              s.removePort(port.id);
            }
          });
        }
      }
    });
    // 移除连接线按钮
    graph.value.on('cell:mouseenter', ({ cell }) => {
      if (cell.isEdge()) {
        cell.removeTool('vertices');
        cell.removeTool('segments');
        cell.removeTool('button-remove');
      }
    });
  }

  function checkEdges(cell, port) {
    const connectedEdges = graph.value.getConnectedEdges(cell);
    // 遍历端口
    // 判断是否被连接
    const isPortConnected = connectedEdges.some((edge) => {
      const sourcePortId = edge.getSourcePortId();
      const targetPortId = edge.getTargetPortId();
      return sourcePortId === port.id || targetPortId === port.id;
    });
    console.log('isPortConnected', isPortConnected);
    return isPortConnected;
  }

  /**
   * 保存流程
   * @param {Object} form
   * @returns   Object res - 保存的流程
   */
  async function addWorkFlowUtil(form) {
    const res = await addWorkFlow(form);
    return res.data;
  }

  /**
   * 获取节点实用进程.
   *@param {String} id
   * @return {Promise} None.
   */
  const getNodeId = ref();
  async function getNodeUtil(id, version = versionId.value) {
    let res = [];

    if (useType.value == 'dataGovernance') {
      res = await getNode(id);
    } else {
      getNodeId.value = id;
      res = await getNodeVersion({
        nodeId: id,
        versionId: version,
      });
    }

    NodeData.value = res.data;
  }

  /**
   * 保存节点实用进程功能。
   *
   * @param {Object} data
   * @return {Promise} None
   */
  async function saveNodeUtil(data) {
    const res = await saveNode(data);
    if (res.code == 200) {
      proxy.$modal.msgSuccess('成功');
    } else {
      proxy.$modal.msgError('失败');
    }
  }

  /**
   * 执行 openChildWorkFlowUtil 函数的异步操作。
   *
   * @return {Promise<void>} 当函数执行完成时解析的 Promise 对象。
   */
  /* async function openChildWorkFlowUtil(data) {
 
  console.log('%c [ data ]-1483', 'font-size:13px; background:pink; color:#bf2c9f;', data)
  console.log('%c [ data ]-1483', 'font-size:13px; background:pink; color:#bf2c9f;', NodeData.value)
  console.log('%c [ data ]-1483', 'font-size:13px; background:pink; color:#bf2c9f;', flowTabs.value)
 
  let res = ''
  if (NodeData.value.program != 'OFFLINE_ALG') {
    console.log('=============================');
 
    console.log('data',data?.pNodeId );
    console.log('NodeData',NodeData?.value.id );
 
    res = await openChildWorkFlow(data ? data.pNodeId : NodeData.value.id)
  }
  else if (NodeData.value.operatorName == '离线同步') {
    res = {
      code: 200,
      data: {
        flowCanvasJson: '{"cells": []}',
        // program: 'OFFLINE_SYNC',
        // flowType: 'OFFLINE_SYNC',
        id: NodeData.value.nodeName,
        pid: NodeData.value.flowId,
        pNodeId: NodeData.value.id,
        // operatorId: NodeData?.value?.operatorId,
        // operatorName: NodeData?.value?.operatorName
 
      }
    }
  }
  else {
    res = {
      code: 200,
      data: {
        flowCanvasJson: '{"cells": []}',
        flowName: '离线算法',
        id: 'OFFLINE_ALG',
        lockStatus: 0,
        pid: 'ETL',
        program: 'OFFLINE_ALG',
      }
    }
  }
 
  openFlowData.value = res.data
  previousNodeData.value = openFlowData.value
 
  let query = {
    flowId: previousNodeData.value.id,
    //text: NodeData?.value?.nodeName ? NodeData?.value?.nodeName : data.text,
    text: data?.text ? data?.text: NodeData?.value?.nodeName,
 
    flowType: res.data.flowType,
    parentKey: previousNodeData.value.pid,
    pNodeId: previousNodeData.value.pNodeId,
    //operatorId: NodeData?.value?.operatorId ? NodeData?.value?.operatorId : data.operatorId,
    //operatorName: NodeData?.value?.operatorName ? NodeData?.value?.operatorName : data.operatorName
    operatorId: data?.operatorId?  data?.operatorId:NodeData?.value?.operatorId,
    operatorName: data?.operatorName? data?.operatorName:NodeData?.value?.operatorName
 
  }
 
  // 判断 flowTabs.value 中是否 有 NodeData.value.flowid已经存在 如果已经纯在说明是第二次 执行切换
  let active = flowTabs.value.some((value) => {
    return value.flowId == openFlowData.value.id;
  });
  let activeTab = flowTabs.value.find((value) => {
    return value.flowId == openFlowData.value.id;
  });
 
  if (active) {
    let qu = {
      props: {
        flowType: activeTab.flowType,
        id: activeTab.operatorId,
        name: activeTab.text,
        label: activeTab.text
      }
    }
    if (activeFlow.value != activeTab.text) {
      console.log('child切换了', activeTab.text)
      activeFlow.value = activeTab.text;
    }
 
  }
 
 
  await tabAddClick(query)
  cancelDrawer()
 
  if (query.operatorName == '离线同步') {
    syncShow.value = true
  }
  reloadGrap();
  isShowButton.value = false
} */
  async function openChildWorkFlowUtil(data) {
    // console.log('data', data)
    console.log('开始open滋滋滋画布');
    console.log('data.pNodeId', data.pNodeId);
    console.log('NodeData.value.id', NodeData.value.id);
    let res = {};
    console.log('useType.value', useType.value);
    if (useType.value == 'dataGovernance') {
      res = await openChildWorkFlow(data ? data.pNodeId : NodeData.value.id);
    } else {
      res = await openChildWorkFlowVersion({
        nodeId: getNodeId.value,
        versionId: versionId.value,
      });
    }

    openFlowData.value = res.data;
    previousNodeData.value = openFlowData.value;
    console.log('activeFlow.value', activeFlow.value);
    drawer.value = false;
    reloadGrap();
  }

  /**
   * 检查数据
   * @param {Object} data
   * @return {Promise} None
   */
  async function toCheckUtil(data) {
    try {
      await toCheck(data);
      await saveWorkFlowUtil();
    } catch (error) {
      //  移除连接线
    }
  }

  async function stopDrawUtil() {
    try {
      const res = await stopDraw({ flowId: previousNodeData.value.id });
      console.log('res', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess('成功');
        webScoketMsg.value = '' ? (nodeLogShow.value = false) : (nodeLogShow.value = true);
      } else {
        proxy.$modal.msgError('失败');
      }
    } catch (error) {
      //  移除连接线
      // proxy.$modal.msgError('失败')
    }
  }

  async function runDrawUtil() {
    try {
      const res = await runDraw({ flowId: previousNodeData.value.id });
      console.log('res', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess('成功');
        webScoketMsg.value = '' ? (nodeLogShow.value = false) : (nodeLogShow.value = true);
      } else {
        proxy.$modal.msgError('失败');
      }
    } catch (error) {
      //  移除连接线
      // proxy.$modal.msgError('失败')
    }
    // const res = await runDraw({ flowId: previousNodeData.value.id })
    // console.log('res*-*-*-*-*', res)
    // if (res.code == 200) {
    //   proxy.$modal.msgSuccess('成功')
    //   webScoketMsg.value = '' ? nodeLogShow.value = false : nodeLogShow.value = true

    // } else {
    //   proxy.$modal.msgError('失败')
    // }
  }

  async function putDrawUtil(data) {
    const res = await putDrawRes({
      flowId: previousNodeData.value.id,
      note: data,
    });
  }

  async function getLogUtil(e) {
    const res = await getLog({ taskInstanceId: e });
    console.log('res', res);
    if (res.code == 200) {
      nodeLogDrawer.value = true;
      LogData.value = res.data;
      console.log('LogData.value ', LogData.value);
    }
  }

  // watch 监听workspaceId的变化 重新获取数据
  watch(workspaceId, (val) => {
    getWorkList();
  });
</script>

<style lang="scss" scoped>
  $white: #ffffff;
  $base-primary: #2468f1;
  $base-bg-color: #f9fafc;
  $base-text-color-regular: #4e5370;
  $base-text-color: #172241; // 主要文字颜色
  $base-text-color-regular: #4e5370; // 常规文字颜色
  $base-text-color-secondary: #9ea0b1; // 次要文字颜色
  $base-text-color-placeholder: #c3c7cc; // 占位符颜色/提示性文字
  $border-color-lighter: #ebecee;

  /* 修改element组件【Tabs 标签页】默认样式
        ---------------------------------------------------*/
  :deep .el-tabs.custom-tabs {
    background-color: $base-bg-color;

    .el-tabs__item {
      margin-top: 5.9px;
      height: 30px;
      font-size: 12px;
      padding: 0 28px !important;
      line-height: 28px;
      border-top: 2px solid transparent;
      color: $base-text-color-regular;
      border-bottom: 2px solid transparent;
      outline: none;
      transition:
        color 0.3s ease-in,
        border-color 0.5s ease-in-out;

      .el-icon-close {
        color: $base-text-color-secondary;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 7px;
      }

      &.is-active {
        color: $base-text-color;
        border-bottom: 1px solid $base-primary;
        font-weight: 600;
        transition:
          color 0.5s ease,
          border-color 0.3s ease-in-out;
      }

      &:last-child {
        padding-right: 28px;
      }
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 32px;
    }
  }

  :deep .el-tabs--card.custom-tabs > .el-tabs__header {
    border-bottom: 1px solid $border-color-lighter;
    margin-bottom: 0;

    .el-tabs__item {
      border-radius: 5px;

      &:last-child {
        padding-right: 28px;
      }

      &.is-active.is-closable,
      &.is-closable:hover {
        padding-left: 28px;
        padding-right: 28px;
      }
    }

    .el-tabs__nav {
      border: none;
      background-color: $white;
      border-radius: 0;
      border-radius: 5px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
      opacity: 0.9;
    }

    .el-tabs__nav-wrap {
      margin-bottom: 0;
    }
  }

  :deep .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: $border-color-lighter;
    border-radius: 10px;
  }

  .tree-container {
    position: relative;
    // overflow: hidden;
    max-width: 220px;
    max-height: 95vh;
  }

  .tree-lap {
    position: absolute;
    cursor: pointer;
    top: 10;
    bottom: 0;
    z-index: 100;
    left: 210px;
    transition: left 0.3s;
  }

  .treeFab {
    height: calc(100% - 24px);
    overflow-y: auto;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .head-title-tree {
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    background: #f7f7fa;
    padding: 5px;
  }

  .head-container {
    //display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    padding: 10px;
    padding-top: 2px;
    width: 100%;
    height: calc(100vh - 52px);
    border-right: 1px solid #dfe3e8;

    // select 宽度
    :deep .el-select {
      width: 100%;
    }
  }

  // .tree-lap {
  //   display: inline-block;
  //   right: 180px;
  //   margin-top: 89vh;
  //   margin-left: -35px;
  //   cursor: pointer;
  //   // 增加过渡效果
  //   // transition: all 0.1s ease-in-out;
  //   z-index: 1;
  // }

  .stencil-app {
    display: flex;
    height: calc(100vh - 132px);

    .app-stencil {
      position: relative;
      width: 225px;
      height: 85vh;
      transition: all 0.3s ease;
    }

    .animate-opacity {
      animation: opacity 1s ease-in-out;
    }

    @keyframes opacity {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }

    .app-sync {
      position: absolute;
      bottom: 0;
      z-index: 100 !important;
      display: block;
    }

    .app-Map {
      position: absolute;
      // 左下角
      bottom: 15vh;
      right: 30px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 0 10px 1px #e9e9e9;
      width: 150px;
      height: 150px;
      z-index: 99;
    }

    #container {
      min-width: 100vw;
      min-height: 100%;
      position: relative;
    }

    .app-nodelog {
      position: absolute;
      bottom: 0;
    }
  }

  #stencil {
    border-right: 1px solid #dfe3e8;
  }

  .stencil-icon {
    position: static;
    font-size: 20px;
    cursor: pointer;
    // margin-left: 15%;
    z-index: 1000;
    opacity: 0.5;
    transition: opacity 0.3s ease-in-out;
  }

  :deep .x6-widget-stencil {
    background-color: #ffffff;
  }

  :deep .x6-widget-stencil-title {
    background-color: #fff;
  }

  :deep .x6-widget-stencil-group-title {
    background-color: #fff !important;
  }

  :deep .x6-widget-transform {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }

  :deep .x6-widget-transform > div {
    border: 1px solid #239edd;
  }

  :deep .x6-widget-transform > div:hover {
    background-color: #3dafe4;
  }

  :deep .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }

  :deep .x6-widget-transform-resize {
    border-radius: 0;
  }

  :deep .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }

  :deep .x6-widget-selection-box {
    opacity: 0;
  }

  .app-container-box {
    overflow-x: hidden;
    overflow-y: hidden;
    width: 100%;
    max-height: 100%;
    // background: #ffffff;
    overflow: hidden;
    // margin-top: 1px;
    padding-top: 1px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /*
        :deep .el-button {
          width: 80px;
          height: 25px;
        }*/

  /*:
        deep .el-button--primary {
          background: #1F78EF;
        }*/

  :deep .el-drawer__header {
    margin-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
    padding: 10px 20px;
  }

  :deep .el-drawer__title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .container {
    display: flex;
  }

  .resizable {
    flex-grow: 1;
  }

  :deep .el-overlay {
    // background: #000;
    position: absolute;
    border: 1px solid #000;
    top: 0;
  }

  // .bounce-enter-active {
  //   animation: bounce-in 0.5s;
  // }

  // .bounce-leave-active {
  //   animation: bounce-in 0.5s reverse;
  // }

  // @keyframes bounce-in {
  //   0% {
  //     transform: scale(0);
  //   }

  //   50% {
  //     transform: scale(1.25);
  //   }

  //   100% {
  //     transform: scale(1);
  //   }
  // }

  // .slide-fade-enter-active {
  //   transition: all 0.3s ease-out;
  // }

  // .slide-fade-leave-active {
  //   transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  // }

  // .slide-fade-enter-from,
  // .slide-fade-leave-to {
  //   transform: translateX(20px);
  //   opacity: 0;
  // }
  // .splitpanes {
  //   background: none;
  // }

  // .splitpanes__pane {
  //   box-shadow: 0 0 5px rgba(0, 0, 0, .2) inset;
  //   justify-content: center;
  //   align-items: center;
  //   display: flex;
  // }

  // .splitpanes--vertical>.splitpanes__splitter {
  //   min-width: 6px;
  //   background: linear-gradient(90deg, #ccc, #111);
  // }

  // .splitpanes--horizontal>.splitpanes__splitter {
  //   min-height: 6px;
  //   background: linear-gradient(0deg, #ccc, #111);
  // }
  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .collapsed {
    width: 0;
    /* 或者设置为你期望的宽度，或使用其他样式来隐藏侧边栏 */
    transition: width 0.3s ease;
    /* 添加过渡效果，可选 */
  }
</style>

<style>
  @keyframes ant-line {
    to {
      stroke-dashoffset: -1000;
    }
  }
</style>
