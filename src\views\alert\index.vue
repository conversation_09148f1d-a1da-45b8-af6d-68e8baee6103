<template>
  <div class="app-container">
    <!-- <HeadTitle title="订阅列表" />

    <el-select @change="getSubscribeList" v-model="queryParams.workspaceId">
      <el-option v-for="space in workSpaceList" :key="space.workspaceId" :label="space.workspaceName"
        :value="space.workspaceId"></el-option>
    </el-select> -->
    <div class="top-container">
      <div
        style="width: 80px; color: #000000; font-weight: 600; font-size: 20px; line-height: 20px"
      >
        <!-- <HeadTitle :title="HeadTitleName" /> -->
        告警中心
      </div>
      <div style="flex: 1">
        <div style="width: 340px; margin: 0 auto">
          <el-radio-group v-model="activeName" @change="handleClick">
            <el-radio-button label="all">订阅列表</el-radio-button>
            <el-radio-button label="warn">告警历史</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div v-if="activeName == 'all'">
      <el-form style="text-align: right; margin: 20px 0" ref="queryRef" :inline="true">
        <el-form-item label="流程名称">
          <el-select
            v-model="flowName"
            :loading="loading"
            :remote-method="remoteMethod"
            remote
            clearable
            filterable
            style="width: 240px"
            @change="getSingleInfo"
          >
            <el-option
              v-for="data in optionList"
              :key="data.id"
              :label="data.flowName"
              :value="data.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订阅时间" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            @change="getTime"
          ></el-date-picker>
        </el-form-item>
        <el-form-item style="margin-right: 5px">
          <span class="table-search-btn">
            <span class="btn btn2" @click="resetQuery">
              <el-icon style="color: #434343"> <Refresh /> </el-icon>
            </span>
          </span>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="subscribeList" @selection-change="handleSelectionChange">
        <el-table-column type="index" label="序号" width="65" align="center">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <el-table-column v-if="true" label="流程名ID" align="center" prop="bizId" />
        <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
        <el-table-column v-if="true" label="流程创建人" align="center" prop="createProcessUser" />
        <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleDelete(scope.row)">取消订阅</el-button>
            <el-button link type="primary" @click="handleSubscribe(scope.row)">告警设置</el-button>
            <el-button link type="primary" @click="goHis(scope.row)">告警历史</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>
    <div v-if="activeName == 'warn'">
      <el-form style="text-align: right; margin: 20px 0" :inline="true">
        <el-form-item label="流程名称">
          <el-input v-model="bizName"></el-input>
        </el-form-item>
        <el-form-item label="通知时间" style="width: 308px">
          <el-date-picker
            v-model="dateRangeForHis"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>

        <el-form-item style="margin-right: 5px">
          <span class="table-search-btn">
            <span class="btn btn1" @click="handleQuery"
              ><el-icon style="color: #fff"> <Search /> </el-icon
            ></span>
            <span class="btn btn2" @click="resetQueryForHis"
              ><el-icon style="color: #434343"> <Refresh /> </el-icon
            ></span>
          </span>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="historyList" @selection-change="handleSelectionChange">
        <el-table-column type="index" label="序号" width="65" align="center">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <el-table-column v-if="true" label="流程名称" align="center" prop="bizName" />
        <el-table-column
          show-overflow-tooltip
          v-if="true"
          label="实例名称"
          align="center"
          prop="bizInstanceName"
        />
        <el-table-column v-if="true" label="实例ID" align="center" prop="bizInstanceId" />
        <el-table-column label="告警来源">
          <template #default="scope">
            <el-tag :class="`source-type-tag source-type-${scope.row.bizType}`">{{
              showType(scope.row.bizType)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" align="center" prop="executionStatus">
          <template #default="scope">
            <div class="table-status">
              <span :class="`task-status-content task-status-${scope.row.executionStatus}`">
                {{ showTaskStatus(scope.row.executionStatus) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="告警渠道" align="center" prop="alertType">
          <template #default="scope">
            <span v-if="scope.row.alertType == '1'">邮件</span>
            <span v-if="scope.row.alertType == '3'">站内信</span>
            <span v-if="scope.row.alertType == '1,3' || scope.row.alertType == '3,1'"
              >邮箱、站内信</span
            >
          </template>
        </el-table-column>
        <el-table-column v-if="true" label="通知时间" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="goInstanceList(scope.row)">查看实例</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="totalForWarn > 0"
        v-model:page="form.pageNum"
        v-model:limit="form.pageSize"
        :total="totalForWarn"
        @pagination="getListForWarn"
      />
    </div>

    <el-dialog
      v-model="openWarn"
      title="告警设置"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      z-index="1000"
      @close="cancelWarn()"
    >
      <el-form ref="warnFormRef" :model="warnForm" :rules="ruleForWarn">
        <el-form-item label="告警策略" prop="subscribeType">
          <el-radio-group v-model="warnForm.subscribeType" class="radio_group">
            <el-radio label="2">成功通知</el-radio>
            <el-radio label="3">失败通知</el-radio>
            <el-radio label="1">成功失败都通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知策略" prop="checkList">
          <el-checkbox-group v-model="warnForm.checkList">
            <el-checkbox label="站内信" />
            <el-checkbox :disabled="onCheck" label="邮箱" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelWarn">取 消</el-button>
          <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { useRouteData } from '@/store/modules/dataCollect';
  import { listHISTORY } from '@/api/alert/history';
  import {
    delSubscribe,
    getSubscribe,
    listSubscribe,
    updateSubscribe,
  } from '@/api/alert/subscribe';
  import { getPage } from '@/api/dataAggregation';
  import { getInfo } from '@/api/login';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { ElMessage } from 'element-plus';
  import { onMounted } from 'vue';
  /// 导入组件 HeadTitle
  import HeadTitle from '@/components/HeadTitle';
  const store = useWorkFLowStore();
  const HeadTitleName = '告警中心';
  const showTaskStatus = (type) => {
    return taskStatus.find((item) => item.value === type)?.label;
  };

  const taskStatus = [
    {
      label: '成功',
      value: 'SUCCESS',
    },
    {
      label: '失败',
      value: 'FAILURE',
    },
  ];
  const showType = (type) => {
    return dataType.find((item) => item.value === type)?.label;
  };
  const dataType = [
    {
      label: '数据开发',
      value: 'DATA_DEVELOPMENT',
    },
    {
      label: '数据质量',
      value: 'DATA_QUALITY',
    },
    {
      label: '元数据采集',
      value: 'MEAT_DATA',
    },
  ];
  onMounted(() => {
    if (queryParams.value && form.value) {
      form.value.workspaceId = queryParams.value.workspaceId = workspaceId.value;
    } else {
      console.error('queryParams 或 form 为空');
    }

    getInfo().then((res) => {
      email.value = res.data.user.email;
      tid.value = res.data.user.tenantId;
    });
    // handleQuery();
    getSingleInfo();
  });
  // 所有的工作流列表
  const allFlowList = ref([]);
  // 搜索框的下拉列表
  const optionList = ref([]);
  const subscribeList = ref([]);
  const getSubscribeList = (e) => {
    if (e.tid && typeof e.tid === 'number') {
      tid.value = e.tid;
    } else {
      tid.value;
    }
    if (e.selectedWorkspaceId && typeof e.selectedWorkspaceId === 'number') {
      queryParams.value.workspaceId = e.selectedWorkspaceId;
    } else {
      queryParams.value.workspaceId;
    }
    activeName.value = 'all';
    form.value.workspaceId = queryParams.value.workspaceId;
    listSubscribe(queryParams.value)
      .then((response) => {
        subscribeList.value = response.rows;
        flowList.value = response.rows;
        totalForWarn.value = response.total;
        loading.value = false;
      })
      .catch(() => (loading.value = false));
  };

  const { proxy } = getCurrentInstance();

  // 时间参数
  const dateRange = ref([]);
  const dateRangeForHis = ref([]);

  const loading = ref(false);
  const loadingForHis = ref(false);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const totalForWarn = ref(0);
  const bizName = ref(null);
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      workspaceId: null,
    },
    form: {
      workspaceId: null,
      id: null,
      pageNum: 1,
      pageSize: 20,
      begin: null,
      end: null,
    },
    rules: {
      userId: [{ required: true, message: '用户 ID 不能为空', trigger: 'blur' }],
      bizId: [{ required: true, message: '订阅业务 ID 如流程 ID 不能为空', trigger: 'blur' }],
      subscribeType: [
        {
          required: true,
          message: '告警订阅场景 1 成功和失败 2 成功 3 失败，才发送不能为空',
          trigger: 'change',
        },
      ],
      alertType: [
        {
          required: true,
          message: '告警方式 1 邮件 2 短信...多个用英文逗号分割不能为空',
          trigger: 'change',
        },
      ],
      tenantId: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
      createBy: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
      updateBy: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
      updateTime: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
      delFlag: [{ required: true, message: '$comment 不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form } = toRefs(data);
  const activeName = ref('all');
  /** 查询告警信息订阅清单列表 */
  function getList() {
    loading.value = true;
    listSubscribe(queryParams.value)
      .then((response) => {
        subscribeList.value = response.rows;
        flowList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      })
      .catch(() => (loading.value = false));
  }
  const historyList = ref([]);
  /** 查询告警历史列表 */
  function getListForWarn() {
    loadingForHis.value = true;
    listHISTORY(form.value)
      .then((response) => {
        historyList.value = response.rows;
        totalForWarn.value = response.total;
        loadingForHis.value = false;
      })
      .catch(() => (loading.value = false));
  }
  const flowList = ref([]);
  const flowName = ref(null);
  // select 框选择变化时，请求列表数据
  const getSingleInfo = () => {
    form.value.bizId = flowName.value;
    loading.value = true;
    listSubscribe(form.value)
      .then((response) => {
        subscribeList.value = response.rows;
        totalForWarn.value = response.total;
        loading.value = false;
      })
      .catch(() => (loading.value = false));
  };

  // select 框下拉搜索事件
  const remoteMethod = (query) => {
    if (query) {
      getPage({
        currentPage: 1,
        pageSize: 999,
        searchVal: query,
        workspaceId: queryParams.value.workspaceId,
      }).then((res) => {
        allFlowList.value = res.data.records;
        optionList.value = allFlowList.value.filter((item) => {
          return item.flowName.toLowerCase().includes(query.toLowerCase());
        });
      });
    } else {
      optionList.value = [];
    }
  };

  const getTime = () => {
    const objParams = {};
    objParams.workspaceId = form.value.workspaceId;
    if (dateRange.value.length) {
      objParams.begin = dateRange.value[0];
      objParams.end = dateRange.value[1];
    } else {
      objParams.begin = null;
      objParams.end = null;
    }
    listSubscribe(objParams).then((response) => {
      subscribeList.value = response.rows;
      flowList.value = response.rows;
      totalForWarn.value = response.total;
    });
  };

  // 通过列表数据跳转到告警历史
  const goHis = (row) => {
    form.value.bizId = row.bizId;
    activeName.value = 'warn';
  };

  /** 搜索按钮操作 */
  function handleQuery() {
    if (dateRangeForHis.value) {
      form.value.begin = dateRangeForHis.value[0];
      form.value.end = dateRangeForHis.value[1];
    } else {
      form.value.begin = null;
      form.value.end = null;
    }
    form.value.workspaceId = queryParams.value.workspaceId;
    loadingForHis.value = true;
    form.value.bizName = bizName.value;
    listHISTORY(form.value)
      .then((response) => {
        historyList.value = response.rows;
        totalForWarn.value = response.total;
        loadingForHis.value = false;
      })
      .catch(() => (loadingForHis.value = false));
  }

  /** 重置按钮操作 */
  function resetQuery() {
    flowName.value = '';
    dateRange.value = [];
    getList();
  }
  /** 重置按钮操作 */
  function resetQueryForHis() {
    console.log(form);
    form.value.pageNum = 1;
    bizName.value = '';
    dateRangeForHis.value = [];
    handleQuery();
  }
  // 点击标签获取相应的列表数据
  const handleClick = (data) => {
    if (data == 'all') {
      form.value.bizId = null;
      getList();
    } else {
      getListForWarn();
    }
  };

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id;
    proxy.$modal
      .confirm('是否确定删除告警信息订阅？')
      .then(function () {
        loading.value = true;
        return delSubscribe(_ids);
      })
      .then(() => {
        loading.value = true;
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  // 告警订阅功能模块
  const warnForm = ref({
    subscribeType: '1',
    checkList: ['站内信'],
  });
  const onCheck = ref(false);
  const openWarn = ref(false);
  const email = ref(null);
  const objForWarn = ref({});
  const ruleForWarn = ref({
    subscribeType: [{ required: true, message: '请选择告警策略', trigger: 'change' }],
    checkList: [{ required: true, message: '请选择通知策略', trigger: 'change' }],
  });
  const handleSubscribe = async (row) => {
    await classify();
    if (!email.value) {
      onCheck.value = true;
      // proxy.$modal.msgWarning('请前往个人用户中心绑定邮箱')
      ElMessage({
        message: '未绑定邮箱，无法使用邮箱通知',
        type: 'warning',
        customClass: 'messageIndex',
      });
    } else {
      onCheck.value = false;
    }
    objForWarn.value.bizId = row.bizId;
    // objForWarn.value.processCode = row.flowIdOfDs;
    // objForWarn.value.projectCode = row.projectCodeOfDs;
    objForWarn.value.userId = row.userId;
    // objForWarn.value.tenantId = tenantId.value;
    objForWarn.value.workspaceId = row.workspaceId;
    objForWarn.value.createProcessUser = row.createProcessUser;
    objForWarn.value.id = row.id;
    if (row.alertType == '1') {
      warnForm.value.checkList = ['邮箱'];
    }
    if (row.alertType == '3') {
      warnForm.value.checkList = ['站内信'];
    }
    if (row.alertType == '1,3') {
      warnForm.value.checkList = ['站内信', '邮箱'];
    }
    warnForm.value.subscribeType = `${row.subscribeType}`;
    openWarn.value = true;
  };

  const classify = async () => {
    await setTimeout(async () => {
      await nextTick(async () => {
        await document
          .querySelectorAll(
            '.app-wrapper .el-radio-group > label > span, .el-overlay .el-radio-group > label > span',
          )
          .forEach((element) => {
            element.style.background = 'none';
          });
      });
    }, 100);
  };

  // 提交设置
  const submitFormWarn = async () => {
    const valid = await proxy.$refs.warnFormRef.validate((valid) => valid);
    if (!valid) return;
    if (warnForm.value.checkList.length === 2) {
      objForWarn.value.alertType = '1,3';
    } else if (warnForm.value.checkList.length === 1) {
      if (warnForm.value.checkList.indexOf('站内信') !== -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    }
    objForWarn.value.subscribeType = warnForm.value.subscribeType;
    updateSubscribe(objForWarn.value).then((res) => {
      if (res.code === 200) {
        openWarn.value = false;
        proxy.$modal.msgSuccess('更新订阅成功');
      }
      getList();
    });
  };
  // 取消设置
  const cancelWarn = () => {
    warnForm.value = {
      subscribeType: '1',
      checkList: ['站内信'],
    };
    proxy.resetForm('warnFormRef');
    openWarn.value = false;
  };
  const storeForRoute = useRouteData();
  const router = useRouter();
  // 跳转到任务实例界面
  const goInstanceList = async (row) => {
    // 根据告警来源跳转到不同界面 新需求
    if (row.bizType == 'DATA_DEVELOPMENT') {
      router.push('/DataAggregation/dispatchInstance');
    } else if (row.bizType == 'DATA_QUALITY') {
      router.push('/dataGovernance/dataQuality/TaskInstance');
    } else if (row.bizType == 'MEAT_DATA') {
      const data = {
        type: row.bizId,
      };
       storeForRoute.setRouteData(data);
      router.push({
        name:'MetadataCollection'
      });
    }
  };

  const workspaceId = computed(() => store.getWorkSpaceId());
  const tid = computed(() => store.getTenantId());
  watch(workspaceId, (val) => {
    if (val) {
      if (queryParams.value && form.value) {
        form.value.workspaceId = queryParams.value.workspaceId = workspaceId.value;
      } else {
        console.error('queryParams 或 form 为空');
      }

      getSubscribeList();
    }
  });
</script>

<style scoped lang="scss">
  @import '@/assets/styles/xg-ui/base.scss';
  .top-container {
    display: flex;
    align-items: center;
  }
  .btn {
    cursor: pointer;
    display: inline-block;
    width: 32px;
    height: 32px;
    padding: 0 10px;
    border-radius: 20px;
    &.btn1 {
      background: #1269ff;
      margin-right: 10px;
    }
    &.btn2 {
      background: #dce5f5;
    }
  }
  .part {
    margin: 10px;
    font-size: 16px;

    .inform {
      margin-left: 10px;
    }
    .radio_group {
      display: flex;
      flex-wrap: nowrap;
      align-content: normal;
      justify-content: center;
      align-items: flex-start;
      // flex-direction: column;
      flex-direction: row-reverse;
    }

    .selectBox {
      margin: 24px 0 24px 66px;
    }
  }

  .pagination-container {
    height: 33px;
  }

  .messageIndex {
    z-index: 99999999 !important;
  }

  .el-message {
    z-index: 99999999 !important;
  }
  .app-wrapper .el-radio-group,
  .el-overlay .el-radio-group {
    background-color: transparent;
  }
  .source-type-tag {
    //   font-weight: bold;
    width: 80px;
    text-align: center;
    border-color: transparent;
  }
  .source-type-DATA_DEVELOPMENT {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  .source-type-DATA_QUALITY {
    background-color: #f0f5ff;
    color: #2f54eb;
  }
  .source-type-MEAT_DATA {
    background-color: #f9f0ff;
    color: #9254de;
  }
  .table-status {
    position: relative;
    padding-left: 18px;
    height: 24px;
    // width: 48px;
    // height: 24px;
    & > span {
      height: 20px;
      line-height: 1;
      color: $--base-color-green;
      background-color: $--base-color-green-disable;
      display: inline-block;
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      &.task-status-SUCCESS {
        color: $--base-color-green;
      }
      &.task-status-FAILURE {
        color: $--base-btn-red-text;
        background-color: $--base-btn-red-bg;
        &::before {
          border: 3px solid $--base-btn-red-text;
        }
      }
      &::before {
        content: '';
        width: 12px;
        height: 12px;
        border: 3px solid $--base-color-green;
        border-radius: 6px;
        position: absolute;
        top: calc(50% - 6px);
        left: 45px;
      }
    }
  }
</style>
