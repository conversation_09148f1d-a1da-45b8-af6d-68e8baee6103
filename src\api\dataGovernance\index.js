import request from '@/utils/request';

//! ------------- 数据地图
// 查表的血缘
//  /operator/lineage/table
export function getTableLineage(params) {
  return request({
    url: '/operator/lineage/table',
    method: 'get',
    params,
  });
}

// 查字段的血缘
// /lineage/column
export function getFieldLineage(params) {
  return request({
    url: '/operator/lineage/column',
    method: 'post',
    data: params,
  });
}

// 获取数据地图列表
// /xugurtp-api-manage/api/box/sys/metadata/dataMap
export function getDataMapList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/dataMap',
    method: 'get',
    params: query,
  });
}

// 类目管理
// 获取类目列表树
// /xugurtp-data-governance/category/tree
export function getCategoryTree(query) {
  return request({
    url: '/xugurtp-data-governance/category/tree',
    method: 'get',
    params: query,
  });
}
// 新增类目
// /xugurtp-data-governance/category/add
export function addCategory(data) {
  return request({
    url: '/xugurtp-data-governance/category/add',
    method: 'post',
    data,
  });
}
// 修改类目
// /xugurtp-data-governance/category/update
export function updateCategory(data) {
  return request({
    url: '/xugurtp-data-governance/category/update',
    method: 'post',
    data,
  });
}
// 删除类目
// /xugurtp-data-governance/category/delete
export function deleteCategory(data) {
  return request({
    url: '/xugurtp-data-governance/category/delete',
    method: 'delete',
    data,
  });
}
// 类目添加或移除资产
export function changeIntoCategory(data) {
  return request({
    url: '/xugurtp-data-governance/categoryAssetRelation/intoCategory',
    method: 'post',
    data,
  });
}
// 根据目录查询资产
export function getDataAssets(data) {
  return request({
    url: '/xugurtp-data-governance/categoryAssetRelation/dataAssets',
    method: 'get',
    params: data,
  });
}
// 查询已录入资产表的数据源;

export function getDatasources(data) {
  return request({
    url: '/xugurtp-data-governance/categoryAssetRelation/datasources',
    method: 'get',
    params: data,
  });
}
// 查询技术资产页左侧树
export function getTechnologyTree(data) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/technologyTree',
    method: 'get',
    params: data,
  });
}

//  分页查询技术资产
// /xugurtp-data-governance/dataAsset/technology
export function getTechnologyPage(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/technology',
    method: 'get',
    params,
  });
}
// 分页查询业务资产
// /xugurtp-data-governance/dataAsset/business
export function getBusinessPage(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/business',
    method: 'get',
    params,
  });
}
// 查询已录入资产表的数据源;

export function getAssetsInDatasource(data) {
  return request({
    url: '/xugurtp-data-governance/categoryAssetRelation/assetsInDatasource',
    method: 'get',
    params: data,
  });
}
// 类目添加或移除资产
export function setIntoCategory(data) {
  return request({
    url: '/xugurtp-data-governance/categoryAssetRelation/intoCategory',
    method: 'post',
    data,
  });
}

//! ------------- 数据安全
// 数据识别规则
// 获取目录树
export function getSensitiveGroup(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveGroup/tree',
    method: 'get',
    params,
  });
}

// 新增分组
export function addSensitiveGroup(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveGroup/add',
    method: 'post',
    data,
  });
}

// 修改分组
export function updateSensitiveGroup(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveGroup/update',
    method: 'post',
    data,
  });
}

// 删除分组
export function deleteSensitiveGroup(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveGroup/delete',
    method: 'delete',
    params,
  });
}

// 识别规则分页查询
export function getListForRule(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveDiscoveries/page',
    method: 'get',
    params,
  });
}

// 新增识别规则
export function addDiscoveryRule(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveDiscoveries/add',
    method: 'post',
    data,
  });
}

// 修改识别规则
export function updateDiscoveryRule(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveDiscoveries/update',
    method: 'post',
    data,
  });
}

// 删除识别规则
export function deleteDiscoveryRule(data) {
  return request({
    url: `/xugurtp-data-governance/sensitiveDiscoveries/delete?id=${data.id}`,
    method: 'post',
  });
}

// 更改识别规则状态
export function updateStatus(data) {
  return request({
    url: `/xugurtp-data-governance/sensitiveDiscoveries/updateStatus?id=${data.id}&status=${data.status}`,
    method: 'put',
  });
}

// 敏感数据发现
// 获取任务列表
export function getSensitiveTask(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/page',
    method: 'get',
    params,
  });
}

// 新增任务
export function addSensitiveTask(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/add',
    method: 'post',
    data,
  });
}

// 修改任务
export function updateSensitiveTask(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/update',
    method: 'post',
    data,
  });
}

// 删除任务
export function deleteSensitiveTask(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/delete',
    method: 'delete',
    params,
  });
}

// 获取任务详情
export function getSensitiveTaskInfo(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/info',
    method: 'get',
    params,
  });
}

// 运行任务
export function runSensitiveTask(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTask/run',
    method: 'get',
    params,
  });
}

// 任务日志
export function logSensitiveTask(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveTaskLog/page',
    method: 'get',
    params,
  });
}

// 获取所有的敏感字段类型列表
export function getAllSensitiveList(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveDiscoveries/list',
    method: 'get',
    params,
  });
}

// 手动修复数据
// 获取列表
export function getSensitiveDataList(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveData/page',
    method: 'get',
    params,
  });
}

// 修改敏感字段类型
export function updateSensitiveData(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveData/updateSensitive',
    method: 'post',
    data,
  });
}

// 删除列表
export function deleteSensitiveData(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveData/delete',
    method: 'delete',
    params,
  });
}

// 敏感数据脱敏
// 获取脱敏规则列表
export function getSensitiveMaskingList(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/page',
    method: 'get',
    params,
  });
}

// 新增脱敏规则
export function addSensitiveMasking(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/add',
    method: 'post',
    data,
  });
}

// 修改脱敏规则
export function updateSensitiveMasking(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/update',
    method: 'post',
    data,
  });
}

// 修改规则状态
export function updateStatusForDeSensitiveRule(data) {
  return request({
    url: `/xugurtp-data-governance/sensitiveMasking/updateStatus?id=${data.id}&status=${data.status}`,
    method: 'put',
  });
}

// 查看规则详情（修改回显使用）
export function getSensitiveRuleInfo(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/info',
    method: 'get',
    params,
  });
}

// 删除
export function deleteSensitiveMasking(params) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/delete',
    method: 'delete',
    params,
  });
}

// 脱敏验证
export function testDeSensitiveRule(data) {
  return request({
    url: '/xugurtp-data-governance/sensitiveMasking/verify',
    method: 'post',
    data,
  });
}

// ! mock
//  /operator/lineage/tt
export function getMockData(params) {
  return request({
    url: '/operator/lineage/tt',
    method: 'get',
    params,
  });
}

// ! 质量算子
// /xugurtp-data-governance/data-quality/ruleList
// 质量算子列表
export function getQualityRuleList(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/ruleList',
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/data-quality/rule/page
// 质量算子分页查询
export function getQualityRulePage(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/rule/page',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/data-quality/result/page
// 质量算子执行结果列表
export function getQualityResultList(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/result/page',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/data-quality/getRuleFormCreateJson
// 质量算子创建表单
export function getQualityRuleFormCreateJson(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/getRuleFormCreateJson',
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/data-quality/getDatasourceOptionsById
// 质量算子数据源下拉列表
export function getQualityDataSourceOptionsById(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/getDatasourceOptionsById',
    method: 'get',
    params,
  });
}

// 数据质量
// 获取数据质量列表
export function getQualityList(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/rule/page',
    method: 'get',
    params,
  });
}
// 查询数据库列表
export function getDatabaseList(query) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params: query,
  });
}
// 作业实例
// /xugurtp-data-governance/data-quality/queryDqProcessInstance
export function getDqProcessInstance(query) {
  return request({
    url: `/xugurtp-data-governance/data-quality/queryDqProcessInstance`, // ?workspaceId=${query.workspaceId || ''}&pageNo=${query.pageNo || ''}&pageSize=${query.pageSize || ''}&stateType=${query.stateType || ''}&searchVal=${query.searchVal || ''}
    method: 'get',
    params: query,
  });
}
// operator/algFlowInstance/delete/{instanceId}
// 删除作业实例
export function deleteDqProcessInstance(query) {
  return request({
    url: `/operator/algFlowInstance/delete/${query.workSpaceId}/${query.id}`,
    method: 'delete',
  });
}
// 脏数据查询
// /xugurtp-data-governance/data-quality/queryDqErrorData
export function getDqErrorData(query) {
  return request({
    url: `/xugurtp-data-governance/data-quality/queryDqErrorData`,
    method: 'get',
    params: query,
  });
}

// 查询表列表
export function getTableList(params) {
  params.schema = params.schemaName;
  delete params.schemaName;
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}
// 查询 模式列表
export function getSchemaList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getSchemaList`,
    method: 'get',
    params,
  });
}

// 查询字段列表
export function getFieldList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getFieldList`,
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/dataAsset/techDetail
// 技术资产详情
export function getTechDetail(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/techDetail',
    method: 'get',
    params,
  });
}

// 画布运行前，查询告警组列表
export function getAlertGroups(params) {
  return request({
    url: '/xugurtp-data-governance/data-quality/alert-groups/list',
    method: 'get',
    params,
  });
}

// /operator/lineage/workFlows
export function getWorkFlows(params) {
  return request({
    url: '/operator/lineage/workFlows',
    method: 'get',
    params,
  });
}

// 元数据采集
// 采集列表
// xugurtp-data-governance/metadataacquisition/list
export function getMetadataAcquisitionList(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/list',
    method: 'get',
    params,
  });
}
// 已采集
// xugurtp-data-governance/metadataacquisition/isGather
export function isGather(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/isgather',
    method: 'get',
    params,
  });
}
// 未采集
/// xugurtp-data-governance/metadataacquisition/nogather
export function noGather(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/nogather',
    method: 'get',
    params,
  });
}
// 采集记录
/// xugurtp-data-governance/metadataacquisition/gatherrecord
export function getGatherRecord(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/gatherrecord',
    method: 'get',
    params,
  });
}
// 采集表
// /xugurtp-data-governance/metadataacquisition/findtable
export function findTable(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/findtable',
    method: 'get',
    params,
  });
}
// 获取采集详情
// /xugurtp-data-governance/metadataacquisition/edit
export function getMetadataAcquisitionEdit(params) {
  return request({
    url: `/xugurtp-data-governance/metadataacquisition/edit/${params.id}`,
    method: 'get',
  });
}
// 采集运行
/// xugurtp-data-governance/metadataacquisition/run
export function runCollection(data) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/run',
    method: 'POST',
    data,
  });
}
// 停止采集
// /
export function stopCollection(id) {
  return request({
    url: `/xugurtp-data-governance/metadataacquisition/cancel/${id}`,
    method: 'put',
    //   data,
  });
}
// 获取日志
/// xugurtp-data-governance/metadataacquisition/log
export function getMetadataLog(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/log',
    method: 'get',
    params,
  });
}
// 删除采集日志
// /xugurtp-data-governance/metadataacquisition/delete/4
export function deleteMetadataLog(params) {
  return request({
    url: `/xugurtp-data-governance/metadataacquisition/delete/${params.id}`,
    method: 'delete',
    params,
  });
}
// 批量删除
// /xugurtp-data-governance/metadataacquisition/remove
export function removeMetadataLogBatch(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/remove',
    method: 'delete',
    params,
  });
}
// 删除采集日志
// /xugurtp-data-governance/metadataacquisition/delete
export function deleteMetadataLogBatch(params) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/delete',
    method: 'delete',
    params,
  });
}
// 配置采集计划
// /xugurtp-data-governance/metadataacquisition/config
export function configCollection(data) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/config',
    method: 'POST',
    data,
  });
}
// 重跑
// /xugurtp-data-governance/metadataacquisition/restart
export function restartCollection(data) {
  return request({
    url: '/xugurtp-data-governance/metadataacquisition/restart',
    method: 'post',
    data,
  });
}

// xugurtp-data-governance/dataAsset/selectDbType?workspaceId=2
// 获取数据库类型
export function getDbType(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/selectDbType',
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/dataAsset/selectTable?workspaceId= 2&pageNum=1&pageSize=10&tableName=&datasourceType=ORACLE
export function getSelectTable(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/selectTable',
    method: 'get',
    params,
  });
}

// 数据地图下搜索页的查询api
export function getSelectApi(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/selectApi',
    method: 'get',
    params,
  });
}

// operator/lineage/qualityTasks
// 质量算子分页查询
export function getQualityTasks(params) {
  return request({
    url: '/operator/lineage/qualityTasks',
    method: 'get',
    params,
  });
}

// dataAsset/secuityLevelCodedataAssetId
// 获取安全等级
export function getSecurityLevel(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/secuityLevelCodedataAssetId',
    method: 'get',
    params,
  });
}

// dataAsset/securityLevelCode/
// 设置安全等级
export function securityLevelById(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/securityLevelCode',
    method: 'get',
    params,
  });
}
// dataAsset/modify
// 修改备注
export function dataAssetModify(data) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/modify',
    method: 'post',
    data,
  });
}
// /xugurtp-data-governance/columnInfo/list
export function getColumnInfoList(params) {
  return request({
    url: '/xugurtp-data-governance/columnInfo/list',
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/dataAsset/previewData
export function getPreviewData(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/previewData',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/homePage/data
export function getHomePageData(params) {
  return request({
    url: '/xugurtp-data-governance/homePage/data',
    method: 'get',
    params,
  });
}

// 治理前缀/dataAsset/column/bizRemark
// Put
export function putColumnBizRemark(data) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/column/bizRemark',
    method: 'put',
    data,
  });
}

// 查询血缘表节点的业务信息
export function getNodeBusinessInfo(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/bizInfos',
    method: 'get',
    params,
  });
}

// 查询血缘字段节点的业务信息
export function getNodeBusinessInfoForCol(params) {
  return request({
    url: '/xugurtp-data-governance/dataAsset/columnBizInfos',
    method: 'get',
    params,
  });
}

// 查询数据源列表
export function getDataSourcesList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list`,
    method: 'get',
    params,
  });
}

// API、资产对应标签编辑接口
export function editAsset(data) {
  return request({
    url: '/xugurtp-data-governance/label/editasset',
    method: 'post',
    data,
  });
}

// /xugurtp-data-governance/dataQualityTask/page
export function getDataQualityTaskList(params) {
  return request({
    url: `/xugurtp-data-governance/dataQualityTask/page`,
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/dataQualityTask/updateStatus
export function updateDataQualityTaskStatus(params) {
  return request({
    url: `/xugurtp-data-governance/dataQualityTask/updateStatus`,
    method: 'put',
    data: params,
  });
}

// /xugurtp-data-governance/qualityTaskRule/add
export function addFlow(params) {
  return request({
    url: `/xugurtp-data-governance/qualityTaskRule/add`,
    method: 'post',
    data: params,
  });
}
// /xugurtp-data-governance/dataQualityTask/add
export function addDataQualityTask(params) {
  return request({
    url: `/xugurtp-data-governance/dataQualityTask/add`,
    method: 'post',
    data: params,
  });
}

/// /xugurtp-data-governance/dataQualityTask/delete
export function delDataQualityTask(params) {
  return request({
    url: `/xugurtp-data-governance/dataQualityTask/delete`,
    method: 'delete',
    params,
  });
}

// /xugurtp-data-governance/dataQualityTask/update
export function updateDataQualityTask(data) {
  return request({
    url: `/xugurtp-data-governance/dataQualityTask/update`,
    method: 'post',
    data,
  });
}

// /xugurtp-data-governance/qualityTaskRule/page
export function getFlowList(params) {
  return request({
    url: `/xugurtp-data-governance/qualityTaskRule/page`,
    method: 'get',
    params,
  });
}

/// xugurtp-data-governance/qualityTaskRule/update
export function updateFlow(params) {
  return request({
    url: `/xugurtp-data-governance/qualityTaskRule/update`,
    method: 'post',
    data: params,
  });
}

// /xugurtp-data-governance/qualityTaskRule/delete
export function delFlow(params) {
  return request({
    url: `/xugurtp-data-governance/qualityTaskRule/delete`,
    method: 'delete',
    params,
  });
}

// /xugurtp-data-governance/qualityTaskRule/addBatch
export function addBatch(params) {
  return request({
    url: `/xugurtp-data-governance/qualityTaskRule/addBatch`,
    method: 'post',
    data: params,
  });
}
