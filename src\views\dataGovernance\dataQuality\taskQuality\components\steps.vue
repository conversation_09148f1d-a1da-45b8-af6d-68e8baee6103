<template>
  <div class="step-container">
    <div class="step-box" v-for="tab in tabList" :key="tab.step">
      <div class="top-box">
        <span class="num-box" :class="{ 'is-activeStep': tab.active }">
          {{ tab.step }}
        </span>
        <span class="label-box" :class="{ 'is-activeStep': tab.active }">{{ tab.label }}</span>
      </div>
      <div class="bot-box">
        <!-- circle -->
        <div class="circle-box" :class="{ 'is-active': tab.active, 'is-finish': tab.finish }"></div>
        <!-- 虚线 -->
        <div
          v-if="tab.step != '03'"
          class="line-box"
          :class="{ 'is-active': tab.active, 'is-finish': tab.finish }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script setup>
  const prop = defineProps({
    activeStep: {
      type: Number,
      default: 1,
    },
  });
  const tabList = computed(() => {
    let arr;
    if (prop.activeStep == 1) {
      arr = [
        { label: '基础信息', step: '01', active: true, finish: false },
        { label: '规则配置', step: '02', active: false, finish: false },
        { label: '调度配置', step: '03', active: false, finish: false },
      ];
    } else if(prop.activeStep == 2) {
      arr = [
        { label: '基础信息', step: '01', active: true, finish: true },
        { label: '规则配置', step: '02', active: true, finish: false },
        { label: '调度配置', step: '03', active: false, finish: false },
      ];
    } else {
      arr = [
        { label: '基础信息', step: '01', active: true, finish: true },
        { label: '规则配置', step: '02', active: true, finish: true },
        { label: '调度配置', step: '03', active: true, finish: false },
      ];
    }
    return arr
  });
</script>
<style lang="scss">
  .step-container {
    display: flex;
    .step-box {
      width: 310px;
      height: 82px;
      margin-right: 20px;
      color: #8c8c8c;
      // 当前及之前的字体颜色
      .is-activeStep {
        color: #000;
      }

      .num-box {
        width: 42px;
        height: 44px;
        font-weight: 800;
        font-size: 28px;
        line-height: 44px;
        margin-right: 6px;
      }

      .label-box {
        font-weight: 500;
        font-size: 20px;
      }

      .bot-box {
        margin-top: 10px;
        display: flex;
        align-items: center;
        .circle-box {
          width: 28px;
          height: 28px;
          border-radius: 14px;
          border: 2px solid #eaf1ff;
          position: relative;
          &.is-active {
            border: 2px solid #1269ff;
          }
          &.is-finish {
            border: none;
            background: #1269ff;
          }
        }
        .circle-box::before {
          content: '';
          position: absolute;
          top: 30%;
          left: 20%;
          transform: translate(-50%, -50%);
          width: 15px;
          height: 6px;
          border-left: 1px solid #fff;
          border-bottom: 1px solid #fff;
          transform: rotate(-45deg);
        }

        .line-box {
          margin-left: 20px;
          width: 270px;
          height: 3px;
          border: 2px solid #eaf1ff;

          &.is-finish {
            // width: 371px;
            height: 3px;
            transform: rotate(-0.000005deg);
            border-radius: 2px;
            border: 2px dashed #1269ff;
            background: #eaf1ff;
          }
        }
      }
    }
  }
</style>
