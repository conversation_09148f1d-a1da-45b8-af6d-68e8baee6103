<template>
  <div class="app-container">
    <HeadTitle title="消息列表" :pull-down="true" @update-list="gteList" />

    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="全部" name="all">
        <el-table
          v-loading="loading"
          :data="subscribeList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" label="id" align="center" prop="id" />
          <el-table-column v-if="true" label="流程id" align="center" prop="bizId" />
          <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
          <el-table-column v-if="true" label="告警订阅类型" align="center" prop="subscribeType" />
          <el-table-column
            v-if="true"
            label="流程创建人"
            align="center"
            prop="flowCreateUserName"
          />
          <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="告警订阅" name="warn">
        <el-table
          v-loading="loading"
          :data="subscribeList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" label="id" align="center" prop="id" />
          <el-table-column v-if="true" label="流程id" align="center" prop="bizId" />
          <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
          <el-table-column v-if="true" label="告警订阅类型" align="center" prop="subscribeType" />
          <el-table-column
            v-if="true"
            label="流程创建人"
            align="center"
            prop="flowCreateUserName"
          />
          <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="系统通知" name="sys">
        <el-table
          v-loading="loading"
          :data="subscribeList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" label="id" align="center" prop="id" />
          <el-table-column v-if="true" label="流程id" align="center" prop="bizId" />
          <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
          <el-table-column v-if="true" label="告警订阅类型" align="center" prop="subscribeType" />
          <el-table-column
            v-if="true"
            label="流程创建人"
            align="center"
            prop="flowCreateUserName"
          />
          <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改告警信息订阅清单对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="subscribeRef" :model="form" :rules="rules" label-width="80px"> </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="subscribe">
  import {
    listSubscribe,
    getSubscribe,
    delSubscribe,
    addSubscribe,
    updateSubscribe,
  } from '@/api/alert/subscribe';
  /// 导入组件HeadTitle
  import HeadTitle from '@/components/HeadTitle';
  const { proxy } = getCurrentInstance();

  const subscribeList = ref([]);
  const open = ref(false);
  const buttonLoading = ref(false);
  const loading = ref(false);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    rules: {
      userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
      bizId: [{ required: true, message: '订阅业务ID如流程ID不能为空', trigger: 'blur' }],
      subscribeType: [
        {
          required: true,
          message: '告警订阅场景1成功和失败2成功3失败，才发送不能为空',
          trigger: 'change',
        },
      ],
      alertType: [
        {
          required: true,
          message: '告警方式1邮件2短信...多个用英文逗号分割不能为空',
          trigger: 'change',
        },
      ],
      tenantId: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      createBy: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      updateBy: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      updateTime: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      delFlag: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const activeName = ref('all');
  /** 查询告警信息订阅清单列表 */
  function getList() {
    loading.value = true;
    listSubscribe(queryParams.value).then((response) => {
      subscribeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      userId: null,
      bizId: null,
      subscribeType: null,
      alertType: null,
      tenantId: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      delFlag: null,
    };
    proxy.resetForm('subscribeRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加告警信息订阅清单';
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    loading.value = true;
    reset();
    const id = row.id || ids.value;
    getSubscribe(id).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '修改告警信息订阅清单';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.subscribeRef.validate((valid) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id != null) {
          updateSubscribe(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          addSubscribe(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal
      .confirm('是否确定删除告警信息订阅清单编号为"' + _IDs + '"的数据项？')
      .then(function () {
        loading.value = true;
        return delSubscribe(_ids);
      })
      .then(() => {
        loading.value = true;
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }
</script>
