<template>
  <section class="Navtoos-box">
    <div>
      <el-tooltip class="box-item" effect="dark" content="运行" placement="bottom-end">
        <el-button
          v-show="isShowButton"
          type="text"
          effect="dark"
          icon="VideoPlay"
          :disabled="CanvasActions"
          @click="testRunDraw"
        />
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="保存" placement="bottom-end">
        <el-button
          type="text"
          effect="dark"
          icon="FolderChecked"
          :disabled="!CanvasActions"
          @click="saveDraw"
        />
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="提交" placement="bottom-end">
        <el-button
          v-show="isShowButton"
          type="text"
          effect="dark"
          icon="Finished"
          :disabled="!CanvasActions"
          @click="putDraw"
        />
      </el-tooltip>
      <!-- <el-tooltip class="box-item" effect="dark" content="提交并允许他人编辑" placement="bottom-end"> -->
      <!-- <el-button type="text" effect="dark" icon="Key" @click="putDrawAndEdit" :disabled="!CanvasActions" /> -->
      <!-- </el-tooltip> -->
      <el-tooltip class="box-item" effect="dark" content="收藏" placement="bottom-end">
        <!-- 收藏 -->
        <el-icon v-show="isShowButton">
          <Star @click="starDraw" />
        </el-icon>
      </el-tooltip>
    </div>

    <div>
      <el-tooltip
        v-if="!CanvasActions"
        class="box-item"
        effect="dark"
        content="锁定编辑"
        placement="bottom-end"
      >
        <el-icon v-show="isShowButton">
          <Lock @click="toggleCanvasActions" />
        </el-icon>
      </el-tooltip>
      <el-tooltip
        v-if="CanvasActions"
        class="box-item"
        effect="dark"
        content="退出编辑"
        placement="bottom-end"
      >
        <el-icon v-show="isShowButton">
          <Unlock style="color: #44a4fa" @click="toggleCanvasActions" />
        </el-icon>
      </el-tooltip>
      <b>
        <!-- {{ CanvasActions ? '退出编辑' : '锁定编辑' }} -->
      </b>
    </div>

    <div>
      <el-tooltip class="box-item" effect="dark" content="放大" placement="bottom-end">
        <el-icon>
          <CirclePlus @click="addDraw" />
        </el-icon>
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="缩小" placement="bottom-end">
        <el-icon>
          <Minus @click="subDraw" />
        </el-icon>
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="全屏" placement="bottom-end">
        <el-icon>
          <FullScreen @click="FullCilck" />
        </el-icon>
      </el-tooltip>
    </div>

    <div>
      <b>节点</b>
      <el-tooltip class="box-item" effect="dark" content="放大" placement="bottom-end">
        <el-tag effect="plain" size="mini" @click="setDrawSize(0.5)"> 小</el-tag>
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="缩小" placement="bottom-end">
        <el-tag effect="plain" size="mini" @click="setDrawSize(0.7)">中</el-tag>
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="全屏" placement="bottom-end">
        <el-tag effect="plain" size="mini" @click="setDrawSize(1)">大</el-tag>
      </el-tooltip>
    </div>
  </section>
</template>

<script setup>
  import { nextTick } from 'vue';

  // import { DagreLayout, GridLayout } from '@antv/layout'
  const props = defineProps({
    graph: {
      type: Object,
    },
    model: {
      type: Object,
      default: () => {},
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
    isShowButton: {
      type: Boolean,
      default: true,
    },
  });

  const { graph, model, CanvasActions, isShowButton } = toRefs(props);
  console.log(isShowButton.value);
  const emit = defineEmits();

  // const CanvasActions = ref(false)
  const isFull = ref(false);
  // 画布缩放
  function addDraw() {
    graph.value.zoom(0.2); // 将画布缩放级别增加 0.2（默认为1）
  }
  function subDraw() {
    graph.value.zoom(-0.2); // 将画布缩放级别增加 0.2（默认为1）
  }
  // 画布位置还原
  function adaptationDraw() {
    graph.value.zoomToFit();
  }
  // 试运行
  function testRunDraw() {
    emit('runDraw');
    // alert('试运行')
  }
  // 保存
  function saveDraw() {
    emit('saveWorkFlowUtil');
    // alert('保存')
  }
  // 提交
  function putDraw() {
    emit('putDraw');
    // alert('提交')
  }

  //  提交并允许他人编辑
  function putDrawAndEdit() {
    emit('putDraw');
    // alert('提交并允许他人编辑')
  }

  // 收藏
  function starDraw() {
    alert('收藏');
  }
  // 删除选中
  function removeDraw() {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.removeCells(cells);
    }
  }

  //  设置为最小
  function setDrawSize(v) {
    graph.value.zoomTo(v);
    // 画布视口回到中心
    graph.value.centerContent();
  }

  // 锁定画布
  function toggleCanvasActions() {
    CanvasActions.value = !CanvasActions.value;
    emit('toggleCanvasActions', CanvasActions.value);
  }
  // 切换
  function upDataCanvasActions(data) {
    console.log(data);
    CanvasActions.value = data;
  }
  function FullCilck() {
    isFull.value = !isFull.value;
    emit('FullCilck', isFull.value);
  }
  // function toGridLayout() {

  //     const gridLayout = new GridLayout({
  //         type: 'grid',
  //         width: 600,
  //         height: 400,
  //         center: [300, 200],
  //         rows: 4,
  //         cols: 4,
  //     })
  //     console.log('model.value', model.value)
  //     const newModel = gridLayout.layout(model.value)
  //     console.log('newModel', newModel)
  //     graph.value.fromJSON(newModel)
  //     console.log('graph.value.getNodes()', graph.value.getNodes())

  //     // graph.value.centerContent();
  // }

  watch(isShowButton, (val) => {
    nextTick(() => {
      isShowButton.value = val;
    });
  });
</script>

<style lang="scss" scoped>
  .Navtoos-box {
    display: grid;
    grid-template-columns: auto 1fr 0fr 0.75fr;
    justify-content: space-between;
    padding: 0 10px;
    height: 40px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;

    div {
      margin: 0 10px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #000000;
      font-weight: 600;
      font-size: 19px;

      // 子元素左右间距 10px
      > * {
        margin: 0 5px;
      }

      b {
        font-weight: normal;
        font-size: 13px;
        // left: 5px;
      }

      :deep .el-tag {
        // margin: 0 5px;
        // border: 1px solid #ebeef5;
        // color: #000000;
        // font-weight: 600;
        font-size: 13px;
        cursor: pointer;
        margin-left: -2px;
      }

      :deep .el-button,
      el-button--text {
        font-size: 18px;
      }
    }
  }
</style>
