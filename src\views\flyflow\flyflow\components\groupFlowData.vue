<script setup lang="ts">
  import { isNotBlank } from '../utils/objutil';
  import {
    CircleCloseFilled,
    Delete,
    DocumentCopy,
    Edit,
    Hide,
    Histogram,
    Menu,
  } from '@element-plus/icons-vue';
  import { disableFlow, enableFlow } from '../api/flow';
  import { ref } from 'vue';
  import { clearProcess, deleteProcessMain } from '../api/group';
  import { useRouter } from 'vue-router';
  import ProcessVersionPopup from '../components/ProcessVersionPopup.vue';
  import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';

  const props = defineProps({
    flow: {
      type: Object,
      default: () => {},
    },
  });

  const emit = defineEmits(['handleQuery']);

  const router = useRouter();

  function toEditFlow(flow) {
    // TODO
    const to = '/flow/create?id=' + flow.uniqueId + '&flowId=' + flow.flowId;

    router.push(to);
  }

  function toCopyFlow(flow) {
    // TODO
    const to = '/flow/create?cp=1&flowId=' + flow.flowId;

    router.push(to);
  }

  function showDisableConfirm(flow) {
    ElMessageBox.confirm('确定要停用该流程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      disableFlow(flow.flowId).then((res) => {
        emit('handleQuery');
      });
    });
  }
  function showEnableConfirm(flow) {
    ElMessageBox.confirm('确定要启用该流程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      enableFlow(flow.flowId).then((res) => {
        emit('handleQuery');
      });
    });
  }

  // 显示版本管理
  const processVersionRef = ref();
  function showVersionManage(flow) {
    processVersionRef.value.show(flow.uniqueId);
  }

  // 删除流程
  function showDeleteConfirm(flow) {
    ElMessageBox.confirm('确定要删除该流程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      deleteProcessMain(flow.uniqueId).then((res) => {
        emit('handleQuery');
      });
    });
  }

  // 清理流程
  function showClearProcessConfirm(flow) {
    ElMessageBox.confirm(
      '本次操作会删除流程所有数据，包括进行中的和已完成的，确定继续吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    ).then(() => {
      clearProcess(flow.uniqueId).then((res) => {
        emit('handleQuery');
      });
    });
  }

  function handleQuery() {
    emit('handleQuery');
  }
</script>

<template>
  <div>
    <process-version-popup
      ref="processVersionRef"
      @close-dialog-event="handleQuery"
    ></process-version-popup>

    <div class="item">
      <div style="position: relative">
        <!-- <el-avatar shape="square" :size="50" :src="flow.logo" /> -->
      </div>
      <div v-if="isNotBlank(flow.remark)" style="margin-left: 20px; width: 300px">
        <div style="width: 300px">
          <el-tooltip class="box-item" effect="dark" :content="flow.name" placement="top-start">
            <el-text truncated>{{ flow.name }}</el-text>
          </el-tooltip>
        </div>
        <div>
          <el-tooltip class="box-item" effect="dark" :content="flow.remark" placement="top-start">
            <el-text truncated type="info">{{ flow.remark }}</el-text>
          </el-tooltip>
        </div>
      </div>
      <div v-else style="margin-left: 20px; width: 300px">
        <div style="height: 60px; line-height: 60px; width: 300px">
          <el-tooltip class="box-item" effect="dark" :content="flow.name" placement="top-start"
            ><el-text truncated>{{ flow.name }}</el-text>
          </el-tooltip>
        </div>
      </div>

      <div
        style="
          margin-left: 50px;
          height: 60px;
          line-height: 60px;
          width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        "
      >
        <template v-if="flow.rangeShow && flow.rangeShow.length > 0">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="flow.rangeShow"
            placement="top-start"
          >
            {{ flow.rangeShow }}
          </el-tooltip>
        </template>
        <template v-else>所有人</template>
      </div>
      <div style="height: 60px; line-height: 60px">
        <el-tag v-if="!flow.stop" type="success">上架</el-tag>
        <el-tag v-else type="danger">下架</el-tag>
      </div>
      <div class="last">
        <el-tooltip class="box-item" effect="dark" content="编辑" placement="top">
          <el-button text :icon="Edit" circle @click="toEditFlow(flow)" />
        </el-tooltip>
        <!-- <el-tooltip class="box-item" effect="dark" content="复制" placement="top"> -->
        <!-- <el-button text :icon="DocumentCopy" circle @click="toCopyFlow(flow)" /> -->
        <!-- </el-tooltip> -->

        <!-- <el-tooltip v-if="!flow.stop" class="box-item" effect="dark" content="停用" placement="top"> -->
          <!-- <el-button text :icon="Hide" circle @click="showDisableConfirm(flow)" /> -->
        <!-- </el-tooltip> -->
        <!-- <el-tooltip v-else class="box-item" effect="dark" content="启用" placement="top"> -->
          <!-- <el-button text :icon="CircleCloseFilled" circle @click="showEnableConfirm(flow)" /> -->
        <!-- </el-tooltip> -->

        <!-- <el-tooltip class="box-item" effect="dark" content="版本管理" placement="top"> -->
        <!-- <el-button text :icon="Menu" circle @click="showVersionManage(flow)" /> -->
        <!-- </el-tooltip> -->

        <!-- <el-tooltip class="box-item" effect="dark" content="清理流程" placement="top"> -->
        <!-- <el-button text :icon="CircleCloseFilled" circle @click="showClearProcessConfirm(flow)" /> -->
        <!-- </el-tooltip> -->
        <!--  -->
        <!-- <el-tooltip class="box-item" effect="dark" content="删除" placement="top"> -->
        <!-- <el-button text :icon="Delete" circle @click="showDeleteConfirm(flow)" /> -->
        <!-- </el-tooltip> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .item {
    display: flex;
    flex-direction: row;
    height: 60px;
    padding-top: 5px;
    margin-bottom: 10px;

    div:nth-child(2) div:first-child {
      font-size: 15px;
      height: 30px;
      font-weight: bolder;
      line-height: 30px;
    }

    div:nth-child(2) div:last-child {
      font-size: 12px;
      height: 20px;
      line-height: 20px;
    }

    .last {
      width: calc(100% - 70px - 200px - 200px - 50px);
      height: 60px;
      line-height: 60px;
      text-align: right;
    }
  }
</style>
