import { getUserProfile } from '@/api/system/user';
import { useWorkFLowStore } from '@/store/modules/workFlow';
import { format } from 'sql-formatter';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import { ref, reactive, nextTick } from 'vue';
import {
  getDataSchemas,
  getDataDatabases,
  getDataScripts,
  getDataSources,
  getDataTables,
  //   testForward,
} from '@/api/APIService';
import {
  getTree,
  addTree,
  addScript,
  sqlRunning,
  deleteTree,
  deleteScript,
  updateScript,
  getInfo,
  updateTree,
} from '@/api/SQLScript';

export default function useSQLScriptService(props) {
  const store = useWorkFLowStore();
  const workSpaceIdData = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  // 数据信息
  const treeInfo = reactive({
    dataType: '1',
    SearchText: '',
    scriptTreeData: [
      { groupId: -1, groupName: '我的文件', visible: false, type: -1, children: [] },

      { groupId: -2, groupName: '他人文件', visible: false, type: -2, children: [] },
    ],
    propsGroupTree: { value: 'groupId', label: 'groupName', children: 'children' },
    allTreeData: {},
    showDatabase: true,
    propsTree: {
      value: 'value',
      label: 'label',
      children: 'children',
    },
  });
  const addFolderDialogInfo = reactive({
    visible: false,
    title: '',
    type: 1,
    searchForm: {},
    rules: {
      name: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
        // 正则 大小写字母、中文、数字下划线、小数点
        {
          pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_.]+$/,
          message: '请输入大小写字母、中文、数字下划线或小数点',
        },
      ],
      parentId: [],
    },
    groupOptions: [],
  });

  const thisChoseLabel = ref('');
  const selectTab = ref('临时脚本'); // 当前页签
  const editableTabs = ref([]);
  const tableConfig = reactive({
    tableData: [],
    tableColumn: [],
  });
  let thisChoseTabDatas = reactive({}); // 当前选中页签对应数据映射
  const testTableData = reactive({
    sql: [],
    query: [],
    header: [],
    body: '',
  }); // 测试 SQL 对应表格数据
  const tabIndex = 1;

  // 对应 database 类型有的类型下拉
  const databaseTypeList = [
    {
      name: 'MYSQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'ORACLE',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'POSTGRESQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SQLSERVER',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'DB2',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'HIVE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'XUGU',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },

    {
      name: 'DAMENG',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GAUSSDB',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GREENPLUM',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GBASE8A',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'KINGBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SYBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    // DWS
    {
      name: 'DWS',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    // CLICKHOUSE
    {
      name: 'CLICKHOUSE',
      options: [{ value: 'database', label: 'database' }],
    },
    // TDENGINE
    {
      name: 'TDENGINE',
      options: [{ value: 'database', label: 'database' }],
    },
    // XUGUTSDB
    {
      name: 'XUGUTSDB',
      options: [{ value: 'database', label: 'database' }],
    },
  ];

  const formItems = ref([
    {
      label: 'DB',
      prop: 'DB',
      type: 'selectGroup',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          setDataBaseTypeOptions(res);
        },
      },
    },
    {
      label: 'Database',
      prop: 'Database',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getSchemaOptions(res);
        },
      },
    },
    {
      label: 'Schema',
      prop: 'Schema',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
        disabled: false,
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getTableOptions(res);
        },
      },
    },
    {
      label: 'Table',
      prop: 'Table',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getSQLOptions(res);
        },
      },
    },
  ]);

  const choseList = reactive({
    info: {},
  });
  // 事件信息
  const treeListener = {
    changeDataSource: (type) => {
      //   treeInfo.scriptTreeData = type === '1' ? allTreeData.treeData : allTreeData.dataBase;
    },
    // 过滤分组树
    filterNode: (value, data) => {
      if (!value) return true;
      let nameData = '';
      if (data.scriptName) {
        nameData = data.scriptName;
      } else {
        nameData = data.groupName;
      }
      return nameData.includes(value); // 使用节点的数据进行比较
    },
    // 树节点点击
    handleNodeClick: async (item) => {
      if (treeInfo.dataType === '1') {
        if (item.data.type === 1) {
          //   if (item.data.scriptId !== thisChoseTabDatas.scriptId) {
          //     addTagById(item.data.scriptId);
          //   }
          await setChoseTab({ id: item.data.groupId });
          nextTick(() => {
            if (item.data.groupType === 1) {
              thisChoseTabDatas.treeListType = 1;
            }
          });
        }
      } else {
        if (item.data.isTable) {
          thisChoseTabDatas.sql += '' + item.data.label;
        }
      }
    },
    treeLoad: (data, resolve, reject) => {
      treeLoad(data, resolve, reject);
    },
    // 新增脚本
    addTabsBtn: (item) => {
      addFolderDialogInfo.searchForm = {
        name: '',
      };
      addFolderDialogInfo.visible = true;
      addFolderDialogInfo.title = '新增脚本';
      addFolderDialogInfo.type = 2;
      thisChoseLabel.value = item;
      const popopRef = `popoverRef${item.data.groupId}`;
      addFolderDialogInfo.rules.parentId = [];
      proxy.$refs[popopRef].hide();
    },
    // 新增分组
    addGroupBtn: (item) => {
      addFolderDialogInfo.searchForm = {
        name: '',
      };
      addFolderDialogInfo.visible = true;
      addFolderDialogInfo.title = '新增文件夹';
      addFolderDialogInfo.type = 1;
      thisChoseLabel.value = item;
      const popopRef = `popoverRef${item.data.groupId}`;
      addFolderDialogInfo.rules.parentId = [];
      proxy.$refs[popopRef].hide();
    },
    // 确认新增
    addGroupCommit: () => {
      proxy.$refs.addFolderRef.validate(async (valid) => {
        if (valid) {
          if (
            addFolderDialogInfo.title === '新增文件夹' ||
            addFolderDialogInfo.title === '新增脚本'
          ) {
            // const thisGroupId =
            //   thisChoseLabel?.value?.data?.groupId > 0
            //     ? thisChoseLabel?.value?.data?.groupId || ''
            //     : '';
            const thisGroupId = thisChoseLabel?.value?.data?.groupId || '';
            const req = {
              id: '',
              groupName: addFolderDialogInfo.searchForm.name,
              parentId: thisGroupId !== -1 ? thisGroupId?.split('%type%')[0] : '',
              tenantId: tenantId.value,
              workspaceId: workSpaceIdData.value,
            };
            //addFolderDialogInfo.type：1 新增文件夹 2 新增脚本
            if (addFolderDialogInfo.type === 1) {
              addTree(req).then(async (res) => {
                if (res.code === 200) {
                  ElMessage.success('新增成功');
                  await getScriptTree();
                  nextTick(() => {
                    proxy.$refs.treeRef.setCurrentKey(thisGroupId);
                    treeInfo.defaultKeys = [thisGroupId];
                  });
                  addFolderDialogInfo.visible = false;
                } else {
                  ElMessage.error('新增失败:' + res.msg);
                }
              });
            } else {
              const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
              req.scriptName = addFolderDialogInfo.searchForm.name;
              req.script = thisChoseTabDatas.sql;
              req.groupId = addFolderDialogInfo.searchForm.parentId
                ? addFolderDialogInfo.searchForm.parentId.split('%type%')[0]
                : thisGroupId.split('%type%')[0];
              req.datasourceId = selectDB.length > 0 ? selectDB[0] : '';
              req.datasourceType = selectDB.length > 1 ? selectDB[1] : '';
              req.databaseName = thisChoseTabDatas.searchForm.Database;
              req.schemaName =
                thisChoseTabDatas.searchForm.Schema || thisChoseTabDatas.searchForm.Database;
              req.tableName = thisChoseTabDatas.searchForm.Table;
              req.parentId = addFolderDialogInfo.searchForm.parentId || thisGroupId;
              req.parentId = req.parentId.split('%type%')[0];

              if (addFolderDialogInfo.searchForm.parentId) {
                addScript(req).then(async (res) => {
                  if (res.code === 200) {
                    ElMessage.success('新增成功');
                    await getScriptTree();
                    // thisChoseTabDatas = editableTabs.value[editableTabs.value.length - 1];
                    thisChoseTabDatas.name = addFolderDialogInfo.searchForm.name;
                    thisChoseTabDatas.scriptId = res.data;

                    //   proxy.$refs.treeRef.setCurrentKey(
                    //     addFolderDialogInfo.searchForm.parentId
                    //       ? addFolderDialogInfo.searchForm.parentId
                    //       : thisChoseLabel.value.data.groupId,
                    //   );
                    //   choseList.info = { data: thisChoseLabel.value.data };

                    //   setChoseTab({ id: item.groupId });
                    nextTick(() => {
                      proxy.$refs.treeRef.setCurrentKey(res.data);
                      choseList.info = { data: thisChoseTabDatas };
                      selectTab.value = res.data;
                    });
                    addFolderDialogInfo.visible = false;
                  } else {
                    ElMessage.error('新增失败:' + res.msg);
                  }
                });
              } else {
                addScript(req).then(async (res) => {
                  if (res.code === 200) {
                    ElMessage.success('新增成功');
                    await getScriptTree();
                    editableTabs.value.push({
                      scriptId: res.data,
                      publishType: false,
                      title: addFolderDialogInfo.searchForm.name,
                      name: addFolderDialogInfo.searchForm.name,
                      searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
                      paneType: 3,
                      sql: '',
                      groupId: thisChoseLabel.value.data.groupId,
                    });

                    thisChoseTabDatas = editableTabs.value[editableTabs.value.length - 1];
                    nextTick(() => {
                      proxy.$refs.treeRef.setCurrentKey(res.data);
                      choseList.info = { data: thisChoseTabDatas };
                      selectTab.value = res.data;
                    });

                    addFolderDialogInfo.visible = false;
                  } else {
                    ElMessage.error('新增失败:' + res.msg);
                  }
                });
              }
            }
          } else {
            // const thisGroupId =
            //   thisChoseLabel?.value?.data?.groupId > 0
            //     ? thisChoseLabel?.value?.data?.groupId || ''
            //     : '';
            const thisGroupId = thisChoseLabel?.value?.data?.groupId || '';
            const thisParentId =
              thisChoseLabel?.value?.data?.parentId?.length > 0 ||
              thisChoseLabel?.value?.data?.parentId > 0
                ? thisChoseLabel?.value?.data?.parentId || ''
                : '';
            const req = {
              id: thisGroupId,
              groupName: addFolderDialogInfo.searchForm.name,
              parentId:
                typeof thisParentId === 'string' ? thisParentId.split('%type%')[0] : thisParentId,
              tenantId: tenantId.value,
              workspaceId: workSpaceIdData.value,
            };
            if (addFolderDialogInfo.type === 1) {
              req.id = req.id.split('%type%')[0];
              updateTree(req).then(async (res) => {
                if (res.code === 200) {
                  ElMessage.success('修改成功');
                  await getScriptTree();
                  nextTick(() => {
                    proxy.$refs.treeRef.setCurrentKey(thisGroupId);
                    treeInfo.defaultKeys = [thisParentId];
                  });
                  addFolderDialogInfo.visible = false;
                } else {
                  ElMessage.error('修改失败:' + res.msg);
                }
              });
            } else {
              const res = await getInfo({ id: thisChoseLabel.value.data.groupId });
              const reqData = {
                ...res.data,
              };
              reqData.scriptName = addFolderDialogInfo.searchForm.name;
              updateScript(reqData).then(async (res) => {
                if (res.code === 200) {
                  ElMessage.success('修改成功');
                  await getScriptTree();
                  nextTick(() => {
                    proxy.$refs.treeRef.setCurrentKey(thisGroupId);
                    treeInfo.defaultKeys = [thisParentId];
                    const tabs = editableTabs.value;
                    tabs.forEach((tab, index) => {
                      if (Number(tab.scriptId) === Number(thisChoseLabel.value.data.groupId)) {
                        tab.name = addFolderDialogInfo.searchForm.name;
                      }
                    });
                  });
                  addFolderDialogInfo.visible = false;
                } else {
                  ElMessage.error('修改失败:' + res.msg);
                }
              });
            }
          }
        }
      });
    },
    closeAddGroupDialog: () => {
      addFolderDialogInfo.searchForm = {
        name: '',
      };
      addFolderDialogInfo.visible = false;
      addFolderDialogInfo.title = '';
      addFolderDialogInfo.type = 1;
    },
    // 编辑
    editGroup: (items) => {
      addFolderDialogInfo.searchForm.name = items.data.groupName;
      addFolderDialogInfo.visible = true;
      thisChoseLabel.value = items;
      if (items.data.type === 1) {
        addFolderDialogInfo.type = 0;
        addFolderDialogInfo.title = '编辑脚本';
      } else {
        addFolderDialogInfo.type = 1;
        addFolderDialogInfo.title = '编辑分组';
      }
      addFolderDialogInfo.rules.parentId = [];
    },
    delete: async (item) => {
      const req = { id: item.data.groupId };
      let res = {};
      //   const message = {};
      //   message.title = item.data.type === 0 ? '确认删除该分组？' : '确认删除该脚本？';
      //   message.content = '移除元数据采集后，将不再采集该数据源下的所有数据。';
      const messageTitle = item.data.type === 0 ? '确认删除该分组？' : '确认删除该脚本？';
      ElMessageBox({
        title: '操作确认',
        // message: h('p', null, [
        //   h('p', null, message.title),
        //   h('span', { style: 'color: teal' }, message.content),
        // ]),
        message: messageTitle,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          if (item.data.type === 0) {
            // 接口调用的时候，分组的要处理成后端传给前端的id
            req.id = req.id.split('%type%')[0];
            res = await deleteTree(req);
          } else {
            res = await deleteScript(req);
          }
          if (res.code === 200) {
            ElMessage.success('删除成功');
            await getScriptTree();
            // 选中和右侧展示往下联动
            nextTick(() => {
              listeners.handleTabsEdit(Number(item.data.groupId), 'remove');
              const pId =
                item.data.parentId.indexOf('%type%') >= 0
                  ? item.data.parentId
                  : item.data.parentId + '%type%' + 0;
              if (editableTabs.value.length > 0) {
                if (thisChoseTabDatas.scriptId <= 1000000000) {
                  proxy.$refs.treeRef.setCurrentKey(thisChoseTabDatas.scriptId);
                } else {
                  proxy.$refs.treeRef.setCurrentKey(pId);
                }
                treeInfo.defaultKeys = [pId];
              } else {
                proxy.$refs.treeRef.setCurrentKey(item.data.parentId);
                treeInfo.defaultKeys = [pId];
                addTabNormal(3);
              }
            });
          } else {
            ElMessage.error('删除失败:' + res.msg);
          }
        })
        .catch(() => {});
    },
  };
  const listeners = {
    // type: 1: 转发，2:表格，3:SQL,4: 查询出的数据
    handleTabsEdit: (targetName, action, type) => {
      if (treeInfo.allTreeData?.treeData && treeInfo.allTreeData.treeData.length <= 0) {
        ElMessage.warning('请先创建分组');
        return;
      }

      // 清空部分数据
      tableConfig.tableData = [];

      if (action === 'add') {
        addTabNormal(type);
      } else if (action === 'remove') {
        const tabs = editableTabs.value;
        let activeName = selectTab.value;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.scriptId === targetName) {
              //   searchForm.value = tab.searchForm;
              const nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.scriptId;
                thisChoseTabDatas = nextTab;
              } else {
                thisChoseTabDatas = {};
              }
            }
          });
        }

        selectTab.value = activeName;
        editableTabs.value = tabs.filter((tab) => tab.scriptId !== targetName);
        if (editableTabs.value.length <= 0) {
          addTabNormal(3);
        }
      }
    },
    choseTabs: async (tabPaneName) => {
      console.log(tabPaneName);
      // 切换标签后需要重置部分信息
      selectTab.value = tabPaneName;
      tableConfig.tableData = [];
      tableConfig.tableColumn = {};
      Object.assign(testTableData, {
        sql: [],
        query: [],
        header: [],
        body: '',
      });

      const tabs = editableTabs.value;

      tabs.forEach((tab) => {
        if (tab.scriptId === tabPaneName) {
          //   searchForm.value = tab.searchForm;
          thisChoseTabDatas = tab;
          //   Object.assign(baseForm, {
          //     ...thisChoseTabDatas.baseForm,
          //     scriptId: thisChoseTabDatas.scriptId,
          //   });
          const datasource = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
          const res = {
            data: {
              datasourceId: datasource[0],
              datasourceType: datasource[1],
              databaseName: thisChoseTabDatas.searchForm.Database,
              schemaName: thisChoseTabDatas.searchForm.Schema,
              tableName: '',
              script: thisChoseTabDatas.sql,
            },
          };
          debugger;
          reSetOptions(thisChoseTabDatas, res);
        }
      });
      //   thisChoseTabDatas = await searchFormShowChange(thisChoseTabDatas);
    },
    save: async () => {
      //   if (
      //     (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0) ||
      //     thisChoseTabDatas.searchForm.DB === '' ||
      //     thisChoseTabDatas.searchForm.Database === ''
      //   ) {
      //     ElMessage.error('请选择 DB、Database 和 Schema');
      //   } else {
      if (thisChoseTabDatas.scriptId === 0 || thisChoseTabDatas.scriptId > 1000000000) {
        addFolderDialogInfo.searchForm = {
          name: '',
        };
        addFolderDialogInfo.visible = true;
        addFolderDialogInfo.title = '新增脚本';
        addFolderDialogInfo.type = 3;
        addFolderDialogInfo.rules.parentId = [
          {
            required: true,
            message: '请选择入名称',
            trigger: 'blur',
          },
        ];
      } else {
        const req = {
          id: thisChoseTabDatas.scriptId,
          scriptName: thisChoseTabDatas.name,
          script: thisChoseTabDatas.sql,
          groupId:
            typeof thisChoseTabDatas.groupId === 'string'
              ? thisChoseTabDatas.groupId.split('%type%')[0]
              : thisChoseTabDatas.groupId,
          datasourceId: thisChoseTabDatas.searchForm.DB.split('_%dt%_')[0],
          datasourceType: thisChoseTabDatas.searchForm.DB.split('_%dt%_')[1],
          databaseName: thisChoseTabDatas.searchForm.Database,
          schemaName: thisChoseTabDatas.searchForm.Schema || '',
          tableName: thisChoseTabDatas.searchForm.Table,
          tenantId: tenantId.value,
          workspaceId: workSpaceIdData.value,
        };
        const res = await updateScript(req);
        if (res.code === 200) {
          ElMessage.success('保存成功');
          if (thisChoseTabDatas.scriptId === 0) {
            await getScriptTree();
          }
        } else {
          ElMessage.error('保存失败:' + res.msg);
        }
      }
      //   }
    },
    run: async () => {
      if (
        (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0) ||
        thisChoseTabDatas.searchForm.DB === '' ||
        thisChoseTabDatas.searchForm.Database === ''
      ) {
        ElMessage.error('请选择 DB、Database 和 Schema');
      } else {
        const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
        const req = {
          datasourceId: selectDB[0],
          datasourceType: selectDB[1],
          schemaName: thisChoseTabDatas.searchForm.Schema || thisChoseTabDatas.searchForm.Database,
          sql: thisChoseTabDatas.sql,
        };
        const res = await sqlRunning(req);
        if (res.code === 200) {
          tableConfig.tableData = res.data.data;
          tableConfig.tableColumn = res.data.metadata;
        } else {
          ElMessage.error('运行失败:' + res.msg);
        }
      }
    },
    formatSQL: () => {
      thisChoseTabDatas.sql = format(thisChoseTabDatas.sql);
    },
    clearSQL: () => {
      thisChoseTabDatas.sql = '';
    },
    copy: async () => {
      //   // 复制内容到剪切板
      //   const transfer = document.createElement('textarea');
      //   document.body.appendChild(transfer);
      //   transfer.value = thisChoseTabDatas.sql; // 这里表示想要复制的内容
      //   transfer.focus();
      //   transfer.select();
      //   if (document.execCommand('copy')) {
      //     document.execCommand('copy');
      //   }
      //   transfer.blur();
      //   ElMessage.success('复制成功');

      //   document.body.removeChild(transfer);
      //   try {
      //     debugger;
      //     console.log(thisChoseTabDatas.sql, 123);
      //     await navigator.clipboard.writeText(thisChoseTabDatas.sql);
      //     console.log('Text copied to clipboard');
      //   } catch (err) {
      //     console.error('Failed to copy text: ', err);
      //   }

      const textarea = document.createElement('textarea');
      textarea.readOnly = 'readonly';
      textarea.style.position = 'absolute';
      textarea.style.left = '-9999px';
      textarea.value = thisChoseTabDatas.sql;
      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, textarea.value.length);
      document.execCommand('Copy');
      document.body.removeChild(textarea);
      ElMessage.success('复制成功');
    },
  };
  // 新增一个默认tab
  const addTabNormal = (type) => {
    const scriptId = new Date().getTime();
    const panesText = '临时脚本';
    // const newTabName = `${++tabIndex}`;
    editableTabs.value.push({
      scriptId,
      publishType: false,
      title: panesText,
      name: panesText,
      searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
      paneType: type,
      sql: '',
      groupId: 0,
    });
    // searchForm.value = { DB: '', Schema: '', Table: '', SQL: '' };
    selectTab.value = scriptId;
    thisChoseTabDatas = editableTabs.value[editableTabs.value.length - 1];

    // 重置部分信息
    formItems.value[1].options = [];
    formItems.value[2].options = [];
    formItems.value[3].options = [];
  };
  // 处理树包含 api，转发的数据
  const deelTreeData = (data, type, parentId) => {
    const returnData = [];
    data?.forEach((children) => {
      const thisItemData = {
        groupName: children.name,
        groupId:
          (children.id ? children.id : children.name) +
          (children.type !== 1 ? '%type%' + children.type : ''), // 为了区分后端返回数据id重复，给分组添加对应后缀

        type: children.type === 0 ? 0 : children.type || -1,
        parentId,
        // groupDesc: children.groupDesc,
        children: deelTreeData(
          children.children,
          type,
          (children.id ? children.id : children.name) +
            (children.type !== 1 ? '%type%' + children.type : ''),
        ),
      };
      if (type === 1) {
        thisItemData.disabled = children.children.length > 0;
      }
      if (type === 2) {
        thisItemData.groupType = 1;
      }
      //   children.apis.forEach((api) => {
      //     thisItemData.children.push({
      //       scriptName: api.scriptName,
      //       scriptId: api.scriptId,
      //       apiStatus: api.apiStatus,
      //       apiType: api.apiType,
      //       groupId: api.groupId,
      //       groupDesc: children.groupDesc,
      //     });
      //   });
      returnData.push(thisItemData);
    });
    return returnData;
  };

  // 获取左侧树
  const getScriptTree = async () => {
    // 获取自己的
    const res = await getTree({
      workspaceId: workSpaceIdData.value,
      isOwner: true,
      isGroup: false,
    });
    const resOther = await getTree({
      workspaceId: workSpaceIdData.value,
      isOwner: false,
      isGroup: false,
    });
    const resGoup = await getTree({
      workspaceId: workSpaceIdData.value,
      isOwner: true,
      isGroup: true,
    });
    if (res) {
      treeInfo.scriptTreeData[0].children = deelTreeData(res.data);
    }
    if (resOther) {
      treeInfo.scriptTreeData[1].children = deelTreeData(resOther.data, 2);
    }
    if (resGoup) {
      const resOptions = deelTreeData(resGoup.data, 1);
      addFolderDialogInfo.groupOptions = resOptions;
    }
    getFirstList(treeInfo.scriptTreeData);
    nextTick(() => {
      // 如果没有数据，默认新增一个
      if (!hasType && editableTabs.value.length <= 0) {
        listeners.handleTabsEdit('', 'add', 3);
      }
    });
    // treeInfo.scriptTreeData = [
    //   {
    //     groupId: 1,
    //     groupName: 'group1',
    //     visible: false,
    //     children: [
    //       {
    //         groupId: 11,
    //         groupName: 'group11',
    //         visible: false,
    //       },
    //       {
    //         groupId: 12,
    //         groupName: 'group12',
    //         visible: false,
    //         children: [
    //           {
    //             groupId: 121,
    //             groupName: 'group121',
    //             visible: false,
    //           },
    //           {
    //             groupId: 122,
    //             groupName: 'group122',
    //             visible: false,
    //           },
    //         ],
    //       },
    //     ],
    //   },
    //   {
    //     groupId: 2,
    //     groupName: 'group2',
    //     visible: false,
    //     children: [
    //       {
    //         groupId: 21,
    //         groupName: 'group21',
    //         visible: false,
    //       },
    //     ],
    //   },
    //   {
    //     groupId: 3,
    //     groupName: 'group3',
    //     visible: false,
    //     children: [
    //       {
    //         groupId: 31,
    //         groupName: 'group31',
    //         visible: false,
    //       },
    //     ],
    //   },
    // ];
  };

  // 处理树 select
  const deelTreeSelect = (data) => {
    const returnData = [];
    if (data && data.length > 0) {
      data.forEach((children) => {
        if (children.groupName && children.groupId) {
          returnData.push({
            label: children.groupName,
            value: children.groupId,
            children: deelTreeSelect(children.children),
          });
        }
      });
    }

    return returnData;
  };

  let hasType = false;

  // 获取第一个值
  const getFirstList = (group) => {
    // if (group[0].children.length > 0) {
    //   getFirstList(group[0].children);
    // } else {
    //   treeInfo.defaultKeys = [group[0].groupId];
    //   treeInfo.defaultKey = group[0].groupId;
    //   nextTick(() => {
    //     proxy.$refs.treeRef.setCurrentKey(group[0].groupId);
    //     choseList.info = { data: group[0] };
    //     // getDetail();
    //   });
    // }
    group.forEach((item) => {
      if (!hasType && item.type === 1) {
        hasType = true;
        nextTick(async () => {
          proxy.$refs.treeRef.setCurrentKey(item.groupId);
          choseList.info = { data: item };
          await setChoseTab({ id: item.groupId });
          nextTick(() => {
            if (item.groupType === 1) {
              thisChoseTabDatas.treeListType = 1;
            }
          });
          // getDetail();
        });
      }
      if (!hasType && item.children.length > 0) {
        getFirstList(item.children);
      }
    });
  };
  // 设置当前选中脚本数据
  const setChoseTab = async (data) => {
    let hasTabs = false;
    const thisTabData = {
      scriptId: 0,
      publishType: false,
      title: '临时脚本',
      name: '临时脚本',
      searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
      paneType: 3,
      sql: '',
    };
    // 添加或者选中对应 Tabs
    editableTabs.value.forEach((tab) => {
      if (Number(tab.scriptId) === Number(data.id)) {
        thisTabData.name = tab.name;
        thisTabData.title = tab.name;
        thisTabData.scriptId = tab.scriptId;
        tab = thisTabData;
        selectTab.value = tab.scriptId;
        hasTabs = true;
      }
    });
    if (!hasTabs) {
      const res = await getInfo({ id: data.id });
      thisTabData.name = res.data.scriptName;
      thisTabData.scriptId = res.data.id;
      thisTabData.groupId = res.data.groupId;
      thisTabData.title = res.data.scriptName;
      editableTabs.value.push(thisTabData);
      //   thisTabData.name = `${++tabIndex}`;
      selectTab.value = thisTabData.scriptId;
      thisChoseTabDatas = editableTabs.value[editableTabs.value.length - 1];
      nextTick(async () => {
        await reSetOptions(thisTabData, res);
      });
    }
  };
  const reSetOptions = async (thisTabData, res) => {
    if (res.data.datasourceId) {
      thisChoseTabDatas.searchForm.DB = res.data.datasourceId + '_%dt%_' + res.data.datasourceType;
      //   await setDataBaseTypeOptions();
      let hasThisDB;
      await formItems.value[0].options.map(async (item) => {
        if (item.label === thisTabData.searchForm.DB.split('_%dt%_')[1]) {
          hasThisDB = item.options.find((option) => {
            return thisTabData.searchForm.DB === option.value;
          });
        }
      });
      if (hasThisDB) {
        // await setDataBaseTypeOptions(thisTabData.searchForm.DB, 2);
        await setDataBaseTypeOptions();
        thisChoseTabDatas.searchForm.Database = res.data.databaseName;

        const hasThisDatabase = formItems.value[1].options.find((item) => {
          return item.value === thisTabData.searchForm.Database;
        });
        if (hasThisDatabase) {
          //   await getSchemaOptions(thisTabData.searchForm.Database, 2);
          await getSchemaOptions();

          thisChoseTabDatas.searchForm.Schema = res.data.schemaName;
          let hasThisSchema = formItems.value[2].options.find((item) => {
            return item.value === thisTabData.searchForm.Schema || thisTabData.searchForm.Database;
          });
          if (formItems.value[2].options.length <= 0) {
            hasThisSchema = {};
            thisChoseTabDatas.searchForm.Schema = '';
          }
          if (hasThisSchema) {
            // await getTableOptions(
            //   thisTabData.searchForm.Schema || thisTabData.searchForm.Database,
            //   2,
            // );
            await getTableOptions();
            // 暂时还是不回显table
            // thisChoseTabDatas.searchForm.Table = res.data.tableName;
            thisChoseTabDatas.sql = res.data.script;
          } else {
            thisTabData.searchForm.Schema = '';
            ElMessage.error('暂无该Schema信息');
          }
        } else {
          thisTabData.searchForm.Database = '';
          thisTabData.searchForm.Schema = '';
          ElMessage.error('暂无该Database信息');
        }
      } else {
        thisTabData.searchForm.DB = '';
        thisTabData.searchForm.Database = '';
        thisTabData.searchForm.Schema = '';
        ElMessage.error('暂无该数据源信息');
      }
    } else {
      formItems.value[1].options = [];
      formItems.value[2].options = [];
      formItems.value[3].options = [];
    }
  };
  // 获取获取分组树
  const getGroupTrees = async () => {
    // 直接沿用左侧分组
    // groupOptions.value = allTreeData.treeData;
    thisChoseTabDatas.baseFormItems[3].options = deelTreeSelect(treeInfo.allTreeData.treeData);
  };
  // 获取 DB 下拉
  const setDBOptions = (data) => {
    formItems.value[0].options = data;
  };

  // 获取database下拉
  const setDataBaseTypeOptions = async (data) => {
    // 对应清空
    formItems.value[1].options = [];
    formItems.value[2].options = [];
    formItems.value[3].options = [];
    thisChoseTabDatas.searchForm.Schema = '';
    thisChoseTabDatas.searchForm.Table = '';
    thisChoseTabDatas.searchForm.Database = '';
    thisChoseTabDatas.sql = '';
    const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
    const reqData = {
      workspaceId: workSpaceIdData.value,
      datasourceId: null,
      schema: '',
      table: '',
      datasourceType: '',
    };
    reqData.datasourceId = selectDB[0];
    reqData.datasourceType = selectDB[1];
    formItems.value[1].options = await getDataSourcesSec(reqData);
  };
  /// / 获取 schema 下拉
  const getSchemaOptions = async (data) => {
    formItems.value[2].options = [];
    formItems.value[3].options = [];
    thisChoseTabDatas.searchForm.Schema = '';
    thisChoseTabDatas.searchForm.Table = '';
    thisChoseTabDatas.sql = '';
    const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
    const catalog = thisChoseTabDatas.searchForm.Database;
    const thisDatabaseType = databaseTypeList?.find((item) => {
      return item.name === selectDB[1];
    });
    if (thisDatabaseType.options.some((obj) => obj.value === 'schema')) {
      const reqData = {
        workspaceId: workSpaceIdData.value,
        datasourceId: null,
        schema: '',
        table: '',
        datasourceType: '',
      };
      reqData.datasourceId = selectDB[0];
      reqData.datasourceType = selectDB[1];
      reqData.catalog = catalog;
      formItems.value[2].options = await getDataSourcesThr(reqData);
      formItems.value[2].props.disabled = false;
    } else {
      formItems.value[2].props.disabled = true;
      getTableOptions();
    }
  };
  // 获取 table 下拉
  const getTableOptions = async (data) => {
    formItems.value[3].options = [];
    thisChoseTabDatas.searchForm.Table = '';
    thisChoseTabDatas.sql = '';
    const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
    const catalog = thisChoseTabDatas.searchForm.Database;
    let schema = thisChoseTabDatas.searchForm.Schema;
    const thisDatabaseType = databaseTypeList.find((item) => {
      return item.name === selectDB[1];
    });
    if (schema === '') {
      schema = catalog;
    }
    const reqData = {
      workspaceId: workSpaceIdData.value,
      datasourceId: null,
      schema: '',
      table: '',
      datasourceType: '',
    };
    reqData.datasourceId = selectDB[0];
    reqData.datasourceType = selectDB[1];
    reqData.databaseName = catalog;
    reqData.schemaName = schema;
    formItems.value[3].options = await getDataSourcesFour(reqData);
  };

  // 获取数据库树
  const getDataSourcesDatas = async (needGet) => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const resArry = [];
    const DBoptions = [];
    treeInfo.allTreeData.dataBase = [];
    for (const mapData in res.data) {
      resArry.push({
        value: mapData,
        label: mapData,
        children: res.data[mapData].map((group) => ({
          value: group.datasourceId,
          label: group.datasourceName,
          children: group.children,
          datasourceId: group.datasourceId,
          datasourceType: group.datasourceType,
        })),
      });
      DBoptions.push({
        value: mapData,
        label: mapData,
        options: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceType,
          label: group.datasourceName,
        })),
      });
      //   allTreeData.dataBase = resArry;
    }
    setDBOptions(DBoptions);
    return resArry;
  };
  // 获取 sql 下拉
  const getSQLOptions = async (data, type = 1) => {
    // thisChoseTabDatas.searchForm.SQL = '';
    const selectDB = thisChoseTabDatas.searchForm.DB.split('_%dt%_');
    const catalog = thisChoseTabDatas.searchForm.Database;
    const table = thisChoseTabDatas.searchForm.Table;
    let schema = thisChoseTabDatas.searchForm.Schema;
    if (schema === '') {
      schema = catalog;
    }
    const datas = {
      datasourceId: selectDB[0],
      table,
      schemaName: schema,
      databaseName: catalog,
    };
    const res = await getDataScripts({ workspaceId: workSpaceIdData.value, ...datas });
    // 目前默认选中后执行 select 语句，填入对应 sql
    const selectsQL = res.data.find((sql) => {
      return sql.type === 'Select';
    });
    if (type === 1) {
      thisChoseTabDatas.sql = selectsQL.value;
    }
  };

  // 获取数据库树，由于是动态树所以下拉不能和树一起请求
  const getDataSourcesDataOption = async (needGet) => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const DBoptions = [];
    for (const mapData in res.data) {
      DBoptions.push({
        value: mapData,
        label: mapData,
        options: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceType,
          label: group.datasourceName,
        })),
      });
    }
    setDBOptions(DBoptions);
  };
  const getDataSourcesSec = async (datas) => {
    let resDatas = [];
    // const res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
    const res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
    resDatas = res.data.map((group) => ({
      value: group.database,
      label: group.database,
      children: group.children,
      datasourceId: datas.datasourceId,
      datasourceType: datas.datasourceType,
      database: group.database,
      catalog: group.catalog,
    }));
    return resDatas;
  };
  const getDataSourcesThr = async (datas) => {
    let resDatas = [];
    const thisDatabaseType = databaseTypeList.find((item) => {
      return item.name === datas.datasourceType;
    });
    if (thisDatabaseType.options.some((obj) => obj.value === 'schema')) {
      const res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
      // const res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
      resDatas = res.data.map((group) => ({
        value: group.schema,
        label: group.schema,
        children: group.children,
        datasourceId: datas.datasourceId,
        datasourceType: datas.datasourceType,
        database: datas.catalog,
        schema: group.schema,
        catalog: datas.catalog,
      }));
    } else {
      datas.schemaName = datas.catalog;
      datas.databaseName = datas.catalog;
      resDatas = await getDataSourcesFour(datas);
    }
    return resDatas;
  };
  const getDataSourcesFour = async (datas) => {
    let resDatas = [];
    const res = await getDataTables({ workspaceId: workSpaceIdData.value, ...datas });

    resDatas = res.data.map((group) => ({
      value: group.tableName,
      label: group.tableName,
      children: group.children,
      datasourceId: datas.datasourceId,
      datasourceType: datas.datasourceType,
      schema: datas.schema || datas.database,
      catalog: datas.catalog,
      remarks: group.remarks,
      tableType: group.tableType,
      isTable: true,
    }));
    return resDatas;
  };

  // 获取数据库子树
  const getDataSourcesTree = async (datas, resolve, reject) => {
    let needGetData = true;
    let returnData = [];
    const treeDataType = datas.level;
    const resData = {
      workspaceId: workSpaceIdData.value,
      datasourceId: null,
      schema: '',
      table: '',
      datasourceType: '',
    };

    switch (treeDataType) {
      case 0:
        if (needGetData) {
          returnData = await getDataSourcesDatas();
        }
        break;
      case 1:
        returnData = datas.data.children;
        break;
      case 2:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        if (needGetData) {
          returnData = await getDataSourcesSec(resData);
        }
        break;
      case 3:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        resData.catalog = datas.data.database;
        // resData.databaseName = datas.data.database;
        // resData.schemaName = datas.data.schema || datas.data.database;
        if (needGetData) {
          returnData = await getDataSourcesThr(resData);
        }
        break;
      case 4:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        resData.databaseName = datas.data.database;
        resData.schemaName = datas.data.schema || datas.data.database;
        if (!datas.data.children || datas.data.children.length === 0) {
          needGetData = true;
        }
        needGetData = !datas.data.isTable;
        if (needGetData) {
          returnData = await getDataSourcesFour(resData);
        }
        break;
      default:
        break;
    }
    return resolve(returnData);
  };
  // 懒加载树
  const treeLoad = (data, resolve, reject) => {
    getDataSourcesTree(data, resolve, reject);
  };

  // 刷新数据库
  const refreshDatabase = () => {
    treeInfo.showDatabase = false;
    setTimeout(() => {
      treeInfo.showDatabase = true;
    });
  };
  const init = async () => {
    editableTabs.value = [];
    // editableTabs.value = [
    //   {
    //     scriptId: 0,
    //     publishType: false,
    //     title: '临时脚本',
    //     name: '临时脚本',
    //     searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
    //     paneType: 3,
    //     sql: '',
    //     groupId: 0,
    //   },
    // ];
    // thisChoseTabDatas = editableTabs.value[0];
    await getDataSourcesDataOption();
    getScriptTree();
    refreshDatabase();
    nextTick(() => {
      if (editableTabs.value.length <= 0) {
        addTabNormal(3);
      }
    });
  };
  watch(workSpaceIdData, (val) => {
    // editableTabs.value.splice(1);
    // if (routers.query.scriptId) {
    //   //   getAPIData(routers.query.scriptId);
    //   addTagById(routers.query.scriptId);
    // }
    init();
  });

  // 初始化
  onMounted(async () => {
    // if (routers.query.scriptId) {
    //   //   getAPIData(routers.query.scriptId);
    //   addTagById(routers.query.scriptId);
    // }
    init();
  });
  return {
    treeInfo,
    treeListener,
    listeners,
    selectTab,
    editableTabs,
    tableConfig,
    formItems,
    addFolderDialogInfo,
  };
}
