import * as components from '@element-plus/icons-vue';
import * as arcoIcons from '@arco-iconbox/vue-update-color-icon';
import * as arcoLineIcons from '@arco-iconbox/vue-update-line-icon';

export default {
  install: (app) => {
    // 注册 Element Plus 图标
    for (const key in components) {
      const componentConfig = components[key];
      app.component(componentConfig.name, componentConfig);
    }

    // 注册 Arco Design 彩色图标
    for (const key in arcoIcons) {
      const iconComponent = arcoIcons[key];
      app.component(key, iconComponent);
    }

    // 注册 Arco Design 线性图标
    for (const key in arcoLineIcons) {
      const iconComponent = arcoLineIcons[key];
      app.component(key, iconComponent);
    }
  },
};
