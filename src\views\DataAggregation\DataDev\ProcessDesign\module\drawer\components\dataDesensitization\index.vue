<template>
  <el-button type="text" @click="add">新增脱敏规则</el-button>
  <section>
    <table class="table-bordered">
      <thead>
        <tr>
          <th>字段</th>
          <th>脱敏规则</th>
        </tr>
      </thead>
    </table>

    <template
      v-for="(syncChange, index) in syncChangeList"
      :key="syncChange.key"
      style="margin-bottom: 150px"
    >
      <el-form ref="syncChangeForm" :model="syncChange" class="container">
        <div class="item">
          <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
            <el-select v-model="syncChange.columnName" placeholder="请输入">
              <el-option
                v-for="data in customerIdList"
                :key="data.columnValue"
                :label="data.columnName"
                :value="data.columnValue"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
            <el-input v-model="syncChange.columnValue" placeholder="请输入"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-button link @click="deleteSyncChange(index)">
            <svg
              t="1699442953096"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="6096"
              width="20"
              height="20"
            >
              <path
                d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                fill="#d81e06"
                p-id="6097"
              ></path>
            </svg>
          </el-button>
        </div>
      </el-form>
    </template>

    <el-button type="text" @click="addSyncChange">添加一行</el-button>

    <el-form style="margin-top: 30px">
      <el-form-item label="结果表别名">
        <el-input v-model="form.tableAliases" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
  </section>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog v-model="dialogVisible" title="新增脱敏规则" width="40%" append-to-body center>
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>新增脱敏规则</span>
      </div>
      <el-divider></el-divider>
    </template>

    <template # default>
      <div style="margin-top: -50px"> </div>
      <el-form :model="form">
        <el-form-item label="敏感数据类型">
          <el-row>
            <el-col :span="6">
              <el-select v-model="sensitiveDataTypeValue" placeholder="">
                <el-option
                  v-for="data in sensitiveDataTypeList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-col>
            <el-col :span="18">
              <el-select
                v-show="sensitiveDataTypeValue == '选择已有'"
                v-model="form.sensitiveDataType"
                placeholder=""
                style="width: 67%"
              >
                <el-option
                  v-for="data in selectExisting"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>

              <el-input
                v-show="sensitiveDataTypeValue == '新增类型'"
                v-model="form.sensitiveDataType"
                placeholder=""
                style="width: 67%"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="脱敏规则名称">
          <el-input
            v-model="form.desensitizationRuleName"
            placeholder=""
            style="width: 63%"
          ></el-input>
        </el-form-item>

        <el-form v-show="show">
          <el-form-item label="脱敏方式">
            <el-radio-group v-model="form.desensitizationMode">
              <el-radio label="1">哈希</el-radio>
              <el-radio label="2">掩盖</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-show="form.desensitizationMode == 2" label="推荐方式">
            <el-select v-model="form.recommendMode" placeholder="" style="width: 63%">
              <el-option
                v-for="data in recommendModeList"
                :key="data.value"
                :label="data.label"
                :value="data.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <hr style="opacity: 0.5" />

        <el-form-item label="样本数据">
          <el-row>
            <el-col :span="20">
              <el-input v-model="form.sampleData" placeholder=""></el-input>
            </el-col>
            <el-col :span="4">
              <el-button @click="toVerificationUtil">脱敏验证</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="脱敏效果">
          <el-input
            v-model="form.desensitizationEffect"
            placeholder=""
            type="textarea"
            style="width: 63%"
            :disabled="true"
          ></el-input>
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getNodeData,
    getRecommendModeList,
    getDataTypeList,
    getMaskingList,
    toVerification,
    maskingSave,
  } from '@/api/DataDev';

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
  });
  const { NodeData } = toRefs(props);
  const emit = defineEmits();

  const syncChangeList = ref([]);
  // 敏感数据类型值
  const sensitiveDataTypeValue = ref('选择已有');
  // 敏感数据类型列表
  const sensitiveDataTypeList = ref([
    { label: '新增类型', value: '新增类型' },
    { label: '选择已有', value: '选择已有' },
  ]);

  // 选择已有列表
  const selectExisting = ref();

  // 脱敏规则列表
  const maskingList = ref();

  // 字段列表
  const customerIdList = ref([{ columnName: '1', columnValue: '字段1' }]);
  // 推荐方式列表
  const recommendModeList = ref();

  const dialogVisible = ref(false);

  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      tableAliases: '',
      recommendMode: '',
      // 脱敏方式
      desensitizationMode: '1',
      // 样本数据
      sampleData: '',
      // 脱敏效果
      desensitizationEffect: '',
      // 脱敏规则名称
      desensitizationRuleName: '',
      // 敏感数据类型
      sensitiveDataType: '',
    },
  });

  const { form } = toRefs(data);

  // 计算属性用于返回show的值
  const show = computed(() => {
    return (
      sensitiveDataTypeValue.value === '新增类型' ||
      (form.value.sensitiveDataType !== '' && form.value.sensitiveDataType !== undefined)
    );
  });

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  /**
   * 提交
   */
  const submitDrawer = async () => {
    const res = await DataProcessing();

    NodeData.value.inputProperties[0].value = res;
    NodeData.value.inputProperties[1].value = form.value.tableAliases;

    await emit('submitDrawer', NodeData.value);
  };

  /**
   * 校验字段
   */
  function DataProcessing() {
    const JSon = syncChangeList.value.map((item) => ({
      columnName: item.columnName,
      columnValue: item.columnValue,
    }));
    return JSON.stringify(JSon);
  }

  const init = async () => {
    console.log('初始化');
    await getNodeDataUtil();
    await getMaskingListUtil();
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };

  /**
   * 删除一行
   */
  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  /**
   * 添加一行
   */
  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    // 生成唯一的key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }
  /**
   * 生成唯一的key
   */
  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取节点数据
   */
  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    customerIdList.value = res.data.metadata[0].columns.map((item) => {
      return {
        columnName: item?.columnName,
        columnValue: item?.columnName,
      };
    });
  };
  /**
   * 获取推荐方式列表
   */
  const getRecommendModeListUtil = async () => {
    const res = await getRecommendModeList();
    recommendModeList.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
        maskRuleValues: item.maskRuleValues,
      };
    });

    // sensitiveDataTypeList.value = res.data.map(item => {
    //     return {
    //         label: item,
    //         value: item,
    //     }
    // })
  };

  /**
   * 获取敏感数据类型列表
   */
  const getDataTypeListUtil = async () => {
    const res = await getDataTypeList();
    selectExisting.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  };

  const getMaskingListUtil = async () => {
    const res = await getMaskingList();
    maskingList.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  };
  const toVerificationUtil = async () => {
    let recommendMode = recommendModeList.value.find(
      (item) => item.value === form.value.recommendMode,
    );

    if (recommendMode) {
      recommendMode = recommendMode.maskRuleValues;
    }
    const res = await toVerification({
      data: form.value.sampleData,
      maskMode: form.value.desensitizationMode,
      maskRuleValues: recommendMode,
    });

    form.value.desensitizationEffect = res.data;
  };

  const confirm = async () => {
    const res = await maskingSave({
      name: form.value.desensitizationRuleName,
      maskMode: form.value.desensitizationMode,
      maskRuleValues: form.value.recommendMode,
      dataType: form.value.sensitiveDataType,
    });
    if (res.code === 200) {
      dialogVisible.value = false;
      await getRecommendModeListUtil();
      await getDataTypeListUtil();
    }
  };

  /**
   * 敏感数据类型值改变
   */

  const add = async () => {
    await getRecommendModeListUtil();
    await getDataTypeListUtil();
    dialogVisible.value = true;
  };

  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .table-bordered {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
    color: #606266;

    th {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      padding: 10px;
    }

    el-button {
      margin-left: 10px;
      border: 1px solid #ebeef5;
    }
  }

  .container {
    display: grid;
    justify-content: start;
    gap: 10px;
    grid-template-columns: 1fr 1fr 0fr;
    border: 1px solid #ebeef5;
  }

  :deep .el-form-item__content {
    padding: 5px;
  }
</style>
