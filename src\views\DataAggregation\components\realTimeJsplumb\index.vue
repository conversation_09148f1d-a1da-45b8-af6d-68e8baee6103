<template>
  <div class="app-container">
    <el-row :gutter="20">
      <b>2.配置数据源</b>
      <el-divider></el-divider>
      <el-col :span="12">
        <b>源数据源</b>
        <el-form ref="dataSourceRef" :model="form" :rules="rules">
          <el-form-item label="源数据源类型" prop="sourceDataType">
            <el-select
              v-model="form.sourceDataType"
              placeholder="源数据源类型"
              clearable
              style="width: 240px"
              @change="getType"
            >
              <el-option
                v-for="dict in sourceDataTypeList"
                :key="dict.value"
                :label="dict.value"
                :value="dict"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="sourceDataSource">
            <el-select
              v-model="form.sourceDataSource"
              placeholder="源数据源"
              clearable
              style="width: 240px"
              @change="getSourceDB"
            >
              <el-option
                v-for="dict in sourceDataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.sourceDataType != 'ORACLE'" label="数据库" prop="sourceDatabase">
            <el-select
              v-model="form.sourceDatabase"
              placeholder="源数据库"
              clearable
              style="width: 240px"
              @change="getSourceTable"
            >
              <el-option
                v-for="dict in sourceDatabaseList"
                :key="dict"
                :label="dict"
                :value="dict"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else label="模式" prop="sourceDatabase">
            <el-select
              v-model="form.sourceDatabase"
              placeholder="源模式"
              clearable
              style="width: 240px"
              @change="getSourceTable"
            >
              <el-option
                v-for="dict in sourceDatabaseList"
                :key="dict"
                :label="dict"
                :value="dict"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="
              form.sourceDataType &&
              form.sourceDataType != 'DAMENG' &&
              form.sourceDataType != 'MYSQL' &&
              form.sourceDataType != 'ORACLE'
            "
            label="模式"
            prop="status"
          >
            <!-- {{ form.sourceDataType }} -->
            <el-select
              v-model="form.sourceTable"
              placeholder="源模式"
              clearable
              style="width: 240px"
              @change="getSourceGP"
            >
              <el-option v-for="dict in sourceTableList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="12">
        <b>目标数据源</b>
        <el-form ref="dataSourceRef" :model="form" :rules="rules">
          <el-form-item label="目标数据源类型" prop="aimDataType">
            <el-select
              v-model="form.aimDataType"
              placeholder="目标数据源类型"
              clearable
              style="width: 240px"
              @change="getTarget"
            >
              <el-option
                v-for="dict in aimDataTypeList"
                :key="dict.value"
                :label="dict.name"
                :value="dict"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="数据源" prop="aimDataSource">
            <el-select
              v-model="form.aimDataSource"
              placeholder="目标数据源"
              clearable
              style="width: 240px"
              @change="getTargetDB"
            >
              <el-option
                v-for="dict in aimDataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.aimDataType != 'ORACLE'" label="数据库" prop="aimDatabase">
            <el-select
              v-model="form.aimDatabase"
              placeholder="目标数据库"
              clearable
              style="width: 240px"
              @change="getTargetTable"
            >
              <el-option
                v-for="dict in aimDatabaseList"
                :key="dict.value"
                :label="dict.name"
                :value="dict"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else label="模式" prop="aimDatabase">
            <el-select
              v-model="form.aimDatabase"
              placeholder="目标模式"
              clearable
              style="width: 240px"
              @change="getTargetTable"
            >
              <el-option
                v-for="dict in aimDatabaseList"
                :key="dict.value"
                :label="dict.name"
                :value="dict"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="
              form.aimDataType &&
              form.aimDataType != 'DAMENG' &&
              form.aimDataType != 'MYSQL' &&
              form.aimDataType != 'ORACLE'
            "
            label="模式"
            prop="status"
          >
            <el-select
              v-model="form.aimTable"
              placeholder="目标模式"
              clearable
              style="width: 240px"
              @change="getGPTable"
            >
              <el-option v-for="dict in aimTableList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-row v-show="srcAndTrg" :gutter="20">
      <b>3.配置字段映射</b>
      <el-divider></el-divider>
      <el-col :span="6">
        <!-- <b>选择源表</b> -->
        <el-input
          v-model="filterText"
          placeholder="源数据库表名搜索"
          clearable
          prefix-icon="Search"
          style="margin-bottom: 20px"
        />
        <el-tree
          ref="deptTreeRef"
          :data="sourceDataTableList"
          :props="defaultProps"
          :expand-on-click-node="false"
          node-key="tableName"
          default-expand-all
          show-checkbox
          style="max-height: 400px; overflow-y: auto; overflow-x: none; min-height: 300px"
          :filter-node-method="filterNode"
          @check="getDeptTreeRef"
        />
      </el-col>
      <el-col :span="18">
        <el-table :data="tableList" max-height="400" style="min-height: 300px">
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" label="已选择源表" prop="tableName" />
          <el-table-column align="center" label="目标表">
            <template #default="scope">
              <el-select
                v-model="scope.row.target"
                :remote-method="remoteMethod"
                remote
                filterable
                clearable
                @change="clearColumn"
              >
                <el-option
                  v-for="table in optionList"
                  :key="table.tableName"
                  :label="table.tableName"
                  :value="table.tableName"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" label="字段映射" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" @click="editField(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <b>4.其他配置</b>
      <el-divider></el-divider>
      <el-col :span="12">
        <el-form-item label="运行模式">
          <el-select v-model="runModel">
            <el-option
              v-for="model in runModelList"
              :key="model.value"
              :label="model.label"
              :value="model.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12" :xs="12">
        <el-checkbox v-model="isCheck" label="是否清空目标表" />
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-row style="margin-top: 20px">
        <el-button v-if="srcAndTrg ? true : false" plain type="primary" @click="sumBitAllParams"
          >确定</el-button
        >
      </el-row>
    </el-row>
    <!-- 字段映射 -->
    <el-dialog
      v-model="open"
      title="字段配置"
      width="780px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelEditField()"
    >
      <div v-loading="loadingForField" class="fieldBox">
        <div class="btnGruop">
          <el-button @click="sameNameConnect">同名连接</el-button>
          <el-button @click="peerConnect">同行连接</el-button>
          <el-button @click="cancelAllConnection">移除所有连接</el-button>
        </div>
        <div class="field-container-box">
          <div class="fieldContainer">
            <div class="list left">
              <div v-for="sourceField in fieldList" :key="sourceField.id" class="listItem">
                <div :id="sourceField.columnName" class="listItenInner leftItem" @click="addPoint">
                  <el-tooltip effect="dark" :content="sourceField.columnName" placement="top">
                    <div>{{ sourceField.columnName }}</div>
                  </el-tooltip>
                  <el-tooltip effect="dark" :content="sourceField.columnType" placement="top">
                    <div>{{ sourceField.columnType }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <div class="list right">
              <div v-for="targetField in newTrgFieldList" :key="targetField.id" class="listItem">
                <div
                  :id="targetField.columnName + '*rightItem/' + targetField.sourceTableName"
                  class="listItenInner rightItem"
                  @click="addPoint"
                >
                  <el-tooltip effect="dark" :content="targetField.columnName" placement="top">
                    <div>{{ targetField.columnName }}</div>
                  </el-tooltip>
                  <el-tooltip effect="dark" :content="targetField.columnType" placement="top">
                    <div>{{ targetField.columnType }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <el-table v-loading="loadingForField" :data="fieldList">
                <el-table-column align="center" label="源表字段及类型">
                    <template #default="scope">
                        <div>字段名：{{ scope.row.columnName }}</div>
                        <div>字段类型：{{ scope.row.columnType }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="目标表字段">
                    <template #default="scope">
                        <el-select clearable  v-model="scope.row.field">
                            <el-option v-for="field in scope.row.trgFieldList" :key="field.columnName"
                                :label="field.show" :value="field.columnName"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
            </el-table> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEditField">取 消</el-button>
          <el-button type="primary" @click="submitFormField">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import {
    tableForGP,
    schemaForGP,
    getDatabaseList,
    getFieldMapList,
    getTableList,
    list as getList,
  } from '@/api/dataSourceManageApi';
  import { ref } from 'vue';
  import jsPlumb from 'jsplumb';
  // jsplumb实例
  const plumbIns = ref(null);
  // 连线数据的左侧集合
  const leftElList = ref(null);
  // 连线数据的右侧集合
  const rightElList = ref(null);
  const { proxy } = getCurrentInstance();

  const show = ref(true);
  // 源数据源类型
  const sourceDataTypeList = ref([
    'MYSQL',
    // "ORACLE",'XUGU', "SQLSERVER", "POSTGRESQL", "DAMENG", "GREENPLUM",
    // 'KAFKA', 'API'
  ]);
  // 目标数据源类型
  const aimDataTypeList = ref([
    'MYSQL',
    // , "ORACLE",'XUGU', "SQLSERVER", "POSTGRESQL", "DAMENG", "GREENPLUM",
    // 'KAFKA', 'API'
  ]);
  // 源数据源
  const sourceDataSourceList = ref();
  // 源数据库
  const sourceDatabaseList = ref();

  // 源表
  const sourceDataTableList = ref();
  // 源模式
  const sourceTableList = ref();

  // 目标数据源
  const aimDataSourceList = ref();
  // 目标数据库
  const aimDatabaseList = ref();

  // 目标表
  // const aimDataTableList = ref()

  // 目标模式
  const aimTableList = ref();

  const router = useRouter();
  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
    saveWorkFlowDataList: {
      type: Object,
      // default: () => { }
    },
  });

  const { flowId, nodeId, nodeName, openWorkFlowData, workFlowType, saveWorkFlowDataList } =
    toRefs(props);
  console.log('nodeName.value', nodeName.value);
  console.log('flowId', flowId.value);
  console.log('nodeId', nodeId.value);
  console.log('saveWorkFlowDataList', saveWorkFlowDataList.value.workspaceId);

  const defaultProps = {
    children: 'children',
    label: 'tableName',
  };
  const data = reactive({
    form: {
      sourceDataType: '',
      sourceDataSource: '',
      sourceDatabase: '',
      sourceTable: '',
      aimDataType: '',
      aimDataSource: '',
      aimDatabase: '',
      aimTable: '',
    },
    rules: {
      sourceDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      sourceDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      sourceDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],

      aimDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      aimDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      aimDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],
    },
  });
  const { form, rules } = toRefs(data);

  // 字段映射的显影
  const srcAndTrg = ref(false);

  // 树状结构Ref
  const deptTreeRef = ref();
  // 字段映射树状结构
  const targetTableList = ref([]);
  // 字段映射表格数据
  const tableList = ref(null);

  // 字段配置 弹窗 loading
  const loadingForField = ref(false);
  // 弹窗字段配置 弹窗
  const open = ref(false);
  // 弹窗字段映射table列表
  const fieldList = ref([]);

  const columnsMapping = ref([]);

  // 搜索字段参数
  const filterText = ref('');
  // 监听输入框的值
  watch(filterText, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  // 对输入框的值进行模糊查询
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.tableName.toLowerCase().includes(value.toLowerCase());
  };

  const optionList = ref([]);
  // 目标表select框下拉搜索事件
  const remoteMethod = (query) => {
    if (query) {
      optionList.value = targetTableList.value.filter((item) => {
        return item.tableName.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      optionList.value = targetTableList.value;
    }
  };

  // 获取源数据源
  const getType = async (data) => {
    // 改变数据 先清空已有数据
    form.value.sourceDataSource = null;
    sourceDataSourceList.value = [];

    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];

    sourceDataTableList.value = [];
    if (data) {
      // form.value.sourceDataSource = null
      await getList({ type: data, workSpaceId: saveWorkFlowDataList.value.workspaceId }).then(
        (res) => {
          sourceDataSourceList.value = res.data;
        },
      );
    } else {
      sourceDataSourceList.value = [];

      // form.value.sourceDataSource = ''
      // getSourceDB(data)
      // getTarget(data)
    }
  };

  // 获取源数据库
  const getSourceDB = async (data) => {
    // 改变数据 先清空已有数据
    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    sourceDataTableList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      await getDatabaseList({ datasourceId: data }).then((res) => {
        sourceDatabaseList.value = res.data;
      });
    } else {
      sourceDatabaseList.value = [];
      // form.value.sourceDatabase = ''
      // form.value.sourceTable = ''
    }
  };

  // 获取目标数据源
  const getTarget = async (data) => {
    // 改变数据 先清空已有数据
    form.value.aimDataSource = null;
    aimDataSourceList.value = [];

    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];
    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }
    if (data) {
      await getList({ type: data, workSpaceId: saveWorkFlowDataList.value.workspaceId }).then(
        (res) => {
          aimDataSourceList.value = res.data;
        },
      );
    } else {
      aimDataSourceList.value = [];
      // form.value.aimDataSource = ''
      // getTargetDB(data)
    }
  };

  // 获取目标数据库
  const getTargetDB = async (data) => {
    // 改变数据 先清空已有数据
    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];
    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }

    if (data) {
      await getDatabaseList({ datasourceId: data }).then((res) => {
        aimDatabaseList.value = res.data;
      });
    } else {
      aimDatabaseList.value = [];
      // form.value.aimDatabase = ''
    }
  };
  // 获取源数据源表数据,如果存在模式则获取相应的模式列表
  const getSourceTable = async (data) => {
    // form.value.sourceTable = ''
    // if(workFlowType.value == 'edit') {
    //     form.value.sourceDatabase = openWorkFlowData.value?.tableMapping[0].sourceDatabase
    // }
    // data = form.value.sourceDatabase
    // 改变数据 先清空已有数据
    form.value.sourceTable = null;
    sourceTableList.value = [];

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      if (
        form.value.sourceDataType &&
        (form.value.sourceDataType == 'MYSQL' ||
          form.value.sourceDataType == 'DAMENG' ||
          form.value.sourceDataType == 'ORACLE')
      ) {
        const objForOr = {};
        if (form.value.sourceDataType == 'ORACLE') {
          (objForOr.datasourceId = form.value.sourceDataSource),
            (objForOr.schema = form.value.sourceDatabase);
        } else {
          (objForOr.datasourceId = form.value.sourceDataSource),
            (objForOr.databaseName = form.value.sourceDatabase);
        }
        await getTableList(objForOr).then((res) => {
          proxy.$modal.loading('正在加载...');
          if (res.data && res.data.length) {
            sourceDataTableList.value = res.data;
            // console.log('sourceDataTableList.value', sourceDataTableList.value)
          } else {
            sourceDataTableList.value = [];
            proxy.$modal.msgWarning('源数据源当前数据库下没有表');
          }
          treeIsshow();
          proxy.$modal.closeLoading();
        });
      } else {
        const obj = {};
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase);
        // if(form.value.sourceDataType == 'XUGU') {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.schema = form.value.sourceDatabase
        // } else {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.databaseName = form.value.sourceDatabase
        // }
        await schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            sourceTableList.value = res.data;
          } else {
            sourceTableList.value = [];
          }
        });
      }
    } else {
      form.value.sourceTable = null;
      // getSourceGP(data)
    }
  };
  // 源数据源存在模式情况，获取模式下的表
  const getSourceGP = async (data) => {
    // if(workFlowType.value == 'edit') {
    //     form.value.sourceTable = openWorkFlowData.value?.tableMapping[0].sourceSchema
    // }
    // data = form.value.sourceTable
    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      const obj = {};
      if (form.value.sourceDataType == 'XUGU') {
        (obj.datasourceId = form.value.sourceDataSource), (obj.schema = data);
      } else {
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase),
          (obj.schema = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          sourceDataTableList.value = res.data;
          treeIsshow();
        } else {
          sourceDataTableList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      sourceDataTableList.value = [];
    }
  };
  // 当配置数据源的数据发生变化时，判断是否展示树状结构
  // 两种情况 ， 是否包含模式
  const treeIsshow = () => {
    if (
      form.value.sourceDataType &&
      (form.value.sourceDataType == 'MYSQL' ||
        form.value.sourceDataType == 'DAMENG' ||
        form.value.sourceDataType == 'ORACLE')
    ) {
      if (sourceDataTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy.$refs.deptTreeRef.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    } else {
      if (sourceTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy.$refs.deptTreeRef.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    }
  };
  // 如果存在模式。则获取模式
  const getTargetTable = async (data) => {
    form.value.aimTable = null;
    aimTableList.value = [];

    // 库发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    optionList.value = [];
    if (data) {
      aimTableList.value = [];
      targetTableList.value = [];
      if (
        form.value.aimDataType &&
        form.value.aimDataType != 'MYSQL' &&
        form.value.aimDataType != 'DAMENG' &&
        form.value.aimDataType != 'ORACLE'
      ) {
        const obj = {};
        (obj.datasourceId = form.value.aimDataSource), (obj.databaseName = form.value.aimDatabase);
        // if(form.value.aimDataType == 'XUGU') {
        //     obj.datasourceId = form.value.aimDataSource,
        //     obj.databaseName = form.value.aimDatabase
        // } else {
        //     obj.datasourceId = form.value.aimDataSource,
        //     obj.databaseName = form.value.aimDatabase
        // }
        await schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            aimTableList.value = res.data;
          } else {
            aimTableList.value = [];
          }
        });
      } else {
        const objForOr = {};
        if (form.value.aimDataType == 'ORACLE') {
          (objForOr.datasourceId = form.value.aimDataSource),
            (objForOr.schema = form.value.aimDatabase);
        } else {
          (objForOr.datasourceId = form.value.aimDataSource),
            (objForOr.databaseName = form.value.aimDatabase);
        }
        await getTableList(objForOr).then((res) => {
          if (res.data && res.data.length) {
            targetTableList.value = res.data;
            // treeIsshow()
          } else {
            targetTableList.value = [];
            // 提示用户
            // proxy.$modal.msgWarning(`目标数据源当前数据库下没有表`);
          }
          // proxy.$modal.closeLoading();
        });
      }
    } else {
      // form.value.aimTable = ''
      // aimTableList.value = []
      targetTableList.value = [];
    }
  };
  // 获取目标数据源模式下的表
  const getGPTable = async (data) => {
    // 模式发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    if (data) {
      targetTableList.value = [];
      const obj = {};
      if (form.value.aimDataType == 'XUGU') {
        (obj.datasourceId = form.value.aimDataSource), (obj.schema = data);
      } else {
        (obj.datasourceId = form.value.aimDataSource),
          (obj.databaseName = form.value.aimDatabase),
          (obj.schema = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          targetTableList.value = res.data;
          // treeIsshow()
        } else {
          targetTableList.value = [];
          // proxy.$modal.msgWarning('目标数据源当前模式下不存在表')
        }
      });
    } else {
      optionList.value = [];
      // form.value.aimTable = ''
      // aimTableList.value = []
      targetTableList.value = [];
    }
  };
  // 树状结构点击勾选事件
  const getDeptTreeRef = (data, checked) => {
    console.log(data, checked.checkedNodes);
    if (checked.checkedNodes.length > 1) {
      proxy.$refs.deptTreeRef.setChecked(
        checked.checkedNodes.filter((item) => item.tableName != data.tableName)[0].tableName,
        false,
      );
      checked.checkedNodes = checked.checkedNodes.filter(
        (item) => item.tableName == data.tableName,
      );
    }
    // 每次勾选时，清空目标表选择
    if (tableList.value?.length) {
      tableList.value[0].target = null;
    }
    // 每次勾选变化时，需要清空已选择的字段映射数据
    columnsMapping.value = [];
    // 添加后续用到的参数 （字段映射）
    if (checked.checkedNodes.length) {
      tableList.value = [];
      // 勾选表
      checked.checkedNodes.map((i) => {
        i.trgFieldList = [];
        i.connectData = [];
        return checked.checkedNodes;
      });
      tableList.value = checked.checkedNodes;
      if (
        !targetTableList.value.length ||
        targetTableList.value.every((res) => res.tableName != data.tableName)
      ) {
        targetTableList.value.unshift({ tableName: data.tableName });
      }
      tableList.value.filter((item) => item.tableName == data.tableName)[0].target = data.tableName;
    } else {
      tableList.value = [];
    }
  };
  // let  tableListId = ref(null)
  const clearColumn = (data) => {
    columnsMapping.value = [];
  };
  const newTrgFieldList = ref([]);
  const connectLimit = ref([]);
  // 定义一个变量保存源表字段
  const sourceTableForcdcParam = ref([]);
  const editField = (row) => {
    // 字段映射
    if (form.value.aimDatabase || form?.value.aimTable) {
      if (row.target) {
        open.value = true;
        loadingForField.value = true;
        const queryObj = {};
        queryObj.srcDatasourceId = form.value.sourceDataSource;
        queryObj.srcDatabaseName = form.value.sourceDatabase;
        if (
          form.value.sourceDataType &&
          form.value.sourceDataType != 'DAMENG' &&
          form.value.sourceDataType != 'MYSQL' &&
          form.value.sourceDataType != 'ORACLE'
        ) {
          queryObj.srcSchemaName = form.value.sourceTable;
        } else {
          queryObj.srcSchemaName = '';
        }
        queryObj.srcTableName = row.tableName;
        queryObj.destDatasourceId = form.value.aimDataSource;
        queryObj.destDatabaseName = form.value.aimDatabase;
        if (
          form.value.aimDataType &&
          form.value.aimDataType != 'DAMENG' &&
          form.value.aimDataType != 'MYSQL' &&
          form.value.aimDataType != 'ORACLE'
        ) {
          queryObj.destSchemaName = form.value.aimTable;
        } else {
          queryObj.destSchemaName = '';
        }
        queryObj.destTableName = row.target;
        console.log(queryObj);
        getFieldMapList(queryObj)
          .then((res) => {
            if (res.data && res.code == 200) {
              sourceTableForcdcParam.value = res.data.srcTable.columns;
              fieldList.value = res.data.srcTable.columns;
              fieldList.value.forEach((i) => {
                i.sourceTableName = row.tableName;
              });
              res.data.destTable.columns.forEach((item) => (item.sourceTableName = row.tableName));
              newTrgFieldList.value = res.data.destTable.columns;
              connectLimit.value = res.data.columnTypeMap;
            } else {
              connectLimit.value = [];
            }
            loadingForField.value = false;
          })
          .then(() => {
            leftElList.value = document.querySelectorAll('.leftItem');
            rightElList.value = document.querySelectorAll('.rightItem');
            // 回显连线数据
            if (
              tableList.value.filter((res) => res.tableName == row.tableName)[0].connectData?.length
            ) {
              tableList.value
                .filter((res) => res.tableName == row.tableName)[0]
                .connectData.forEach((item) => {
                  plumbIns.value.ready(() => {
                    plumbIns.value.connect({
                      // 连线起点
                      source: item.source,
                      // 连线终点
                      target: item.target,
                      anchor: ['Left', 'Right'],
                      connector: ['Straight'],
                      endpoint: 'Blank',
                      overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
                      // 添加样式
                      paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
                    });
                  });
                });
            }
          });
        nextTick(() => {
          plumbIns.value = jsPlumb.jsPlumb.getInstance({ Container: 'content' });
        });
      } else {
        return proxy.$modal.msgWarning('请选择目标表');
      }
    } else {
      return proxy.$modal.msgWarning('请选择目标库或者模式');
    }
  };

  // 存放端点数组
  const point = ref([]);
  // 连线样式
  const pointStyle = {
    // 端点的颜色样式
    paintStyle: { stroke: 'black' },
    // 设置端点的类型，大小、css类名、浮动上去的css类名
    endpoint: ['Dot', { radius: 5, cssClass: 'initial_endpoint', hoverClass: 'hover_endpoint' }],
  };
  /**
   * e.srcElement.id - DOM节点对应的id名
   * @param {String} e -点击节点对应的DOM属性
   */
  const addPoint = (e) => {
    console.log(e);
    console.log(point.value);
    // 点击左侧dom时，判断是否该节点已存在端点，存在的话需要删除当前的端点和连线
    if (
      point.value.length &&
      point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id).length
    ) {
      // 删除当前DOM的连线
      plumbIns.value.deleteConnectionsForElement(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0].anchor
          .elementId,
      );
      // 删除当前DOM的端点
      plumbIns.value.deleteEndpoint(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0],
      );
      // 更新当前存在的端点数据
      point.value = point.value.filter((res) => res.elementId != e.srcElement.offsetParent.id);
    } else {
      // 右边的dom不需要增加端点
      if (!e.srcElement.offsetParent.className.includes('rightItem')) {
        point.value.push(
          plumbIns.value.addEndpoint(
            e.srcElement.offsetParent.id,
            {
              anchors: ['Right'],
            },
            pointStyle,
          ),
        );
      }
    }
    console.log(point.value);
    // 左侧每个DOM只能有一条连线
    if (allData.value.some((data) => data.sourceId == point.value.slice(-1)[0]?.anchor.elementId)) {
      return;
    }
    // 右侧DOM只能有一个箭头
    if (allData.value.some((data) => data.targetId == e.srcElement.offsetParent.id)) {
      return;
    }
    // 点击右边的DOM时，才触发连线操作
    if (e.srcElement.offsetParent.className.includes('rightItem')) {
      // 端点数组可能没有数据，需要判断一下
      point.value.length &&
        plumbIns.value.ready(() => {
          plumbIns.value.connect({
            // 连线起点
            source: point.value.slice(-1)[0].anchor.elementId,
            // 连线终点
            target: e.srcElement.offsetParent.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 存放所有的连接信息
  const allData = ref([]);
  // 删除所有连线
  const cancelAllConnection = () => {
    allData.value = [];
    point.value = [];
    plumbIns.value.deleteEveryConnection();
    plumbIns.value.deleteEveryEndpoint();
  };
  // 同行连接
  const peerConnect = () => {
    cancelAllConnection();
    // 根据长度判断循环连线的数据
    if (leftElList.value.length <= rightElList.value.length) {
      leftElList.value.forEach((res, index) => {
        plumbIns.value.connect({
          // 连线起点
          source: res.id,
          // 连线终点
          target: rightElList.value[index].id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
      });
    } else if (leftElList.value.length > rightElList.value.length) {
      rightElList.value.forEach((res, index) => {
        plumbIns.value.connect({
          // 连线起点
          source: leftElList.value[index].id,
          // 连线终点
          target: res.id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
      });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 同名连接
  const sameNameConnect = () => {
    cancelAllConnection();
    leftElList.value.forEach((res) => {
      rightElList.value.forEach((key) => {
        if (res.id == key.id.split('*')[0]) {
          plumbIns.value.connect({
            // 连线起点
            source: res.id,
            // 连线终点
            target: key.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        }
      });
    });
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };

  // 保存字段配置（本次取消判断是否匹配）
  const submitFormField = () => {
    // 过滤目标为空的字段
    const item = plumbIns.value.getAllConnections();
    if (item.length) {
      // 将连接的数据进行保存，用于回显连线
      if (
        tableList.value.filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
          .connectData?.length
      ) {
        // 每次赋值前，清空之前选择的数据
        tableList.value.filter(
          (res) => res.tableName == item[0].targetId.split('/')[1],
        )[0].connectData = [];
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .connectData.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId,
              target: i.targetId,
            });
        });
      } else {
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .connectData.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId,
              target: i.targetId,
            });
        });
      }
      // 连线数据
      // 判断是否是第一次添加表格中的字段映射数据
      if (columnsMapping.value?.length) {
        columnsMapping.value = [];
        item.map((i) => {
          columnsMapping.value.push({ source: i.sourceId, target: i.targetId.split('*')[0] });
        });
      } else {
        item.map((i) => {
          columnsMapping.value.push({ source: i.sourceId, target: i.targetId.split('*')[0] });
        });
      }
    } else {
      proxy.$modal.msgWarning('字段映射不能为空');
    }
    open.value = false;
  };
  const cancelEditField = () => {
    cancelAllConnection();
    open.value = false;
  };
  // 运行模式
  const runModel = ref(null);
  // 运行模式的下拉框数据
  const runModelList = ref([
    { label: 'yarn-cluster', value: 'yarn-cluster' },
    { label: 'flink-cluster', value: 'flink-cluster' },
  ]);
  // 删除目标表勾选框
  const isCheck = ref(false);

  const sumBitAllParams = () => {
    if (!columnsMapping.value.length) {
      return proxy.$modal.msgWarning('请配置字段映射');
    }
    const colLen = tableList.value?.length;
    if (colLen) {
      let allParams = [];
      allParams = columnsMapping.value;
      // 增加模式字段
      // allParams.forEach(i => {
      //     i.destSchema = ''
      //     i.sourceSchema = ''
      // })
      // 如果数据源配置中有模式选项，则相应赋值
      // if (form.value.sourceDataType && (form.value.sourceDataType != 'MYSQL' && form.value.sourceDataType != 'DAMENG' && form.value.sourceDataType != 'ORACLE')) {
      //     allParams.forEach(i => {
      //         i.sourceSchema = form.value.sourceTable
      //     })
      // }
      // if (form.value.aimDataType && (form.value.aimDataType != 'MYSQL' && form.value.aimDataType != 'DAMENG' && form.value.aimDataType != 'ORACLE')) {
      //     allParams.forEach(i => {
      //         i.destSchema = form.value.aimTable
      //     })
      // }

      // 参数拼接（cdc任务需要的接口参数）
      const cdcParams = [
        {
          type: 'REALTIME_ALG_SOURCE_JDBC',
          configProperties: {
            outputFields: {
              name: 'outputFields',
              dataType: 'VARCHAR',
              valueFrom: 'from_ui',
              value: '',
              dataOutputType: 0,
              transmissible: false,
            },
            datasource: {
              name: 'datasource',
              dataType: 'VARCHAR',
              valueFrom: 'from_ui',
              value: {},
              dataOutputType: 0,
              transmissible: false,
            },
          },
        },
        {
          type: 'REALTIME_ALG_SINK_JDBC',
          configProperties: {
            truncate: {
              name: 'truncate',
              dataType: 'VARCHAR',
              valueFrom: 'from_ui',
              value: isCheck.value,
              dataOutputType: 0,
              transmissible: false,
            },
            columnsMapping: {
              name: 'columnsMapping',
              dataType: 'VARCHAR',
              valueFrom: 'from_alg_metadata_column',
              value: JSON.stringify(allParams),
              dataOutputType: 0,
              transmissible: false,
            },
            outputFields: {
              name: 'outputFields',
              dataType: 'VARCHAR',
              valueFrom: 'from_ui',
              value: '',
              dataOutputType: 0,
              transmissible: false,
            },
            datasource: {
              name: 'datasource',
              dataType: 'VARCHAR',
              valueFrom: 'from_ui',
              value: {},
              dataOutputType: 0,
              transmissible: false,
            },
          },
        },
      ];
      const objForcdc = [];
      const targetForcdc = [];
      console.log(tableList.value[0]);

      sourceTableForcdcParam.value.map((item, index) => {
        objForcdc.push({
          columnName: null,
          columnType: null,
          columnSize: null,
          decimalDigit: null,
          isPrimaryKey: null,
          isAutoIncrement: null,
          isNullable: null,
          comment: null,
          defaultValue: null,
          checked: true,
        });
        objForcdc[index].columnName = item.columnName;
        objForcdc[index].columnType = item.columnType;
        objForcdc[index].columnSize = item.columnSize;
        objForcdc[index].decimalDigit = item.decimalDigit;
        objForcdc[index].isPrimaryKey = item.isPrimaryKey;
        objForcdc[index].isAutoIncrement = item.isAutoIncrement;
        objForcdc[index].isNullable = item.isNullable;
        objForcdc[index].comment = item.comment;
        objForcdc[index].defaultValue = item.defaultValue;
        return objForcdc;
      });

      tableList.value[0].trgFieldList.map((item, index) => {
        targetForcdc.push({
          columnName: null,
          columnType: null,
          columnSize: null,
          decimalDigit: null,
          isPrimaryKey: null,
          isAutoIncrement: null,
          isNullable: null,
          comment: null,
          defaultValue: null,
          checked: true,
        });
        targetForcdc[index].columnName = item.columnName;
        targetForcdc[index].columnType = item.columnType;
        targetForcdc[index].columnSize = item.columnSize;
        targetForcdc[index].decimalDigit = item.decimalDigit;
        targetForcdc[index].isPrimaryKey = item.isPrimaryKey;
        targetForcdc[index].isAutoIncrement = item.isAutoIncrement;
        targetForcdc[index].isNullable = item.isNullable;
        targetForcdc[index].comment = item.comment;
        targetForcdc[index].defaultValue = item.defaultValue;
        return targetForcdc;
      });

      // 获取源表字段
      cdcParams[0].configProperties.outputFields.value = JSON.stringify(objForcdc);
      // 获取源数据源的连接信息,类型，数据源id
      cdcParams[0].configProperties.datasource.value.datasourceType = form.value.sourceDataType;
      cdcParams[0].configProperties.datasource.value.datasourceId = form.value.sourceDataSource;
      cdcParams[0].configProperties.datasource.value.databaseName = form.value.sourceDatabase;
      cdcParams[0].configProperties.datasource.value.tableName = tableList.value[0].tableName;
      cdcParams[0].configProperties.datasource.value.connectionParams =
        sourceDataSourceList.value.filter(
          (res) => res.id == form.value.sourceDataSource,
        )[0].connectionParams;
      // 获取目标表字段

      cdcParams[1].configProperties.outputFields.value = JSON.stringify(targetForcdc);
      // 获取源数据源的连接信息,类型，数据源id
      cdcParams[1].configProperties.datasource.value.datasourceType = form.value.aimDataType;
      cdcParams[1].configProperties.datasource.value.datasourceId = form.value.aimDataSource;
      cdcParams[1].configProperties.datasource.value.connectionParams =
        aimDataSourceList.value.filter(
          (res) => res.id == form.value.aimDataSource,
        )[0].connectionParams;
      cdcParams[1].configProperties.datasource.value.databaseName = form.value.aimDatabase;
      cdcParams[1].configProperties.datasource.value.tableName = tableList.value[0].target;

      // 转为JSON格式
      cdcParams[0].configProperties.datasource.value = JSON.stringify(
        cdcParams[0].configProperties.datasource.value,
      );
      cdcParams[1].configProperties.datasource.value = JSON.stringify(
        cdcParams[1].configProperties.datasource.value,
      );

      saveNode({
        id: nodeId.value,
        operatorId: '5tyHdfcg918bb91e12y74eg345gdk',
        createTime: '2023-11-22 10:05:21',
        createUser: null,
        updateTime: null,
        updateUser: null,
        aliasName: null,
        nodeName: nodeName.value,
        flowId: flowId.value,
        parentFlowId: null,
        parentNodeId: null,
        nodeType: 'REALTIME_CDC_ALG',
        parents: null,
        jobId: null,
        outputProperty: null,
        program: 'REALTIME_CDC_ALG',
        algorithmName: '实时同步CDC',
        inputProperties: [
          {
            id: '5FshAsu45M4w58DMb6dcMd1erec25',
            name: 'deployMode',
            displayName: '运行模式',
            operatorId: '5tyHdfcg918bb91e12y74eg345gdk',
            description: null,
            isGroupProperty: false,
            dataType: 'VARCHAR',
            valueDescription: null,
            valueInputDesc: null,
            valueMaxLength: null,
            valueMinLength: null,
            required: 0,
            hidden: 0,
            defaultValue: null,
            exdPropertyName: null,
            exdPropertyValue: null,
            relationPropertyName: null,
            relationPropertyValue: null,
            viewType: 'deploy-mode-picker',
            viewValueOptions: null,
            groupValues: null,
            multiple: 0,
            step: 0,
            viewGroupId: null,
            valueFrom: 'from_ui',
            inputSeq: 1,
            allowCreate: false,
            dataOutputType: null,
            metadataOutput: 0,
            valueRegexp: null,
            createTime: '2022-11-23 05:56:41',
            createUser: 'admin',
            updateTime: null,
            updateUser: null,
            value: runModel.value,
            inputPropertyId: '5FshAsu45M4w58DMb6dcMd1erec25',
          },
          {
            id: '8aFKw3fc4HfgdF7w58DMbrPcMd1w',
            name: 'sourceConfig',
            displayName: '源参数',
            operatorId: '5tyHdfcg918bb91e12y74eg345gdk',
            description: null,
            isGroupProperty: false,
            dataType: 'VARCHAR',
            valueDescription: null,
            valueInputDesc: null,
            valueMaxLength: null,
            valueMinLength: null,
            required: 1,
            hidden: 0,
            defaultValue: null,
            exdPropertyName: null,
            exdPropertyValue: null,
            relationPropertyName: null,
            relationPropertyValue: null,
            viewType: 'configs-picker',
            viewValueOptions: null,
            groupValues: null,
            multiple: 0,
            step: 0,
            viewGroupId: null,
            valueFrom: 'from_ui',
            inputSeq: 2,
            allowCreate: null,
            dataOutputType: null,
            metadataOutput: 1,
            valueRegexp: null,
            createTime: '2022-11-23 05:56:41',
            createUser: 'admin',
            updateTime: null,
            updateUser: null,
            value: JSON.stringify(cdcParams[0]),
            inputPropertyId: '8aFKw3fc4HfgdF7w58DMbrPcMd1w',
          },
          {
            id: 'd6Ea5ed4HfgdFgrt58DMbcMd1',
            name: 'sinkConfig',
            displayName: '目标参数',
            operatorId: '5tyHdfcg918bb91e12y74eg345gdk',
            description: null,
            isGroupProperty: false,
            dataType: 'VARCHAR',
            valueDescription: null,
            valueInputDesc: null,
            valueMaxLength: null,
            valueMinLength: null,
            required: 1,
            hidden: 0,
            defaultValue: null,
            exdPropertyName: null,
            exdPropertyValue: null,
            relationPropertyName: null,
            relationPropertyValue: null,
            viewType: 'configs-picker',
            viewValueOptions: null,
            groupValues: null,
            multiple: 0,
            step: 0,
            viewGroupId: null,
            valueFrom: 'from_ui',
            inputSeq: 2,
            allowCreate: null,
            dataOutputType: null,
            metadataOutput: 1,
            valueRegexp: null,
            createTime: '2022-11-23 05:56:41',
            createUser: 'admin',
            updateTime: null,
            updateUser: null,
            value: JSON.stringify(cdcParams[1]),
            inputPropertyId: 'd6Ea5ed4HfgdFgrt58DMbcMd1',
          },
        ],
        isDrillDown: false,
        configDatasource: 1,
        isLogOutput: false,
        isReportOutput: false,
      }).then((res) => {
        if (res.code != 200) {
          return proxy.$modal.msgError(res.msg);
        }
        proxy.$modal.msgSuccess(res.msg);
      });
    } else {
      return proxy.$modal.msgWarning('请选择需要同步的源表和目标表');
    }
  };

  onMounted(() => {
    if (workFlowType.value == 'edit') {
      // 回显 源和目标 数据下拉
      form.value.sourceDataType = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[0].value).configProperties.datasource
          .value,
      ).datasourceType;
      getType(form.value.sourceDataType);
      form.value.sourceDataSource = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[0].value).configProperties.datasource
          .value,
      ).datasourceId;
      getSourceDB(form.value.sourceDataSource);
      form.value.sourceDatabase = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[0].value).configProperties.datasource
          .value,
      ).databaseName;
      getSourceTable(form.value.sourceDatabase);
      // form.value.sourceTable = openWorkFlowData.value?.tableMapping[0].sourceSchema

      form.value.aimDataType = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties.datasource
          .value,
      ).datasourceType;
      getTarget(form.value.aimDataType);
      form.value.aimDataSource = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties.datasource
          .value,
      ).datasourceId;
      getTargetDB(form.value.aimDataSource);
      form.value.aimDatabase = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties.datasource
          .value,
      ).databaseName;
      getTargetTable(form.value.aimDatabase);
      // form.value.aimTable = openWorkFlowData.value?.tableMapping[0].destSchema

      // 回显请求

      // getSourceGP(form.value.sourceTable)

      // getGPTable(form.value.aimTable)

      const sourceTableName = [];
      sourceTableName[0] = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[0].value).configProperties.datasource
          .value,
      ).tableName;
      const destTableName = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties.datasource
          .value,
      ).tableName;

      runModel.value = openWorkFlowData.value?.inputProperties.filter(
        (item) => item.displayName == '运行模式',
      )[0].value;
      isCheck.value = JSON.parse(
        JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties.truncate
          .value,
      );

      nextTick(() => {
        treeIsshow();
        // 树结构勾选
        deptTreeRef.value?.setCheckedKeys(sourceTableName, false);

        // 表数据渲染
        tableList.value = [
          {
            tableName: sourceTableName[0],
            target: destTableName,
          },
        ];
        // 获取字段映射配置
        columnsMapping.value = JSON.parse(
          JSON.parse(openWorkFlowData.value?.inputProperties[2].value).configProperties
            .columnsMapping.value,
        );
        editField(tableList.value[0]);
        open.value = false;
      });
    }
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .btnGruop {
    text-align: center;
    margin-bottom: 25px;
  }

  .field-container-box {
    height: calc(100vh - 440px);
    overflow: scroll;
    .fieldContainer {
      height: auto;
      overflow: auto;
      position: relative;
      padding: 10px;

      .list {
        float: left;

        .listItem {
          height: 60px;
          text-align: center;
          line-height: 25px;
          padding-top: 5px;
          box-sizing: border-box;
          cursor: pointer;
          min-width: 150px;
          max-width: 150px;
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .listItenInner {
            position: relative;
            border: 1px solid #d8dade;
            color: #4e5370;
            border-radius: 2px;
            height: 52px;
            padding: 0 10px;
          }
        }
      }

      .left {
        margin-right: 160px;
      }
    }
  }
</style>
