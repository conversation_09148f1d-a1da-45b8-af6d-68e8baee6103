/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

module.exports = {
  root: true,
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-prettier/skip-formatting',
    './.eslintrc-auto-import.json',
    // Vue语法的ESLint插件
    'plugin:vue/vue3-recommended',
    // // 继承Vue官方提供的ESLint标准配置
    '@vue/eslint-config-standard',
    // // 继承Vue官方提供的ESLintPrettier标准配置
    '@vue/eslint-config-prettier',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
  },
  // extends: [
  // ],
  rules: {
    // 'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/multi-word-component-names': 'off',
    'vue/no-mutating-props': 'off',
    // 'prettier/prettier': 'off',
    camelcase: 'off',
  },
};
