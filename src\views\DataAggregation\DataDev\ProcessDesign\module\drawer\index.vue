<template>
  <el-drawer
    v-model="drawer"
    :show-close="true"
    size="40%"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    title="节点配置"
    @close="closeDrawer"
  >
    <el-form ref="nodeInfoFromRef" label-width="100px" :model="NodeData" :rules="formRules">
      <el-form-item label="算子名称">
        <span>
          {{ NodeData?.operatorName }}
        </span>
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <!-- <span>
          {{ NodeData?.nodeName }}
        </span> -->
        <el-input
          v-model="NodeData.nodeName"
          placeholder="请输入节点名称"
          :disabled="!CanvasActions"
          maxlength="20"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="前置任务" prop="nodeName">
        <el-select multiple v-model="NodeData.nodeName" placeholder="请选择前置任务"></el-select>
      </el-form-item> -->
    </el-form>

    <template v-if="ETLType">
      <OffLineEtl
        ref="OffLineETLRef"
        :workspace-id="workspaceId"
        :NodeData="NodeData"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="JDBCType">
      <JDBC
        ref="JDBCRed"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program == 'OFFLINE_ALG'">
      <offLineSync
        ref="offLineSyncRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
        @tab-remove="tabRemove"
      />
    </template>

    <template v-if="NodeData?.program == 'REALTIME_ALG'">
      <realSync
        ref="realSyncRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program == 'KETTLE_ALG'">
      <kettle
        ref="realSyncRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @open-child-work-flow-util="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program == 'HTTP_ALG'">
      <httpSync
        ref="httpSyncRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'ETL_REALTIME_TRANSFORM_ADDFIELD' ||
        NodeData?.program === 'ETL_OFFLINE_TRANSFORM_ADDFIELD'
      "
    >
      <newFields
        ref="newFieldsRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program === 'HIVE_SQL_ALG' || NodeData?.program === 'SPARK_SQL_ALG'">
      <hiveSql
        ref="hiveSqlSqlRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <!-- 通用 sql -->

    <template v-if="NodeData?.program === 'GENERAL_SQL_ALG'">
      <currentSql
        ref="currentSqlRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <!--  pyhtone 是 finksql -->

    <template v-if="NodeData?.program === 'FLINK_SQL_ALG'">
      <pythonSc
        ref="pythonScRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.operatorName === '数据脱敏'">
      <dataDesensitization
        ref="dataDesensitization"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.operatorName === '记录去重'">
      <recordDeduplication
        ref="recordDeduplication"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="coditionFType">
      <coditionFiltering
        ref="coditionFilteringRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.operatorName === '文件输入'">
      <fileInput
        ref="fileInputRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.operatorName === '文件输出'">
      <fileOutput
        ref="fileOutputRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program === 'HTTP_SOURCE_JDBC'">
      <httpInput
        ref="httpInputRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'ETL_REALTIME_SOURCE_KAFKA' ||
        NodeData?.program === 'ETL_REALTIME_SINK_KAFKA' ||
        NodeData?.program === 'REALTIME_ALG_SOURCE_KAFKA' ||
        NodeData?.program === 'REALTIME_ALG_SINK_KAFKA'
      "
    >
      <KafkaInput
        ref="fileInputRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'HTTP_ALG_SOURCE_API' || NodeData?.program === 'HTTP_ALG_BEFORE_API'
      "
    >
      <apiInput
        ref="apiInputRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program === 'SEATUNNEL_ALG'">
      <seatunnel
        ref="seatunnelRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program === 'SCRIPT_ALG'">
      <shell
        ref="shellRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'ETL_OFFLINE_SOURCE_HIVE' ||
        NodeData?.program === 'ETL_OFFLINE_SINK_HIVE'
      "
    >
      <hiveSync
        ref="hiveRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>
    <template
      v-if="
        NodeData?.program === 'ETL_OFFLINE_TRANSFORM_SQL' ||
        NodeData?.program === 'ETL_REALTIME_TRANSFORM_SQL' ||
        NodeData?.program === 'REALTIME_ALG_TRANSFORM_SQL'
      "
    >
      <executeSQL
        ref="hiveRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'ETL_OFFLINE_TRANSFORM_REPLACE' ||
        NodeData?.program === 'ETL_REALTIME_TRANSFORM_REPLACE'
      "
    >
      <StringSubstitution
        ref="StringSubstitutionRef"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template
      v-if="
        NodeData?.program === 'ETL_OFFLINE_TRANSFORM_SPLIT' ||
        NodeData?.program === 'ETL_REALTIME_TRANSFORM_SPLIT'
      "
    >
      <fieldSplitting
        ref="FieldSplitting"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>

    <template v-if="NodeData?.program === 'DATA_QUALITY_ALG'">
      <qualityControl
        ref="FieldSplitting"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>
    <template v-if="NodeData?.program === 'ETL_REALTIME_SOURCE_ACTIVEMQ'">
      <ACTIVEMQ
        ref="FieldSplitting"
        :NodeData="NodeData"
        :workspace-id="workspaceId"
        :CanvasActions="CanvasActions"
        @submit-drawer="submitDrawer"
        @close-drawer="closeDrawer"
        @tab-add-click="tabAddClick"
      />
    </template>
  </el-drawer>
</template>

<script setup>
  import apiInput from './components/apiInput/index.vue';
  import coditionFiltering from './components/conditionalFiltering/index.vue';
  import currentSql from './components/currencySql/index.vue';
  import dataDesensitization from './components/dataDesensitization/index.vue';
  import executeSQL from './components/executeSQL/index.vue';
  import fieldSplitting from './components/fieldSplitting/index.vue';
  import fileInput from './components/fileInput/index.vue';
  import fileOutput from './components/fileOutput/index.vue';
  import hiveSql from './components/hiveSql/index.vue';
  import hiveSync from './components/hiveSync/index.vue';
  import httpInput from './components/httpInput/index.vue';
  import httpSync from './components/httpSync/index.vue';
  import JDBC from './components/JDBC/index.vue';
  import KafkaInput from './components/kafka/index.vue';
  import kettle from './components/kettle/index.vue';
  import newFields from './components/newFields/index.vue';
  import OffLineEtl from './components/offLineETL/index.vue';
  import offLineSync from './components/offSync/index.vue';
  import pythonSc from './components/pythonSc/index.vue';
  import realSync from './components/realSync/index.vue';
  import recordDeduplication from './components/recordDeduplication/index.vue';
  import seatunnel from './components/seatunnel/index.vue';
  import shell from './components/shell/index.vue';
  import StringSubstitution from './components/StringSubstitution/index.vue';
  import qualityControl from './components/qualityControl/index.vue';
  import ACTIVEMQ from './components/ACTIVEMQ/index.vue';

  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store

  import { getFlowNodes } from '@/api/DataDev';
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const props = defineProps({
    drawer: {
      type: Boolean,
      default: false,
    },
    NodeData: {
      type: Object,
      default: {},
    },
    workspaceId: {
      type: String,
      default: null,
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
  });
  const { drawer, NodeData, CanvasActions } = toRefs(props);
  const formRules = ref({
    nodeName: [{ required: true, message: '请输入正确格式算子名称', trigger: 'blur' }],
  });
  const emit = defineEmits(['tabRemoveClick']);

  // 关闭对应tab
  const tabRemove = () => {
    emit('tabRemoveClick', NodeData.value.id);
  };

  // 使用计算属性判断 JDBC 类型
  const JDBCType = computed(() => {
    const jdbcPrograms = [
      'ETL_REALTIME_SINK_JDBC',
      'ETL_REALTIME_SOURCE_JDBC',
      'ETL_OFFLINE_SOURCE_JDBC',
      'ETL_OFFLINE_SINK_JDBC',
      'REALTIME_ALG_SOURCE_JDBC',
      'REALTIME_ALG_SINK_JDBC',
      'HTTP_ALG_SINK_JDBC',
    ];
    return jdbcPrograms.includes(NodeData.value?.program);
  });
  // ETLType
  const ETLType = computed(() => {
    const ETLPrograms = ['ETL_OFFLINE_ALG', 'ETL_REALTIME_ALG'];
    return ETLPrograms.includes(NodeData.value?.program);
  });
  const coditionFType = computed(() => {
    const coditionFPrograms = [
      'ETL_OFFLINE_TRANSFORM_FILTER',
      'ETL_REALTIME_TRANSFORM_FILTER',
      'REALTIME_ALG_TRANSFORM_FILTER',
    ];
    return coditionFPrograms.includes(NodeData.value?.program);
  });
  const closeDrawer = () => {
    // 清空
    qualityRulesStore.$reset();
    emit('update:drawer', false);
  };
  const submitDrawer = (e) => {
    emit('submitDrawer', e);
  };
  const tabAddClick = () => {
    emit('tabAddClick');
  };

  const getFlowNode = async () => {
    console.log(NodeData.value);
    const req = { flowId: 0, nodeId: 0, operatorId: 0 };
    await getFlowNodes(req);
  };
  onMounted(() => {
    getFlowNode();
  });
</script>

<style lang="scss" scoped></style>
