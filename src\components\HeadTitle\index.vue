<template>
  <el-row>
    <el-col :span="4">
      <div :style="headTitleCss">{{ props.title }}</div>
    </el-col>
    <el-col :span="13">
      <template v-if="pullDown">
        <el-form>
          <el-row>
            <el-col :span="12">
              <el-form-item v-if="isAdmin" label="租户">
                <el-select v-model="tenantId" placeholder="租户名称" @change="getWorkspace">
                  <el-option
                    v-for="data in tenantList"
                    :key="data.tenantId"
                    :label="data.tenantName"
                    :value="data.tenantId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作空间">
                <el-select v-model="workSpaceId" placeholder="工作空间" @change="getDatasourceList">
                  <el-option
                    v-for="data in workSpaceList"
                    :key="data.workspaceId"
                    :label="data.workspaceName"
                    :value="data.workspaceId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </el-col>
  </el-row>
  <el-divider v-if="dividerShow"></el-divider>
</template>

<script setup>
  import { getWorkspaceList } from '@/api/dataSourceManageApi';
  import { getInfo } from '@/api/login';
  import { getTenantList } from '@/api/system/user';
  const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || '';
  console.log(storageSetting.theme);

  const headTitleCss = reactive({
    fontSize: '20px',
    fontWeight: '600',
    color: storageSetting?.theme ? storageSetting.theme : '#303133',
  });

  const { proxy } = getCurrentInstance();

  const emit = defineEmits();

  // 组件接收传参
  const props = defineProps({
    title: {
      type: String,
      default: () => '',
    },

    // 下划线 显隐
    dividerShow: {
      type: Boolean,
      default: () => false,
    },

    // 租户和工作空间  显隐
    pullDown: {
      type: Boolean,
      default: () => false,
    },
  });

  const tenantList = ref([]);
  const workSpaceList = ref([]);
  const tenantId = ref(undefined);
  const isAdmin = ref(true);
  const workSpaceId = ref();

  // 获取工作空间
  const getWorkspace = (data) => {
    // sessionStorage.setItem('tenantId', tenantId.value)

    workSpaceList.value = [];
    workSpaceId.value = undefined;

    return new Promise((resolve, reject) => {
      getWorkspaceList({ tenantId: data })
        .then((res) => {
          if (res.data.length) {
            workSpaceList.value = res.data;
            const storedWorkSpaceId = getStoredValue('workSpaceId');
            const storedWorkspace = workSpaceList.value.find(
              (workspace) => workspace.workspaceId === storedWorkSpaceId,
            );
            workSpaceId.value = storedWorkspace
              ? storedWorkSpaceId
              : workSpaceList.value[0].workspaceId;
            getDatasourceList();
            resolve(workSpaceList.value);
          } else {
            workSpaceList.value = [];
            proxy.$modal.msgWarning('该租户下没有工作空间');
            reject('该租户下没有工作空间');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 获取数据源列表
  const getDatasourceList = () => {
    if (workSpaceId.value !== null) {
      const obj = { selectedWorkspaceId: workSpaceId.value, tid: tenantId };
      emit('update-list', obj);
    }
  };

  // Function to get information from persistent storage
  const getStoredValue = (key) => {
    const storedValue = localStorage.getItem(key);
    return storedValue ? JSON.parse(storedValue) : null;
  };

  // Function to set information in persistent storage
  const setStoredValue = (key, value) => {
    localStorage.setItem(key, JSON.stringify(value));
  };

  onMounted(() => {
    if (!props.pullDown) return;
    // 检索存储的值
    const storedTenantId = getStoredValue('tenantId');
    const storedWorkSpaceId = getStoredValue('workSpaceId');

    getInfo().then((res) => {
      // 判断是不是特定用户
      if (res.data.user.userType !== 'sys_user') {
        isAdmin.value = false;
        // 不是特定用户 直接请求 获取工作空间列表
        getWorkspace(res.data.user.tenantId)
          .then(() => {
            nextTick(() => {
              if (workSpaceList.value.length > 0) {
                console.log('res.data.user.tenantId', res.data.user.tenantId);
                // 如果与当前值不同，请使用存储值
                // 储存的值不为空 使用储存的值   为空使用 列表中的第一位
                // 查询workSpaceList.value 内是否有 storedWorkSpaceId 如果没有 使用第一位
                const storedWorkspace = workSpaceList.value.find(
                  (workspace) => workspace.workspaceId === storedWorkSpaceId,
                );
                workSpaceId.value = storedWorkspace
                  ? storedWorkSpaceId
                  : workSpaceList.value[0].workspaceId;
                // workSpaceId.value = storedWorkSpaceId !== null ? storedWorkSpaceId : workSpaceList.value[0].workspaceId;
                getDatasourceList();
              }
            });
          })
          .catch((error) => {
            console.error('Error fetching workspace:', error);
          });
      }
      // 不是特定用户
      else {
        isAdmin.value = true;
        // 先请求租户
        getTenantList().then((res) => {
          tenantList.value = res.data;

          if (tenantList.value.length > 0) {
            // 如果与当前值不同，请使用存储值
            // tenantId.value = storedTenantId !== null ? storedTenantId : tenantList.value[0].tenantId;

            // 检查 tenantList.value 中是否存在 storedTenantId
            const storedTenant = tenantList.value.find(
              (tenant) => tenant.tenantId === storedTenantId,
            );
            // console.log('storedTenant', storedTenant)
            // 如果存在，请使用 storedTenantId;否则，请使用列表中的第一个租户
            tenantId.value = storedTenant ? storedTenantId : tenantList.value[0].tenantId;

            // 请求工作空间
            getWorkspace(tenantId.value)
              .then(() => {
                nextTick(() => {
                  if (workSpaceList.value.length > 0) {
                    // 如果与当前值不同，请使用存储值
                    const storedWorkspace = workSpaceList.value.find(
                      (workspace) => workspace.workspaceId === storedWorkSpaceId,
                    );
                    workSpaceId.value = storedWorkspace
                      ? storedWorkSpaceId
                      : workSpaceList.value[0].workspaceId;

                    // workSpaceId.value = storedWorkSpaceId !== null ? storedWorkSpaceId : workSpaceList.value[0].workspaceId;
                    getDatasourceList();
                  }
                });
              })
              .catch((error) => {
                console.error('Error fetching workspace:', error);
              });
          }
        });
      }
    });

    // 当存储的值发生更改时，请更新存储值
    watch(tenantId, (newValue) => {
      if (newValue !== storedTenantId) {
        setStoredValue('tenantId', newValue);
      }
    });

    watch(workSpaceId, (newValue) => {
      if (newValue != undefined) {
        if (newValue !== storedWorkSpaceId) {
          setStoredValue('workSpaceId', newValue);
        }
      }
    });
  });
</script>
<style lang="scss" scoped>
  .head-title {
    font-size: 25px;
    font-weight: bold;
    // line-height: 40px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    // margin-bottom: 20px;
  }
</style>
