import request from '@/utils/request';
import { parseStrEmpty } from '@/utils/xugu';
import { encrypt, decrypt } from '@/utils/jsencrypt'; // 加密 解密

// 查询租户列表
export function getTenantList(query) {
  return request({
    url: `/system/user/getTenantList`,
    method: 'get',
    params: query,
  });
}

export function getTenantRoles(tenantId) {
  return request({
    url: '/system/user/?' + `tenantId=${parseStrEmpty(tenantId)}`,
    method: 'get',
  });
}

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get',
  });
}

// 新增用户
export function addUser(data) {
  // console.log('data', data)
  // console.log('data.data.password', data.password)
  //   if (data.password?.length < 20) {
  data.password = encrypt(data.password);
  //   }
  return request({
    url: '/system/user',
    method: 'post',
    data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data,
  });
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete',
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  //   if (password.length < 20) {
  password = encrypt(password);
  //   }

  const data = {
    userId,
    password,
  };

  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/getInfo',
    method: 'get',
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    ...(oldPassword ? { oldPassword: encrypt(oldPassword) } : {}),
    ...{ newPassword: encrypt(newPassword) },
  };

  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data,
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get',
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data,
  });
}

// 查询部门下拉树结构
// export function deptTreeSelect() {
//   return request({
//     url: '/system/user/deptTree',
//     method: 'get'
//   })
// }

// 获取导出日志
/// resource/catalogIO/records
export function getExportLog(params) {
  return request({
    url: '/resource/catalogIO/records',
    method: 'get',
    params,
  });
}
// 获取导入日志
// resource/catalogIO/records
export function getImportLog(params) {
  return request({
    url: '/resource/catalogIO/records',
    method: 'get',
    params,
  });
}
