<template>
  <div class="app-container">
    <splitpanes class="default-theme">
      <pane min-size="16" max-size="25" size="16" class="App-theme">
        <el-row class="head-title-tree"><span>流程列表</span></el-row>
        <el-row>
          <el-col :span="24">
            <el-input v-model="filterText" prefix-icon="Search" placeholder="请输入名称" />
          </el-col>

          <el-col :span="24">
            <el-tree-v2
              ref="treeRef"
              :data="dataTree"
              :props="props"
              :height="600"
              highlight-current="true"
              :filter-method="filterMethod"
              @node-click="handleNodeClick"
            >
            </el-tree-v2>
          </el-col>
        </el-row>
      </pane>
      <pane>
        <el-empty
          v-if="tableData.length === 0"
          description="暂无数据"
          :image-size="100"
          :image="EmptyImage"
        />
        <section v-else class="App-theme">
          <!-- 占位符 -->
          <!-- 暂无数据 -->
          <span class="TitleName">列表管理</span>
          <br />
          <br />

          <el-table
            ref="tableRef"
            row-key="date"
            :data="tableData"
            height="730px"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
            <el-table-column label="操作" fixed="right" min-width="200" width="240">
              <template #default="scope">
                <!--  编辑 -->
                <el-button type="text" size="small" @click="edit(scope)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :pager-count="maxCount"
            :total="total"
            @pagination="listPage"
          />
        </section>
      </pane>
    </splitpanes>
  </div>
</template>

<script setup>
  //   import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { queryGroupList, queryGroupMainFlowList } from '../../api/group';
  import { useRouter } from 'vue-router';
  import { ref } from 'vue';

  //   const store = useWorkFLowStore();
  //   const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `业务模块`, visible: true, prop: 'groupName' },
    { key: 1, label: `流程名称`, visible: true, prop: 'name' },
    { key: 2, label: `备注`, visible: true, prop: 'remark' },
    { key: 3, label: `更新时间`, visible: true, prop: 'updated' },
  ]);

  const maxCount = ref(5);
  const total = ref(0);
  const filterText = ref();
  const props = {
    value: 'id',
    label: 'groupName',
    children: 'children',
  };
  const dataTree = ref();

  onMounted(async () => {
    await getCatalogTreeUtil();
    await handleQuery();
    nextTick(() => {
      if (dataTree.value[0]) {
        document.querySelector('.el-tree-node__content').click();
      }
    });
  });

  const tableRef = ref();
  const tableData = ref([]);
  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },

        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      database: [
        { required: true, message: '请输入类型', trigger: 'blur' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { queryParams } = toRefs(data);

  const getCatalogTreeUtil = async () => {
    const res = await queryGroupList();
    if (res.code !== 200) return;
    dataTree.value = res.data;
  };

  const handleSelectionChange = (selection) => {};

  const handleNodeClick = async (data, e) => {
    if (!e) return proxy.$message.error('请选择一个节点');

    tableData.value = findFlowById(e.key)?.items.map((it) => ({
      ...it,
      groupName: findFlowById(e.key).name,
    }));
  };

  const findFlowById = (flowId) => {
    if (!flowId) return;
    return typeTableData.value.find((item) => item.id === flowId);
  };
  const router = useRouter();

  const edit = (data) => {
    if (!data.row) return;
    const to = '/flow/create?id=' + data.row.uniqueId + '&flowId=' + data.row.flowId;
    router.push(to);
  };
  const typeTableData = ref([]);
  const handleQuery = async () => {
    const res = await queryGroupMainFlowList();
    const { data } = res;
    for (const it of data) {
      it.showFlowList = true;
    }
    typeTableData.value = data;
  };

  watch(filterText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });

  const filterMethod = (value, data) => {
    if (!value) return true;
    return data.groupName.includes(value); // 使用节点的数据进行比较
  };
</script>

<style lang="scss" scoped>
  .app-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    padding: 1px;
    background: #ffffff;
    border-radius: 4px;
    // margin-top: 20px;
  }

  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .App-theme {
    // margin-top: 20px;
    // // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    overflow: auto;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }
  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
</style>
