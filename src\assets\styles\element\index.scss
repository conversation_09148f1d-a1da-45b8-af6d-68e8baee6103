$--colors: (
  "primary": (
    "base": rgb(0, 75, 0),
  ),
  "success": (
    "base": #00ff3c,
  ),
  "warning": (
    "base": #ff6600,
  ),
  "danger": (
    "base": #ff0000,
  ),
  "error": (
    "base": #800000,
  ),
  "info": (
    "base": #002631,
  ),
);

@forward "element-plus/theme-chalk/src/mixins/config.scss" with (
  $namespace: "ep"
);

@forward "element-plus/theme-chalk/src/common/var.scss" with (
  // 不要使用相同的名称，它将覆盖。
  $colors: $--colors,
  $button-padding-horizontal: ("default": 50px)
);

// 自定义暗色变量
@use "./dark.scss";
