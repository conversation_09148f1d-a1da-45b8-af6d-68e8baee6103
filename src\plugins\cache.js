const MAX_SIZE = 5 * 1024 * 1024; // 5MB

const checkStorageSize = (key, value) => {
  const allKeys = Object.keys(localStorage);
  let totalSize = 0;

  for (const k of allKeys) {
    totalSize += localStorage.getItem(k).length;
  }

  totalSize += value.length;

  return totalSize <= MAX_SIZE;
};

const sessionCache = {
  set(key, value) {
    if (!sessionStorage) {
      return;
    }
    if (key != null && value != null) {
      sessionStorage.setItem(key, value);
    }
  },
  get(key) {
    if (!sessionStorage) {
      return null;
    }
    if (key == null) {
      return null;
    }
    return sessionStorage.getItem(key);
  },
  setJSON(key, jsonValue) {
    if (jsonValue != null) {
        if (checkStorageSize(key, JSON.stringify(jsonValue))) {
          this.set(key, JSON.stringify(jsonValue));
        } else {
            throw new Error(`超出 sessionStorage 存储配额 ${MAX_SIZE} 字节`);
        }
    }
  },
  getJSON(key) {
    const value = this.get(key);
    if (value != null) {
      return JSON.parse(value);
    }
  },
  remove(key) {
    sessionStorage.removeItem(key);
  },
};
const localCache = {
  set(key, value) {
    if (!localStorage) {
      return;
    }
    if (key != null && value != null) {
      localStorage.setItem(key, value);
    }
  },
  get(key) {
    if (!localStorage) {
      return null;
    }
    if (key == null) {
      return null;
    }
    return localStorage.getItem(key);
  },
  setJSON(key, jsonValue) {
    if (jsonValue != null) {
        if (checkStorageSize(key, JSON.stringify(jsonValue))) {
          this.set(key, JSON.stringify(jsonValue));
        } else {
            throw new Error(`超出 localStorage 存储配额 ${MAX_SIZE} 字节`);
        }
    }
  },
  getJSON(key) {
    const value = this.get(key);
    if (value != null) {
      return JSON.parse(value);
    }
  },
  remove(key) {
    localStorage.removeItem(key);
  },
};

export default {
  /**
   * 会话级缓存
   */
  session: sessionCache,
  /**
   * 本地缓存
   */
  local: localCache,
};
