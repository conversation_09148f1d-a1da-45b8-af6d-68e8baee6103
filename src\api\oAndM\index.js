import { encrypt } from '@/utils/jsencrypt'; // 加密 解密
import request from '@/utils/request';
import { requestWithTimeout } from '@/utils/requestWithTimeout';

//! 主机
// http://10.28.12.156:8080/xugurtp-imagehub/machine/connect
// 测试主机链接
export function toConnect(data) {
  return request({
    url: '/xugurtp-imagehub/machine/connect',
    method: 'post',
    data,
  });
}
// http://10.28.12.156:8080/xugurtp-imagehub/machine/1
// 删除主机
export function deleteMachine(data) {
  return request({
    url: '/xugurtp-imagehub/machine/' + data,
    method: 'delete',
  });
}

// http://10.28.12.156:8080/xugurtp-imagehub/machine
// 修改主机
export function updateMachine(data, oldPs) {
  // 密码未加密，加密
  if (data.password?.length) {
    if (data.password !== oldPs) {
      data.password = encrypt(data.password);
    }
  }

  return request({
    url: '/xugurtp-imagehub/machine',
    method: 'put',
    data,
  });
}
// http://10.28.12.156:8080/xugurtp-imagehub/machine
// 新增主机
export function addMachine(data) {
  // 密码未加密，加密
  // if (data.password.length < 20) {
  data.password = encrypt(data.password);
  // }
  return request({
    url: '/xugurtp-imagehub/machine',
    method: 'post',
    data,
  });
}
// http://10.28.12.156:8080/xugurtp-imagehub/machine/list
// 查询主机列表
export function getMachineList(params) {
  return request({
    url: '/xugurtp-imagehub/machine/list',
    method: 'get',
    params,
  });
}

//! DockerImage
//  http://10.28.12.156:8080/xugurtp-imagehub/image
// 上传镜像

export function uploadImage(data, customTimeout = 30000) {
  return requestWithTimeout(
    {
      url: '/xugurtp-imagehub/image',
      method: 'post',
      data,
    },
    customTimeout,
  );
}
// http://10.28.12.156:8080/xugurtp-imagehub/image/list
// 查询镜像列表
export function getImageList(params) {
  return request({
    url: `/xugurtp-imagehub/image/list`,
    method: 'get',
    params,
  });
}

// http://10.28.12.156:8080/xugurtp-imagehub/machine/1
// 删除镜像
export function deleteImage(query) {
  return request({
    url: `/xugurtp-imagehub/image/${query}`,
    method: 'delete',
  });
}
// http://10.28.12.156:8080/xugurtp-imagehub/image/detail?imageId=6
// 查询镜像详情
export function getImageDetail(params) {
  return request({
    url: '/xugurtp-imagehub/image/detail',
    method: 'get',
    params,
  });
}

//  http://10.28.12.156:8080/xugurtp-imagehub/switchContainerTag
// 切换镜像版本
export function switchContainerTag(data, customTimeout = 30000) {
  return requestWithTimeout(
    {
      url: `/xugurtp-imagehub/image/switchContainerTag?imageId=${data}`,
      method: 'post',
    },
    customTimeout,
  );
}

// http://10.28.12.156:8080/xugurtp-imagehub/image/upload
export function uploadImageZpi(data, customTimeout) {
  return requestWithTimeout(
    {
      url: '/xugurtp-imagehub/image/upload',
      method: 'post',
      data,
    },
    customTimeout,
  );
}

// /xugurtp-imagehub/image/history/list
// 查询镜像历史版本
export function getImageHistoryList(params) {
  return request({
    url: '/xugurtp-imagehub/image/history/list',
    method: 'get',
    params,
  });
}
