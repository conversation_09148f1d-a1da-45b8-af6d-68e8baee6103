import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LoginData, LoginResult } from './types';

/**
 * 登录 API
 *
 * @param data {LoginData}
 * @returns
 */
export function loginApi(data: LoginData): AxiosPromise<LoginResult> {
  return request({
    url: '/xugurtp-flow/login/login',
    method: 'post',
    data: data,
  });
}

export function loginByTokenApi(token: string): AxiosPromise<LoginResult> {
  return request({
    url: '/xugurtp-flow/login/loginByToken?token=' + token,
    method: 'get',
  });
}
//钉钉登录
export function loginAtDingTalkApi(token: string): AxiosPromise<LoginResult> {
  return request({
    url: '/xugurtp-flow/login/loginAtDingTalk?authCode=' + token,
    method: 'get',
  });
}

/**
 * 注销 API
 */
export function logoutApi() {
  return request({
    url: '/xugurtp-flow/login/logout',
    method: 'post',
  });
}
