import { createApp } from 'vue';

import Cookies from 'js-cookie';

import ElementPlus from 'element-plus';
import locale from 'element-plus/es/locale/lang/zh-cn'; // 中文语言

import '@/assets/styles/index.scss'; // global css
// import '@/assets/text/text.css';

import App from './App';
// import store from './store'
import router from './router';
import directive from './directive'; // directive
import { createPinia } from 'pinia';
// 引入持久化插件
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

// 注册指令
import plugins from './plugins'; // plugins
import { download } from '@/utils/request';

// svg图标
import 'virtual:svg-icons-register';
import SvgIcon from '@/components/SvgIcon';
import elementIcons from '@/components/SvgIcon/svgicon';

import './permission'; // permission control

import { useDict } from '@/utils/dict';
// import { getConfigKey, updateConfigByKey } from "@/api/system/config";
import { updateConfigByKey } from '@/api/system/config';
import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
  selectDictLabels,
} from '@/utils/xugu';

// 加密 解密
import { encrypt, decrypt } from '@/utils/jsencrypt';

// 空数据的全局图片链接 后续全局图片依次增加
import imgUrlForEmpty from '@/assets/default/defaultData.png';

// 分页组件
import Pagination from '@/components/Pagination';
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar';
// 富文本组件
import Editor from '@/components/Editor';
// 文件上传组件
import FileUpload from '@/components/FileUpload';
// 图片上传组件
import ImageUpload from '@/components/ImageUpload';
// 图片预览组件
import ImagePreview from '@/components/ImagePreview';
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect';
// 字典标签组件
import DictTag from '@/components/DictTag';
// 导入导出模块组件
import ExportAndImport from '@/components/exportAndImport';

import 'element-plus/theme-chalk/src/message.scss';

import heartbeatService from '@/api/heartbeatService';

const app = createApp(App);

router.onError((error) => {
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    window.location.reload();
  } else if (error.message.includes('Failed to load module script:')) {
    window.location.reload();
  }
});

// 处理问题：线上页面停留一段时间，点击菜单，页面不跳转
window.addEventListener(
  'error',
  function (event) {
    // 判断是否为模块加载异常 Failed to load module script:
    if (event.target.tagName == 'LINK') {
      window.location.reload();
    }
  },
  true,
);

// 全局方法挂载
app.config.globalProperties.useDict = useDict;
// app.config.globalProperties.getConfigKey = getConfigKey
app.config.globalProperties.updateConfigByKey = updateConfigByKey;
app.config.globalProperties.download = download;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addDateRange = addDateRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.selectDictLabels = selectDictLabels;
app.config.globalProperties.encryptMin = encrypt;
app.config.globalProperties.decryptMin = decrypt;
app.config.globalProperties.imgUrlForEmpty = imgUrlForEmpty;

// 全局组件挂载
app.component('DictTag', DictTag);
app.component('ExportAndImport', ExportAndImport);
app.component('Pagination', Pagination);
app.component('TreeSelect', TreeSelect);
app.component('FileUpload', FileUpload);
app.component('ImageUpload', ImageUpload);
app.component('ImagePreview', ImagePreview);
app.component('RightToolbar', RightToolbar);
app.component('Editor', Editor);
const store = createPinia(piniaPluginPersistedstate);
// 将插件提供给store实例
store.use(piniaPluginPersistedstate);
app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.component('SvgIcon', SvgIcon);

directive(app);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default',
});
// 修改 el-dialog 默认点击遮照为不关闭
app._context.components.ElDialog.props.closeOnClickModal.default = false;

// 全局设置表格斑马纹
const ElementComponents = app._context.components;
// 这个选项用于控制表格是否显示斑马纹。
ElementComponents.ElTable.props.stripe = { type: Boolean, default: true };

// 这个选项用于控制表格是否显示边框。
ElementComponents.ElTable.props.border = { type: Boolean, default: false };

ElementComponents.ElTable.props.height = { type: String, default: '550' };

// 该选项用于设置表格的尺寸，可选值包括 'large', 'small' 'default'
ElementComponents.ElTable.props.size = { type: String, default: 'large' };

// 这个选项用于控制是否显示表格的头部。
ElementComponents.ElTable.props.showHeader = { type: Boolean, default: true };

// 该选项用于设置标签的显示效果，可选值包括 'dark', 'light', 'plain'。
ElementComponents.ElTag.props.effect = { type: String, default: 'plain' };

// 设置全局的ElTag为round 类型
ElementComponents.ElTag.props.round = { type: Boolean, default: true };

// 设置全局的按钮 为 round 类型
// ElementComponents.ElButton.props.round = { type: Boolean, default: true };

// 设置全局的按钮 显示效果，plain
// ElementComponents.ElButton.props.plain = { type: Boolean, default: true };

// 设置表单的 label-position
// ElementComponents.ElForm.props.labelPosition = { type: String, default: 'top' };

// 设置 表单的 size
// ElementComponents.ElForm.props.size = { type: String, default: 'default' };

// 设置 switch 的 active-icon
ElementComponents.ElSwitch.props.activeIcon = { type: String, default: 'Check' };

// 设置 开关的 inactive-icon
ElementComponents.ElSwitch.props.inactiveIcon = { type: String, default: 'Close' };

// 设置 inline-prompt 为 true 图标显示在内部
ElementComponents.ElSwitch.props.inlinePrompt = { type: Boolean, default: true };

// 设置 tabs 的 tab-position
// ElementComponents.ElTabs.props.tabPosition = { type: String, default: 'right' };

// 启动心跳检测
// heartbeatService.start();

// 监听窗口关闭或刷新事件以停止心跳
window.addEventListener('beforeunload', () => {
  heartbeatService.stop();
});

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    //    document.title = '------';
    heartbeatService.start();
  } else {
    //  document.title = '';
    heartbeatService.stop();
  }
});

app.mount('#app');

// import { getInfo } from "@/api/login";

// getInfo().then(res => {
// console.log('res', res.data.user.userType)
// if (res.data.user.userType != 'sys_user') {
// document.addEventListener('keydown', function (e) {
//   if (e.key === 'F12' || e.keyCode === 123) {
//     e.preventDefault();
//   }
// });

// document.addEventListener('contextmenu', function (e) {
//   e.preventDefault();
// });

// } else {
// }
// })
