import request from '@/utils/request';

// 创建kettle工作流
export function saveNode(data) {
  return request({
    // url: '/scheduler-adapter/workflow/createKettle',
    url: '/operator/algFlowNode/saveNode',
    method: 'post',
    data,
  });
}
// 编辑kettle工作流
export function editKettle(data) {
  return request({
    url: '/scheduler-adapter/workflow/edit',
    method: 'put',
    data,
  });
}
// 查询DS项目信息
export function getDS(data) {
  return request({
    url: '/scheduler-adapter/project',
    method: 'get',
    params: data,
  });
}
// 查询工作流列表
export function getWorkflowList(code, data) {
  return request({
    url: `/scheduler-adapter/workflow/list/${code}`,
    method: 'get',
    params: data,
  });
}
// 运行工作流
export function runWorkflow(codeT, data) {
  return request({
    url: `/operator/algFlow/runInScheduler/${codeT}/${data}`,
    // url: `/scheduler-adapter/workflow-instance/run/${codeT}/${data}`,
    method: 'get',
    // params: data
    params: {},
  });
}
// 删除工作流定义
export function DeleteWorkflow(codeT, data) {
  return request({
    url: `/scheduler-adapter/workflow/delete/${codeT}/${data}`,
    method: 'DELETE',
    params: data,
  });
}
// 创建流程
export function addWorkFlow(data) {
  return request({
    url: '/operator/algFlow/addWorkFlow',
    method: 'post',
    data,
  });
}
// 获取流程列表
export function getPage(params) {
  return request({
    url: '/operator/algFlow/page',
    method: 'get',
    params,
  });
}
// 删除流程
export function deleteById(params) {
  return request({
    url: `/operator/algFlow/delete/${params}`,
    method: 'delete',
  });
}
// 构建工作流节点
export function buildFlowNode(params) {
  return request({
    url: '/operator/algFlowNode/buildFlowNode',
    method: 'get',
    params,
  });
}

// 新增离线采集实例
export function createInstance(data) {
  return request({
    url: '/operator/offline-operator/createOrUpdate',
    method: 'post',
    data,
  });
}
// 回显回显kettle
export function kettleByCode(data) {
  return request({
    url: `/operator/algFlow/backShow/${data}`,
    method: 'get',
  });
}

// 创建调度配置SchedulingConfiguration
export function SchedulingConfiguration(data) {
  console.log('query', data);
  return request({
    url: `/operator/scheduler/create`,
    method: 'post',
    data,
  });
}

// 调度配置查询
export function schedulerPage(data) {
  return request({
    url: `/operator/scheduler/listPage`,
    method: 'post',
    data,
  });
}

// 调度配置更新
export function schedulerUpdate(data) {
  console.log('query', data);
  return request({
    url: `/operator/scheduler/update`,
    method: 'post',
    data,
  });
}
// 下拉模糊查询工作流
export function selectFlowBySearch(query) {
  console.log('query', query);
  return request({
    url: `/operator/algFlow/list?workSpaceId=${query}&searchVal=${''}`,
    method: 'get',
    // query
  });
}

// 删除调度配置
export function selectFlowDelete(params) {
  return request({
    url: `/operator/scheduler/delete`,
    method: 'get',
    params,
  });
}

// 调度配置上线
export function selectFlowOnline(data) {
  return request({
    url: `/operator/scheduler/online`,
    method: 'post',
    data,
  });
}
// 调度配置下线
export function selectFlowOFFLINE(query) {
  console.log('query', query);
  return request({
    url: `/operator/scheduler/offline`,
    method: 'get',
    params: query,
  });
}

// KEET回显
export function backShow(query) {
  console.log('query!!!!!!!!!!!!!!!!!!!', query);
  return request({
    url: `/operator/algFlow/backShow/${query}`,
    method: 'get',
    // params: query
  });
}

// 版本列表
export function versionsList(query) {
  return request({
    url: `/operator/flow/version/page`,
    method: 'get',
    params: query,
  });
}
// 版本删除
export function versionsDelete(query) {
  return request({
    url: `/operator/flow/version/delete`,
    method: 'delete',
    params: query,
  });
}
// 版本切换
export function versionsSwitch(data) {
  return request({
    url: `/operator/flow/version/rollback`,
    method: 'post',
    data,
  });
}

// /operator/scheduler/listScheduler
export function listScheduler(query) {
  return request({
    url: `/operator/scheduler/listScheduler`,
    method: 'get',
    params: query,
  });
}
