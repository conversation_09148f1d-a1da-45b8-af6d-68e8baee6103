<script setup>
  import TaskHandle from '@/views/flyflow/flyflow/components/task/handler/task.vue';

  import { onMounted, reactive, ref } from 'vue';

  import { getTask, queryMineTask } from '@/views/flyflow/flyflow/api/task';

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import applyApprovalDetail from '@/views/APIService/applyApproval/applyApprovalDetail';
  import { Position } from '@element-plus/icons-vue';
  import { useRoute } from 'vue-router';
  import pagination from '@/views/flyflow/flyflow/components/pagination.vue';
  import { copyToBoard, isNotBlank } from '@/views/flyflow/flyflow/utils/objutil';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const showPage = ref(false);
  const loading = ref(false);
  let choseRow = reactive({});
  const total = ref(0);

  const copy = (value) => {
    copyToBoard(value);
  };

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const roleList = ref();

  const taskHandler = ref();
  /**
   * 点击开始处理
   * @param row
   */
  const deal = (row) => {
    showPage.value = true;
    choseRow = row;
  };
  const deelDialog = (row) => {
    getTask(row.taskId).then((res) => {
      const d = {
        taskId: row.taskId,
        processInstanceId: row.processInstanceId,
        flowId: row.flowId,
      };

      taskHandler.value.deal(d);
    });
  };

  // 流程编码的表格宽度
  const processInstanceBizCodeWidth = ref(200);

  /**
   * 查询
   */
  function handleQuery() {
    loading.value = true;
    queryMineTask(queryParams)
      .then(({ data }) => {
        for (const itm of data.records) {
          const number = itm.processInstanceBizCode?.length * 12;
          if (number > processInstanceBizCodeWidth.value) {
            processInstanceBizCodeWidth.value = number;
          }
        }
        roleList.value = data.records;
        total.value = data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const route = useRoute();

  onMounted(() => {
    handleQuery();

    const query = route.query;

    if (isNotBlank(query.taskId)) {
      // 跳转过来的
      deal({ taskId: query.taskId });
    }
  });

  const searchForm = reactive({
    name: '',
    outputValue: '',
  });
  const getList = () => {
    const query = {
      ...searchForm,
      workspaceId: workspaceId.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
  };
  watch(workspaceId, (val) => {
    // getGroupList();
  });

  const handleCallback = () => {
    showPage.value = false;
    choseRow = {};
  };
</script>

<template>
  <div v-show="!showPage" class="app-container">
    <el-form ref="" v-model="searchForm" label-position="left" inline label-width="auto">
      <el-form-item label="审批流名称" prop="roleName">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入审批流名称"
          style="width: 250px"
          @change="handleQuery"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="审批状态" prop="roleName">
        <el-select
          v-model="searchForm.outputValue"
          placeholder="请选择"
          style="width: 250px"
          @change="handleQuery"
        >
          <el-option label="全部" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="roleName">
        <el-button type="primary" icon="Search" :disabled="false" @click="getList">
          <!-- @keyup.enter="handleQuery" -->
          查询
        </el-button>
      </el-form-item>
    </el-form>

    <el-table ref="dataTableRef" v-loading="loading" :data="roleList" highlight-current-row>
      <el-table-column label="规则ID" prop="groupName" min-width="100" />
      <el-table-column label="审批流名称" prop="processName" min-width="200" />
      <!-- <el-table-column -->
      <!-- label="编码" -->
      <!-- prop="processInstanceBizCode" -->
      <!-- :width="processInstanceBizCodeWidth" -->
      <!-- > -->
      <!-- <template #default="scope"> -->
      <!-- <el-text> -->
      <!-- <el-icon @click="copy(scope.row.processInstanceBizCode)"> -->
      <!-- <DocumentCopy /> -->
      <!-- </el-icon> -->
      <!-- {{ scope.row.processInstanceBizCode }} -->
      <!-- </el-text> -->
      <!-- </template> -->
      <!-- </el-table-column> -->
      <el-table-column label="关联服务" prop="rootUserName" width="150" />
      <el-table-column label="状态" prop="startTime" width="180" />
      <el-table-column label="审批时间" prop="taskName" width="150" />
      <!-- <el-table-column label="任务时间" prop="taskCreateTime" width="180" /> -->

      <!-- <el-table-column fixed="right" width="100" label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" link @click="deal(scope.row)">
            <el-icon>
              <Position />
            </el-icon>
            处理
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
  </div>
  <task-handle ref="taskHandler" @task-submit-event="handleQuery"></task-handle>
  <applyApprovalDetail
    v-if="showPage"
    :page-row="choseRow"
    @callback="handleCallback"
    @deel-flow="deelDialog"
  />
</template>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    padding: 20px;
    background: $--base-color-bg;
    // margin-top: 20px;
  }
</style>
