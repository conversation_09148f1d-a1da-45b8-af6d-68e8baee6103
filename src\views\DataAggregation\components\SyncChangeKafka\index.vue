<template>
  <b>2.编辑器-KS</b>
  <el-col :span="3">
    <el-tooltip placement="top">
      <template #content>
        <div class="box-item">
          <el-button
            icon="DocumentCopy"
            size="small"
            circle
            @click="copyInfo('formworkAPItoToken')"
          />
          <Codemirror v-model="formworkAPItoToken" :disabled-type="true" />
        </div>
      </template>
      <el-tag class="ml-2" type="warning" effect="light" round>实时同步kafka</el-tag>
    </el-tooltip>
  </el-col>
  <el-divider></el-divider>
  <el-col :span="24" style="margin-bottom: 20px">
    <div ref="el">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button
            icon="FullScreen"
            size="small"
            circle
            type="info"
            style="position: relative; bottom: -25px; z-index: 1"
            @click="toggle"
          />
        </el-col>
        <el-col :span="12">
          <!-- <el-tag @click="exit">退出全屏</el-tag> -->
        </el-col>
      </el-row>
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
    </div>
  </el-col>
  <el-button
    type="primary"
    plain
    style="margin-top: 20px; margin-bottom: 20px"
    @click="getParentData"
    >确定</el-button
  >
  <!-- <el-button plain @click="getParentData" style="margin-top: 20px; margin-bottom: 20px;" disabled="true">取消</el-button> -->
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { Base64 } from 'js-base64';
  import { useClipboard, useFullscreen } from '@vueuse/core';
  const el = ref();
  const { isFullscreen, toggle, enter, exit } = useFullscreen(el);
  // 监听键盘F10 如果点击了F10则执行全屏
  // window.onkeydown = function (event) {
  //   if (event.keyCode == 121) {
  //     enter()
  //   }
  // }
  const { copy } = useClipboard();

  const { proxy } = getCurrentInstance();
  const parentData = ref(''); // 创建一个 ref 来存储从子组件获取的数据

  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
  });

  const { flowId, nodeName, nodeId, openWorkFlowData, workFlowType } = toRefs(props);

  const formworkAPItoToken = `
--Flink的checkpoint时间，单位是毫秒
 set 'execution.checkpointing.interval' = '3000' ;
 --FLink时区
 set 'table.local-time-zone' = 'Asia/Shanghai';
 
 set 'jobmanager.memory.process.size' = '1024m';
 
 set 'taskmanager.memory.process.size' = '2048m';
 
 set 'taskmanager.numberOfTaskSlots' = '3';
  --Flink保留的checkpoint个数 默认值时保留一个 假如要保留3个,代码中无法设置
 set 'state.checkpoint.num-retained' = '3';
 

 set 'state.checkpoints.dir' = 'hdfs:/flink16/checkpoints/kafka_test';
 set 'state.savepoints.dir' = 'hdfs:/flink16/savepoints/kafka_test';


-- 拆分算子
set 'pipline.operator-chaining' = 'false';

--开启动态语句
set 'table.dynamic-table-options.enabled'= 'true';

-- kafka

drop table if exists kafka_test;
CREATE TABLE kafka_test (
  'type' int, 
  'data' ARRAY< ROW<
		type int ,
		'_id' STRING,
		DateTime ROW<
		'$date' bigint
		>,		
		'Value' double>>
) WITH (
    'connector' = 'kafka',  -- using kafka connector
    'topic' = 'xugu_6',  -- kafka topic
    'properties.bootstrap.servers' = '************:9092,************:9092,************:9092',  -- kafka broker address
	--'properties.fetch.max.bytes' = '524288000',
	--'properties.fetch.max.wait.ms' = '200',
	--'properties.max.poll.records' = '50000',
	'scan.startup.mode' = 'earliest-offset',  -- reading from the beginning
    'format' = 'json',  -- the data format is json
    'json.fail-on-missing-field' = 'false', -- 字段解析缺失，跳过
    'json.ignore-parse-errors' = 'true'  -- 忽略字段解析异常，会将字段解析为null
);

-- xugu

drop table if exists xugu_test;
CREATE TABLE xugu_test (
	'type' int,
	data_type int,
	data_id STRING,
	data_date bigint,
	data_value double
)with( 'connector'='jdbc',  
       'url'='****************************************************',
       'username'='SYSDBA', 
       'password'='SYSDBA',  
       'table-name'='xugu_test15',
	   'sink.max-retries' = '3',
	   'sink.buffer-flush.max-rows' = '500000',
	   'sink.buffer-flush.interval' = '1',
	   'sink.parallelism' = '6');



insert into xugu_test
select type, dtype,id, 'DateTime'.'$date' as date1,'Value' 
from kafka_test
CROSS JOIN UNNEST('data') AS t(dtype, id, 'DateTime','Value');
`;
  console.log('openWorkFlowData.value', openWorkFlowData.value);
  // 保存
  const getParentData = () => {
    // 判断是否有数据
    if (!parentData.value) {
      proxy.$modal.msgWarning('请先填写数据');
      return;
    }

    const query = {
      id: nodeId.value,
      operatorId: 'j937jdaf823rd92nda63crgvdiwe3bb',
      createTime: '',
      createUser: null,
      updateTime: null,
      updateUser: null,
      aliasName: null,
      nodeName: nodeName.value,
      flowId: flowId.value,
      parentFlowId: '',
      parentNodeId: '',
      nodeType: 'FLINK_SQL_ALG',
      parents: null,
      jobId: null,
      outputProperty: null,
      program: 'FLINK_SQL_ALG',
      operatorName: 'FlinkSQL',
      inputProperties: [
        {
          id: 'wd5g77ud23r345gdvhf42667c49',
          name: 'sqlText',
          displayName: 'SQL文本',
          operatorId: 'j937jdaf823rd92nda63crgvdiwe3bb',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          value: Base64.encode(parentData.value),
          inputPropertyId: 'wd5g77ud23r345gdvhf42667c49,',
          program: 'FLINK_SQL_ALG',
        },
      ],
      isDrillDown: false,
      configDatasource: 0,
      isLogOutput: false,
      isReportOutput: false,
    };

    saveNode(query).then((response) => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess('保存成功');
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  };

  const copyInfo = (data) => {
    if (data == 'formworkAPItoTable') {
      copy(formworkAPItoTable);
    } else if (data == 'formworkAPItoToken') {
      copy(formworkAPItoToken);
    }
    // 提示用户
    proxy.$modal.msgSuccess('复制成功');
  };
  onMounted(() => {
    if (workFlowType.value == 'edit') {
      parentData.value = Base64.decode(openWorkFlowData?.value?.inputProperties[0].value);
    }
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .box-item {
    white-space: pre-line;
    max-width: auto;
    max-height: 400px;
    overflow-y: auto;

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }
</style>
