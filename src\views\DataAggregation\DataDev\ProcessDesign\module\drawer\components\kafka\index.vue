<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item label="数据源" prop="DataSource">
      <el-select
        v-model="form.DataSource"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="getTopicUtil"
      >
        <el-option
          v-for="dict in DataSourceList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="Topic" prop="Topic">
      <el-select
        v-model="form.Topic"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="changeTopic"
      >
        <el-option
          v-for="dict in TopicList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="NodeData?.program == 'REALTIME_ALG_SINK_KAFKA'"
      label="批量写入大小"
      prop=""
    >
      <el-input
        v-model="form.batchSize"
        placeholder=""
        suffix="GB"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>
    <!-- <el-form-item label="结果表别名" prop="topicMatching"> -->
    <!-- <el-input v-model="form.topicMatching" placeholder="" suffix="GB" :disabled="!CanvasActions"></el-input> -->
    <!-- </el-form-item> -->

    <el-form-item
      v-if="
        NodeData?.program == 'ETL_REALTIME_SOURCE_KAFKA' ||
        NodeData?.program == 'REALTIME_ALG_SOURCE_KAFKA'
      "
      label="消费组"
      prop="consumerGroup"
    >
      <el-input
        v-model="form.consumerGroup"
        placeholder=""
        suffix="GB"
        :disabled="!CanvasActions"
        max="10"
        clearable
      ></el-input>
    </el-form-item>
    <el-form-item
      v-if="
        NodeData?.program == 'ETL_REALTIME_SOURCE_KAFKA' ||
        NodeData?.program == 'REALTIME_ALG_SOURCE_KAFKA'
      "
      label="初始策略"
      prop="consumptionInitialPolicy"
    >
      <el-select
        v-model="form.consumptionInitialPolicy"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option
          v-for="dict in consumptionInitialPolicyList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      v-if="NodeData?.program == 'REALTIME_ALG_SOURCE_KAFKA'"
      label="IF_Debezium"
      prop="Debezium"
    >
      <el-select
        v-model="form.Debezium"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option
          v-for="dict in DebeziumList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      v-if="
        NodeData?.program == 'ETL_REALTIME_SOURCE_KAFKA' ||
        NodeData?.program == 'REALTIME_ALG_SOURCE_KAFKA'
      "
      label="格式类型"
      prop="dataFormatType"
    >
      <el-select
        v-model="form.dataFormatType"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="switchDeploy"
      >
        <el-option
          v-for="dict in dataFormatTypeList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      v-if="
        NodeData?.program == 'ETL_REALTIME_SOURCE_KAFKA' ||
        NodeData?.program == 'REALTIME_ALG_SOURCE_KAFKA'
      "
      label="数据视图"
      prop="openDeploy"
    >
      <el-button type="text" :disabled="isDataSource" @click="openDeploy">配置</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog
    v-model="open"
    title="采样配置"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    @close="cancelEditField()"
  >
    <template v-if="form.dataFormatType != 'json'">
      <el-form label-position="right" label-width="auto">
        <el-form-item label="样本数据" prop="typeName">
          <el-input v-model="testConnection" placeholder="" type="textarea" show-word-limit />
        </el-form-item>

        <el-form-item label="分隔符" prop="typeName">
          <el-input v-model="separator" placeholder="" />
        </el-form-item>

        <el-form-item label="">
          <el-button plain @click="sampleAnl"> 样本解析 </el-button>
        </el-form-item>

        <el-form-item label="字段映射" prop="typeName">
          <el-button
            type="primary"
            plain
            :disabled="!CanvasActions"
            style="margin-left: 95%; margin-bottom: 1%"
            @click="addSyncChange"
          >
            添加
          </el-button>
          <!--  -->
          <el-table
            ref="request"
            :data="syncChangeList"
            row-class-name="rowClass"
            empty-text="暂无数据"
            height="240"
          >
            <el-table-column label="序号" type="index" width="60" />

            <el-table-column label="返回字段名" prop="jsonPath">
              <template #default="scope">
                <el-input v-model="scope.row.jsonPath" placeholder="" :disabled="!CanvasActions" />
              </template>
            </el-table-column>

            <el-table-column label="字段类型" prop="fieldType">
              <template #default="scope">
                <el-row style="width: 100%">
                  <el-col :span="12">
                    <el-select v-model="scope.row.fieldType" :disabled="!CanvasActions">
                      <el-option
                        v-for="items in customerIdList"
                        :key="items.id"
                        :label="items.label"
                        :value="items.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-input
                      v-if="scope.row.fieldType == 'decimal'"
                      v-model="scope.row.decimal"
                      placeholder="例(10,2)"
                    />
                  </el-col>
                </el-row>
              </template>
            </el-table-column>

            <el-table-column prop="fieldType">
              <template #header>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="不填写标准变量名时,该字段将不会向下传递或作为字段映射的数据源"
                  placement="top-start"
                >
                  <span>
                    字段标准名
                    <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                      <WarningFilled />
                    </el-icon>
                  </span>
                </el-tooltip>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.jsonField" placeholder="" :disabled="!CanvasActions" />
              </template>
            </el-table-column>

            <el-table-column v-if="NodeData?.program === 'HTTP_ALG_BEFORE_API'">
              <template #header>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="启用后,下游的 API 算子可将该字段作为参数 params 使用"
                  placement="top-start"
                >
                  <span>
                    将此字段向下传递
                    <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                      <WarningFilled />
                    </el-icon>
                  </span>
                </el-tooltip>
              </template>
              <template #default="scope">
                <el-switch v-model="scope.row.isOutputParam" :disabled="!CanvasActions"></el-switch>
              </template>
            </el-table-column>

            <el-table-column label="操作" fixed="right">
              <template #default="scope">
                <el-button icon="Delete" @click="deleteSyncChange(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </template>
    <!-- #region -->

    <template v-if="form.dataFormatType == 'json'">
      <el-form label-position="right" label-width="auto">
        <!-- <el-form-item label="样本数据" prop="typeName"> -->
        <!-- <div style="display: flex"> -->
        <!-- <div class="test-connection"> -->
        <!-- <json-viewer -->
        <!-- :key="true" -->
        <!-- :value="testConnection" -->
        <!-- copyable -->
        <!-- boxed -->
        <!-- :expanded="true" -->
        <!-- :expand-depth="25" -->
        <!-- /> -->
        <!-- </div> -->
        <div class="copy-text" @click="copyText">
          <el-tooltip class="box-item" content="复制" effect="light" placement="top-start">
            <el-icon>
              <CopyDocument />
            </el-icon>
          </el-tooltip>
        </div>

        <div
          v-if="testConnection != null"
          class="test-connection"
          :class="{ 'disabled-tree': !CanvasActions }"
        >
          <div v-for="(item, key) in testConnection" :key="key">
            <treeJson
              :item="item"
              :key-name="key"
              :root="true"
              :original-json="testConnection"
              :parent-json="testConnection"
              :expand-all="expandAll"
              @update="updatePath"
            >
            </treeJson>
          </div>
        </div>
        <!-- </div> -->
        <!-- </el-form-item> -->
        <el-form-item label="字段映射" prop="typeName">
          <el-button
            type="primary"
            plain
            :disabled="!CanvasActions"
            style="margin-left: 95%; margin-bottom: 1%"
            @click="addSyncChange"
          >
            添加
          </el-button>
          <!--  -->
          <el-table
            ref="request"
            :data="syncChangeList"
            row-class-name="rowClass"
            empty-text="暂无数据"
            height="240"
          >
            <el-table-column label="序号" type="index" width="60" />

            <el-table-column label="返回字段名" prop="jsonPath">
              <template #default="scope">
                <el-input v-model="scope.row.jsonPath" placeholder="" :disabled="!CanvasActions" />
              </template>
            </el-table-column>

            <el-table-column label="字段类型" prop="fieldType">
              <template #default="scope">
                <el-row style="width: 100%">
                  <el-col :span="12">
                    <el-select v-model="scope.row.fieldType" :disabled="!CanvasActions">
                      <el-option
                        v-for="items in customerIdList"
                        :key="items.id"
                        :label="items.label"
                        :value="items.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-input
                      v-if="scope.row.fieldType == 'decimal'"
                      v-model="scope.row.decimal"
                      placeholder="例(10,2)"
                    />
                  </el-col>
                </el-row>
              </template>
            </el-table-column>

            <el-table-column prop="fieldType">
              <template #header>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="不填写标准变量名时,该字段将不会向下传递或作为字段映射的数据源"
                  placement="top-start"
                >
                  <span>
                    字段标准名
                    <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                      <WarningFilled />
                    </el-icon>
                  </span>
                </el-tooltip>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.fieldName" placeholder="" :disabled="!CanvasActions" />
              </template>
            </el-table-column>

            <el-table-column v-if="NodeData?.program === 'HTTP_ALG_BEFORE_API'">
              <template #header>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="启用后,下游的 API 算子可将该字段作为参数 params 使用"
                  placement="top-start"
                >
                  <span>
                    将此字段向下传递
                    <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                      <WarningFilled />
                    </el-icon>
                  </span>
                </el-tooltip>
              </template>
              <template #default="scope">
                <el-switch v-model="scope.row.isOutputParam" :disabled="!CanvasActions"></el-switch>
              </template>
            </el-table-column>

            <el-table-column label="操作" fixed="right">
              <template #default="scope">
                <el-button icon="Delete" @click="deleteSyncChange(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </template>
    <!-- #endregion -->

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField()">取 消</el-button>
        <el-button type="primary" :disabled="!CanvasActions" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getDataSource, getTopic, getSampleDataKfk, getFiledType } from '@/api/DataDev';
  import treeJson from '@/components/treeJson/index';
  import { JsonViewer } from 'vue3-json-viewer';
  // 添加样式
  import 'vue3-json-viewer/dist/index.css';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const store = useWorkFLowStore();
  const expandAll = ref(false);
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();
  const { proxy } = getCurrentInstance();

  // 使用 计算属性 form.DataSource 是否 有 值
  const isDataSource = computed(() => {
    return !form.value.DataSource;
  });

  // 数据源列表
  const DataSourceList = ref([]);
  // Topic列表
  const TopicList = ref([]);
  // 消费格式类型
  const dataFormatTypeList = ref([
    { label: 'CSV', value: 'csv' },
    { label: 'TEXT', value: 'text' },
    { label: 'JSON', value: 'json' },
  ]);
  // 消费初始策略
  const consumptionInitialPolicyList = ref([
    { label: 'earliest', value: 'earliest' },
    { label: 'latest', value: 'latest' },
  ]);
  //  Debeziumlist
  const DebeziumList = ref([
    { label: '是', value: 'true' },
    { label: '否', value: 'false' },
  ]);

  const connectionParams = ref();
  const testConnection = ref();
  const allValues = ref([]);
  const DataSource = ref();
  const datasourceType = ref();

  const data = reactive({
    form: {
      // 数据源
      DataSource: '',
      // Topic
      Topic: '',
      // topic匹配符
      topicMatching: '',
      // 消费组
      consumerGroup: '',
      // 数据格式类型
      dataFormatType: '',
      // 消费初始策略
      consumptionInitialPolicy: '',
    },
    rules: {
      DataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
      Topic: [{ required: true, message: '请选择Topic', trigger: 'blur' }],
      topicMatching: [{ required: true, message: '请选择结果表别名', trigger: 'change' }],
      consumerGroup: [{ required: true, message: '请输入消费组', trigger: 'blur' }],
      dataFormatType: [{ required: true, message: '请选择数据格式类型', trigger: 'blur' }],
      consumptionInitialPolicy: [
        { required: true, message: '请选择消费初始策略', trigger: 'change' },
      ],
      Debezium: [{ required: true, message: '请选择是否Debezium数据', trigger: 'change' }],
    },
  });

  const { form, rules } = toRefs(data);

  const cancelDrawer = () => {
    form.value = {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      checkpointInterval: '',
    };
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;

    DataSource.value = {
      datasourceId: form.value.DataSource,
      datasourceType: 'KAFKA',
      connectionParams: connectionParams.value,
      tableNames: form.value.Topic,
    };


    syncChangeList.value.forEach(items=>{
        items.jsonField =items.fieldName
    })


    // 实时ETL 输入
    if (NodeData.value?.program == 'ETL_REALTIME_SOURCE_KAFKA') {
      NodeData.value.inputProperties.forEach((property, index) => {
        switch (index) {
          case 0:
            // property.value = NodeData.value?.program != 'ETL_REALTIME_SINK_KAFKA' ? '10.28.23.131:9092' : JSON.stringify(DataSource.value);
            property.value = form.value.topicMatching;
            break;
          case 1:
            property.value = separator.value;
            break;
          case 2:
            property.value =
              NodeData.value?.program != 'ETL_REALTIME_SINK_KAFKA'
                ? JSON.stringify(DataSource.value)
                : property.defaultValue;
            break;
          case 3:
            property.value = JSON.stringify(testConnection.value);
            break;
          case 4:
            property.value = form.value.dataFormatType;
            break;
          case 5:
            property.value = form.value.consumerGroup;
            break;
          case 6:
            property.value = form.value.consumptionInitialPolicy;
            break;
          case 8:
            property.value = JSON.stringify(syncChangeList.value);
            break;
          case 9:
            property.value = form.value.Topic;
            break;
          default:
            property.value = property.defaultValue;
            break;
        }
      });
    } // 实时ETL 输出
    else if (NodeData.value?.program == 'ETL_REALTIME_SINK_KAFKA') {
      NodeData.value.inputProperties.forEach((property, index) => {
        switch (index) {
          case 0:
            property.value = JSON.stringify(DataSource.value);
            break;
          case 1:
            property.value = form.value.Topic;
            break;
          default:
            property.value = property.defaultValue;
            break;
        }
      });
    }
    // 实时同步 输入
    if (NodeData.value?.program == 'REALTIME_ALG_SOURCE_KAFKA') {
      DataSource.value = {
        datasourceId: form.value.DataSource,
        datasourceType: 'KAFKA',
        connectionParams: connectionParams.value,
        tableName: form.value.Topic,
      };
      console.log(syncChangeList.value);
      // 循环 syncChangeList.value 更改内容 value field  type  返回新的数组
      const newSyncChangeList = syncChangeList.value.map((item) => {
        return {
          value: item.jsonPath,
          field: item.fieldName,
          type: item.fieldType,
        };
      });

      const newSyncChangeList2 = syncChangeList.value.map((item) => {
        return {
          source: item.jsonPath,
          target: item.fieldName,
        };
      });

      NodeData.value.inputProperties.forEach((property, index) => {
        switch (index) {
          case 0:
            property.value = JSON.stringify(DataSource.value);
            break;
          case 1:
            property.value = form.value.consumerGroup;
            break;
          case 2:
            property.value = form.value.consumptionInitialPolicy;
            break;
          case 3:
            property.value = form.value.Debezium;
            break;
          case 4:
            property.value = form.value.dataFormatType;
            break;
          case 5:
            property.value = JSON.stringify(
              form.value.dataFormatType == 'json' ? newSyncChangeList2 : newSyncChangeList,
            );
            break;
          case 6:
            property.value = separator.value;
            break;
          case 7:
            property.value = JSON.stringify(testConnection.value);
            break;
        }
      });
    } else if (NodeData.value?.program == 'REALTIME_ALG_SINK_KAFKA') {
      DataSource.value = {
        datasourceId: form.value.DataSource,
        datasourceType: 'KAFKA',
        connectionParams: connectionParams.value,
        tableName: form.value.Topic,
      };
      NodeData.value.inputProperties.forEach((property, index) => {
        switch (index) {
          case 0:
            property.value = JSON.stringify(DataSource.value);
            break;
          case 1:
            property.value = form.value.batchSize;
            break;
        }
      });
    }

    emit('submitDrawer', NodeData.value);
  };

  const init = async () => {
    // 实时ETL 输入
    if (NodeData.value?.program == 'ETL_REALTIME_SOURCE_KAFKA') {
      dataFormatTypeList.value = [
        { label: 'TEXT', value: 'TEXT' },
        { label: 'JSON', value: 'json' },
      ];

      const setInputPropertyValue = (index) => {
        const property = NodeData.value.inputProperties[index];
        const value = property.value ? property.value : property.defaultValue;
        form.value[getPropertyKey(index)] = value;
      };

      form.value.topicMatching = NodeData.value.inputProperties[0].value
        ? NodeData.value.inputProperties[0].value
        : '';

      separator.value = NodeData.value.inputProperties[1].value
        ? NodeData.value.inputProperties[1].value
        : '';

      form.value.DataSource = NodeData.value.inputProperties[2].value
        ? JSON.parse(NodeData.value.inputProperties[2].value).datasourceId
        : '';

      await getDataSourceUtil();
      await getTopicUtil();

      form.value.Topic = NodeData.value.inputProperties[2].value
        ? JSON.parse(NodeData.value.inputProperties[2].value).tableNames
        : '';

      syncChangeList.value = NodeData.value.inputProperties[8].value
        ? JSON.parse(NodeData.value.inputProperties[8].value)
        : [];

      const getPropertyKey = (index) => {
        switch (index) {
          case 0:
            return 'topicMatching';
          case 4:
            return 'dataFormatType';
          case 5:
            return 'consumerGroup';
          case 6:
            return 'consumptionInitialPolicy';
          case 9:
            return 'Topic';
          default:
            return '';
        }
      };

      setInputPropertyValue(0);
      setInputPropertyValue(4);
      setInputPropertyValue(5);
      setInputPropertyValue(6);
      setInputPropertyValue(9);

      testConnection.value = NodeData.value.inputProperties[3].value
        ? form.value.dataFormatType === 'json'
          ? JSON.parse(NodeData.value.inputProperties[3].value)
          : NodeData.value.inputProperties[3].value
        : '';
    } // 实时ETL 输出
    else if (NodeData.value?.program == 'ETL_REALTIME_SINK_KAFKA') {
      form.value.DataSource = NodeData.value.inputProperties[0].value
        ? JSON.parse(NodeData.value.inputProperties[0].value).datasourceId
        : '';
      await getDataSourceUtil();
      await getTopicUtil();

      form.value.Topic = NodeData.value.inputProperties[1].value
        ? NodeData.value.inputProperties[1].value
        : NodeData.value.inputProperties[1].defaultValue;
    }

    // 实时同步 输入
    if (NodeData.value?.program == 'REALTIME_ALG_SOURCE_KAFKA') {
      dataFormatTypeList.value = [
        { label: 'CSV', value: 'csv' },
        { label: 'JSON', value: 'json' },
      ];

      const setInputPropertyValue = (index) => {
        const property = NodeData.value.inputProperties[index];
        const value = property.value ? property.value : property.defaultValue;
        form.value[getPropertyKey(index)] = value;
      };

      form.value.topicMatching = NodeData.value.inputProperties[0].value
        ? NodeData.value.inputProperties[0].value
        : '';

      separator.value = NodeData.value.inputProperties[6].value
        ? NodeData.value.inputProperties[6].value
        : '';

      form.value.DataSource = NodeData.value.inputProperties[0].value
        ? JSON.parse(NodeData.value.inputProperties[0].value).datasourceId
        : '';

      await getTopicUtil();

      form.value.Debezium = NodeData.value.inputProperties[3].value
        ? NodeData.value.inputProperties[3].value
        : '';

      form.value.Topic = NodeData.value.inputProperties[0].value
        ? JSON.parse(NodeData.value.inputProperties[0].value).tableName
        : '';

      testConnection.value = NodeData.value.inputProperties[7].value
        ? JSON.parse(NodeData.value.inputProperties[7].value)
        : '';

      syncChangeList.value = NodeData.value.inputProperties[5].value
        ? // 循环 syncChangeList.value 更改内容 value field  type
          JSON.parse(NodeData.value.inputProperties[5].value).map((item) => {
            item.jsonPath = item.value;
            item.fieldName = item.field;
            item.fieldType = item.type;
            delete item.value;
            delete item.field;
            delete item.type;
            return item;
          })
        : [];

      const getPropertyKey = (index) => {
        switch (index) {
          case 1:
            return 'consumerGroup';
          case 2:
            return 'consumptionInitialPolicy';
          case 4:
            return 'dataFormatType';

          default:
            return '';
        }
      };
      setInputPropertyValue(1);
      setInputPropertyValue(2);
      setInputPropertyValue(4);

      getDataSourceUtil();
    } // 实时同步 输出
    else if (NodeData.value?.program == 'REALTIME_ALG_SINK_KAFKA') {
      form.value.DataSource = NodeData.value.inputProperties[0].value
        ? JSON.parse(NodeData.value.inputProperties[0].value).datasourceId
        : '';
      getTopicUtil();
      getDataSourceUtil();

      form.value.Topic = NodeData.value.inputProperties[0].value
        ? JSON.parse(NodeData.value.inputProperties[0].value).tableName
        : NodeData.value.inputProperties[0].defaultValue;

      form.value.batchSize = NodeData.value.inputProperties[1].value
        ? NodeData.value.inputProperties[1].value
        : NodeData.value.inputProperties[1].defaultValue;
    }

    // getTopicUtil()
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };
  const operChange = () => {
    console.log(' ', form.value.operationModel);
  };
  const getDataSourceUtil = async () => {
    const res = await getDataSource({
      type: 'KAFKA',
      workSpaceId: store.getWorkSpaceId(),
    });

    if (res.code !== 200) return;

    DataSourceList.value = res.data.map((item) => {
      return {
        ...item,
        label: item.name,
        value: item.id,
      };
    });
    connectionParams.value = res.data[0]?.connectionParams;
    datasourceType.value = res.data[0]?.type;
  };

  const getTopicUtil = async () => {
    form.value.Topic = '';

    if (!form.value.DataSource) return;

    const res = await getTopic({ datasourceId: form.value.DataSource });
    if (res.code !== 200) return;

    TopicList.value = res.data.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    const changeData = DataSourceList.value.find((item) => item.id === form.value.DataSource);
    connectionParams.value = changeData?.connectionParams;
    datasourceType.value = changeData?.type;
  };

  const changeTopic = () => {
    //     form.value.consumerGroup = ''
    //     form.value.dataFormatType = ''
    //     form.value.consumptionInitialPolicy = ''
    if (!NodeData.value.inputProperties[2]) return;

    if (form.value.Topic != JSON.parse(NodeData.value.inputProperties[2].value)?.tableNames) {
      syncChangeList.value = [];
    }
  };

  const open = ref(false);

  const syncChangeList = ref([]);

  const customerIdList = ref([
    {
      id: 'ID',
      label: 'ID',
    },
    {
      id: 'String',
      label: 'String',
    },
  ]);
  const requestQueryList = ref([
    {
      id: 'Custom',
      label: '自定义',
    },
    {
      id: 'Sign',
      label: '签名串',
    },
    {
      id: 'Precondition',
      label: '前置条件',
    },
  ]);

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    // 生成唯一的key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  const openDeploy = async () => {
    await getFiledTypeUtil();
    if (syncChangeList.value.length != 0) {
      open.value = true;
    } else {
      const isSuccess = await getSampleDataKfkUtil();
      if (!isSuccess) {
        console.log('获取数据失败，不继续执行');
        return;
      }
      open.value = true;
    }
  };
  const cancelEditField = () => {
    open.value = false;
  };
  const submit = () => {
    // // 判断是否有空的
    // const isNull = syncChangeList.value.some(item => !item.fieldName)
    // if (isNull) {
    //     return proxy.$message.error('标准变量名不能为空')
    // }
    // 不能有中文
    const isChinese = syncChangeList.value.some((item) => {
      const reg = /[\u4e00-\u9fa5]/g;
      return reg.test(item.fieldName);
    });
    if (isChinese) {
      return proxy.$message.error('标准变量名不能有中文');
    }
    // 不能有空格
    const isSpace = syncChangeList.value.some((item) => {
      const reg = /\s+/g;
      return reg.test(item.fieldName);
    });
    if (isSpace) {
      return proxy.$message.error('标准变量名不能有空格');
    }
    // 判断fieldName 值是否有重复的 如果有 提示用户
    // 只获取jsonPath有值的fieldName
    const fieldNameList = syncChangeList.value
      .filter((item) => item.jsonPath && item.fieldName)
      .map((item) => item.fieldName);

    const set = new Set(fieldNameList);

    if (set.size !== fieldNameList.length) {
      return proxy.$message.error('标准变量名不能重复');
    }
    open.value = false;
  };

  const getSampleDataKfkUtil = async () => {
    const query = {
      datasourceId: form.value.DataSource,
      topic: form.value.Topic,
      selectedFormat: form.value.dataFormatType,
    };
    const res = await getSampleDataKfk(query);

    if (res.code != 200) {
      proxy.$message.error(res.message);
      return false; // 返回 false 表示获取数据失败
    }
    if (!res.data) {
      proxy.$message.error('未获取到数据');
      return false; // 返回 false 表示获取数据失败
    }

    testConnection.value = res.data;

    if (form.value.dataFormatType == 'json') {
      testConnection.value = JSON.parse(res.data);

      //   const rese = Object.keys(testConnection.value).map((item) => {
      //     return {
      //       jsonPath: item,
      //       fieldType: 'string',
      //       fieldName: item,
      //     };
      //   });
      //   syncChangeList.value = rese;
    }

    return true; // 返回 true 表示获取数据成功
  };
  const separator = ref();

  const sampleAnl = () => {
    // 根据传递的分割符 进行解析 testConnection.value
    const res = testConnection.value.split(separator.value);
    // 把解析的 数据放入到 syncChangeList.value 中
    syncChangeList.value = res.map((item) => {
      return {
        jsonPath: item,
        fieldType: 'string',
        fieldName: null,
      };
    });
    console.log('syncChangeList.value', syncChangeList.value);
  };
  const updatePath = () => {
    const { path, isChecked } = store.pare;

    // 如果 path 里有 [数字]，则将 [数字] 替换为 [*]
    const reg = /\[\d+\]/g;
    const newPath = path.replace(reg, '[*]');
    // 处理 newPath 只保留 . 后面的数据
    const index = newPath.lastIndexOf('.');
    const newNewPath = newPath.substring(index + 1);
    const val = {
      jsonPath: newPath,
      jsonField: newNewPath,
      fieldName: newNewPath,
      fieldType: 'string',
      isParam: false,
      isOutputParam: NodeData.value.program === 'HTTP_ALG_BEFORE_API',
      isCustom: false,
    };

    if (!isChecked) {
      // 追加之前，先检查 fieldMappings.value 中是否已经存在该元素，如果存在，则不追加
      if (syncChangeList.value.findIndex((item) => item.jsonPath === newPath) === -1) {
        syncChangeList.value.push(val);
      } else {
        // 提示用户
        proxy.$modal.msgError('不能添加重复的字段');
      }
    } else {
      syncChangeList.value = syncChangeList.value.filter((item) => item.jsonPath != path);
    }
  };

  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'flink',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };
  const switchDeploy = () => {
    // 提示用户 切换后 之前的数据会清空
    proxy
      .$confirm('切换此项会导致采样配置清空，请谨慎操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        // 点击确定清空
        syncChangeList.value = [];
      })
      .catch(() => {
        // 点击取消 重置为之前的值
        form.value.dataFormatType = NodeData.value.inputProperties[4].value
          ? NodeData.value.inputProperties[4].value
          : NodeData.value.inputProperties[4].defaultValue;
      });
  };
  onMounted(() => {
    init();
  });

  watch(NodeData, () => {
    // console.log(1);
    init();
  });

  const copyText = () => {
    const textarea = document.createElement('textarea');
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = JSON.stringify(testConnection.value);
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    proxy.$modal.msgSuccess('复制成功');
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .per {
    height: 300px;
    overflow: auto;
    // 使用原样展示
    white-space: pre-wrap;
  }

  .test-connection {
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
    // max-width: 100%;
    // min-width: 100%;
    // max-width: 50%;
    // min-width: 50%;
    border: 1px solid #ebeef5;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      border-left: 1px solid #ebeef5;
    }
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }

  .copy-text {
    display: flex;
    justify-content: flex-end;
  }
</style>
