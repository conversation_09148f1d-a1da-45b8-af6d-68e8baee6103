<template>
  <el-form
    ref="dataSourceRef"
    style="margin-top: 30px"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item :label="form.formItemO" prop="split_field">
      <!-- 下拉框 -->
      <el-select
        v-model="form.split_field"
        placeholder="请选择"
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option v-for="dict in metadataList" :key="dict" :label="dict" :value="dict" />
      </el-select>
    </el-form-item>
    <el-form-item :label="form.formItemT" prop="separator">
      <el-input v-model="form.separator" placeholder="请输入" :disabled="!CanvasActions"></el-input>
    </el-form-item>
    <el-form-item :label="form.formItemTH" prop="tableData">
      <el-table
        ref="tableRef"
        :data="form.tableData"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        border="1"
        size="mini"
        height="260"
        empty-text="暂无数据"
        style="min-height: 150px"
      >
        <!-- <el-table-column type="index" width="50"></el-table-column> -->
        <!-- 根据定义的 columns 循环生成 -->
        <el-table-column v-for="(item, index) in busKeyCol" :key="index" v-bind="item">
          <template #default="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.' + item.prop"
              :rules="busKeyRules[item.prop]"
            >
              <el-input
                v-model="scope.row[item.prop]"
                placeholder="Please input"
                :disabled="!CanvasActions"
              >
                <template #append>
                  <el-button
                    icon="Delete"
                    :disabled="!CanvasActions"
                    @click="deleteSyncChange(scope.$index)"
                  />
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        type="text"
        style="margin-left: 40%"
        :disabled="!CanvasActions"
        @click="addSyncChange"
      >
        添加
      </el-button>
    </el-form-item>
    <el-form-item v-if="false" label="结果表别名">
      <el-input
        v-model="form.tableAliases"
        :placeholder="form.tableAliasesPlaceholder"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>
  </el-form>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import { getNodeData } from '@/api/DataDev';
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const tableData = ref([]);

  const data = reactive({
    form: {
      split_field: '',
      separator: '',
      output_fields: '',
      tableData: [],
    },
    rules: {
      split_field: [{ required: true, message: '请选择源字段', trigger: 'blur' }],
      separator: [{ required: true, message: '请选择分隔符', trigger: 'blur' }],
      tableData: [{ required: true, message: '请选择目标字段', trigger: 'blur' }],
    },
  });

  const { form, rules } = toRefs(data);

  const metadataList = ref([]);

  const getMetaData = async () => {
    try {
      const res = await getNodeData(NodeData.value.id);
      if (res.code !== 200) return;
      if (res.data && res.data.metadata.length) {
        const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
        if (currentIndex > 0) {
          const previousNode = res.data.metadata[currentIndex - 1];
          metadataList.value = previousNode
            ? previousNode.columns.map((item) => item?.columnName)
            : [];
        } else if (res.data.metadata.length === 1) {
          const currentNode = res.data.metadata[0];
          metadataList.value = currentNode
            ? currentNode.columns.map((item) => item?.columnName)
            : [];
        } else {
          metadataList.value = res.data.metadata[res.data.metadata.length - 1].columns.map(
            (item) => item?.columnName,
          );
        }
      }
      //   metadataList.value = res.data.metadata[0].columns.map((item) => item?.columnName);
    } catch (error) {
      console.error('Error fetching metadata:', error);
    }
  };

  const init = () => {
    form.value = {
      split_field: '',
      separator: '',
      tableAliases: '',
    };
    tableData.value = [];

    getMetaData();
    form.value.formItemO = NodeData.value.inputProperties[0].displayName;
    form.value.formItemT = NodeData.value.inputProperties[1].displayName;
    form.value.formItemTH = NodeData.value.inputProperties[2].displayName;
    form.value.tableAliasesPlaceholder = NodeData.value.inputProperties[1].description;

    form.value.split_field = NodeData.value.inputProperties[0].value;
    form.value.separator = NodeData.value.inputProperties[1].value;
    form.value.tableData = JSON.parse(NodeData.value.inputProperties[2].value)
      ? JSON.parse(NodeData.value.inputProperties[2].value)
      : [];
    form.value.tableAliases = NodeData.value.inputProperties[3].value;
  };

  function deleteSyncChange(index) {
    form.value.tableData.splice(index, 1);
  }

  function DataProcessing() {
    NodeData.value.inputProperties[0].value = form.value.split_field;
    NodeData.value.inputProperties[1].value = form.value.separator;
    NodeData.value.inputProperties[2].value = JSON.stringify(form.value.tableData);
    NodeData.value.inputProperties[3].value = form.value.tableAliases;
  }
  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const { proxy } = getCurrentInstance();

  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    await emit('submitDrawer', NodeData.value);
  };

  const busKeyCol = [
    {
      prop: 'value',
      label: '字段名',
      minWidth: 100,
      tooltip: true,
      timestamp: true,
    },
  ];
  const busKeyRules = {
    value: [
      { required: true, message: '请输入', trigger: 'change' },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '只允许大小写字符，下划线，数字',
        trigger: 'blur',
      },
    ],
  };
  const addHeaderCellClassName = (params) => {
    // const { columnIndex } = params;
    // if (columnIndex === 0) {
    //   return 'required';
    // }
  };

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      value: '',
      //   isRegex: '',
      //   oldStr: '',
      //   newStr: '',
      //   isCaseSensitive: '',
      //   targetField: '',
    };
    form.value.tableData.push(newSyncChange);
    nextTick(() => {
      scrollToBottomOfTable();
    });
  }
  const scrollToBottomOfTable = () => {
    setTimeout(() => {
      const lastIndex = form.value.tableData.length - 1;
      const newRow = proxy.$refs.tableRef.$el.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
      );
      newRow.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
    }, 100);
  };
  onMounted(() => {
    init();
  });

  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  //   .table-bordered {
  //     border: 1px solid #ebeef5;
  //     border-collapse: collapse;
  //     width: 100%;
  //     font-size: 14px;
  //     color: #606266;

  //     th {
  //       background-color: #f5f7fa;
  //       border: 1px solid #ebeef5;
  //       padding: 10px;
  //     }

  //     el-button {
  //       margin-left: 10px;
  //       border: 1px solid #ebeef5;
  //     }
  //   }

  //   .container {
  //     display: grid;
  //     justify-content: start;
  //     gap: 10px;
  //     grid-template-columns: 1fr 1fr 0fr;
  //     border: 1px solid #ebeef5;
  //   }

  :deep .el-form-item__content {
    padding: 5px;
  }

  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      background-color: red;
      transform: translate(20px, 5px);
      color: red;
    }
  }
  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }
  .el-form-item .el-form-item {
    margin-bottom: 1.8rem;
  }
</style>
