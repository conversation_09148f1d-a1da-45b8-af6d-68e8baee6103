<template>
  <div class="app-container">
    <HeadTitle :title="HeadTitleName" :divider-show="false" />
    <div style="margin-top: 20px">
      <el-form
        v-if="workFlowType == 'edit' && saveWorkFlowData"
        ref="flowRef"
        :model="form"
        :rules="rules"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流程名称" prop="flowName">
              <el-input
                v-model="form.flowName"
                maxlength="30"
                :disabled="getTaskType === 'h6cda11f475tya7e8def'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                maxlength="100"
                show-word-limit
                :disabled="getTaskType === 'h6cda11f475tya7e8def'"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template
      v-for="(syncChange, index) in syncChangeList"
      :key="syncChange.id"
      style="margin-bottom: 150px"
    >
      <el-row class="deleteSyncChange">
        <el-col :span="18">
          <span>
            <b> {{ '任务' + (index + 1) }}</b>
          </span>
        </el-col>
        <el-col :span="6">
          <el-button
            icon="delete"
            plain
            type="info"
            @click="deleteSyncChange(syncChange.id)"
          ></el-button>
        </el-col>
      </el-row>

      <SyncChangeS
        :node-id="syncChange.nodeId"
        :AddButton="AddButton"
        :save-work-flow-data-list="saveWorkFlowData"
        @change-work-flow-data="ChangeWorkFlowData"
        @update:save-work-flow-data="updateParentData"
        @change-get-task-type="ChangeGetTaskType"
      />
      <br />
    </template>
    <div class="sticky-button" style="padding: 16px 0 4px 0">
      <el-col :span="10"></el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          :disabled="
            workFlowType == 'edit' ||
            getTaskType == 'h6cda11f475tya7e8def' ||
            getTaskType == '5tyHdfcg918bb91e12y74eg345gdk'
          "
          @click="addSyncChange"
          >+新增任务</el-button
        >
        <el-button type="primary" :disabled="syncChangeList.length == 0" @click="save"
          >提交</el-button
        >
        <el-button @click="back">取消</el-button>
      </el-col>
    </div>
  </div>
</template>

<script setup>
  import { openWorkFlow, saveWorkFlow } from '@/api/dataSourceManageApi';
  import HeadTitle from '@/components/HeadTitle';
  import SyncChangeS from '../components/SyncChangeS/index.vue';

  const syncChangeList = ref([{}]);
  const saveWorkFlowDataFlowName = ref();
  const { proxy } = getCurrentInstance();

  const HeadTitleName = ref('新增工作流');

  const saveWorkFlowData = ref();

  const route = useRoute();

  const workFlowType = route.query.type;

  if (workFlowType == 'edit') {
    syncChangeList.value = [];
    HeadTitleName.value = '修改工作流';
  }

  const orderNum = ref(-1);
  const flowId = ref(route.query.id);
  const nodeList = ref([]);

  const arrayType = ref([]);
  const AddButton = ref();

  const getTaskType = ref();

  const ID = ref(1);
  const data = reactive({
    form: {
      flowName: '',
      description: '',
    },
    rules: {
      flowName: [
        { required: true, message: '流程名称不能为空', trigger: 'blur' },
        { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
          message: '名称只能包含中文、英文、数字和下划线',
        },
      ],
    },
  });
  const { rules, form } = toRefs(data);

  // 禁止新增KETTLE
  const ChangeGetTaskType = (data) => {
    arrayType.value.push(data);
    // AddButton.value = data
    console.log('data', data);
    getTaskType.value = data;
    console.log('getTaskType', getTaskType);
  };

  // 更新数据
  const ChangeWorkFlowData = (data) => {
    console.log(
      'data********************************************************-----------------',
      data,
    );

    nodeList.value.push({
      flowId: flowId.value,
      nodeId: data,
      orderNum: orderNum.value + 1,
    });

    // nodeList.value =
    // {
    //   flowId: flowId.value,
    //   nodeId: data,
    //   orderNum: orderNum.value + 1
    // }
    // orderNum.value += 1
    console.log('data', nodeList);
  };

  // 保存节点
  const save = () => {
    // 如果 arrayType.value 包含了 h6cda11f475tya7e8def 和其他任意类型  就禁止提交并提示用户删除 h6cda11f475tya7e8def
    // 和其他任意类型
    if (arrayType.value.includes('h6cda11f475tya7e8def') && arrayType.value.length > 1) {
      proxy.$modal.msgError('禁止同时新增离线和实时,请删除离线任务');
      return;
    }

    let query = {};
    if (workFlowType == 'edit') {
      query = {
        ...saveWorkFlowData.value,
        flowName: form.value.flowName,
        description: form.value.description,
      };
    } else {
      if (nodeList.value.length < 1 && getTaskType.value != 'h6cda11f475tya7e8def')
        return proxy.$modal.msgError('至少保留一个节点');
      query = {
        ...saveWorkFlowData.value,
        nodeList: nodeList.value,
        // flowName: saveWorkFlowDataFlowName
      };
    }
    // console.log('saveWorkFlowData.value', saveWorkFlowData.value)
    // console.log('data', nodeList.value)
    //
    // return

    if (getTaskType.value == 'h6cda11f475tya7e8def') {
      console.log('1212', 1212);
      back();
      return;
    }

    if (workFlowType == 'edit') {
      proxy.$refs.flowRef?.validate((valid) => {
        if (valid) {
          // return
          saveWorkFlow(query).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess(res.msg);
              router.push('/DataAggregation/SyncTaskManage');
            } else {
              proxy.$modal.msgError(res.msg);
            }
          });
        }
      });
    } else {
      saveWorkFlow(query).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(res.msg);
          router.push('/DataAggregation/SyncTaskManage');
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    }
  };

  const router = useRouter();
  // 返回工作流列表
  const back = () => {
    router.push('/DataAggregation/SyncTaskManage');
  };

  // 多节点 SyncChangeS 组件
  function addSyncChange(data) {
    if (!getTaskType.value) return;

    AddButton.value = true;
    const newSyncChange = nodeList.value == [] ? {} : nodeList.value;
    console.log('newSyncChange', newSyncChange);

    if (workFlowType == 'edit') {
      console.log('newSyncChange', nodeList.value);
      console.log('syncChangeList.value', syncChangeList.value);

      // syncChangeList.value = [...newSyncChange, ...syncChangeList.value]
    }

    syncChangeList.value.push({
      id: ID.value++,
      nodeId: '',
    });

    console.log('syncChangeList.value', syncChangeList.value);
    getTaskType.value = '';
  }

  // 删除 SyncChangeS 组件
  function deleteSyncChange(index) {
    proxy
      .$confirm('确定要删除吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        // 如果数量小于等于 1 直接返回
        if (syncChangeList.value.length <= 1) {
          // 提示用户
          proxy.$modal.msgError('流程中至少保留一个任务');
          return;
        }

        const indexIn = syncChangeList.value.findIndex((val) => {
          return val.id == index; // 这样
        });

        syncChangeList.value.splice(indexIn, 1);
        // 找到nodeList对应的index 删除 nodeList 中 对应的数据
        nodeList.value.splice(index, 1);
        console.log('nodeList', nodeList);
      });
  }

  // 页面离开时提示
  onBeforeRouteLeave((to, from, next) => {
    if (nodeList.value.length >= 1) {
      // 是否离开当前网页 你所做的更改可能未保存。
      proxy
        .$confirm('你所做的更改可能未保存', '是否离开当前页面?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          next();
        })
        .catch(() => {
          // 用户取消操作时不跳转
        })
        .catch(() => {
          // 用户取消操作时不跳转
        });
    } else {
      next();
    }
  });

  onMounted(() => {
    openWorkFlow(flowId.value).then((res) => {
      saveWorkFlowData.value = res.data;
      nextTick(() => {
        form.value.flowName = saveWorkFlowData.value.flowName;
        form.value.description = saveWorkFlowData.value.description;
      });
      if (res.data.nodeList.length > 0) {
        syncChangeList.value = res.data.nodeList;
      }

      console.log('syncChangeList.value*-*-*-*-', syncChangeList.value);
    });
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .sticky-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255);
    /* 可以根据需要设置背景色 */
    padding: 20px;
    /* 可以根据需要设置按钮容器的内边距 */
    box-shadow: 0px 10px 1px 10px rgb(0, 0, 0.9);
    /* 可以根据需要添加阴影效果 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
  }

  .deleteSyncChange {
    margin: 20px auto;
    background-color: #44a4fa;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    span {
      b {
        padding: 10px;
      }

      color: white;
      text-align: center;
      line-height: 30px;
      font-size: 14px;
    }
  }
</style>
