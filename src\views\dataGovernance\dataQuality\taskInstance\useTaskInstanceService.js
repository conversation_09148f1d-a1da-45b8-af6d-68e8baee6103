import { ref, reactive, nextTick } from 'vue';
import { useWorkFLowStore } from '@/store/modules/workFlow';
import { RUNNING_STATUS } from '@/views/dataGovernance/dataQuality/taskInstance/common/constants';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getTaskList, getTaskInstanceLog } from '@/api/dataSourceManageApi';
import { deleteDqProcessInstance, getDqProcessInstance } from '@/api/dataGovernance';
import { tableLabelToValue, tableValueToLabel } from '@/utils/xugu';
export default function useTaskInstanceService(props) {
  const { proxy } = getCurrentInstance();

  const store = useWorkFLowStore();
  const workSpaceIdData = computed(() => store.getWorkSpaceId());
  const showDetail = ref(false);
  // 初始化表格、查询框、弹出框
  const searchInfo = reactive({
    searchForm: {
      stateType: '',
      searchVal: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      maxCount: 10,
      total: 10,
    },
  });
  const detailsInfo = reactive({
    id: 45,
    data: {},
  });
  const options = reactive({
    statusOptions: [...RUNNING_STATUS],
  });

  const tableInfo = reactive({
    columns: [
      {
        prop: 'processInstanceName',
        label: '实例名称',
        width: 400,
        showOverflowTooltip: true,
      },
      {
        prop: 'status',
        label: '运行状态',
      },
      {
        prop: 'startTime',
        label: '开始时间',
      },
      {
        prop: 'endTime',
        label: '结束时间',
      },
      {
        prop: 'runTimes',
        label: '运行时长',
      },
    ],
    tableData: [],
  });
  const dialogInfo = reactive({
    data: [
      {
        title: '输入项标题',
        name: 'name',
        type: '输入项类型',
      },
      {
        title: '输入项标题',
        name: 'name',
        type: '输入项类型',
      },
      {
        title: '输入项标题',
        name: 'name',
        type: '输入项类型',
      },
    ],
    columns: [
      {
        prop: 'title',
        label: '输入项标题',
      },
      {
        prop: 'name',
        label: '输入占位符',
      },
      {
        prop: 'type',
        label: '输入项类型',
      },
    ],
    dialogVisible: false,
    dialogTitle: '',
  });

  // 事件
  const tableListener = reactive({
    tableSearch: () => {
      tableSearch();
    },
    rerun: () => {
      dialogInfo.dialogVisible = true;
    },
    showResults: (res) => {
      showDetail.value = true;
      detailsInfo.id = res.row.id;
      detailsInfo.data = res.row;
    },
    // showLog: (scope) => {
    //   dialogInfo.dialogVisible = true;
    //   handleLog(scope.row);
    // },
    // 查看日志弹窗
    handleLog: (scope) => {
      const query = {
        workSpaceId: workSpaceIdData.value,
        id: scope.row.processInstanceId,
      };
      getTaskList(query).then((res) => {
        if (res.code === 200) {
          logInfo.LogTitle = '运行日志';
          logInfo.LogContentList = res.data;
          logInfo.openLog = true;
        }
      });
    },
    deleteItem: ({ row }) => {
      const message = {};
      message.title = '删除该实例';
      message.content = '删除后该数据将无法恢复';
      ElMessageBox({
        title: '操作确认',
        message: h('p', null, [
          h('p', null, message.title),
          h('span', { style: 'color: teal' }, message.content),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const query = {
            workSpaceId: workSpaceIdData.value,
            id: row.processInstanceId,
          };
          console.log(query);
          const res = await deleteDqProcessInstance(query);
          if (res.code !== 200) return ElMessage({ type: 'error', message: res.msg });
          ElMessage({ type: 'success', message: res.msg });
          tableSearch();
        })
        .catch(() => {});
    },
  });
  const dialogListener = reactive({
    submitSpatial: () => {
      dialogInfo.dialogVisible = false;
    },
    closeSpatial: () => {},
  });
  const detailsListener = reactive({
    callback: () => {
      showDetail.value = false;
    },
  });

  // 日志对应对象和事件
  const logInfo = reactive({
    openLog: false,
    LogTitle: '',
    LogContentList: '',
    defaultProps: {
      children: 'children',
      label: 'name',
    },
    defaultExpandedKeys: [],
    logStrB: '',
  });
  const logListener = reactive({
    handleNodeClick: (r) => {
      logInfo.logStrB = '';
      processTaskInstance(r);
    },
    openLogClose: () => {
      logInfo.openLog = false;
      logInfo.logStrB = '';
    },
  });
  /**
   * 日志限制增量
   * @type {number}
   */
  const LOG_LIMIT_INCREMENT = 1000;
  /**
   * 任务实例日志数组
   * @type {Array}
   */
  const taskInstanceLogs = [
    {
      lineTotal: 0,
      message: '',
    },
  ];
  /**
   * 判断是否没有更多日志
   * @param {number} lineNum - 行数
   * @param {string} logStr - 日志字符串
   * @returns {boolean} 是否没有更多日志
   */
  const isNoMoreLog = (lineNum, logStr) => {
    return (
      (lineNum === 1 && logStr === '$日志终点$') ||
      logStr.includes('Roll view log error: connect to')
    );
  };
  /**
   * 创建日志映射
   * @param {number} lineTotal - 总行数
   * @param {string} message - 消息
   * @returns {Object} 日志映射
   */
  function createLogMap(lineTotal, message) {
    return { lineTotal, message };
  }

  /**
   * 处理任务实例
   * @param {Object} taskInstance - 任务实例
   */
  async function processTaskInstance(taskInstance) {
    console.log('taskInstance', taskInstance);

    if (!taskInstance.host) {
      logInfo.logStrB.value = '获取日志失败，请检查自定义参数配置';
      logInfo.taskInstanceLogs.push(createLogMap(0, '错误的日志'));
      return;
    }

    const taskId = taskInstance.id;
    let limit = 0;
    let skipLineNum = 0;
    let lineNum = 0;
    let taskInstanceLog = {};
    let messageInfo = '';
    do {
      limit += LOG_LIMIT_INCREMENT;
      skipLineNum += lineNum;

      try {
        taskInstanceLog = await getTaskInstanceLog(taskId, limit, skipLineNum);
        lineNum = taskInstanceLog.data.lineNum;
        logInfo.logStrB += taskInstanceLog.data.message;
        messageInfo = taskInstanceLog.data.message;
      } catch (error) {
        console.error(error);
      }
      console.log(`已查到工作流实例 [] 下任务实例 [${taskId}]，本次行数：${lineNum}`);
    } while (!isNoMoreLog(lineNum, messageInfo));

    taskInstanceLogs.push(createLogMap(skipLineNum, logInfo.logStrB));
  }
  //   /**
  //    * 处理任务实例列表
  //    * @param {Array} taskJsonList - 任务实例列表
  //    */
  //   const processTaskInstances = async (taskJsonList) => {
  //     await taskJsonList.forEach((taskInstance) => processTaskInstance(taskInstance));
  //   };

  // 展开日志第一个
  watch(
    () => logInfo.LogContentList,
    (val) => {
      if (logInfo.LogContentList.length > 0) {
        console.log('LogContentList.value[0]', logInfo.LogContentList[0]);
        logInfo.defaultExpandedKeys.value = [logInfo.LogContentList[0].lineNum];
        logListener.handleNodeClick(logInfo.LogContentList[0]);
        nextTick(() => {
          proxy.$refs.logTree.setCurrentKey(logInfo.LogContentList[0].taskLogNo);
        });
      }
    },
  );

  const tableSearch = async () => {
    const params = {
      ...searchInfo.queryParams,
      ...searchInfo.searchForm,
    };
    tableInfo.tableData = [];
    // tableInfo.tableData = [
    //   {
    //     id: 1,

    //     name: '规则名称',
    //     type: '规则类型',
    //     status: 1,
    //     statusLabel: '运行成功',
    //     startTime: '2022-01-01 00:00:00',
    //     endTime: '2022-01-01 00:00:00',
    //     runTime: '00:00:00',
    //   },
    //   {
    //     id: 2,
    //     name: '规则名称',
    //     type: '规则类型',
    //     status: 2,
    //     statusLabel: '运行中',
    //     startTime: '2022-01-01 00:00:00',
    //     endTime: '2022-01-01 00:00:00',
    //     runTime: '00:00:00',
    //   },
    //   {
    //     id: 3,
    //     name: '规则名称',
    //     type: '规则类型',
    //     status: 0,
    //     statusLabel: '运行失败',
    //     startTime: '2022-01-01 00:00:00',
    //     endTime: '2022-01-01 00:00:00',
    //     runTime: '00:00:00',
    //   },
    // ];
    const req = {
      workspaceId: workSpaceIdData.value,
      pageNo: searchInfo.queryParams.pageNum,
      pageSize: searchInfo.queryParams.pageSize,
      ...searchInfo.searchForm,
    };
    // if (req.stateType) {
    //   req.stateType = req.stateType + '';
    // }
    // const formData = new FormData();
    // formData.append('workspaceId', req.workspaceId);
    // formData.append('pageNo', req.pageNo);
    // formData.append('pageSize', req.pageSize);
    // formData.append('stateType', req.stateType);
    // formData.append('searchVal', req.searchVal);

    const res = await getDqProcessInstance(req);
    if (res.code === 200) {
      tableInfo.tableData = res.data.rows.map((item) => {
        item.status = dealState(item.state); // item.state === 6 ? 0 : item.state === 7 ? 1 : 2;
        item.statusLabel = tableValueToLabel(RUNNING_STATUS, item.state);
        return item;
      });
      searchInfo.queryParams.total = res.data.total;
    } else {
      tableInfo.tableData = [];
    }
    // const res = await proxy.$http.get('/api/rule/list', { params });
  };
  // 处理状态对应颜色
  const dealState = (state) => {
    let returnData = 1;
    switch (state) {
      case 6 || 16:
        returnData = 0;
        break;
      case 0 || 7:
        returnData = 1;
        break;
      case 2 || 3 || 4 || 5 || 15:
        returnData = 3;
        break;
      default:
        returnData = 2;
        break;
    }
    return returnData;
  };

  watch(workSpaceIdData, (val) => {
    init();
  });

  const init = async () => {
    tableSearch();
  };
  // 初始化
  onMounted(async () => {
    init();
  });
  return {
    searchInfo,
    tableInfo,
    dialogInfo,
    tableListener,
    dialogListener,
    options,
    detailsInfo,
    showDetail,
    detailsListener,
    logInfo,
    logListener,
  };
}
