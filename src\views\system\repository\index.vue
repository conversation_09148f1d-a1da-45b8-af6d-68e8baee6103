<template>
  <SplitPanes class="App-theme">
    <template #left>
      <!-- 左侧树结构，参考bazaarList -->
      <div class="interface-left-box">
        <!-- 可以添加搜索或按钮 -->
        <el-input v-model="treeSearchText" placeholder="请输入搜索内容" suffix-icon="Search" />
        <el-tree ref="treeRef" :data="fileTreeData" :props="propsTree" :filter-node-method="filterNode" :highlight-current="true">
          <template #default="{ node, data }">
            <span>{{ data.label }}</span>
          </template>
        </el-tree>
      </div>
    </template>
    <template #right>
      <div class="interface-right-box">
        <!-- 上方按钮: 上传, 批量移动, 批量删除, 模型管理 -->
        <el-button type="primary" @click="uploadFile">上传</el-button>
        <el-button @click="batchMove">批量移动</el-button>
        <el-button @click="batchDelete">批量删除</el-button>
        <el-button @click="openModelDrawer">模型管理</el-button>
        <!-- 搜索表单 -->
        <el-form inline>
          <el-form-item label="文件内容或名称">
            <el-input v-model="searchForm.keyword" placeholder="请输入" clearable />
          </el-form-item>
          <!-- 新增下拉框搜索 -->
          <el-form-item label="文件类型">
            <el-select v-model="searchForm.fileType" placeholder="请选择" style="width: 1000px;">
              <el-option label="PDF" value="PDF" />
              <el-option label="Image" value="Image" />
              <!-- 其他选项 -->
            </el-select>
          </el-form-item>
          <!-- 其他搜索项 -->
          <el-button type="primary" icon="Search" @click="searchFiles">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form>
        <!-- 表格 -->
        <el-table :data="fileTableData" @selection-change="handleSelectionChange" height="calc(100% - 100px)">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="fileName" label="文件名称" />
          <el-table-column prop="fileType" label="文件类型" />
          <el-table-column prop="status" label="状态" />
          <el-table-column prop="uploader" label="上传人" />
          <el-table-column prop="uploadTime" label="上传时间" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="configSecurity(scope.row)">配置密级</el-button>
              <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
              <el-button type="text" @click="deleteFile(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getFileList" />
      </div>
    </template>
  </SplitPanes>
  <!-- 配置密级弹窗 -->
  <el-dialog v-model="securityDialogVisible" title="选择密级">
    <el-select v-model="selectedSecurity" placeholder="请选择内容">
      <el-option label="L0/公开数据" value="L0" />
      <!-- 其他选项 -->
    </el-select>
    <template #footer>
      <el-button @click="securityDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmSecurity">确定</el-button>
    </template>
  </el-dialog>
  <!-- 模型管理抽屉 -->
  <el-drawer v-model="modelDrawerVisible" title="模型管理" direction="rtl" size="50%">
    <!-- 模型列表表格 -->
    <el-button @click="addModel">新增模型</el-button>
    <el-table :data="modelTableData">
      <el-table-column prop="modelName" label="模型名称" />
      <el-table-column prop="modelType" label="模型类型" />
      <el-table-column prop="status" label="可用状态" />
      <el-table-column prop="creator" label="创建人" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="url" label="URL" />
      <el-table-column label="操作" />
    </el-table>
    <pagination :total="modelTotal" />
    <!-- 新增模型弹窗 -->
    <el-dialog v-model="addModelDialogVisible" title="新增模型" append-to-body>
      <el-form>
        <el-form-item label="*模型名称">
          <el-input v-model="newModel.name" placeholder="请输入内容" maxlength="50" />
        </el-form-item>
        <el-form-item label="*模型类型">
          <el-select v-model="newModel.type" placeholder="请选择内容" />
        </el-form-item>
        <el-form-item label="URL">
          <el-input v-model="newModel.url" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addModelDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createModel">创建</el-button>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup>
import { ref, reactive } from 'vue';
import SplitPanes from '@/components/SplitPanes/index.vue';
import Pagination from '@/components/Pagination/index.vue';
// 假设有API导入，如 import { getFiles, uploadFileApi } from '@/api/system/repository';
const treeSearchText = ref('');
const propsTree = { label: 'label', children: 'children' };
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.includes(value);
};
const searchForm = reactive({ keyword: '' });
const queryParams = reactive({ pageNum: 1, pageSize: 10 });
const selectedRows = ref([]);
const securityDialogVisible = ref(false);
const selectedSecurity = ref('');
const currentFile = ref(null);
const modelDrawerVisible = ref(false);
const addModelDialogVisible = ref(false);
const newModel = reactive({ name: '', type: '', url: '' });
// 函数定义
const getFileList = async () => {
  // 调用API获取文件列表
  // fileTableData.value = response.data;
  // total.value = response.total;
};
const uploadFile = () => {
  // 实现上传逻辑
};
const batchMove = () => {
  // 批量移动
};
const batchDelete = () => {
  // 批量删除
};
const openModelDrawer = () => {
  modelDrawerVisible.value = true;
  // 获取模型列表
};
const searchFiles = () => {
  getFileList();
};
const resetSearch = () => {
  searchForm.keyword = '';
  getFileList();
};
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
const configSecurity = (row) => {
  // 检查权限，如果是上传人
  // if (currentUser.id === row.uploaderId) {
    currentFile.value = row;
    selectedSecurity.value = row.securityLevel || '';
    securityDialogVisible.value = true;
  // } else {
  //   ElMessage.error('您没有权限配置密级');
  // }
};
const confirmSecurity = () => {
  // 更新文件密级
  securityDialogVisible.value = false;
};
const downloadFile = (row) => {
  // 检查用户密级 >= 文件密级
  // if (userSecurity >= row.securityLevel) {
  //   下载
  // } else {
  //   ElMessage.error('您没有相关权限，无法访问');
  // }
};
const deleteFile = (row) => {
  // 类似权限检查
};
const addModel = () => {
  addModelDialogVisible.value = true;
};
const createModel = () => {
  // 创建模型，确保只有一个启用
  addModelDialogVisible.value = false;
};
// 初始化
getFileList();

const fileTreeData = ref([
  { label: 'Folder 1', children: [{ label: 'File 1.1' }, { label: 'File 1.2' }] },
  { label: 'Folder 2', children: [{ label: 'File 2.1' }] }
]);
const fileTableData = ref([
  { fileName: 'Document1.pdf', fileType: 'PDF', status: '正常', uploader: 'User1', uploadTime: '2023-01-01' },
  { fileName: 'Image2.jpg', fileType: 'Image', status: '解析中', uploader: 'User2', uploadTime: '2023-01-02' }
]);
const total = ref(2);
const modelTableData = ref([
  { modelName: 'Model A', modelType: 'Type1', status: '可用', creator: 'Admin', createTime: '2023-01-01', url: 'http://example.com' },
  { modelName: 'Model B', modelType: 'Type2', status: '不可用', creator: 'User', createTime: '2023-01-03', url: 'http://example2.com' }
]);
const modelTotal = ref(2);
</script>



