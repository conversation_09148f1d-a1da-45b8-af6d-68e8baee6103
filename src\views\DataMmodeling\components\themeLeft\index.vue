<template>
  <div class="theme">
    <el-row class="head-title">
      <el-col :span="10">
        <span class="titleName">{{ title }}</span>
      </el-col>

      <el-col v-if="showAddButton" :span="14" class="right-btn-box">
        <ExportAndImport
          moduleName="Theme"
          :allowClick="{
            output: { disabled: false, msg: '' },
            input: { disabled: false, msg: '' },
            logs: { disabled: false, msg: '' },
          }"
          @reload="getGroupsTree"
        ></ExportAndImport>
        <el-tooltip content="新增" placement="top">
          <el-button class="add-btn" type="primary" icon="Plus" @click="tierAddUtil" />
        </el-tooltip>
      </el-col>
    </el-row>

    <div v-if="showHeadBtn" class="head-btn">
      <el-radio-group v-model="dataTreeType" @change="changeRadio">
        <el-radio-button v-for="item in dataTreeTypeList" v-bind="item" :key="item" />
      </el-radio-group>
    </div>

    <div class="tree-box">
      <el-input
        v-model="filterText"
        suffix-icon="Search"
        placeholder="请输入名称"
        class="tree-search"
        @change="onSearch"
      />

      <el-tree-v2
        ref="treeRef"
        :data="dataTree"
        :props="treeProps"
        :height="650"
        class="left-tree-box"
        highlight-current="true"
      >
        <template #default="{ node, data }">
          <div
            :class="{ clickable: node.level === 3 }"
            class="itemTree"
            @click="handleNodeClick($event, data, node)"
            @contextmenu="showContextMenu($event, data, node)"
          >
            <div class="tree-item-box">
              <el-icon>
                <component :is="iconMap[node.level]" />
              </el-icon>

              <el-tooltip
                :content="data?.label"
                effect="light"
                placement="top"
                :disabled="disTooltip(data)"
              >
                {{ sliceTooltip(data) }}
              </el-tooltip>
              <span v-if="showLabelClass" :class="classMap[node.level]">
                {{ labelMap[node.level] }}
              </span>
            </div>

            <div v-if="showOperateBtn" class="tree-btn-box">
              <span v-if="!showAddBtnItem(node)" @click.stop="treeAddItem($event, data, node)">
                <el-icon>
                  <Plus />
                </el-icon>
              </span>
              <!-- <span
                v-if="node.level === 2 && (!data.children || data.children?.length <= 0)"
                @click.stop="treeDeleteItem($event, data, node)"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </span> -->

              <!-- <el-popover  trigger="click" placement="top"> -->
              <!-- <el-cascader-panel v-model="select" :props="{ expandTrigger: 'hover' }" -->
              <!-- :options="baseOptions" :border="false" @change="handleMenuClick"> -->
              <!-- <template #default="{ node, data }"> -->
              <!-- <span class="flow-contextmenu__node">{{ data.label }}</span> -->
              <!-- </template> -->
              <!-- </el-cascader-panel> -->
              <!-- <template #reference> -->
              <!-- <span class="tree-icon" @click.stop="data.visible = true"> -->
              <!-- <el-icon> -->
              <!-- <MoreFilled /> -->
              <!-- </el-icon> -->
              <!-- </span> -->
              <!-- </template> -->
              <!-- </el-popover> -->
            </div>
          </div>
        </template>
      </el-tree-v2>
    </div>
  </div>
</template>

<script setup>
  import { FolderOpened, Cpu, Help } from '@element-plus/icons-vue';

  const { dataTree, treeProps, showAddButton } = defineProps({
    dataTree: {
      type: Array,
      required: true,
    },
    treeProps: {
      type: Object,
      default: () => ({}),
    },
    showAddButton: {
      type: Boolean,
      default: false,
    },
    showHeadBtn: {
      type: Boolean,
      default: false,
    },
    dataTreeTypeList: {
      type: Array,
      default: () => [],
    },
    showLabelClass: {
      type: Boolean,
      default: false,
    },
    showOperateBtn: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '主题目录',
    },
    baseOptions: {
      type: Array,
      default: () => [
        {
          value: 'deploy',
          label: '可视化配置',
        },
        {
          value: 'remove',
          label: '删除',
        },
      ],
    },
  });

  const emit = defineEmits([
    'node-click',
    'filter-change',
    'add-tier',
    'context-menu',
    'treeDeleteItem',
  ]);

  const filterText = ref('');
  const treeRef = ref(null);

  const onSearch = (value) => {
    emit('filter-change', value);
  };
  const treeData = reactive({
    iconMap: {
      1: FolderOpened,
      2: Cpu,
      3: Help,
    },
    classMap: {
      1: 'sort',
      2: 'area',
      3: 'course',
    },
    labelMap: {
      1: '业务分类',
      2: '主题域',
      3: '业务过程',
    },
  });
  const { iconMap, classMap, labelMap } = treeData;

  const handleNodeClick = (event, data, node) => {
    // if (node.level === 3) {
    emit('node-click', { event, data, node });
    // }
  };

  const tierAddUtil = () => {
    emit('add-tier');
  };

  const showContextMenu = (event, data, node) => {
    emit('context-menu', { event, data, node });
  };

  const dataTreeType = ref(1);
  const changeRadio = (value) => {
    emit('changeRadio', value);
  };

  const getGroupsTree = () => {
    emit('filter-change', '');
  };

  const sliceTooltip = (data) => {
    return data?.label?.length > 10 ? data?.label?.slice(0, 10) + '...' : data?.label;
  };
  const disTooltip = (data) => {
    return data?.label?.length < 10;
  };

  const menuPosition = ref({ left: '0px', top: '0px' });
  const handleMenuClick = (action) => {
    emit('onMenuClick', action);
  };
  const treeAddItem = (event, data, node) => {
    emit('treeAddItem', { event, data, node });
  };
  const treeDeleteItem = (event, data, node) => {
    emit('treeDeleteItem', { event, data, node });
  };

  const showAddBtnItem = (node) => {
    return node.data.showOperateBtnAd === false;
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .theme {
    height: 100%;
    // overflow: auto;

    .titleName {
      padding-left: 10px;
      font-size: 17px;
      height: 32px;
      line-height: 32px;
      position: relative;
      display: inline-block;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        border-radius: 4px;
        background: $--base-color-primary;
        position: absolute;
        left: 0;
        top: 8px;
      }
    }

    .head-btn {
      margin-bottom: 10px;
    }

    .tree-box {
      height: calc(100% - 50px);
      background-color: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      .tree-search {
        margin-bottom: 10px;
      }

      .left-tree-box {
        height: calc(100% - 40px);
        background: $--base-color-item-light;

        .tree-info {
          display: flex;
          align-items: center;
        }

        .itemTree {
          //   display: grid;
          //   grid-template-columns: repeat(3, 1fr);
          display: flex;
          justify-content: space-between;
          align-content: center;
          width: 100%;
          .tree-item-box {
            width: calc(100% - 40px);
          }
          .tree-btn-box {
            width: 40px;
            text-align: right;
          }
        }
      }
    }

    .head-title {
      font-size: 16px;
      line-height: 30px;
      margin-bottom: 15px;
      .add-btn {
        width: 28px;
        height: 28px;
      }
      .right-btn-box {
        text-align: right;
      }
    }
  }

  .sort,
  .area,
  .course {
    border-radius: 10px;
    margin-left: 5px;
    padding: 3px;
    font-size: 12px;
    width: 60px;
    text-align: center;
  }

  .sort {
    background: rgba(230, 247, 255, 0.5);
    color: rgb(24, 144, 255);
  }

  .area {
    border-radius: 4px;
    background: #dbe7ff;
    color: #4868ed;
  }

  .course {
    border-radius: 4px;
    background: #fef6e4;
    color: #ffb300;
  }

  .tree-prop-icon {
  }

  .tree-prop-box {
    display: flex;
    flex-direction: column;
    align-items: baseline;
  }
  :deep .export-and-import {
    display: inline-block;
    margin-right: 10px;
  }
</style>
