// checkViteConfig.js

const fs = require('fs');

const configFile = 'vite.config.js';
const expectedTarget = 'http://10.28.23.131:9000/prod-api';

try {
  const configContent = fs.readFileSync(configFile, 'utf8');
  const config = JSON.parse(configContent); // Parse as JSON object

  if (config && config.server && config.server.target === expectedTarget) {
    console.log(`✅ ${configFile} 的配置符合预期。`);
    process.exit(0); // Exit with success status
  } else {
    console.error(`❌ ${configFile} 的配置不符合预期。`);
    process.exit(1); // Exit with failure status
  }
} catch (err) {
  console.error(`❌ 读取或解析 ${configFile} 发生错误：${err.message}`);
  process.exit(1); // Exit with failure status
}
