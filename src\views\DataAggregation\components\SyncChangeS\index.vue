<template>
  <div class="app-container">
    <el-row :gutter="20">
      <b>1.基础信息</b>
      <el-divider></el-divider>
      <el-col :span="12">
        <!-- <el-form-item label="同步任务名称" prop="status">
          <el-input v-model="form.syncTaskName" type="input" placeholder="请输入名称"></el-input>

        </el-form-item> -->
        <el-form-item label="任务类型">
          <el-select v-model="typeT" :disabled="typeT ? true : false" @change="getTaskType">
            <el-option
              v-for="typeT in typeList"
              :key="typeT.value"
              :value="typeT.value"
              :label="typeT.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <!-- <el-form-item label="描述"> -->
        <!-- <el-input v-model="form.describe" type="textarea" placeholder="请输入描述" maxlength="100" show-word-limit></el-input> -->
        <!-- </el-form-item> -->
      </el-col>
    </el-row>

    <!-- SQL -->
    <el-row :gutter="20">
      <SyncChangeSQL
        v-if="TaskType == 'SQL'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
        :AddButton="AddButton"
        :save-work-flow-data-list="saveWorkFlowDataList"
      />
    </el-row>

    <!-- KETTLE -->
    <el-row :gutter="20">
      <SyncChangeKETTLE
        v-if="TaskType == 'KETTLE'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
        :save-work-flow-data-list="saveWorkFlowDataList"
      />
    </el-row>

    <!-- SYNC -->
    <el-row :gutter="20">
      <SyncChangeData
        v-if="TaskType == 'SYNC'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
        :save-work-flow-data-list="saveWorkFlowDataList"
      />
    </el-row>

    <!-- Kafka -->
    <el-row :gutter="20">
      <SyncChangeKafka
        v-if="TaskType == 'Kafka'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
      />
    </el-row>

    <!-- API -->
    <el-row :gutter="20">
      <SyncChangeApi
        v-if="TaskType == 'API'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
      />
    </el-row>

    <!-- File -->
    <el-row :gutter="20">
      <SyncChangeFile
        v-if="TaskType == 'File'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
      />
    </el-row>

    <!-- 实时同步 -->
    <el-row :gutter="20">
      <SyncChangeRealtime
        v-if="TaskType == 'flinkCdc'"
        :flow-id="flowId"
        :node-name="nodeName"
        :node-id="nodeId"
        :open-work-flow-data="openWorkFlowData"
        :work-flow-type="workFlowType"
        :save-work-flow-data-list="saveWorkFlowDataList"
      />
    </el-row>
  </div>

  <!-- <el-button type="primary" @click="save">提交122</el-button> -->
</template>

<script setup>
  import {
    FlowNode,
    getByNodeId,
    getNode,
    getUUid,
    openWorkFlow,
    saveWorkFlow,
  } from '@/api/dataSourceManageApi';
  import SyncChangeData from '../SyncChangeData/index.vue';
  import SyncChangeKETTLE from '../SyncChangeKETTLE/index.vue';
  import SyncChangeSQL from '../SyncChangeSQL/index.vue';
  import SyncChangeKafka from '../SyncChangeKafka/index.vue';
  import SyncChangeApi from '../SyncChangeApi/index.vue';
  import SyncChangeFile from '../SyncChangeFile/index.vue';
  import SyncChangeRealtime from '../SyncChangeRealtime/index.vue';

  // import { useCounterStore } from '@/store/modules/workFlow'
  // const counterStore = useCounterStore();
  // console.log('counterStore.count', counterStore.count)
  // const increment = counterStore.increment
  const typeT = ref(null);
  const typeList = ref([
    {
      value: 'a7f24fc505a14937b58c5ecec67c4d30',
      label: 'SQL',
    },
    {
      value: 'h6cda11f475tya7e8def',
      label: 'KETTLE',
    },
    {
      value: 'j937jdaf823rd92nda63crgvdiwe3bb',
      label: 'kafka输入',
    },
    {
      value: '111111111111111',
      label: '离线同步',
    },
    {
      value: '5fchweb4148bb81e12y74eg345gdk',
      label: 'API输入',
    },
    {
      value: '4uyhrj418bb91e12y74eg345gdk',
      label: '文本输入',
    },
    {
      value: '5tyHdfcg918bb91e12y74eg345gdk',
      label: 'FlinkCdc',
    },
  ]);

  const TaskType = ref();
  const HeadTitleName = ref('新增工作流');

  const saveWorkFlowData = ref();
  const openWorkFlowData = ref();

  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    AddButton: {
      type: Boolean,
      default: () => false,
    },
    saveWorkFlowDataList: {
      type: Object,
      default: () => {},
    },
  });

  const { AddButton, saveWorkFlowDataList } = toRefs(props);
  console.log('saveWorkFlowDataList', saveWorkFlowDataList.value);
  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
  });
  const { form } = toRefs(data);

  const route = useRoute();
  const nodeName = ref();
  const flowId = route.query.id;
  const workFlowType = route.query.type;
  const nodeId = ref();

  if (workFlowType === 'edit') {
    HeadTitleName.value = '修改工作流';
    const { nodeId: editedNodeId } = toRefs(props);
    nodeId.value = editedNodeId;
  } else {
    // 在条件块内部赋默认值
    nodeId.value = ref();
  }

  const nodeList = ref([]);

  const emit = defineEmits([]);

  const getTaskType = async (data) => {
    console.log('data', data);
    // 用于禁止新建KETTLE
    emit('ChangeGetTaskType', data);

    if (data == 'h6cda11f475tya7e8def') {
      TaskType.value = 'KETTLE';
      typeT.value = 'KETTLE';
    }
    // 数据到数据库
    else if (data == '111111111111111') {
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: typeT.value,
      };
      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        await FlowNode(query).then((res) => {
          if (res.code == 200) {
            nodeId.value = res.data.id;
            console.log('res.data.id', res.data.id);
            emit('ChangeWorkFlowData', res.data.id);
          }
        });
      }
      TaskType.value = 'SYNC';
    }
    // 数据到SQL
    else if (data == 'a7f24fc505a14937b58c5ecec67c4d30') {
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: typeT.value,
      };

      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        await FlowNode(query).then((res) => {
          nodeId.value = res.data.id;
          if (res.code == 200) {
            console.log('res.data.id------------------------------------', res.data.id);
            emit('ChangeWorkFlowData', res.data.id);
          }
        });
      }
      TaskType.value = 'SQL';
    }
    // 数据到kafka
    else if (data == 'j937jdaf823rd92nda63crgvdiwe3bb') {
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: 'j937jdaf823rd92nda63crgvdiwe3bb',
      };
      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        await FlowNode(query).then((res) => {
          nodeId.value = res.data.id;
          emit('ChangeWorkFlowData', res.data.id);
        });
      }
      TaskType.value = 'Kafka';
    }
    // 数据到api
    else if (data == '5fchweb4148bb81e12y74eg345gdk') {
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: typeT.value,
      };
      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        await FlowNode(query).then((res) => {
          nodeId.value = res.data.id;
          if (res.code == 200) {
            emit('ChangeWorkFlowData', res.data.id);
          }
        });
      }
      TaskType.value = 'API';
    }
    // 数据到文件
    else if (data == '4uyhrj418bb91e12y74eg345gdk') {
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: '4uyhrj418bb91e12y74eg345gdk',
      };
      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        await FlowNode(query).then((res) => {
          nodeId.value = res.data.id;
          if (res.code == 200) {
            emit('ChangeWorkFlowData', res.data.id);
          }
        });
      }
      TaskType.value = 'File';
    }
    // 数据到CDC
    else if (data == '5tyHdfcg918bb91e12y74eg345gdk') {
      // 获取唯一值
      await ToGetUUid();
      const query = {
        flowId,
        nodeName: nodeName.value,
        operatorId: '5tyHdfcg918bb91e12y74eg345gdk',
      };
      // 非修改 执行
      if (workFlowType !== 'edit' || (workFlowType === 'edit' && AddButton.value == true)) {
        // 获取节点
        await FlowNode(query).then((res) => {
          nodeId.value = res.data.id;
          if (res.code == 200) {
            emit('ChangeWorkFlowData', res.data.id);
          }
        });
      }
      TaskType.value = 'flinkCdc';
    }
  };

  const ToGetUUid = async () => {
    await getUUid().then((res) => {
      if (res.code == 200) {
        nodeName.value = res.data;
      }
    });
  };

  const save = () => {
    const query = {
      ...saveWorkFlowData.value,
      nodeList: nodeList.value,
    };

    saveWorkFlow(query).then((res) => {
      if (res.code == 200) {
        console.log('res', res);
      }
    });
  };

  const ToGetByNodeId = async () => {
    console.log('nodeId.value', nodeId.value);
    await getByNodeId(nodeId.value.value).then((res) => {
      if (res.code == 200) {
        // console.log('res.**************************************data', res.data)
        // console.log('openWorkFlowData.value', openWorkFlowData.value)
        openWorkFlowData.value = res.data;
      }
    });
  };

  const ToGetNode = async () => {
    await getNode(nodeId.value.value).then((res) => {
      if (res.code == 200) {
        typeT.value = res.data.operatorId;
        getTaskType(typeT.value);
        // console.log('res.**************************************data', res.data)
        // openWorkFlowData.value = res.data
        // console.log('openWorkFlowData.value', openWorkFlowData.value)
        console.log('res.data.operatorId', res.data.operatorId);
        // 如果是SQL任务
        if (
          res.data.operatorId == '5tyHdfcg918bb91e12y74eg345gdk' ||
          res.data.operatorId == 'a7f24fc505a14937b58c5ecec67c4d30' ||
          res.data.operatorId == '5fchweb4148bb81e12y74eg345gdk' ||
          res.data.operatorId == 'j937jdaf823rd92nda63crgvdiwe3bb' ||
          res.data.operatorId == '4uyhrj418bb91e12y74eg345gdk'
        ) {
          openWorkFlowData.value = res.data;
        }
      }
    });
  };

  onMounted(async () => {
    if (workFlowType == 'edit' && AddButton.value == false) {
      if (route.query.flowTaskType == false || route.query.flowTaskType == 'false') {
        console.log('route.query.flowTaskType', route.query.flowTaskType);
        // 回显节点
        await ToGetByNodeId();
        // 回显任务类型
        await ToGetNode();
      } else {
        // 回显KETTLE
        getTaskType(saveWorkFlowDataList.value.operatorId);
      }
    }
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .sticky-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.062);
    /* 可以根据需要设置背景色 */
    padding: 20px;
    /* 可以根据需要设置按钮容器的内边距 */
    box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
    /* 可以根据需要添加阴影效果 */
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
