<template>
  <div style="padding-top: 20px">
    <el-row>
      <el-col :span="18">
        <el-check-tag :checked="checked" round @change="onChange">表血缘</el-check-tag>
        <el-check-tag :checked="!checked" round @change="onChange">字段血缘</el-check-tag>
      </el-col>
      <!-- 下拉框 -->
      <el-col :span="6">
        <el-form v-show="!checked" :model="form" class="fixed-height-form">
          <el-form-item label="选择字段">
            <el-select
              v-model="form.dataSource"
              placeholder="请选择数据来源"
              multiple
              collapse-tags
              @change="getFieldLineageUtil"
            >
              <el-option
                v-for="item in fieldList"
                :key="item.columnName"
                :label="item.columnName"
                :value="item.columnName"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div v-show="checked" class="fixed-height-placeholder"></div>
      </el-col>
    </el-row>
  </div>
  <div class="er-flow-container">
    <erFlow
      :er="er"
      v-if="showErFlow"
      :isTable="checked"
      :workspaceId="props.assetData.workspaceId"
    />
  </div>
</template>

<script setup>
  import { getTableLineage, getFieldLineage } from '@/api/dataGovernance';
  import { getFieldList } from '@/api/DataDev';
  import erFlow from '../../components/ER/index';
  import { TRUE } from 'sass';

  const proxy = getCurrentInstance();
  const props = defineProps({
    assetData: {
      type: Object,
      required: true,
    },
  });

  const { assetData } = toRefs(props);

  const form = reactive({
    dataSource: '',
  });

  const er = ref();
  const showErFlow = ref(true);

  const checked = ref(true);
  const onChange = async () => {
    checked.value = !checked.value;
    nextTick(() => {
      er.value = null;
    });

    checked.value ? await getTableLineageUtil() : await getFieldListUtil();
  };

  const fieldList = ref([]);
  const getFieldListUtil = async () => {
    // 如果 catalog 那么改为 databaseName
    const query = {
      ...assetData.value,
    };
    query.databaseName = query.catalog;
    delete query.catalog;

    const res = await getFieldList(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (res.data.length === 0) return proxy.$message.error('字段列表为空');
    fieldList.value = res.data;
    form.dataSource = [res.data[0].columnName];
    form.dataSource && getFieldLineageUtil(form.dataSource);
  };

  // 获取文字长度
  function getTextWidth(text, font) {
    // 创建一个canvas元素
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // 设置字体样式
    context.font = font;

    // 测量文本的宽度
    const metrics = context.measureText(text);
    return metrics.width;
  }

  const fetchLineageData = async (apiFunc, params) => {
    // 判断 params 的  schema 是否为空  -  为空则删除该字段
    if (params.schema === '-') {
      delete params.schema;
    }

    const res = await apiFunc(params);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    const resData = JSON.parse(res.data);
    const NodeWidth = {
      down: [],
      up: [],
    };
    resData.forEach((item) => {
      if (item.label) {
        const labelTitle = item.label.split('  ')[0];
        // 子项目里面有超过该长度的文字，则以子文字长度计算
        if (
          labelTitle.indexOf('下') >= 0 ||
          labelTitle.indexOf('上') >= 0 ||
          labelTitle.indexOf('主') >= 0
        ) {
          NodeWidth[labelTitle] = { width: getTextWidth(item.label, 12), x: item.position.x };
          item.ports.forEach((port) => {
            const thisPortWidth = getTextWidth(port.attrs.portNameLabel.text, 12);
            if (NodeWidth[labelTitle].width < thisPortWidth) {
              NodeWidth[labelTitle].width = thisPortWidth;
            }
          });
        } else {
          // 有可能其他label但是也是在某层同级别
          for (const obj in NodeWidth) {
            if (NodeWidth[obj].x === item.position.x) {
              item.levelValue = obj;
              const thisPortWidth = getTextWidth(item.label, 12);
              if (NodeWidth[obj].width < thisPortWidth) {
                NodeWidth[obj].width = thisPortWidth;
              }
            }
          }
          //   NodeWidth.forEach((othersItem) => {
          //     if (othersItem.position.x === item.position.x) {
          //       othersItem.levelValue = item.labelTitle;
          //       if (NodeWidth[labelTitle].width < NodeWidth) {
          //         NodeWidth[labelTitle].width = NodeWidth;
          //       }
          //     }
          //   });
        }
        if (NodeWidth[labelTitle]) {
          NodeWidth[labelTitle].width = Math.ceil(NodeWidth[labelTitle].width) + 60;
          NodeWidth[labelTitle].moreWidth = NodeWidth[labelTitle].width - 150;
        }
        if (labelTitle.indexOf('下') >= 0) {
          NodeWidth.down.push(
            (NodeWidth.down.length === 0
              ? NodeWidth[labelTitle].moreWidth
              : NodeWidth.down[NodeWidth.down.length - 1]) + NodeWidth[labelTitle].moreWidth,
          );
        } else if (labelTitle.indexOf('上') >= 0) {
          NodeWidth.up.push(
            (NodeWidth.up.length === 0
              ? NodeWidth[labelTitle].moreWidth
              : NodeWidth.up[NodeWidth.up.length - 1]) + NodeWidth[labelTitle].moreWidth,
          );
        } else if (item.levelValue) {
          // 不是当前层第一个
          if (item.levelValue.indexOf('下') >= 0) {
            NodeWidth.down.push(
              (NodeWidth.down.length === 0
                ? NodeWidth[item.levelValue].moreWidth
                : NodeWidth.down[NodeWidth.down.length - 1]) + NodeWidth[item.levelValue].moreWidth,
            );
          } else if (item.levelValue.indexOf('上') >= 0) {
            NodeWidth.up.push(
              (NodeWidth.up.length === 0
                ? NodeWidth[item.levelValue].moreWidth
                : NodeWidth.up[NodeWidth.up.length - 1]) + NodeWidth[item.levelValue].moreWidth,
            );
          }
        } else {
          NodeWidth.down.push(NodeWidth[labelTitle].moreWidth);
          NodeWidth.up.push(NodeWidth[labelTitle].moreWidth);
        }
      }
    });
    // 再次循环，让阶段宽度填入最大值
    resData.map((item) => {
      if (item.label) {
        const labelTitle = item.label.split('  ')[0];
        const labelLevelValue = item.levelValue?.split('  ')[0];

        if (labelLevelValue) {
          item.width =
            NodeWidth[labelLevelValue].width > 150 ? NodeWidth[labelLevelValue].width : 150;
        } else {
          item.width = NodeWidth[labelTitle].width > 150 ? NodeWidth[labelTitle].width : 150;
        }
        if (labelTitle.indexOf('下') >= 0 || labelLevelValue?.indexOf('下') >= 0) {
          if (labelTitle.split('层')[0] >= 1) {
            item.position.x += NodeWidth.down[labelTitle.split('层')[0] - 1];
          } else if (labelLevelValue?.split('层')[0] >= 1) {
            item.position.x += NodeWidth.down[labelLevelValue.split('层')[0] - 1];
          }
        } else if (labelTitle.indexOf('上') >= 0 || labelLevelValue?.indexOf('上') >= 0) {
          if (labelTitle.split('层')[0] >= 1) {
            item.position.x -= NodeWidth.up[labelTitle.split('层')[0] - 1];
          } else if (labelLevelValue?.split('层')[0] >= 1) {
            item.position.x -= NodeWidth.up[labelLevelValue.split('层')[0] - 1];
          }
        }
      }
      return item;
    });
    er.value = resData;
  };

  const getTableLineageUtil = async () => {
    await fetchLineageData(getTableLineage, assetData.value);
  };

  const getFieldLineageUtil = async (v) => {
    if (!v || v.length === 0) return;
    // const columnArr = JSON.stringify(v);
    const params = {
      ...assetData.value,
      columnArr: v,
    };

    // showErFlow.value = false;
    await fetchLineageData(getFieldLineage, params);
    // showErFlow.value = true;
  };

  onMounted(async () => {
    await getTableLineageUtil();
  });
</script>

<style lang="scss" scoped>
  .fixed-height-form,
  .fixed-height-placeholder {
    min-height: 50px;
    max-height: 50px;
  }

  .er-flow-container {
    max-height: 480px;
    overflow-y: auto;
  }

  :deep .el-form {
    height: 40px;
    overflow: hidden;
  }
  :deep .el-input__wrapper {
    padding: 0 0.625rem;
    border-radius: 0.5rem;
    border: none;
    background: #fbfcff;
    color: #434343;
    height: 40px;
    overflow: hidden;
  }
  :deep .el-select__tags {
    height: 30px;
    overflow: hidden;
  }
</style>
