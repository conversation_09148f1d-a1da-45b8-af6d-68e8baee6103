<template>
  <div ref="divide" class="divide" @mousemove="onMouseMove" @mouseup="onMouseUp">
    <div class="pane1" :style="{ height: pixel + 'px' }">
      <slot :height="pixel" name="pane1"></slot>
    </div>
    <div
      v-if="isDrag"
      class="resizer"
      :style="{ top: pixel + 'px' }"
      @mousedown="onMouseDown"
    ></div>
    <div class="pane2" :style="{ height: otherPixel + 'px' }">
      <slot :height="otherPixel" name="pane2"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ResizerPane',
    props: {
      // 是否是像素值
      isPixel: {
        type: Boolean,
        default: true,
      },
      // 是否可拖拽
      isDrag: {
        type: Boolean,
        default: false,
      },
      // 最小
      min: {
        type: Number,
        default: 161,
      },
    },
    data() {
      return {
        // 是否开始改变
        active: false,
        // pane1高度
        pixel: 0,
        // pane2高度
        otherPixel: 0,
        // 组件高度
        offsetHeight: 200,
        // 底部最小高度
        minPixel: 161,
      };
    },
    computed: {},
    watch: {
      // 监听拖拽
      isDrag(val) {
        if (val) {
          this.otherPixel = this.minPixel;
        } else {
          this.otherPixel = 0;
        }
        this.pixel = this.offsetHeight - this.otherPixel;
      },
    },
    mounted() {
      this.offsetHeight = this.$refs.divide.offsetHeight;
      this.pixel = this.isDrag ? this.offsetHeight - this.otherPixel : this.offsetHeight - 3;
      this.$nextTick(() => {
        this.$emit('mountedNextTick');
      });
    },
    methods: {
      /**
       * 鼠标放开
       */
      onMouseUp() {
        this.active = false;
      },
      /**
       * 鼠标按下
       */
      onMouseDown() {
        this.active = true;
      },
      /**
       * 鼠标移动
       * @param e 事件对象
       */
      onMouseMove(e) {
        if (e.buttons === 0 || e.which === 0) {
          this.active = false;
        }
        if (this.active) {
          let offset = 0;
          let target = e.currentTarget;
          while (target) {
            offset += target.offsetTop;
            target = target.offsetParent;
          }
          const currentPage = e.pageY;
          const targetOffset = e.currentTarget.offsetHeight;
          const pixel = currentPage - offset;
          if (this.isPixel) {
            // pane1的高度至少为组件高度的一半，最多为组件高度减去pane1的最小高度
            if (pixel > targetOffset / 2 - 10 && pixel < targetOffset - this.minPixel) {
              this.pixel = pixel;
              this.otherPixel = targetOffset - pixel;
            }
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .divide {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    position: relative;
    .pane1,
    .pane2 {
      width: 100%;
      /*height: 100%;*/
    }
  }
  .resizer {
    width: 100%;
    height: 1px;
    background: $base-border-color;
    cursor: row-resize;
    padding: 1px 0;
    position: absolute;
    top: 0;
    z-index: $z-index-1000;
  }
</style>
