import request from '@/utils/request';
import { encrypt } from '@/utils/jsencrypt'; // 加密 解密

// 获取工作流列表ID
export function getTaskList(params) {
  return request({
    url: '/operator/algFlowInstance/taskIds',
    method: 'get',
    params,
  });
}
// 获取工作流列表log

export function getTaskInstanceLog(taskId, limit, skipLineNum) {
  return request({
    url:
      '/operator/algFlowInstance/log?taskId=' +
      `${taskId}&limit=${limit}&skipLineNum=${skipLineNum}`,
    method: 'get',
  });
}
// 数据源测试连接
export function connect(data) {
  // if (!data.password) {
  //   data.password = '';
  // } else if (data.password.length < 20) {
  //   data.password = encrypt(data.password);
  // }
  return request({
    url: '/xugurtp-datasource/datasources/connect',
    method: 'post',

    data,
  });
}
// getNode
export function getNode(query) {
  return request({
    url: `/operator/algFlowNode/getNode?nodeId=${query}`,
    method: 'get',
  });
}
// getUUid
export function getUUid() {
  return request({
    url: '/operator/project/getUUid',
    method: 'get',
  });
}

// 保存工作流
export function saveWorkFlow(data) {
  return request({
    url: '/operator/algFlow/saveWorkFlow',
    method: 'post',
    data,
  });
}

// 打开工作流
export function openWorkFlow(data) {
  return request({
    url: `/operator/algFlow/openWorkFlow?id=${data}`,
    method: 'get',
  });
}
// getByNodeId
export function getByNodeId(data) {
  return request({
    url: `/operator/offline-operator/getByNodeId?nodeId=${data}`,
    method: 'get',
  });
}

// 查询数据源列表
export function list(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list`,
    method: 'get',
    params,
  });
}

// 分页查询数据源列表
export function listPaging(params) {
  return request({
    url: '/xugurtp-datasource/datasources/list-paging',
    method: 'get',
    params,
  });
}

// 新增数据源
export function create(data) {
  // console.log('data', data);
  // if (!data.password) {
  //   data.password = '';
  // } else if (data.password.length < 20) {
  data.password = encrypt(data.password);
  // }
  return request({
    url: '/xugurtp-datasource/datasources/create',
    method: 'post',
    data,
  });
}

// 更新数据源
export function update(data, old) {
  // 密码未加密，加密
  if (!data.password) {
    data.password = '';
  } else if (data.password !== old) {
    //
    // if (data.password.length < 20) {
    data.password = encrypt(data.password);
    console.log('data.password', data.password);
    // }
  }

  return request({
    url: '/xugurtp-datasource/datasources/update',
    method: 'post',
    data,
  });
}

// 删除数据源
export function deleteDatasource(query) {
  return request({
    url: `/xugurtp-datasource/datasources/delete?id=${query}`,
    method: 'get',
  });
}

// 根据ID测试数据源
export function connectById(query) {
  return request({
    url: `/xugurtp-datasource/datasources/connect-by-id?id=${query}`,
    method: 'get',
  });
}

// 根据ID查询数据源详情
export function getInfoById(query) {
  return request({
    url: `/xugurtp-datasource/datasources/getById?id=${query}`,
    method: 'get',
  });
}

// 查询数据源详情
export function updateUi(query) {
  return request({
    url: `/xugurtp-datasource/datasources/update-ui`,
    method: 'get',
    params: query,
  });
}

// 查询数据库列表
export function getDatabaseList(query) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params: query,
  });
}

// 查询表列表
export function getTableList(params) {
  params.schema = params.schemaName || params.schema;
  delete params.schemaName;
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}

// 新增分页查询表列表
export function getTableForPage(params) {
  params.schema = params.schemaName || params.schema;
  delete params.schemaName;
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTablePage`,
    method: 'get',
    params,
  });
}

// 查询字段列表
export function getFieldList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getFieldList`,
    method: 'get',
    params,
  });
}

// 查询工作空间列表
export function getWorkspaceList(query) {
  return request({
    url: '/system/workspace/getWorkspaceList',
    method: 'get',
    params: query,
  });
}

// 获取元数据字段映射关系
export function getFieldMapList(query) {
  return request({
    url: '/operator/metadata/getFieldMap',
    method: 'get',
    params: query,
  });
}

// 构建 流程 节点
export function FlowNode(query) {
  return request({
    url: `/operator/algFlowNode/buildFlowNode?operatorId=${query.operatorId}&flowId=${query.flowId}&nodeName=node-${query.nodeName}`,
    method: 'get',
  });
}
// 构建 流程 节点
export function FlowNodeSaveNode(data) {
  return request({
    url: `/operator/algFlowNode/saveNode`,
    method: 'post',
    data,
  });
}
// 创建或更新
export function createOrUpdate(data) {
  return request({
    url: `/operator/offline-operator/createOrUpdate`,
    method: 'post',
    data,
  });
}

//! ---------------------------------------------------
// /taskFlowInstance/log
export function getTaskFlowInstanceLog(data) {
  return request({
    url: '/xugurtp-task-executor/taskFlowInstance/log',
    method: 'post',
    data,
  });
}

// /taskFlowInstance/page
export function getEmbellishRestrictList(params) {
  return request({
    url: `xugurtp-task-executor/taskFlowInstance/page`,
    method: 'get',
    params,
  });
}

// Task instance 分页查询
export function processInstance(query) {
  return request({
    url: `/operator/algFlowInstance/page`,
    method: 'get',
    params: query,
  });
}
// 删除流程实例
export function deleteWorkflowInstance(query) {
  return request({
    url: `/operator/algFlowInstance/delete/${query.workSpaceId}/${query.id}`,
    method: 'delete',
  });
}
// 删除流程实例
// /taskFlowInstance/delete
export function deleteTaskFlowInstance(params) {
  return request({
    url: `/xugurtp-task-executor/taskFlowInstance/delete`,
    method: 'delete',
    params,
  });
}

// 查询工作流日志
export function getWorkflowInstanceLog(query) {
  return request({
    url: `/operator/algFlowInstance/log/${query.workSpaceId}/${query.id}`,
    method: 'get',
  });
}

// 查询前置列表
export function listForPreApi(query) {
  return request({
    url: `/xugurtp-datasource/datasources/listForPreApi?workSpaceId=${query}`,
    method: 'get',
  });
}

// Gp查询模式
export function schemaForGP(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getSchemaList`,
    method: 'get',
    params,
  });
}
// Gp查询表
export function tableForGP(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}

// apiDetail
export function apiDetail(query) {
  return request({
    url: `/xugurtp-datasource/datasources/apiDetail?id=${query}`,
    method: 'get',
  });
}

// 查询cron表达式
export function schedules(data) {
  return request({
    url: `operator/scheduler/preview`,
    method: 'post',
    data,
  });
}
// 重跑工作流
export function Rerun(query) {
  console.log('query', query);
  return request({
    url: `operator/algFlowInstance/runAgain/${query.workSpaceId}/${query.processCode}`,
    // url: `operator/algFlowInstance/runAgain/${query.workSpaceId}/${query.processCode}/${query.userName}`,
    method: 'post',
    // data
  });
}

// 停止工作流
export function StopProcess(query) {
  console.log('query', query);
  return request({
    url: `operator/algFlowInstance/stop/${query.workSpaceId}/${query.flowInstanceId}`,
    method: 'post',
    // data
  });
}

// getByTypeTableList
export function getByTypeTableList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getByTypeTableList`,
    method: 'get',
    params,
  });
}

// 新增接口-用于单次运行查询实例
export function getSingleRunInstance(params) {
  return request({
    url: `/operator/algFlowInstance/last`,
    method: 'get',
    params,
  });
}

// /datasource/datasources/connectMinio
export function connectMinio(data) {
  return request({
    url: `/datasource/datasources/connectMinio`,
    method: 'post',
    data,
  });
}

// /xugurtp-datasource/datasources/createMinio
export function createMinio(data) {
  return request({
    url: `/xugurtp-datasource/datasources/createMinio`,
    method: 'post',
    data,
  });
}

// /xugurtp-datasource/datasources/metadata/getBucketList
export function getBucketList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getBucketList`,
    method: 'get',
    params,
  });
}

// /xugurtp-datasource/datasources/metadata/getBucketObjList
export function getBucketObjList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getBucketObjList`,
    method: 'get',
    params,
  });
}
// /xugurtp-datasource/datasources/metadata/getBucketRootObjs
export function getBucketRootObjs(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getBucketRootObjs`,
    method: 'get',
    params,
  });
}
