<template>
  <div v-if="divShow">
    <SplitPanes>
      <template #left>
        <themeLeft
          :data-tree="dataTree"
          :tree-props="props"
          @node-click="handleNodeClick"
          @filter-change="onChange"
        />
      </template>
      <template #right>
        <section class="App-theme">
          <div style="height: 100%">
            <el-empty v-if="!dataNode" description="请选择" style="height: 100%"></el-empty>

            <el-tabs
              v-if="dataNode"
              v-model="activeName"
              class="demo-tabs"
              @tab-click="handleClick"
            >
              <el-tab-pane label="明细表" name="first">
                <div class="pm">
                  <el-row>
                    <el-col :span="8">
                      <el-button icon="Plus" type="primary" @click="jumpTo">新增</el-button>
                      <!-- <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> -->
                      <!-- <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> -->
                      <!-- <el-button type="" @click="remove">删除</el-button> -->
                    </el-col>

                    <el-col :span="16">
                      <div class="operationType">
                        <div class="input-box">
                          <el-input
                            v-model="input3"
                            placeholder="请输入名称"
                            class="input-with-select"
                            size="mini"
                          >
                            <template #prepend>
                              <el-select
                                v-model="selectName"
                                placeholder="Select"
                                style="width: 115px"
                                size="mini"
                              >
                                <el-option
                                  v-for="dict in model_search_type"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </template>
                          </el-input>
                        </div>

                        <div class="input-box">
                          <el-date-picker
                            v-model="time"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="[
                              new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 1, 1, 23, 59, 59),
                            ]"
                            :disabled-date="disablesDate"
                          ></el-date-picker>
                        </div>

                        <right-toolbar
                          :search="false"
                          :columns="columns"
                          class="toolbar-right"
                          @query-table="reload(dataNode?.id)"
                        ></right-toolbar>
                        <el-button
                          circle
                          class="search-btn"
                          icon="Search"
                          @click="getDataModelLogicListUtil(dataNode?.id)"
                        ></el-button>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <div class="table-box">
                  <el-table
                    ref="tableRef"
                    row-key="date"
                    :data="ODStableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                  >
                    <!-- 选择框 -->
                    <!-- <el-table-column type="selection" width="55" align="center" /> -->
                    <el-table-column
                      prop="code"
                      label="明细表名称"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columns[0].visible"
                      prop="name"
                      label="明细表注释"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columns[1].visible"
                      label="表类型"
                      width="200"
                      align="center"
                      prop="tableType"
                    >
                      <template #default="scope">
                        <dict-tag :options="table_type" :value="scope.row.tableType" />
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="columns[2].visible"
                      prop="status"
                      label="状态"
                      width="200"
                      :filters="[
                        { text: '草稿', value: '2' },
                        { text: '上线', value: '1' },
                        { text: '下线', value: '0' },
                      ]"
                      :filter-method="filterTag"
                      filter-placement="bottom-end"
                    >
                      <template #default="scope">
                        <el-tag
                          :type="filterTagType(scope.row.status)"
                          :disable-transitions="true"
                          round
                          effect="plain"
                        >
                          {{ filterTagTypeText(scope.row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="columns[3].visible"
                      prop="catalogName"
                      label="所属主题"
                      width="200 "
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[4].visible"
                      prop="remark"
                      label="描述"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[5].visible"
                      prop="createBy"
                      label="创建人"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[6].visible"
                      prop="updateTime"
                      label="更新时间"
                      sortable
                      width="200"
                    />

                    <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                      <template #default="scope">
                        <el-button type="text" size="small" @click="revamp(scope)"
                          >{{ scope.row.status === 2 ? '编辑' : '查看' }}
                        </el-button>

                        <el-button
                          v-if="scope.row.status == 0 || scope.row.status == 2"
                          type="text"
                          @click="updateStatusUtil(scope.row, 1)"
                        >
                          发布
                        </el-button>
                        <el-button
                          v-if="scope.row.status === 1"
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="updateStatusUtil(scope.row, 0)"
                        >
                          下线
                        </el-button>
                        <el-button
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="remove(scope.row)"
                        >
                          删除
                        </el-button>
                        <!-- <el-dropdown> -->
                        <!-- <span class="el-dropdown-link"> -->
                        <!-- 更多 -->
                        <!-- <el-icon class="el-icon--right"> -->
                        <!-- <arrow-down /> -->
                        <!-- </el-icon> -->
                        <!-- </span> -->
                        <!-- <template #dropdown> -->
                        <!-- <el-dropdown-menu> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" @click="updateStatusUtil(scope.row, 0)"> -->
                        <!-- 下线 -->
                        <!-- </el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" @click="remove(scope.row)" :disabled="scope.row.status == 1"> -->
                        <!-- 删除 -->
                        <!-- </el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- </el-dropdown-menu> -->
                        <!-- </template> -->
                        <!-- </el-dropdown> -->
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <div style="margin-bottom: 20px">
                  <!-- 分页 -->
                  <pagination
                    v-show="total > 0"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    :pager-count="maxCount"
                    :total="total"
                    @pagination="listPage"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </section>
      </template>
    </SplitPanes>
  </div>
  <dwdModule
    v-if="DWIshow"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :data-node="dataNode"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  />
</template>

<script setup>
  import {
    deleteDataModelLogic,
    getCatalogTree,
    getDataModelLogicDetail,
    getDataModelLogicList,
    updateDataModelLogic,
    warehouseType,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import SplitPanes from '@/components/SplitPanes/index';
  import themeLeft from '@/views/DataMmodeling/components/themeLeft/index.vue';

  import dwdModule from './module/dwdModule/index.vue';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const { table_type, model_search_type } = proxy.useDict('table_type', 'model_search_type');
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `明细表中文名`, visible: true },
    { key: 1, label: `表类型`, visible: true },
    { key: 2, label: `状态`, visible: true },
    { key: 3, label: `所属主题`, visible: true },
    { key: 4, label: `描述`, visible: true },
    { key: 5, label: `创建人`, visible: true },
    { key: 6, label: `更新时间`, visible: true },
  ]);

  const maxCount = ref(5);
  const total = ref(0);
  const listPage = async () => {
    await getDataModelLogicListUtil(nodeClick.value.data.id);
  };

  const updateStatusUtil = async (row, status) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下操作');
      return;
    }
    const query = {
      id: row.id,
      status,
    };
    const res = await updateDataModelLogic(query);
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id);
    }
  };

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };
  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '0',
      pid: '',
      database: '',
      datasource: '',
      // 状态
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        // 校验
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
        // 校验
        {
          pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入描述', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const tableRef = ref();

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };
  const filterHandler = (value, row, column) => {
    const property = column.property;
    return row[property] === value;
  };

  // 使用计算属性 判断类型 如果是 online 就是 success 如果是下线就是 danger 如果是草稿 是 ' '
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };

  const dialogVisible = ref(false);

  const addTree = () => {
    dialogVisible.value = true;
  };

  const filterText = ref();
  const onChange = () => {
    getCatalogTreeUtil();
  };
  const activeName = ref('first');

  const handleClick = (tab, event) => {
    input3.value = '';
    time.value = '';
    if (tab.props.name == 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'DWD');
    } else if (tab.props.name == 'second') {
      getDataModelLogicListUtil(dataNode.value.id, 'DWI');
    }
  };
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();

  function showContextMenu(event, data, node) {
    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    const menuDataCopy = data;
    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  const clickHandler = (event) => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };
  const dataTree = ref();
  const dataNode = ref();
  const nodeClick = ref();
  const handleNodeClick = async ({ e, data, node }) => {
    input3.value = '';
    time.value = '';
    dataNode.value = data;
    nodeClick.value = node;
    activeName.value = 'first';
    await getDataModelLogicListUtil(data.id);
    // console.log(dataNode.value)
    // console.log(nodeClick.value)
    // console.log(node)
  };

  const getCatalogTreeUtil = async (value) => {
    ODStableData.value = [];
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      searchName: value,
    };
    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  const ODStableData = ref();
  const time = ref();
  const selectName = ref();
  const reload = async (data) => {
    input3.value = '';
    time.value = '';
    await getDataModelLogicListUtil(data);
  };

  const getDataModelLogicListUtil = async (data, level = 'DWD') => {
    const query = {
      workspaceId: workspaceId.value,
      thmeId: data,
      // type: '0',
      level,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
      [selectName.value]: input3?.value,

      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    const res = await getDataModelLogicList(query);
    if (res.code === 200) {
      ODStableData.value = res.rows;
      // maxCount.value = res.total
      total.value = res.total;
    }
  };
  const DWIshow = ref(false);
  const ODSshow = ref(false);
  //
  // 使用计算属性 判断 divShow 是否显示   !ODSshow 或者 !DWIshow
  const divShow = computed(() => {
    return !ODSshow.value && !DWIshow.value;
  });
  watch(divShow, (val) => {
    console.log(val);
    if (val) {
      console.log(val);
      nextTick(() => {
        console.log(nodeClick.value);
        proxy.$refs.treeRef.setCurrentKey(dataNode.value?.id);
        proxy.$refs.treeRef.expandNode(nodeClick?.value?.parent);
        proxy.$refs.treeRef.expandNode(nodeClick?.value?.parent?.parent);
      });
    }
  });

  // 获取当前数据库类型
  const getDatabaseType = async () => {
    let res = '';
    // 这里是异步获取数据库类型的逻辑
    res = await warehouseType();
    return res;
  };
  const jumpTo = async (row) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }
    const DatabaseType = await getDatabaseType();
    rowData.value.dataType = DatabaseType.data;
    if (activeName.value === 'first') {
      DWIshow.value = true;
    } else if (activeName.value === 'second') {
      ODSshow.value = true;
    }
  };
  // const addDataModelLogicUtil = async () => {
  //     let query = {
  //         workspaceId: workspaceId.value,
  //         thmeId: dataTree.value.id,
  //         name: form.name,
  //         code: form.code,
  //         desc: form.desc,
  //     }
  //     const res = await addDataModelLogic(query)
  //     console.log(res)
  //     // 查询
  // }

  const toBack = async (e) => {
    ODSshow.value = false;
    DWIshow.value = false;
    rowData.value = {};
    await getDataModelLogicListUtil(dataNode.value.id);
  };
  const fulfill = async (e, level) => {
    await getDataModelLogicListUtil(dataNode.value.id, level);
  };

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref([]);
  const input3 = ref('');
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };

  const revamp = (data) => {
    console.log(data.row);
    // ODSshow.value = true
    getDataModelLogicDetailUtil(data.row.id);
  };

  const rowData = ref({});
  const getDataModelLogicDetailUtil = async (data) => {
    const query = {
      id: data,
    };
    const res = await getDataModelLogicDetail(query);
    if (res.code === 200) {
      console.log(res);
      rowData.value = res.data;
      const DatabaseType = await getDatabaseType();
      rowData.value.dataType = DatabaseType.data;
      if (activeName.value === 'first') {
        DWIshow.value = true;
      } else if (activeName.value === 'second') {
        ODSshow.value = true;
      }
    } else {
      proxy.$modal.msgError(res.message);
    }
  };
  const remove = async (data) => {
    const Vname =
      INames?.value && ids?.value.length > 0 ? INames?.value : treeData?.value?.label || data?.name;
    // const ossIds = (ids?.value && ids?.value.length > 0) ? ids?.value : (treeData?.value?.id || data?.id)

    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');

    if (!res) return;
    await deleteCatalogUtil(data.id);
    await getDataModelLogicListUtil(dataNode.value.id);
  };

  const deleteCatalogUtil = async (ids) => {
    const query = {
      id: ids,
    };
    const res = await deleteDataModelLogic(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    await getCatalogTreeUtil();
  };

  onMounted(async () => {
    // window.addEventListener("beforeunload", beforeunload());
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, (val) => {
    console.log(val);
    getCatalogTreeUtil();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .tree-box {
    height: 100%;
    background-color: $--base-color-item-light;
    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: 100%;
      background: $--base-color-item-light;
    }
  }
  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    height: 100%;
    overflow: auto;
    padding: 0px !important;
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px 0px;
    margin: 10px 0px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    .search-btn,
    .input-box,
    .toolbar-right {
      display: inline-block;
      vertical-align: top;
      margin-left: 10px;
    }
    .input-box {
      width: calc(50% - 100px);
      & > div {
        width: 100%;
      }
    }
    display: inline-block;
    text-align: right;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
    :deep & > div,
    & > button {
      margin-left: 16px;
    }
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }
  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
</style>
