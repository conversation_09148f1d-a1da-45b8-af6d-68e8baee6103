<template>
  <div class="box-body" :style="'height:' + props.height + 'px;'">
    <div class="time">
      <div class="left-controls">
        <el-tooltip v-if="props.needTime" content="插入当前时间" placement="bottom" effect="light">
          <el-icon class="icon fa" @click="range(editor, '123')">
            <Timer />
          </el-icon>
        </el-tooltip>
        <el-tooltip v-if="props.needTips" content="添加注释" placement="bottom" effect="light">
          <el-icon class="icon fa" @click="tips(editor)">
            <SemiSelect />
          </el-icon>
        </el-tooltip>
      </div>

      <div class="editor-controls">
        <el-tooltip content="编辑器设置" placement="bottom" effect="light">
          <el-icon class="icon setting-icon" @click="openConfigDialog">
            <Setting />
          </el-icon>
        </el-tooltip>
      </div>
    </div>

    <div id="codeBox"></div>

    <!-- 编辑器配置对话框 -->
    <EditorConfigDialog v-model:visible="configDialogVisible" v-model:config="editorConfig" />
  </div>
</template>
<script setup>
  import { SemiSelect, Setting, Timer } from '@element-plus/icons-vue';
  import * as monaco from 'monaco-editor';
  import { onBeforeUnmount, onMounted, ref, toRaw, watch } from 'vue';
  import { defaultEditorConfig, registerLanguageProviders } from './config.js';
  import EditorConfigDialog from './EditorConfigDialog.vue';
  import vCompletion from './sql.js';
  import { range, tips, setupCustomContextMenu } from './util.js';

  const emit = defineEmits([
    'contentChange',
    'update:disabled',
    'update:showMinimap',
    'update:modelValue',
    'update:language',
    'writeToField',
  ]);

  const props = defineProps({
    value: { type: String, default: '' },
    height: { type: [String, Number], default: 500 },
    needTips: { type: Boolean, default: true },
    needTime: { type: Boolean, default: true },

    disabled: { type: Boolean, default: false },
    showMinimap: { type: Boolean, default: true },

    modelValue: { type: String, default: '' }, // 支持v-model
    language: { type: String, default: 'XML' }, // 编辑器语言
  });

  const editor = ref(null);
  const provider = ref(null);
  const myLang = ref(null);
  const color = ref(null);
  const hover = ref(null);

  // 配置对话框相关
  const configDialogVisible = ref(false);

  const editorConfig = ref({
    ...defaultEditorConfig,
    readOnly: props.disabled,
    minimap: { enabled: props.showMinimap },
  });

  // 打开配置对话框
  const openConfigDialog = () => {
    configDialogVisible.value = true;
  };

  // 创建编辑器实例
  const createEditorInstance = () => {
    // 合并默认配置和用户配置
    const mergedConfig = {
      ...editorConfig.value,
      value: props.modelValue || props.value, // 优先使用modelValue支持v-model
      language: props.language || editorConfig.value.language, // 优先使用props.language
      readOnly: props.disabled, // 优先使用props.disabled
      minimap: {
        ...editorConfig.value.minimap,
        enabled: props.showMinimap, // 优先使用props.showMinimap
      },
    };

    editor.value = monaco.editor.create(document.getElementById('codeBox'), mergedConfig);

    // 设置自定义右键菜单
    setupCustomContextMenu(editor.value, emit);

    // 监听内容变化
    editor.value.onDidChangeModelContent(() => {
      const content = toRaw(editor.value).getValue();
      emit('contentChange', content);
      emit('update:modelValue', content); // 支持v-model
    });
  };

  // 初始化编辑器
  const initEditor = () => {
    registerLanguageProviders(monaco, provider, myLang, color, hover, vCompletion);
    createEditorInstance();
  };

  onMounted(() => {
    initEditor();
  });

  // 组件销毁前清理资源
  onBeforeUnmount(() => {
    if (editor.value) {
      // 销毁编辑器实例
      toRaw(editor.value).dispose();
    }

    // 清理语言提供者
    if (provider.value) provider.value.dispose();
    if (myLang.value) myLang.value.dispose();
    if (color.value) color.value.dispose();
    if (hover.value) hover.value.dispose();
  });

  // 统一监听value和modelValue变化
  watch(
    [() => props.value, () => props.modelValue],
    ([newValue, newModelValue]) => {
      if (editor.value) {
        const currentValue = toRaw(editor.value).getValue();
        // 优先使用modelValue，如果modelValue不存在则使用value
        const valueToUse = newModelValue !== undefined ? newModelValue : newValue;

        // 避免重复设置相同的值
        if (valueToUse !== currentValue) {
          toRaw(editor.value).setValue(valueToUse);
        }
      }
    },
    { deep: true },
  );

  // 监听disabled属性变化
  watch(
    () => props.disabled,
    (newVal) => {
      if (editor.value) {
        toRaw(editor.value).updateOptions({ readOnly: newVal });
        editorConfig.value.readOnly = newVal; // 同步配置对象
      }
    },
  );

  // 监听showMinimap属性变化
  watch(
    () => props.showMinimap,
    (newVal) => {
      if (editor.value) {
        toRaw(editor.value).updateOptions({ minimap: { enabled: newVal } });
        editorConfig.value.minimap.enabled = newVal; // 同步配置对象
      }
    },
  );

  // 监听配置变更
  watch(
    () => editorConfig.value,
    (newConfig) => {
      if (editor.value) {
        // 更新编辑器配置
        toRaw(editor.value).updateOptions(newConfig);

        // 更新编辑器模型的语言
        if (newConfig.language) {
          monaco.editor.setModelLanguage(toRaw(editor.value).getModel(), newConfig.language);
        }

        // 同步props
        emit('update:disabled', newConfig.readOnly);
        emit('update:showMinimap', newConfig.minimap.enabled);
        emit('update:language', newConfig.language);
      }
    },
    { deep: true },
  );

  // 监听language属性变化
  watch(
    () => props.language,
    (newLanguage) => {
      if (editor.value) {
        // 更新编辑器模型的语言
        monaco.editor.setModelLanguage(toRaw(editor.value).getModel(), newLanguage);
        // 同步配置对象
        editorConfig.value.language = newLanguage;
      }
    },
  );
</script>
<style scoped>
  #codeBox {
    width: 100%;
    height: calc(100% - 40px);
    /* 固定高度计算，减去顶部控制栏高度 */
  }

  .time {
    width: 100%;
    border: 1px solid #ccc;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    height: 40px;
    /* 固定高度 */
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .left-controls {
    display: flex;
    align-items: center;
    margin-left: 15px;
  }

  .icon {
    display: inline-block;
    font-size: 20px !important;
    margin: 0 10px;
  }

  .icon:hover {
    color: #ffa500;
    font-weight: bold;
  }

  .editor-controls {
    display: flex;
    align-items: center;
    margin-right: 15px;
    gap: 15px;
  }

  .control-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .control-label {
    font-size: 12px;
    color: #606266;
  }

  .setting-icon {
    cursor: pointer;
    font-size: 18px;
    color: #409eff;
    transition: transform 0.3s ease;
  }

  .setting-icon:hover {
    transform: rotate(30deg);
    color: #66b1ff;
  }
</style>
