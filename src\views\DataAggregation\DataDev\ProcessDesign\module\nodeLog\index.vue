<template>
  <div class="node-log">
    <div class="title-log">
      <div>
        <span>运行日志</span>
        <!-- <span
          style="margin-left: 10px; cursor: pointer"
          @click="emit('refreshLog', nodeLogData[0].id)"
        >
          <el-icon style="color: #434343"><Refresh /></el-icon>
        </span> -->
        <el-button
          size="small"
          style="margin-left: 10px"
          type="light"
          v-if="tableData.length"
          @click="emit('refreshLog', nodeLogData[0].processDefinitionCode)"
          >刷新</el-button
        >
        <!-- <span
          style="margin-left: 20px; cursor: pointer"
          @click="emit('closeLog')"
        >
          <el-icon><Close /></el-icon>
        </span> -->
        <el-button size="small" style="margin-left: 10px" type="light" @click="emit('closeLog')"
          >关闭</el-button
        >
      </div>
    </div>
    <el-table
      :data="tableData"
      height="100px"
      size="mini"
      empty-text="暂无数据"
      style="min-height: 50px"
    >
      <el-table-column type="index" width="60" align="center" label="序号"> </el-table-column>
      <el-table-column label="流程实例ID" prop="id" show-overflow-tooltip align="center" />
      <el-table-column label="流程名称" prop="name" show-overflow-tooltip align="center" />
      <!-- <el-table-column label="流程类型" prop="commandType" /> -->
      <el-table-column label="执行状态" prop="state" show-overflow-tooltip align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.state == 'FAILURE'" type="danger">失败</el-tag>
          <el-tag v-if="scope.row.state == 'STOP'" type="info">停止</el-tag>
          <el-tag v-if="scope.row.state == 'RUNNING_EXECUTION'">正在执行</el-tag>
          <el-tag v-if="scope.row.state == 'SUCCESS'" type="SUCCESS">成功</el-tag>
          <el-tag v-if="scope.row.state == 'SERIAL_WAIT'" type="info">排队中...</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="运行类型" prop="commandType" show-overflow-tooltip align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.commandType == 'REPEAT_RUNNING'" type="warning">重跑</el-tag>
          <el-tag v-if="scope.row.commandType == 'STOP'" type="info">停止</el-tag>
          <el-tag v-if="scope.row.commandType == 'RECOVER_SERIAL_WAIT'" type="info"
            >串行恢复</el-tag
          >
          <el-tag v-if="scope.row.commandType == 'START_PROCESS'">开始进程</el-tag>
          <el-tag v-if="scope.row.commandType == 'SCHEDULER'" effect="plain">调度执行</el-tag>
          <el-tag v-if="scope.row.commandType == 'RECOVER_TOLERANCE_FAULT_PROCESS'" effect="plain"
            >容错恢复过程</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="startTime" show-overflow-tooltip align="center" />
      <el-table-column label="结束时间" prop="endTime" show-overflow-tooltip align="center" />
      <el-table-column label="运行时间" prop="duration" show-overflow-tooltip align="center" />
      <el-table-column label="操作" min-width="300">
        <template #default="scope">
          <!-- <el-button link type="primary" @click="handleUpdate(scope.row)" disabled="true">编辑</el-button> -->

          <el-button type="text" @click="emit('viewLog', scope.row.id)"> 查看日志 </el-button>
          <el-button
            :disabled="
              scope.row.state == 'FAILURE' ||
              scope.row.state == 'SUCCESS' ||
              scope.row.state == 'STOP'
            "
            type="text"
            @click="stopTask(scope.row.id)"
          >
            停止
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup>
  import { StopProcess } from '@/api/dataSourceManageApi';
  import { getCurrentInstance } from 'vue';
  const props = defineProps({
    runningColumn: {
      type: Array,
      default: () => [],
    },
    height: {
      type: [String, Number],
      default: 0,
    },
    nodeLogData: {
      type: [Object, Array, String],
      default: () => ({}),
    },
    workspaceId: {
      type: Number,
      default: null,
    },
  });
  const { nodeLogData } = toRefs(props);

  // eslint-disable-next-line vue/valid-define-emits
  const emit = defineEmits();

  const tableData = ref([]);
  // 定义表头 循环显示
  const columns = [
    {
      prop: 'flowId',
      label: '流程 ID',
      minWidth: 20,
      tooltip: true,
      width: 125,
    },
    {
      prop: 'flowName',
      label: '流程名称',
      minWidth: 40,
      tooltip: true,
      width: 125,
    },
    {
      prop: 'nodeId',
      label: '节点 ID',
      minWidth: 40,
      tooltip: true,
      width: 125,
    },
    {
      prop: 'nodeName',
      label: '节点名称',
      minWidth: 40,
      tooltip: true,
      width: 125,
    },
    {
      prop: 'statusCode',
      label: '状态',
      width: 125,
      minWidth: 120,
    },
    {
      prop: 'beginTime',
      label: '开始时间',
      width: 125,
      tooltip: true,
      timestamp: true,
    },
    {
      prop: 'endTime',
      label: '结束时间',
      width: 125,
      tooltip: true,
      timestamp: true,
    },
    {
      prop: 'cost',
      label: '耗时 (ms)',
      width: 125,
    },
    {
      prop: 'msg',
      label: '日志内容',
      minWidth: 80,
      tooltip: true,
      width: 160,
    },
  ];

  // 处理时间
  const timestamp = (value) => {
    if (!value) return '';
    // 不使用第三方库 处理 日期
    const date = new Date(value);
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    const hour = ('0' + date.getHours()).slice(-2);
    const minute = ('0' + date.getMinutes()).slice(-2);
    const second = ('0' + date.getSeconds()).slice(-2);
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  };

  const calculateTableHeight = () => {
    const rowsCount = tableData.value.length;
    if (rowsCount === 0) {
      return 150; // Minimum height
    } else if (rowsCount <= 3) {
      return rowsCount * 50 + 100; // Estimate height based on rows
    } else {
      return 260; // Max height
    }
  };

  const tableHeight = ref(calculateTableHeight());

  watch(nodeLogData, (newNodeLogData) => {
    tableData.value = newNodeLogData;
    tableHeight.value = calculateTableHeight();
    if(newNodeLogData?.length) {
      autoRefresh(newNodeLogData[0]);
    }
  });

  const autoRefresh = (data) => {
    if (data.state == 'RUNNING_EXECUTION') {
      emit('changeRunStatus',true)
      setTimeout(() => {
        emit('refreshLog', data.processDefinitionCode);
      }, 2000);
    } else {
      emit('changeRunStatus',false)
    }
  };

  const { proxy } = getCurrentInstance();

  // 停止实例
  function stopTask(id) {
    const query = {
      workSpaceId: props.workspaceId,
      flowInstanceId: id,
    };
    StopProcess(query).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        emit('refreshLog', props.nodeLogData[0].processDefinitionCode);
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  }
  defineExpose({stopTask})
</script>
<style lang="scss" scoped>
  $base-border-color: #ebeef5;
  $base-bg-color: #f5f7fa;
  $base-primary: #2468f1;
  $base-success: #33c89c;
  $base-text-color-regular: #98a5b9;
  $base-danger: #ff6760;

  .node-log {
    width: 100% !important;
    position: relative;

    .title-log {
      height: 38px;
      line-height: 38px;
      padding-left: 16px;
      font-size: 14px;
      font-weight: 600;
      border-top: 1px solid $base-border-color;
      background-color: $base-bg-color;
    }

    .custom-tag {
      width: 60px;
      height: 22px;
      background: rgba(36, 104, 241, 0.05);
      border-radius: 2px;
      border: 1px solid rgba(36, 104, 241, 0.6);
      color: $base-primary;
      display: block;
      text-align: center;

      &.success {
        background: rgba(51, 200, 156, 0.05);
        border: 1px solid rgba(51, 200, 156, 0.6);
        color: $base-success;
      }

      &.fail {
        background: rgba(152, 165, 185, 0.05);
        border: 1px solid rgba(152, 165, 185, 0.6);
        color: $base-text-color-regular;
      }

      &.error {
        background: rgba(255, 103, 96, 0.05);
        border: solid rgba(255, 103, 96, 0.6);
        color: $base-danger;
      }
    }
  }
</style>
