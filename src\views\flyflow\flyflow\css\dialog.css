.person_body {
  border: 1px solid #f5f5f5;
  height: 500px;
}
.tree_nav p {
  display: inline-block;
  padding-right: 10px;
  margin-right: 5px;
  max-width: 6em;
	color:var(--el-color-primary);

	font-size: 12px;
  cursor: pointer;
  background: url(../assets/images/jiaojiao.png) no-repeat right center;
}
.tree_nav span:last-of-type {
  background: none;
}
.person_tree {
  padding: 10px 12px 0 8px;
  width: 260px;
  height: 100%;
  border-right: 1px solid #f5f5f5;
}
/*.person_tree input {*/
/*  padding-left: 22px;*/
/*  width: 210px;*/
/*  height: 30px;*/
/*  font-size: 12px;*/
/*  border-radius: 2px;*/
/*  border: 1px solid #d5dadf;*/
/*  background: url(../assets/images/list_search.png) no-repeat 10px center;*/
/*  background-size: 14px 14px;*/
/*  margin-bottom: 14px;*/
/*}*/
.l {
	float: left;
}
