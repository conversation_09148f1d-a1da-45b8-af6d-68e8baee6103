<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground
        "
        :text-color="sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
          @item-click="turnTo"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import variables from '@/assets/styles/variables.module.scss';
  // import router from '@/router';
  import useAppStore from '@/store/modules/app';
  import usePermissionStore from '@/store/modules/permission';
  import useSettingsStore from '@/store/modules/settings';
  import Logo from './Logo';
  import SidebarItem from './SidebarItem';

  const route = useRoute();
  const appStore = useAppStore();
  const settingsStore = useSettingsStore();
  const permissionStore = usePermissionStore();

  const sidebarRouters = computed(() => permissionStore.sidebarRouters);
  const showLogo = computed(() => settingsStore.sidebarLogo);
  const sideTheme = computed(() => settingsStore.sideTheme);
  const theme = computed(() => settingsStore.theme);
  const isCollapse = computed(() => !appStore.sidebar.opened);

  const activeMenu = computed(() => {
    const { meta, path } = route;
    // if set path, the sidebar will highlight the path you set
    if (meta.activeMenu) {
      return meta.activeMenu;
    }
    return path;
  });
  const turnTo = (routePath, params) => {
    const thisParams = JSON.parse(params.src).IframeSrc;
    const queryString = new URLSearchParams({ src: thisParams }).toString();
    const url = `/${routePath}?${decodeURIComponent(queryString)}`;
    window.open(url, '_blank');
    // router.push({ path: routePath, query: { src: params.src } });
  };
</script>

<style lang="scss" scoped></style>
