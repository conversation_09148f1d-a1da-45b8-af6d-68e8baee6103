<template>
  <div class="container">
    <el-row>
      <el-col :span="24">
        <el-button type="text" @click="toBack"> {{ '<' }} 返回上一层</el-button>
      </el-col>
    </el-row>
    <div class="demo-tabs">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="基本配置 时间" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
          >
            <el-form-item label="时间限定名称" prop="code">
              <el-input v-model="form.code"></el-input>
            </el-form-item>

            <el-form-item label="限定中文名称" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="时间配置" prop="timeUnit">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="form.timeUnit">
                    <el-radio-button
                      v-for="item in timeunitList"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <div class="rgroup">
                    <el-radio-group v-model="form.timeConfigurationOptions">
                      <el-row>
                        <el-col :span="24">
                          <el-radio label="1">
                            快速选择: 前
                            <el-input-number
                              v-model="quickSelect"
                              :min="1"
                              :max="10"
                              size="small"
                              controls-position="right"
                              @change="handleChange"
                            />
                            {{ ' ' }}
                            至
                            {{ ' ' }}
                            当前年
                          </el-radio>
                        </el-col>
                        <el-col :span="24">
                          <el-radio label="2">
                            自定义:
                            <el-input-number v-model="customTimeOne" size="small" />
                            {{ ' ' }}
                            至
                            <el-input-number v-model="customTimeTwo" size="small" />
                            年
                          </el-radio>
                        </el-col>
                      </el-row>
                    </el-radio-group>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="描述" prop="description">
              <el-input v-model="form.description" type="textarea" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <div class="bont">
          <!-- 分割线 -->
          <el-divider></el-divider>
          <el-button type="primary" @click="fulfill"> 完成</el-button>
          <el-button type="danger" plain @click="toBack">关闭</el-button>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
  import { getDwDatabaseList, getFiledType, getGlobalVarListByModel } from '@/api/datamodel';
  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { nodeClick, workspaceId, rowData } = toRefs(props);
  const { proxy } = getCurrentInstance();
  console.log(nodeClick.value);
  console.log(workspaceId.value);
  console.log(rowData.value);
  const emit = defineEmits();

  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],

      code: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
        // 校验
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      store: [{ required: true, message: '请选择储存库', trigger: 'blur' }],
      isExternal: [{ required: true, message: '请选择建模方式', trigger: 'blur' }],
      type: [{ required: true, message: '请选择存储类型', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });
  const dwDatabaseList = ref([]);
  const codeData = ref();
  const drawer = ref(false);
  const getDwDatabaseListUtil = async (data) => {
    const res = await getDwDatabaseList({ catalogId: data });
    dwDatabaseList.value = res.data.map((item) => item);
  };

  const { form, rules, queryParams } = toRefs(data);
  const toBack = () => {
    emit('toBack', true);
  };

  const timeunitList = ref([
    { value: 'year', label: '按 年' },
    { value: 'month', label: '按 月' },
    { value: 'day', label: '按 日' },
    { value: 'hour', label: '按 时' },
    { value: 'minute', label: '按 分' },
    { value: 'second', label: '按 秒' },
  ]);
  const codetableField = ref([
    {
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: null,
      notNul: null,
      partitionField: true,
    },
  ]);

  const dialogVisible = ref(false);
  // #region

  const activeName = ref('first');
  const tableData = ref([]);

  const beforeleave = (tab, oldTab) => {
    console.log(tab, oldTab);
    return new Promise((resolve) => {
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.name &&
        form.value.code &&
        form.value.dwDatabase &&
        form.value.isExternal &&
        form.value.isFromDatasource
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const handleClick = (tab, event) => {};
  const addStep = async () => {
    const res = await beforeleave();
    if (!res) {
      return proxy.$modal.msgWarning('请先完善基本配置');
    }
    activeName.value = 'second';
  };
  const delStep = () => {
    console.log(1);
    activeName.value = 'first';
  };
  const fulfill = async () => {
    const res = await proxy.$refs.formRef.validate((valid) => valid);
    if (!res) return;

    const data = {
      name: form.value.name,
      code: form.value.code,
      description: form.value.description,
      timeUnit: form.value.timeUnit,
      quickSelectValue: quickSelect.value,
      customTimeRange:
        customTimeOne.value && customTimeTwo.value
          ? customTimeOne.value + ',' + customTimeTwo.value
          : '',
    };

    console.log(rowData.value);
    if (rowData.value && rowData.value.name) {
      emit('fulfill', data, 'timeLimitsEdit');
    } else {
      emit('fulfill', data, 'timeLimits');
    }
    //     // 清空数据
    //     form.value = {

    //     }
    //     // 清空数据
    //     tableData.value = []
    //     activeName.value = 'first'
    //     toBack()
  };

  // const getFieldListUtil = async () => {
  //     let query = {
  //         // tableName: form.value.tableName,
  //         // databaseName: form.value.databaseName,
  //         // datasourceId: form.value.datasourceId,
  //         tableName: 'TABLE',
  //         databaseName: 'd_migrate',
  //         datasourceId: '624',
  //     }
  //     let res = await getFieldList(query)
  //     console.log(res)
  //     tableData.value = res.data
  // }
  const delTableData = (row) => {
    tableData.value.splice(tableData.value.indexOf(row), 1);
  };
  const addTableData = (e) => {
    console.log(e);
    tableData.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: false,
      partitionField: false,
      notNul: false,
      workspaceId: workspaceId.value,
    });
  };

  const handleEdit = (row) => {
    console.log(row);
  };

  const delCodetableField = (row) => {
    codetableField.value.splice(codetableField.value.indexOf(row), 1);
  };

  const quickSelect = ref();
  const customTimeOne = ref();
  const customTimeTwo = ref();

  const cascaderOptions = ref([]);
  // watch form.datasheet
  watch(
    () => tableData.value,
    (val) => {
      cascaderOptions.value = tableData.value.map((item) => {
        return {
          value: item.columnName,
          label: item.columnName,
        };
      });
    },
  );

  watch(
    () => nodeClick.value,
    (val) => {
      nextTick(() => {
        getDwDatabaseListUtil(val.key);
      });
    },
    {
      deep: true,
    },
  );

  const partitionCodeList = ref([]);

  const handleChange = (e) => {
    console.log(e);
  };

  const addCodetableField = () => {
    codetableField.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: null,
      partitionField: true,
      notNul: null,
    });
  };

  const customerIdList = ref([]);
  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'hive',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  const getGlobalVarListByModelUtil = async () => {
    const res = await getGlobalVarListByModel();
    // console.log(res)
    partitionCodeList.value = res.data.map((item) => {
      return {
        value: item.code,
        label: item.name,
        ...item,
      };
    });
  };

  onMounted(() => {
    console.log(rowData.value);
    if (rowData.value && rowData.value.name) {
      form.value.name = rowData.value.name;
      form.value.code = rowData.value.code;
      form.value.timeUnit = rowData.value.timeUnit;
      form.value.timeConfigurationOptions = rowData.value.quickSelectValue ? '1' : '2';
      quickSelect.value = rowData.value.quickSelectValue;
      customTimeOne.value = rowData.value.customTimeOne;
      form.value.description = rowData.value.description;
    }
    console.log(form.value.timeConfigurationOptions);
    nextTick(() => {
      getDwDatabaseListUtil(nodeClick.value.key);
    });
    getFiledTypeUtil();
    getGlobalVarListByModelUtil();
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 10px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    margin: 5px;
  }

  .demo-tabs {
    padding: 5px 25px 0;
  }

  .bont {
    text-align: right;
    padding: 10px 0;
  }

  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .codeCs {
    padding: 10px;
    white-space: pre;
    font-family: 'Courier New', monospace;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .rgroup {
    // border-top: 1px solid #0400ff3f;
    // border-left: 1px solid #E6E6E6;
    // border-right: 1px solid #E6E6E6;
    // border-bottom: 1px solid #E6E6E6;
    padding: 10px;
  }
</style>
