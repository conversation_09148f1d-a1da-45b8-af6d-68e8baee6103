@import './variables.module.scss';
@import '@/assets/styles/xg-ui/base.scss';
#app {
  .main-container {
    padding: 20px 20px 20px 0;
    box-sizing: border-box;
    height: 100%;
    transition: margin-left 0.28s;
    margin-left: 240px;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    background-color: $base-menu-background;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    // -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    // box-shadow: 1px 0 1px rgba(0, 21, 41, 0.35);
    // reset element-ui css
    .horizontal-collapse-transition {
      transition:
        0s width ease-in-out,
        0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 100px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item,
    .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
      & svg{
        height: 56px;
      }
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
        background-color: $--base-color-primary !important;
        color: $--base-color-text3 !important;
        border-radius: 4px;
        box-shadow: 0px 8px 16px 0px rgba(17, 105, 255, 0.17);
      }
    }

    & .theme-dark .is-active {
      & > .el-sub-menu__title {
        color: $base-menu-color-active !important;
      }
      &:hover {
        & > .el-sub-menu__title {
          color: $--base-color-text3 !important;
        }
      }
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
        background-color: $--base-color-primary !important;
        color: $--base-color-text3 !important;
        border-radius: 4px;
        box-shadow: 0px 8px 16px 0px rgba(17, 105, 255, 0.17);
      }
    }

    // & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    // & .theme-dark .el-sub-menu .el-menu-item {
    //   background-color: $base-sub-menu-background !important;

    //   &:hover {
    //     background-color: $base-sub-menu-hover !important;
    //   }
    // }
    .el-menu-item {
      &.is-active {
        background-color: $--base-color-primary;
        color: $--base-color-text3 !important;
        border-radius: 4px;
        box-shadow: 0px 8px 16px 0px rgba(17, 105, 255, 0.17);
      }
    }
    .el-sub-menu {
      .el-menu-item {
        &.is-active {
          background-color: $--base-color-primary !important;
          color: $--base-color-text3 !important;
          border-radius: 4px;
          box-shadow: 0px 8px 16px 0px rgba(17, 105, 255, 0.17);
        }
      }
      .el-sub-menu,
      .el-menu {
        .el-sub-menu__title,
        .el-menu-item {
          .svg-icon {
            display: none;
          }
        }
      }
      &.is-active {
        & > .el-sub-menu__title {
          color: $--base-color-primary !important;
          &:hover {
            color: $--base-color-text3 !important;
          }
        }
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 64px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          & > i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
      background-color: $--base-color-primary !important;
      color: $--base-color-text3 !important;
      border-radius: 4px;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
