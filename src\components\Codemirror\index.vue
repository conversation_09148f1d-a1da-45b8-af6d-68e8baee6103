<template>
  <!-- :indent-with-tab="true" 是否自动获取焦点-->
  <codemirror
    v-model="code"
    placeholder="请输入..."
    :autofocus="true"
    :tab-size="2"
    :extensions="extensions"
    :indent-with-tab="true"
    :disabled="disabledType"
    @change="changeText"
  />
</template>

<script setup>
  import { Codemirror } from 'vue-codemirror'; // 编辑器
  // import { javascript } from "@codemirror/lang-javascript"; // js 语法
  import { sql } from '@codemirror/lang-sql'; // sql 补齐
  import { oneDark } from '@codemirror/theme-one-dark'; // 主题
  import { EditorView } from '@codemirror/view';
  import { defineEmits } from 'vue';
  import { debounceO } from '@/utils/index';

  const emit = defineEmits();

  const myTheme = EditorView.theme(
    {
      // 输入的字体颜色
      '&': {
        color: '#0052D9',
        backgroundColor: '#FFFFFF',
      },
      '.cm-content': {
        caretColor: '#0052D9',
      },
      // 激活背景色
      '.cm-activeLine': {
        backgroundColor: '#FAFAFA',
      },
      // 激活序列的背景色
      '.cm-activeLineGutter': {
        backgroundColor: '#FAFAFA',
      },
      // 光标的颜色
      '&.cm-focused .cm-cursor': {
        borderLeftColor: '#0052D9',
      },
      // 选中的状态
      '&.cm-focused .cm-selectionBackground, ::selection': {
        backgroundColor: '#0052D9',
        color: '#FFFFFF',
      },
      // 左侧侧边栏的颜色
      '.cm-gutters': {
        backgroundColor: '#FFFFFF',
        color: '#ddd', // 侧边栏文字颜色
        border: 'none',
      },
    },
    { dark: true },
  );

  const props = defineProps({
    code: {
      type: String,
      default: '',
    },
    disabledType: {
      type: Boolean,
      default: false,
    },
  });
  const changeText = () => {
    debounceO(() => {
      emit('change', code);
    }, 600);
  };
  const extensions = [sql(), oneDark];
  const code = props.code;
</script>
