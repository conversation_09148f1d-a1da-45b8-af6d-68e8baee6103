<template>
  <div class="app-container">
    <HeadTitle title="订阅列表" />
    <el-select v-model="queryParams.workspaceId" @change="getSubscribeList">
      <el-option
        v-for="space in workSpaceList"
        :key="space.workspaceId"
        :label="space.workspaceName"
        :value="space.workspaceId"
      ></el-option>
    </el-select>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="订阅列表" name="all">
        <el-table
          v-loading="loading"
          :data="subscribeList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" label="id" align="center" prop="id" />
          <el-table-column v-if="true" label="流程id" align="center" prop="bizId" />
          <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
          <el-table-column v-if="true" label="告警订阅类型" align="center" prop="subscribeType" />
          <el-table-column v-if="true" label="流程创建人" align="center" prop="createProcessUser" />
          <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                v-hasPermi="['alert:subscribe:remove']"
                link
                type="primary"
                @click="handleDelete(scope.row)"
                >取消订阅</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                @click="handlesubscribe(scope.row)"
                >编辑设置</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                @click="handleUpdate(scope.row)"
                >告警历史</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="告警历史" name="warn">
        <el-table
          v-loading="loading"
          :data="subscribeList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" label="id" align="center" prop="id" />
          <el-table-column v-if="true" label="流程id" align="center" prop="bizId" />
          <el-table-column v-if="true" label="流程名称" align="center" prop="flowName" />
          <el-table-column v-if="true" label="告警订阅类型" align="center" prop="subscribeType" />
          <el-table-column
            v-if="true"
            label="流程创建人"
            align="center"
            prop="flowCreateUserName"
          />
          <el-table-column v-if="true" label="订阅时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                v-hasPermi="['alert:subscribe:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-hasPermi="['alert:subscribe:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      v-model="openWarn"
      title="告警设置"
      width="560px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelWarn()"
    >
      <div class="part">
        <div class="inform">通知策略</div>
        <div class="selectBox">
          <el-radio-group v-model="radio">
            <el-radio :label="1">成功失败都通知</el-radio>
            <el-radio :label="2">失败通知</el-radio>
            <el-radio :label="3">成功通知</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="part">
        <div class="inform">通知渠道</div>
        <div class="selectBox">
          <el-checkbox v-model="mail" :disabled="onCheck" label="邮箱" @change="getSubscribeType" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelWarn">取 消</el-button>
          <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getInfo } from '@/api/login';
  import { getWorkspaceList } from '@/api/dataSourceManageApi';
  import {
    listSubscribe,
    getSubscribe,
    delSubscribe,
    addSubscribe,
    updateSubscribe,
  } from '@/api/alert/subscribe';
  import { onMounted } from 'vue';
  const tid = ref(null);
  const workSpaceList = ref([]);
  onMounted(() => {
    getInfo().then((res) => {
      tid.value = res.data.user.tenantId;
      getWorkspaceList({ tenantId: tid.value }).then((res) => {
        if (res.data.length) {
          workSpaceList.value = res.data;
          queryParams.value.workspaceId = res.data[0].workspaceId;
          listSubscribe(queryParams.value).then((response) => {
            subscribeList.value = response.rows;
            total.value = response.total;
            loading.value = false;
          });
        } else {
          workSpaceList.value = [];
          proxy.$modal.msgWarning('该用户下没有工作空间');
        }
      });
    });
  });
  const subscribeList = ref([]);
  const getSubscribeList = (data) => {
    queryParams.value.workspaceId = data;
    listSubscribe(queryParams.value).then((response) => {
      subscribeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  };

  const { proxy } = getCurrentInstance();

  const open = ref(false);
  const buttonLoading = ref(false);
  const loading = ref(false);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      workspaceId: null,
    },
    rules: {
      userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
      bizId: [{ required: true, message: '订阅业务ID如流程ID不能为空', trigger: 'blur' }],
      subscribeType: [
        {
          required: true,
          message: '告警订阅场景1成功和失败2成功3失败，才发送不能为空',
          trigger: 'change',
        },
      ],
      alertType: [
        {
          required: true,
          message: '告警方式1邮件2短信...多个用英文逗号分割不能为空',
          trigger: 'change',
        },
      ],
      tenantId: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      createBy: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      updateBy: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      updateTime: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      delFlag: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const activeName = ref('all');
  /** 查询告警信息订阅清单列表 */
  function getList() {
    loading.value = true;
    listSubscribe(queryParams.value).then((response) => {
      subscribeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      userId: null,
      bizId: null,
      subscribeType: null,
      alertType: null,
      tenantId: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      delFlag: null,
    };
    proxy.resetForm('subscribeRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加告警信息订阅清单';
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    loading.value = true;
    reset();
    const id = row.id || ids.value;
    getSubscribe(id).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '修改告警信息订阅清单';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.subscribeRef.validate((valid) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id != null) {
          updateSubscribe(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          addSubscribe(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal
      .confirm('是否确定删除告警信息订阅清单编号为"' + _IDs + '"的数据项？')
      .then(function () {
        loading.value = true;
        return delSubscribe(_ids);
      })
      .then(() => {
        loading.value = true;
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  // 告警订阅功能模块
  const onButton = ref(true);
  const radio = ref(1);
  const mail = ref(false);
  const onCheck = ref(false);
  const openWarn = ref(false);
  // const email = ref(null)
  const objForWarn = ref({});
  const getSubscribeType = (data) => {
    if (data) {
      onButton.value = false;
    } else {
      onButton.value = true;
    }
  };
  const handlesubscribe = (row) => {
    openWarn.value = true;
    objForWarn.value.bizId = row.id;
    objForWarn.value.processCode = row.processCode;
    objForWarn.value.projectCode = row.projectCode;
    objForWarn.value.userId = row.userId;
    objForWarn.value.tenantId = row.tenantId;
    objForWarn.value.workspaceId = row.workspaceId;
    objForWarn.value.createProcessUser = row.createProcessUser;
    if (row.alertType == '1') {
      mail.value = true;
      getSubscribeType(mail.value);
    } else {
      mail.value = false;
      getSubscribeType(mail.value);
    }
  };

  // 提交设置
  const submitFormWarn = () => {
    if (mail.value) {
      objForWarn.value.subscribeType = 1;
    }
    if (radio.value == 1) {
      objForWarn.value.alertType = '1';
    } else if (radio.value == 2) {
      objForWarn.value.alertType = '2';
    } else {
      objForWarn.value.alertType = '3';
    }
    // 校验是否有
    console.log(objForWarn.value.processCode);
    console.log(objForWarn.value.projectCode);
    if (objForWarn.value.processCode == null || objForWarn.value.projectCode == null) {
      proxy.$modal.msgError('请先发布流程调度');
      return;
    }
    updateSubscribe(objForWarn.value).then((res) => {
      if (res.code == 200) {
        openWarn.value = false;
        proxy.$modal.msgSuccess('订阅成功，请前往告警中心查看并管理已订阅流程');
      }
    });
  };
  // 取消设置
  const cancelWarn = () => {
    mail.value = false;
    onButton.value = true;
    openWarn.value = false;
  };
</script>

<style scoped lang="scss">
  .part {
    margin: 10px;
    font-size: 16px;

    .inform {
      margin-left: 10px;
    }

    .selectBox {
      margin: 24px 0 24px 66px;
    }
  }
</style>
