<template>
  <div class="quality-report">
    <div class="top-container">质量报告</div>
    <SplitPanes>
      <template #left>
        <div class="left-container">
          <div style="margin-bottom: 10px">
            <el-radio-group v-model="dataTreeType" @change="changeType">
              <el-radio-button label="0">技术报告</el-radio-button>
              <el-radio-button label="1">业务报告</el-radio-button>
            </el-radio-group>
          </div>
          <div v-if="dataTree.length">
            <el-tree
              v-if="dataTreeType == '0'"
              ref="treeRef"
              @node-click="handleClick"
              :data="dataTree"
              class="treeFab"
              node-key="id"
              :highlight-current="true"
              :default-expanded-keys="defaultExpandIds"
              :current-node-key="treeId"
            >
              <template #default="{ node, data }">
                <span v-if="node.level == 2">
                  <el-tooltip
                    :content="data.label"
                    placement="top"
                    :disabled="data.label.length < 20"
                  >
                    {{ data.label.length > 20 ? data.label.slice(0, 20) + '...' : data.label }}
                  </el-tooltip>
                </span>
              </template>
            </el-tree>
            <el-tree
              v-else
              ref="treeRef"
              @node-click="handleClick"
              :data="dataTree"
              class="treeFab"
              node-key="id"
              :current-node-key="treeId"
              :highlight-current="true"
            >
              <template #default="{ node, data }">
                <span>
                  <el-icon style="margin-right: 5px;">
                    <Menu />
                  </el-icon>
                  <el-tooltip
                    :content="data.label"
                    placement="top"
                    :disabled="data.label.length < 20"
                  >
                    {{ data.label.length > 20 ? data.label.slice(0, 20) + '...' : data.label }}
                  </el-tooltip>
                </span>
              </template>
            </el-tree>
          </div>
          <div v-else>
            <el-empty :image="imgUrlForEmpty"></el-empty>
          </div>
        </div>
      </template>
      <template #right>
        <div v-if="searchResult" class="right-container">
          <!-- <Technology  :name="titleName" /> -->
          <component
            ref="componentRef"
            :is="currentComponent"
            :treeId="treeId"
            :name="titleName"
            :workspaceId="workspaceId"
            :dbType="dbType"
            :type="currentComponent"
            @change-data="handleChange"
          />
        </div>
        <div v-else>
          <el-empty :image="imgUrlForEmpty" />
        </div>
      </template>
    </SplitPanes>
  </div>
</template>
<script setup>
  import Technology from './technology.vue';
  import Commerce from './commerce.vue';
  import SplitPanes from '@/components/SplitPanes/index';
  import { getTreeData } from '@/api/dataGovernance/qualityReport.js';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const dataTreeType = ref('0');
  const componentMap = {
    0: Technology,
    1: Commerce,
  };
  const componentRef = ref(null);
  const searchResult = computed(() => treeId.value);
  const currentComponent = shallowRef(Technology);
  const defaultExpandIds = ref([]);
  const handleChange = (data) => {
    if (dataTreeType.value == data) return false;
    dataTreeType.value = data;
    changeType(data);
  };
  const changeType = (data) => {
    nextTick(async () => {
      // await componentRef.value.resetData();
      getTree(data);
    });
  };
  // 切换报告类型获取对应的tree数据
  const getTree = async (type = '0') => {
    // console.log(defaultExpandIds.value);
    const data = {
      workspaceId: workspaceId.value,
      type,
    };
    try {
      const res = await getTreeData(data);
      if (type == '0') {
        dataTree.value = treeStructure(res.data);
      } else {
        dataTree.value = res.data.map((k) => {
          return {
            id: k.id,
            label: k.name,
            children: [],
          };
        });
      }
    } catch {}
  };

  const treeStructure = (data) => {
    return Object.keys(data).map((key) => ({
      label: key,
      children: data[key].map((db) => ({
        id: db.id,
        label: db.name,
        children: [],
      })),
    }));
  };
  const titleName = ref(null);
  const dataTree = ref([]);
  const treeRef = ref(null);
  const treeId = ref(null);
  const dbType = ref('');
  const handleClick = (item, node) => {
    if (dataTreeType.value == '0') {
      if (node.level == 1) return;
      titleName.value = item.label;
      dbType.value = node.parent.label;
      currentComponent.value = componentMap[dataTreeType.value];
    } else {
      titleName.value = item.label;
      currentComponent.value = componentMap[dataTreeType.value];
    }
    treeId.value = item.id;
    defaultExpandIds.value.push(item.id);
    getReportData();
  };
  // 获取右边的报告数据
  const getReportData = () => {
    nextTick(() => {
      componentRef.value.resetData();
      componentRef.value.getDataForTreeId();
      componentRef.value.getList();
    });
  };
  onMounted(() => {
    getTree();
  });
  watch(workspaceId, () => {
    dataTreeType.value = '0';
    treeId.value = null;
    dbType.value = '';
    getTree();
    componentRef.value.resetData();
  });
</script>
<style lang="scss" scoped>
  .quality-report {
    height: calc(100% - 40px);
    .top-container {
      padding: 20px 0 0 30px;
      color: #000000;
      font-weight: 600;
      font-size: 20px;
      line-height: 20px;
    }
  }
</style>
