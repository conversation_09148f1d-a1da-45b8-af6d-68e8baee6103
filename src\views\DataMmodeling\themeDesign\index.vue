<template>
  <SplitPanes>
    <template #left>
      <themeLeft
        :data-tree="dataTree"
        :tree-props="props"
        :show-label-class="true"
        :show-add-button="true"
        :base-options="baseOptions"
        :show-operate-btn="true"
        @node-click="handleNodeClick"
        @filter-change="onChange"
        @add-tier="addSiblings"
        @context-menu="showContextMenu"
        @on-menu-click="onMenuClick"
        @tree-add-item="treeAddItem"
        @tree-delete-item="treeDeleteItem"
      />

      <!-- <transition name="slide-fade"> -->
      <!-- <div v-if="showMenu" class="custom-menu" :style="{ top: `${menuY}px`, left: `${menuX}px` }"> -->
      <!-- <div v-show="menuNode.level == 1"> -->
      <!-- <a @click="append('notTier')">新增主题域</a> -->
      <!-- </div> -->
      <!-- <div v-show="menuNode.level == 2 ? true : false"> -->
      <!-- <a @click="appendBusiness('notTier')">新增业务过程</a> -->
      <!-- </div> -->
      <!-- </div> -->
      <!-- </transition> -->
    </template>
    <template #right>
      <section v-if="BasicInformation" class="item-box top-breadcrumb">
        <div class="turn-back" @click="showList">
          <IconGoBack></IconGoBack>
        </div>
        <el-breadcrumb separator-icon="ArrowRight">
          <template v-if="nodeClick.level == 1">
            <el-breadcrumb-item>业务分类</el-breadcrumb-item>
          </template>

          <template v-if="nodeClick.level == 2">
            <el-breadcrumb-item>业务分类</el-breadcrumb-item>
            <el-breadcrumb-item>主题域</el-breadcrumb-item>
          </template>

          <template v-if="nodeClick.level == 3">
            <el-breadcrumb-item>业务分类</el-breadcrumb-item>
            <el-breadcrumb-item>主题域</el-breadcrumb-item>
            <el-breadcrumb-item>业务过程</el-breadcrumb-item>
          </template>
        </el-breadcrumb>
      </section>

      <section v-if="BasicInformation" class="item-box base-card-box">
        <span class="TitleName">基本信息</span>

        <div class="info">
          <div class="header-box">
            <div class="header-box-icon" :class="`header-box-icon-${nodeClick.level}`"></div>
            <div class="header-box-content">
              <div class="header-title">{{ BasicInformation.name }}</div>
              <div class="header-title-type" :class="`header-title-type-${nodeClick.level}`">
                {{ filterDataType(nodeClick.level) }}
              </div>
            </div>
          </div>
          <el-descriptions
            title=""
            column="3"
            class="card-descriptions"
            :border="false"
            :colon="true"
            size="large"
          >
            <el-descriptions-item label="英文编码">
              {{ BasicInformation.code }}</el-descriptions-item
            >
            <el-descriptions-item label="创建人">
              {{ BasicInformation.createBy }}</el-descriptions-item
            >
            <el-descriptions-item label="修改人">
              {{ BasicInformation.updateBy }}</el-descriptions-item
            >
            <el-descriptions-item label="状态">
              <div :class="`detail-status-content detail-status-${BasicInformation.status}`">
                {{ filterTagTypeText(BasicInformation.status) }}
              </div></el-descriptions-item
            >
            <el-descriptions-item label="创建时间">
              {{ BasicInformation.createTime }}</el-descriptions-item
            >
            <el-descriptions-item label="修改时间">
              {{ BasicInformation.updateTime }}</el-descriptions-item
            >
            <el-descriptions-item label="所属主题">
              {{ BasicInformation.name }}</el-descriptions-item
            >
            <el-descriptions-item label="备注"> {{ BasicInformation.remark }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </section>

      <section
        v-if="tableDataUtil"
        class="table-data-util"
        :class="{
          'less-table-box': BasicInformation,
          'least-table-box': nodeClick?.level === 1,
        }"
      >
        <div class="this-table-box">
          <div class="table-top-box">
            <span class="TitleName">{{ nodeClickeUtil }}管理</span>
            <div class="table-search-box">
              <div class="operationType">
                <el-input
                  v-model="input3"
                  placeholder="请输入名称"
                  class="input-with-select"
                  size="mini"
                >
                  <template #prepend>
                    <el-select
                      v-model="selectName"
                      placeholder="Select"
                      style="width: 115px"
                      size="mini"
                    >
                      <el-option
                        v-for="dict in model_search_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </template>
                </el-input>

                <div class="form-label">更新时间：</div>
                <el-date-picker
                  v-model="time"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  :disabled-date="disablesDate"
                ></el-date-picker>

                <div class="btn-box">
                  <el-tooltip class="box-item" effect="light" content="查询" placement="top-start">
                    <el-button
                      circle
                      icon="Search"
                      type="primary"
                      @click="getListCatalogUtil(BasicInformation?.id)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip class="box-item" effect="light" content="重置" placement="top-start">
                    <el-button style="font-size: 16px" circle @click="reset"
                      ><IconRefresh
                    /></el-button>
                  </el-tooltip>
                </div>
                <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
              </div>
            </div>
          </div>
          <div class="pm">
            <el-row>
              <el-col :span="8">
                <el-button icon="Plus" type="primary" @click="appends"
                  >新增{{ nodeClickeUtil }}</el-button
                >
                <!-- <el-button type="" @click="updateStatusUtil(_, 1)" :disabled="false">发布</el-button> -->
                <!-- <el-button type="" @click="updateStatusUtil(_, 0)" :disabled="false">下线</el-button> -->
                <!-- <el-button type="" @click="remove" :disabled="false">删除</el-button> -->
              </el-col>

              <el-col v-if="nodeClick?.level == undefined" :span="16">
                <right-toolbar
                  :search="false"
                  :columns="columns"
                  @query-table="reload(BasicInformation?.id)"
                >
                </right-toolbar>
              </el-col>
            </el-row>
          </div>

          <div class="table-box">
            <el-table
              ref="tableRef"
              row-key="date"
              :data="tableData"
              style="width: 100%"
              height="100%"
              @selection-change="handleSelectionChange"
            >
              <!-- 选择框 -->
              <!-- <el-table-column type="selection" width="55" align="center" /> -->
              <el-table-column
                prop="name"
                :label="nodeClickeUtil + '名称'"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[0].visible"
                prop="code"
                label="英文编码"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[1].visible"
                prop="pname"
                label="所属主题"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[2].visible"
                prop="createBy"
                label="创建人"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[3].visible"
                prop="remark"
                label="描述"
                width="200"
                :show-overflow-tooltip="true"
              />

              <el-table-column v-if="columns[4].visible" prop="status" label="状态" width="200">
                <template #default="scope">
                  <!-- <el-tag
                    :type="filterTagType(scope.row.status)"
                    :disable-transitions="true"
                    round
                    effect="plain"
                  >
                    {{ filterTagTypeText(scope.row.status) }}
                  </el-tag> -->
                  <div :class="`table-status-content table-status-${scope.row.status}`">
                    {{ filterTagTypeText(scope.row.status) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                v-if="columns[5].visible"
                prop="createTime"
                label="更新时间"
                sortable
                width="200"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                v-if="tableData[0] !== '{}'"
                fixed="right"
                label="操作"
                width="auto"
                min-width="200"
              >
                <template #default="scope">
                  <el-button type="text" size="small" @click="revamp(scope.row)">编辑</el-button>
                  <el-button
                    v-if="scope.row.status != 1"
                    type="text"
                    size="small"
                    @click="updateStatusUtil(scope.row, 1)"
                  >
                    发布
                  </el-button>
                  <!-- <el-button
                    v-if="scope.row.status != 0"
                    type="text"
                    size="small"
                    :disabled="scope.row.status !== 1"
                    @click="updateStatusUtil(scope.row, 0)"
                  >
                    下线
                  </el-button> -->
                  <el-button
                    type="text"
                    :disabled="scope.row.status === 1"
                    @click="remove(scope.row)"
                  >
                    删除
                  </el-button>
                  <!-- <el-dropdown> -->
                  <!-- <span class="el-dropdown-link"> -->
                  <!-- 更多 -->
                  <!-- <el-icon class="el-icon--right"> -->
                  <!-- <arrow-down /> -->
                  <!-- </el-icon> -->
                  <!-- </span> -->
                  <!-- <template #dropdown> -->
                  <!-- <el-dropdown-menu> -->
                  <!-- <!~~ <el-dropdown-item> ~~> -->
                  <!-- <!~~ <el-button type="text" @click="updateStatusUtil(scope.row, 0)" ~~> -->
                  <!-- <!~~ :disabled="scope.row.status == 0"> ~~> -->
                  <!-- <!~~ 下线 ~~> -->
                  <!-- <!~~ </el-button> ~~> -->
                  <!-- <!~~ </el-dropdown-item> ~~> -->
                  <!-- <el-dropdown-item> -->
                  <!-- <el-button type="text" @click="remove(scope.row)"> -->
                  <!-- 删除 -->
                  <!-- </el-button> -->
                  <!-- </el-dropdown-item> -->
                  <!-- </el-dropdown-menu> -->
                  <!-- </template> -->
                  <!-- </el-dropdown> -->
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div>
            <!-- 分页 -->
            <pagination
              v-show="total > 0"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :pager-count="maxCount"
              :total="total"
              @pagination="listPage"
            />
          </div>
        </div>
      </section>
    </template>
  </SplitPanes>
  <!-- 新增 -->
  <el-dialog
    v-model="dialogVisibleSiblings"
    :title="dialogTitle"
    width="650px"
    append-to-body
    :draggable="true"
    @close="cleanVisibleSiblings"
  >
    <el-form ref="dialogVisibleSiblingsRef" label-width="95px" :model="form" :rules="rules">
      <el-form-item :label="dialogTitleUtil2" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="英文编码" prop="code">
        <el-input
          v-model="form.code"
          placeholder="请输入编码,每个主题编码在平台需保持唯一性"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="
          dialogTitle === '新增主题域' ||
          dialogTitle === '编辑主题域' ||
          dialogTitle === '新增业务过程' ||
          dialogTitle === '编辑业务过程'
        "
        label="上级主题"
        prop="themeDesign"
      >
        <el-input v-model="form.themeDesign" placeholder="" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item v-if="dialogTitleUtil" label="数仓类型" prop="database">
        <!-- <el-input v-model="form.type" placeholder="请输入英文编码"></el-input> -->
        <!-- 下拉框  -->
        <!-- <el-select -->
        <!-- v-model="form.database" -->
        <!-- placeholder="请选择" -->
        <!-- style="width: 100%" -->
        <!-- @change="getDataSourcesListUtil" -->
        <!-- > -->
        <!-- <el-option label="HIVE" value="HIVE" /> -->
        <!-- </el-select> -->
        <!-- 输入框 -->
        <el-input v-model="form.database" placeholder="请输入内容" :disabled="true"></el-input>
      </el-form-item>

      <el-form-item v-if="dialogTitleUtil" label="数据源" prop="datasource">
        <!-- <el-input v-model="form.datasource" placeholder="请输入主题名称"></el-input> -->
        <!-- 下拉框 -->
        <el-select
          v-model="form.datasource"
          placeholder="请选择"
          :disabled="dialogTitle.indexOf('编辑') >= 0"
          style="width: 100%"
        >
          <el-option
            v-for="dict in dataSourceTypeList"
            :key="dict.id"
            :value="dict.id"
            :label="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入主题描述"
          :maxlength="100"
          :show-word-limit="true"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cleanVisibleSiblings">取 消</el-button>
        <el-button type="primary" @click="submitVisibleSiblings">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 批量上线 -->
  <el-dialog v-model="dialogVisible" title="批量上线" width="50%" append-to-body :draggable="true">
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column prop="name" label="名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="编码" width="200" :show-overflow-tooltip="true" />
      <el-table-column
        prop="tag"
        label="状态"
        width="220"
        :filters="[
          { text: '草稿', value: 'draft' },
          { text: '上线', value: 'online' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-tag
            :type="filterTagType(scope.row.tag)"
            :disable-transitions="true"
            round
            effect="plain"
          >
            {{ scope.row.tag }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    createCatalog,
    deleteCatalog,
    getCatalog,
    getCatalogTree,
    getDataSourcesList,
    getDetailCatalog,
    getListCatalog,
    updateCatalog,
    updateStatus,
    warehouseType,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { nextTick } from 'vue';
  import SplitPanes from '@/components/SplitPanes/index';
  import themeLeft from '@/views/DataMmodeling/components/themeLeft/index.vue';
  import { IconRefresh, IconGoBack } from '@arco-iconbox/vue-update-line-icon';
  import { ElMessage } from 'element-plus';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const model_search_type = ref([
    {
      label: '名称',
      value: 'name',
    },
    {
      label: '编码',
      value: 'code',
    },
  ]);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `英文编码`, visible: true },
    { key: 1, label: `所属主题`, visible: true },
    { key: 2, label: `创建人`, visible: true },
    { key: 3, label: `描述`, visible: true },
    { key: 4, label: `状态`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
  ]);
  const maxCount = ref(5);
  const total = ref(0);

  const time = ref('');

  const dataSourceTypeList = ref();

  const filterText = ref();
  const selectName = ref();
  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };
  const dataTree = ref();

  const init = async () => {
    await getCatalogTreeUtil();
    await getListCatalogUtil();
    selectName.value = model_search_type.value[0].value;
  };

  onMounted(async () => {
    // window.addEventListener("beforeunload", beforeunload());
    window.addEventListener('click', clickHandler);
    init();
  });
  const getDataSourcesListUtil = async () => {
    const res = await getDataSourcesList({
      type: form.value.database,
      workSpaceId: workspaceId.value,
    });
    if (res.code === 200) {
      dataSourceTypeList.value = res.data;
    }
  };
  const onChange = (value) => {
    getCatalogTreeUtil(value);
  };
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();

  function showContextMenu({ event, data, node }) {
    closeContextMenu();
    treeData.value = data;
    node.level >= 3 ? (showMenu.value = false) : (showMenu.value = true);

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }
    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value, 'menuNode123');
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    treeData.value = null;
    // menuNode.value = null;
  }

  const clickHandler = () => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };
  const tableRef = ref();

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  const tableData = ref([
    {
      date: '2016-05-02',
    },
  ]);

  const filterTagType = (value) => {
    if (value === 1) {
      return 'success';
    } else if (value === 0) {
      return 'danger';
    } else if (value === 2) {
      return '';
    }
  };
  const filterDataType = (value) => {
    if (value === 1) {
      return '业务分类';
    } else if (value === 2) {
      return '主题域';
    } else if (value === 3) {
      return '业务过程';
    }
  };

  const filterTagTypeText = (value) => {
    if (value === 1) {
      return '上线';
    } else if (value === 0) {
      return '下线';
    } else if (value === 2) {
      return '草稿';
    }
  };

  const dialogVisible = ref(false);
  const dialogTitle = ref('');

  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '0',
      pid: '',
      database: '',
      datasource: '',
      // 状态
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },

        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      database: [
        { required: true, message: '请输入类型', trigger: 'blur' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [{ required: true, message: '请输入数据源', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const getCatalogTreeUtil = async (value) => {
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      searchName: value,
      themeMenuFlg: true,
    };
    const res = await getCatalogTree(query);
    dataTree.value = showOperateBtnAdd(res.data);
  };

  // 使用计算属性判断 dialogTitle
  // eslint-disable-next-line vue/return-in-computed-property
  const dialogTitleUtil = computed(() => {
    if (dialogTitle.value === '新增业务分类' || dialogTitle.value === '编辑业务分类') {
      return true;
    } else if (dialogTitle.value === '新增主题域' || dialogTitle.value === '编辑主题域') {
      return false;
    } else if (dialogTitle.value === '新增业务过程' || dialogTitle.value === '编辑业务过程') {
      return false;
    }
  });

  // 使用计算属性判断 dialogTitle
  // eslint-disable-next-line vue/return-in-computed-property
  const dialogTitleUtil2 = computed(() => {
    if (dialogTitle.value === '新增业务分类' || dialogTitle.value === '编辑业务分类') {
      return '业务分类名';
    } else if (dialogTitle.value === '新增主题域' || dialogTitle.value === '编辑主题域') {
      return '主题域名称';
    } else if (dialogTitle.value === '新增业务过程' || dialogTitle.value === '编辑业务过程') {
      return '业务过程名';
    }
  });
  // 使用计算属性判断  nodeClick.value
  const nodeClickeUtil = computed(() => {
    if (nodeClick.value?.level === 0) {
      return '业务分类';
    } else if (nodeClick.value?.level === 1) {
      return '主题域';
    } else if (nodeClick.value?.level === 2) {
      return '业务过程';
    }
    //  默认 业务分类
    return '业务分类';
  });

  const BasicInformationLabel = computed(() => {
    if (nodeClick.value?.level === 1) {
      return '业务分类名';
    } else if (nodeClick.value?.level === 2) {
      return '主题域名称';
    } else if (nodeClick.value?.level === 3) {
      return '业务过程名称';
    }
    //  默认 业务分类
    return '业务分类名';
  });

  const dialogVisibleSiblings = ref(false);

  // 使用计算属性判断 tableData.value.length 和节点级别
  // eslint-disable-next-line vue/return-in-computed-property
  const tableDataUtil = computed(() => {
    //  tableData.value 有长度 nodeClick?.level 并且不等于 3
    if (nodeClick.value?.level !== 3) {
      return true;
    }
  });

  const addSiblings = async () => {
    dialogVisibleSiblings.value = true;
    dialogTitle.value = '新增业务分类';
    const res = await getDatabaseType();
    if (res.code !== 200) return;
    form.value.database = res.data;
    getDataSourcesListUtil();
  };
  const getDatabaseType = async () => {
    let res = '';
    // 这里是异步获取数据库类型的逻辑
    res = await warehouseType();
    return res;
  };

  const append = () => {
    // if (name == 'notTier') {
    //   form.value.themeDesign = treeData?.value?.label || !nodeClick.value?.parent?.label ? nodeClick.value?.parent?.label : '全部'
    // } else {
    //   form.value.themeDesign = treeData?.value?.label || nodeClick.value?.parent?.label
    // }
    console.log(treeData?.value?.label);
    console.log(nodeClick.value?.parent);
    console.log(nodeClick.value);

    let themeDesign = '';
    let pid = '';

    if (treeData?.value?.label) {
      themeDesign = treeData?.value?.label;
    } else if (nodeClick.value?.parent?.label) {
      themeDesign = nodeClick.value?.parent?.label;
    } else if (nodeClick.value?.label) {
      themeDesign = nodeClick.value?.label;
    }

    if (treeData?.value?.label) {
      pid = treeData?.value?.id;
    } else if (nodeClick.value?.parent?.label) {
      pid = nodeClick.value?.parent?.key;
    } else if (nodeClick.value?.label) {
      pid = nodeClick.value?.key;
    }

    console.log(treeData?.value?.label);
    console.log(nodeClick.value?.parent?.label);
    console.log(nodeClick.value);
    dialogVisibleSiblings.value = true;
    form.value.pid = pid;
    form.value.themeDesign = themeDesign;
    // treeData?.value?.label || nodeClick.value?.parent?.label ? nodeClick.value?.parent?.label : nodeClick.value?.label
    dialogTitle.value = '新增主题域';
  };

  const appendBusiness = () => {
    dialogTitle.value = '新增业务过程';
    let themeDesign = '';
    let pid = '';
    if (treeData?.value?.label) {
      themeDesign = treeData?.value?.label;
      pid = treeData?.value?.id;
    } else if (nodeClick.value?.parent?.label && dialogTitle.value !== '新增业务过程') {
      themeDesign = nodeClick.value?.parent?.label;
      pid = nodeClick.value?.parent?.key;
    } else if (nodeClick.value?.label) {
      themeDesign = nodeClick.value?.label;
      pid = nodeClick.value?.key;
    }

    // themeDesign = nodeClick.value?.parent?.label
    form.value.themeDesign = themeDesign;
    // treeData?.value?.label || nodeClick.value?.parent?.label

    dialogVisibleSiblings.value = true;
    console.log(treeData.value?.id);
    console.log(nodeClick.value?.parent?.key);

    form.value.pid = pid;
    // form.value.themeDesign = themeDesignUtil.value
  };

  const appends = () => {
    // 根据 nodeClickeUtil.value  进行判断
    // if (nodeClickeUtil.value == '业务分类') {
    //   addSiblings();
    // } else if (nodeClickeUtil.value == '主题域') {
    //   append('notTier');
    // } else if (nodeClickeUtil.value == '业务过程') {
    //   appendBusiness('notTier');
    // }
    // 根据当前选中等级判断
    const thisChoseLevel = nodeClick.value?.level || 0;
    switch (thisChoseLevel) {
      case 1:
        append('notTier');
        break;
      case 2:
        appendBusiness('notTier');
        break;
      default:
        addSiblings();
        break;
    }
  };

  const rowData = ref();
  const revamp = (data) => {
    console.log(data);
    getCatalogUtil(data?.id ? data.id : treeData?.value?.id, data);
    // form.value = data
    if (menuNode?.value?.level) {
      // dialogTitle.value = titleMap.value[menuNode.value.level];
      dialogTitle.value = '编辑' + nodeClickeUtil.value;
    } else if (nodeClick.value?.level) {
      rowData.value = data;
      // dialogTitle.value = titleMap.value[nodeClick.value?.level + 1];
      dialogTitle.value = '编辑' + nodeClickeUtil.value;
    } else if (data.pid == null && data.pname == null) {
      dialogTitle.value = '编辑业务分类';
      rowData.value = data;
    }
    dialogVisibleSiblings.value = true;
  };

  const getCatalogUtil = async (id, row) => {
    const res = await getCatalog(id);
    // console.log(row.pname)
    form.value = res.data;
    form.value.database = res.data?.catalogDatasource?.datasourceType;
    await getDataSourcesListUtil();
    form.value.datasource = res.data?.catalogDatasource?.datasourceId;
    form.value.themeDesign = row.pname;
  };
  const remove = async (data) => {
    console.log('---------------', data);

    const Vname =
      INames?.value && ids?.value.length > 0 ? INames?.value : treeData?.value?.label || data?.name;
    const ossIds =
      ids?.value && ids?.value.length > 0 ? ids?.value : treeData?.value?.id || data?.id;

    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');

    if (res) {
      if (data?.id) {
        await deleteCatalogUtil(data?.id);
        await getListCatalogUtil(data?.pid ? data?.pid : '');
      } else {
        await deleteCatalogUtil(ossIds);
        await getCatalogTreeUtil();
        await getListCatalogUtil(menuNode?.value?.parent?.key);
      }
    }
  };

  const catalogUtil = async (data) => {
    const res = await createCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  const updateCatalogUtil = async (data) => {
    const res = await updateCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref([]);
  const input3 = ref('');
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };

  const deleteCatalogUtil = async (data) => {
    const res = await deleteCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  const updateStatusUtil = async (data, state = 0) => {
    let ossIds = data?.id || ids?.value;
    const res = await updateStatus({
      ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
      // data?.status 如果是 0 就是传值 1    如果是 1 传值 0
      status: state,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }

    await getListCatalogUtil(data?.pid);
  };
  const reload = async (data) => {
    input3.value = '';
    time.value = '';
    await getListCatalogUtil(data);
  };
  const getListCatalogUtil = async (data) => {
    data = Number(data) ? data : '';
    const query = {
      pid: data,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      type: '0',
      workspaceId: workspaceId.value,
      [selectName.value]: input3?.value,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
    };
    console.log('***************', query);

    const res = await getListCatalog(query);
    // console.log(res)
    if (res.code === 200) {
      if (res.rows?.length > 0) {
        nextTick(() => {
          console.log(res.rows);
          tableData.value = res.rows;
          total.value = res.total;
          // maxCount.value = res.total
        });

        // input3.value = ''
        // time.value = ''
        //
      } else {
        // const res = await getListCatalog({ workspaceId: workspaceId.value, type: '0', })
        tableData.value = [];
        // input3.value = ''
        // time.value = ''
        // proxy.$modal.msgWarning('没有找到对应数据，已重新请求数据')
      }
    }
    // console.log(tableData.value)
  };
  // 重置
  const reset = () => {
    //   BasicInformation.value = null;
    input3.value = '';
    selectName.value = model_search_type.value[0].value;
    time.value = [];
    getListCatalogUtil('');
  };

  // 返回
  const showList = () => {
    BasicInformation.value = null;
    nodeClick.value = undefined;
    reset();
  };

  const createActions = ['新增业务分类', '新增主题域', '新增业务过程'];
  const modifyActions = ['编辑业务分类', '编辑主题域', '编辑业务过程'];

  const newDatabaseName = ref('');
  const newDatasourceName = ref('');
  const newSchemaName = ref('');

  // const 取值
  const getValues = () => {
    // 使用  form.value.datasource 查找 datasourceName
    const targetItem = dataSourceTypeList?.value?.find((item) => item.id === form.value.datasource);
    if (targetItem) {
      newDatasourceName.value = targetItem.name;
      newDatabaseName.value = JSON.parse(targetItem.connectionParams).database;
      newSchemaName.value = JSON.parse(targetItem.connectionParams).schema;
    }
  };

  const submitVisibleSiblings = async () => {
    const res = await proxy.$refs.dialogVisibleSiblingsRef.validate((valid) => valid);
    if (!res) return;

    getValues();

    // 处理 form.value 格式
    // #region
    if (form.value) {
      form.value.workspaceId = workspaceId.value;
      form.value.catalogDatasource = {
        databaseName: newDatabaseName.value,
        datasourceId: form.value.datasource,
        datasourceName: newDatasourceName.value,
        schemaName: newSchemaName.value,
        warehouseType: form.value.warehouseType,
        datasourceType: form.value.database,
      };
      // #endregion

      // 判断是新建还是修改
      // #region
      if (createActions.includes(dialogTitle.value)) {
        await catalogUtil(form.value);
        tableData.value = [];
        BasicInformation.value = null;

        if (dataNode?.value?.id) {
          await getDetailCatalogUtil(dataNode?.value?.id);
          await getListCatalogUtil(dataNode?.value?.id);
        } else {
          // await getDetailCatalogUtil()
          await getListCatalogUtil();
        }
      } else if (modifyActions.includes(dialogTitle.value)) {
        console.log(rowData?.value?.id);

        const pid = treeData?.value?.id ? treeData?.value?.id : dataNode?.value?.id;
        await updateCatalogUtil(form.value);
        await getListCatalogUtil(pid);
      }
      // #endregion

      dialogVisibleSiblings.value = false;
    } else {
      console.error('form or form.value is undefined');
    }
  };

  const cleanVisibleSiblings = () => {
    // 清空 form
    form.value = {
      type: '0',
    };
    dialogVisibleSiblings.value = false;
    proxy.resetForm('dialogVisibleSiblingsRef');
  };

  const BasicInformation = ref();
  const nodeClick = ref();
  const dataNode = ref();
  const handleNodeClick = async ({ data, event, node }) => {
    console.log(data);

    // if (nodeClick.value && nodeClick.value.key === e.key) {
    //   nodeClick.value = undefined;
    // } else {
    // 选中事件会被认为是右键 tooltip 的模块，选中时手动取消右键气泡
    showMenu.value = false;

    nodeClick.value = node;
    dataNode.value = data;
    treeData.value = data;
    console.log('nodeClick.value', nodeClick.value);
    console.log('nodeClick.value.level', nodeClick.value.level);

    console.log(data);

    await getDetailCatalogUtil(data.id);
    await getListCatalogUtil(data.id);
    // }
  };
  const getDetailCatalogUtil = async (data) => {
    console.log(data);
    const res = await getDetailCatalog(data);
    if (res.code === 200) {
      console.log('*-*-*-*-', res.data);
      BasicInformation.value = res.data;
    }
  };

  const listPage = async () => {
    await getListCatalogUtil(nodeClick?.value?.data?.id);
  };

  watch(workspaceId, async () => {
    // 刷新页面
    // window.location.reload();
    // await getCatalogTreeUtil();
    // await getListCatalogUtil();
    // window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    await getListCatalogUtil();
    selectName.value = model_search_type.value[0].value;
  });
  const baseOptions = ref([
    { value: 'deploy', label: '编辑' },
    { value: 'remove', label: '删除' },
  ]);
  const onMenuClick = (e) => {
    console.log(e);
  };

  const treeAddItem = async ({ event, data, node }) => {
    await showContextMenu({ event, data, node });
    if (node.level == 1) {
      append('notTier');
    } else if (node.level == 2) {
      appendBusiness('notTier');
    }
  };
  const treeDeleteItem = async ({ event, data, node }) => {
    const res = await proxy.$modal.confirm('是否确定删除" ' + data.label + ' "主题域？');
    if (res) {
      const res = await deleteCatalog(data.id);
      if (res.code === 200) {
        ElMessage.success('删除成功');
        await getCatalogTreeUtil();
      }
    }
  };

  // 处理数据 是否禁用 showOperateBtnAdd
  const showOperateBtnAdd = (data, currentDepth = 0) => {
    return data.map((item) => {
      const newItem = { ...item };

      if (currentDepth === 2) {
        newItem.showOperateBtnAd = false;
      }

      if (newItem.children && newItem.children.length > 0) {
        newItem.children = showOperateBtnAdd(newItem.children, currentDepth + 1);
      }

      return newItem;
    });
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;
    ::v-deep
      .el-descriptions--large
      .el-descriptions__body
      .el-descriptions__table:not(.is-bordered)
      .el-descriptions__cell {
      padding-bottom: 6px;
    }
  }
  .tree-box {
    height: 100%;
    background-color: $--base-color-item-light;
    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: 100%;
      background: $--base-color-item-light;
    }
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .item-box {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 20px;
    // height: 100%;
    overflow: auto;
    & > div {
      height: 100%;
    }
    &.top-breadcrumb {
      padding: 0px;
      margin-bottom: 10px;
      & > div {
        display: inline-block;
        line-height: 24px;
        vertical-align: top;
        color: #ffffff;
        font-size: 14px;
        &.turn-back {
          cursor: pointer;
          margin-right: 4px;
          svg {
            font-size: 20px;
            margin-top: 2px;
          }
        }
      }
    }
    &.base-card-box {
      height: 214px;
      padding: 0;
      .info {
        height: 166px;
        border-radius: 8px;
        background: $--base-color-item-light;
        padding: 10px;
        .header-box {
          height: 56px;
          border-bottom: 1px solid #f7f8fb;
          .header-box-icon {
            width: 46px;
            height: 46px;
            display: inline-block;
            background: $--base-color-tag-bg
              url('@/assets/images/dataModeling/detail-type-business.png') no-repeat center;
            background-size: auto 38px;
            border-radius: 8px;
            &.header-box-icon-2 {
              background: $--base-color-tag-bg
                url('@/assets/images/dataModeling/detail-type-theme.png') no-repeat center;
              background-size: auto 38px;
            }
            &.header-box-icon-3 {
              background: $--base-color-tag-bg
                url('@/assets/images/dataModeling/detail-type-process.png') no-repeat center;
              background-size: auto 38px;
            }
          }
          .header-box-content {
            display: inline-block;
            margin-left: 20px;
            vertical-align: top;
            .header-title {
              font-size: 20px;
              line-height: 20px;
              color: $--base-color-title1;
            }
            .header-title-type {
              height: 18px;
              padding: 0px 4px;
              font-size: 12px;
              line-height: 18px;
              display: inline-block;
              color: $--base-color-primary;
              background: $--base-color-tag-bg2;
              &.header-title-type-1 {
                background: $--base-color-tag-bg3;
                color: $--base-color-text6;
              }
              &.header-title-type-2 {
                background: $--base-color-tag-bg2;
                color: $--base-color-text4;
              }
              &.header-title-type-3 {
                background: $--base-color-tag-yellow;
                color: $--base-color-yellow;
              }
            }
          }
        }
        .detail-status-content {
          display: inline-block;
          padding: 0 6px;
          background: $--base-color-green-disable;
          color: $--base-color-green;
          border-radius: 4px;
          .detail-status-0 {
            background: $--base-color-text2;
            color: $--base-color-text1;
          }
        }
      }
    }
  }

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
  .table-data-util {
    height: 100%;
    // padding: 15px;
    .this-table-box {
      height: 100%;
    }
    .table-top-box {
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-search-box {
        width: calc(100% - 180px);
        display: inline-block;
        vertical-align: middle;
        text-align: right;
      }
    }
    .table-box {
      height: calc(100% - 160px);
      .table-status-content {
        &.table-status-0 {
          color: $--base-color-text2;
          &::before {
            background-color: $--base-color-text2;
          }
        }
        &.table-status-1 {
          color: $--base-color-green;
          &::before {
            background-color: $--base-color-green;
          }
        }

        &.table-status-null {
          &::before {
            display: none;
          }
        }
        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-right: 4px;
          display: inline-block;
          background-color: $--base-color-text2;
        }
      }
    }
    .pagination-container {
      margin: 0;
    }
    &.less-table-box {
      height: calc(100% - 280px);
      margin-top: 20px;
    }
    &.least-table-box {
      height: calc(100% - 280px);
      margin-top: 20px;
    }
  }
  .this-table-box {
    height: 100%;
  }
  .table-top-box {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-search-box {
      width: calc(100% - 180px);
      display: inline-block;
      vertical-align: middle;
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    .card-descriptions {
      padding: 6px 0px;
    }
  }

  .pm {
    padding: 2px;
    margin: 20px 0;
    :deep .top-right-btn {
      margin-right: -2px !important;
    }
  }
  :deep .right {
    height: 100%;
    padding: 0;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    //   display: grid;
    //   grid-template-columns: 1fr 1fr 1fr;
    //   grid-gap: 10px;
    //   margin-left: 100px;
    //   display: flex;
    //   justify-content: end;
    display: inline-block;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    border: none;
  }
  :deep .el-virtual-scrollbar {
    right: -6px !important;
  }
</style>
