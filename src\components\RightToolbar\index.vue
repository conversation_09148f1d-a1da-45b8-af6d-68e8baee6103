<template>
  <div class="top-right-btn">
    <el-row>
      <el-tooltip
        v-if="search"
        class="item"
        effect="light"
        :content="showSearch ? '隐藏搜索' : '显示搜索'"
        placement="top"
      >
        <el-button circle icon="Search" @click="toggleSearch()" />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="刷新" placement="top">
        <!-- <el-button circle icon="Refresh" @click="refresh()" /> -->
        <div class="table-top-btn" @click="refresh()">
          <IconRefresh />
        </div>
      </el-tooltip>
      <el-tooltip v-if="columns" class="item" effect="light" content="显隐列" placement="top">
        <!-- <el-button circle icon="Menu" @click="showColumn()" /> -->
        <div class="table-top-btn" @click="showColumn()">
          <IconUpdatemode />
        </div>
      </el-tooltip>
    </el-row>
    <el-dialog v-model="open" :title="title" append-to-body :draggable="true">
      <div style="text-align: center">
        <el-transfer
          v-model="value"
          :titles="['显示', '隐藏']"
          :data="columns"
          style="text-align: left; display: inline-block"
          filterable
          :left-default-checked="[2, 3]"
          :right-default-checked="[1]"
          :render-content="renderFunc"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}',
          }"
          @change="dataChange"
        ></el-transfer>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { IconRefresh, IconUpdatemode } from '@arco-iconbox/vue-update-line-icon';
  const props = defineProps({
    showSearch: {
      type: Boolean,
      default: true,
    },
    columns: {
      type: Array,
    },
    search: {
      type: Boolean,
      default: false,
    },
    gutter: {
      type: Number,
      default: 10,
    },
  });

  const emits = defineEmits(['update:showSearch', 'queryTable']);

  // 显隐数据
  const value = ref([]);
  // 弹出层标题
  const title = ref('显示/隐藏');
  // 是否显示弹出层
  const open = ref(false);

  const style = computed(() => {
    const ret = {};
    if (props.gutter) {
      ret.marginRight = `${props.gutter / 2}px`;
    }
    return ret;
  });

  // 搜索
  function toggleSearch() {
    emits('update:showSearch', !props.showSearch);
  }

  // 刷新
  function refresh() {
    emits('queryTable');
  }

  // 右侧列表元素变化
  function dataChange(data) {
    for (const item in props.columns) {
      const key = props.columns[item].key;
      props.columns[item].visible = !data.includes(key);
    }
  }

  // 打开显隐列 dialog
  function showColumn() {
    open.value = true;
  }

  // 显隐列初始默认隐藏列
  for (const item in props.columns) {
    if (props.columns[item].visible === false) {
      value.value.push(parseInt(item));
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  :deep(.el-transfer__button) {
    border-radius: 50%;
    display: block;
    margin-left: 0px;
  }
  :deep(.el-transfer__button:first-child) {
    margin-bottom: 10px;
  }

  .my-el-transfer {
    text-align: center;
  }
  .top-right-btn {
    :deep(.el-row) {
      justify-content: right;
      &:last-child {
        margin-right: 0;
      }
    }
    .table-top-btn {
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border: 1px solid $--base-input-bd;
      border-radius: 8px;
      margin-left: 16px;
      cursor: pointer;
      font-size: 16px;
    }
  }
</style>
