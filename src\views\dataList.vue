<template>
  <!-- <el-page-header @back="goBack"></el-page-header> -->
  <div class="app-container">
    <el-button plain icon="ArrowLeft" type="primary" @click="goBack">返回上一层</el-button>
    <!-- <section class="head-title"> -->
    <!-- <el-row style="margin-bottom: 20px;"> -->
    <!-- <el-col :span="8"> -->
    <!-- <span>数据源名称:</span> -->
    <!-- <span>{{ form.name }}</span> -->
    <!-- </el-col> -->
    <!-- <el-col :span="5"> -->
    <!-- <span>类型:</span> -->
    <!-- <span>{{ form.type }}</span> -->
    <!-- </el-col> -->
    <!-- <el-col :span="5"> -->
    <!-- <span>用户名：</span> -->
    <!-- <span> {{ form.user }}</span> -->
    <!-- </el-col> -->
    <!--  -->
    <!-- </el-row> -->
    <!--  -->
    <!-- <el-row> -->
    <!-- <el-col :span="8"> -->
    <!-- <span style="color: #409EFF"> -->
    <!-- {{ }} -->
    <!-- <!~~ JDBC_URL : ~~> -->
    <!-- </span> -->
    <!-- <el-tooltip class="box-item" effect="dark" :content="form.jdbcUrl" placement="bottom"> -->
    <!-- <span -->
    <!-- style="display: inline-block; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"> -->
    <!-- {{ form.jdbcUrl }} -->
    <!-- </span> -->
    <!-- </el-tooltip> -->
    <!--  -->
    <!-- </el-col> -->
    <!-- <el-col :span="5"> -->
    <!-- <span>IP 主机名:</span> -->
    <!-- <span>{{ form.host }}</span> -->
    <!-- </el-col> -->
    <!-- <el-col :span="5"> -->
    <!-- <span>描述:</span> -->
    <!-- <span>{{ form.note }}</span> -->
    <!-- </el-col> -->
    <!-- </el-row> -->
    <!-- </section> -->
    <!--  -->
    <el-card class="box-card" shadow="never" style="margin-top: 20px">
      <div class="header-box">
        <div class="header-box-icon" :class="`header-icon-type-${form.type}`"></div>
        <div class="header-box-content">
          <div class="header-title">{{ form.name }}</div>
          <div class="header-title-type">{{ form.type }}</div>
        </div>
      </div>
      <el-descriptions
        title=""
        :column="route?.query?.type === 'API' ? 1 : 3"
        class="card-descriptions"
        :border="false"
        :colon="true"
        size="large"
      >
        <!-- <el-descriptions-item label="数据源名称">{{ form.name }}</el-descriptions-item> -->
        <el-descriptions-item width="33.33%" label="类型:">{{ form.type }}</el-descriptions-item>
        <template v-if="form?.type == 'MINIO' || form?.type == 'S3'">
          <el-descriptions-item width="33.33%" label="access_key:">
            {{ form.access_key }}
          </el-descriptions-item>
          <el-descriptions-item width="33.33%" label="region:">
            {{ form.region }}
          </el-descriptions-item>
          <el-descriptions-item width="33.33%" label="endpoint:">
            {{ form.endpoint }}
          </el-descriptions-item>
          <!-- 目录 -->
          <el-descriptions-item width="33.33%" label="目录:">
            {{ form.bucket }}
          </el-descriptions-item>
        </template>

        <template v-else>
          <el-descriptions-item v-if="form.type != 'API'" width="33.33%" label="用户名:">{{
            form.user
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="form.type != 'API'"
            width="33.33%"
            :label="route?.query?.type === 'API' ? 'API_URL' : 'JDBC_URL'"
          >
            <el-tooltip effect="light" :content="form.jdbcUrl" placement="bottom">
              <!-- <el-tag size="small" class="box-item">
              {{ form.jdbcUrl }}
            </el-tag> -->
              <div class="more-length-item"> {{ form.jdbcUrl }}</div>
            </el-tooltip>
            <!-- {{ form.jdbcUrl }} -->
          </el-descriptions-item>
          <el-descriptions-item v-if="form.type != 'API'" width="33.33%" label="主机:">
            {{ form.host }}
          </el-descriptions-item>
        </template>
        <!-- <el-descriptions-item label="IP主机名">{{ form.host }}</el-descriptions-item> -->
        <el-descriptions-item width="33.33%" label="创建时间:">{{
          form.createTime
        }}</el-descriptions-item>
        <el-descriptions-item v-if="form.type === 'API'" width="33.33%" label="请求方式:">{{
          form.user
        }}</el-descriptions-item>
        <el-descriptions-item v-if="form.type != 'API'" width="33.33%" label="数据源编号:">{{
          form.id
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="连接名称">{{ form.name }}</el-descriptions-item> -->

        <el-descriptions-item width="33.33%" label="所属组织:">{{
          form.organization
        }}</el-descriptions-item>
        <el-descriptions-item width="33.33%" label="备注:">{{ form.note }}</el-descriptions-item>
      </el-descriptions>
      <div v-if="form.type == 'API'" class="api-more-info-box">
        <div v-for="switchItem in switchLists" :key="switchItem.name" class="api-more-info">
          <el-switch
            v-model="switchItem.data"
            active-icon=""
            inactive-icon=""
            style="--el-switch-on-color: #13ce66"
            disabled
            active-color="#13ce66"
          />
          <span>{{ switchItem.label }}</span>
        </div>
      </div>
    </el-card>
    <!-- <el-divider></el-divider> -->

    <el-row v-if="form.type != 'API'" style="height: calc(100% - 220px)">
      <splitpanes class="default-theme">
        <pane min-size="16" max-size="25" size="16">
          <!-- <el-col :span="7" :xs="24"> -->
          <el-scrollbar>
            <el-tree
              ref="menuTree"
              v-loading="loadingForTree"
              :data="dataTree"
              :props="defaultProps"
              :expand-on-click-node="false"
              node-key="label"
              style="padding-left: 18px; overflow-y: auto"
              highlight-current="true"
              @node-click="handleNodeClick"
            >
              <template #default="{ data, node }">
                <!-- 未展开时的图标 -->
                <el-icon
                  v-show="data?.children?.length"
                  v-if="!node.expanded"
                  class="data-icon icon"
                >
                  <caretRight />
                </el-icon>
                <!-- 展开时的图标 -->
                <el-icon v-show="data?.children?.length" v-else class="data-icon icon">
                  <caretBottom />
                </el-icon>
                <span v-show="node.level == '1'" class="data-icon"
                  ><img src="@/assets/icons/源.png"
                /></span>
                <span v-show="node.level == '2'" class="data-icon"
                  ><img src="@/assets/icons/库.png"
                /></span>
                <span v-show="node.level == '3'" v-if="getComputedColumn()" class="data-icon"
                  ><img src="@/assets/icons/表.png"
                /></span>

                <div v-else>
                  <span v-show="node.level == '3'" class="data-icon">
                    <el-icon><Film /></el-icon>
                  </span>
                  <span v-show="node.level == '4'" class="data-icon">
                    <img src="@/assets/icons/表.png" />
                  </span>
                </div>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="data.label"
                  placement="top-start"
                >
                  <span> {{ data.label }}</span>
                </el-tooltip>
              </template>
            </el-tree>
          </el-scrollbar>
          <!-- </el-col> -->
        </pane>
        <pane>
          <!-- <el-col :span="17" :xs="24"> -->
          <!-- <h3>{{ 表格名称 }}</h3> -->
          <!-- <span></span> -->
          <!-- <el-space direction="vertical" style="margin: 10px;"> -->
          <!-- <el-text> -->
          <!-- <el-icon> -->
          <!-- <Reading /> -->
          <!-- </el-icon> -->
          <!-- {{ titleName }} -->
          <!-- </el-text> -->
          <!-- </el-space> -->
          <div class="table-box table-page-box">
            <el-table
              v-if="showInfo"
              border
              height="100%"
              :show-header="false"
              :cell-style="columnStyle"
              :data="dataForTable"
            >
              <el-table-column width="120px" prop="name" />
              <el-table-column prop="value" />
            </el-table>

            <el-table v-if="showTable" height="100%" :data="dataInfo">
              <el-table-column align="center" label="序号" width="60" type="index">
                <template #default="scope">
                  {{ tablePage.pageSize * (tablePage.page - 1) + (scope.$index + 1) }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="表名" prop="tableName">
                <template #default="scope">
                  <el-button @click="showDrawer(scope.row)">{{ scope.row.tableName }}</el-button>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" label="表注释" type="index">
                <template #default="scope">
                  {{ tablePage.pageSize * (tablePage.page - 1) + (scope.$index + 1) }}
                </template>
              </el-table-column> -->
            </el-table>

            <el-table v-if="showColumn" height="100%" :data="columnInfo">
              <el-table-column align="center" label="序号" width="60" type="index">
              </el-table-column>
              <el-table-column align="center" label="字段名称" prop="columnName" />
              <el-table-column align="center" label="字段类型" prop="columnType" />
              <el-table-column align="center" label="字段备注" prop="comment" />
            </el-table>

            <!-- 字段 -->
            <el-table v-if="showTopic" height="100%" :data="topicInfo">
              <el-table-column align="center" label="序号" width="60" type="index">
              </el-table-column>
              <el-table-column align="center" label="topic" prop="tableName" />
            </el-table>
            <!-- MINIO -->
            <el-table v-if="showMinIO" height="100%" :data="dataMinIO">
              <el-table-column align="center" label="序号" width="60" type="index" />
              <el-table-column
                align="center"
                label="对象名称"
                prop="objectName"
                width="160"
                show-overflow-tooltip="true"
              />
              <el-table-column align="center" label="大小" prop="size" width="100" />
              <el-table-column
                align="center"
                label="Url"
                prop="url"
                width="160"
                show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <ImagePreview
                    v-if="checkFileSuffix(scope.row)"
                    :width="100"
                    :height="100"
                    :src="scope.row.url"
                    :preview-src-list="[scope.row.url]"
                  />
                  <span v-if="!checkFileSuffix(scope.row)" v-text="scope.row.url" />
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="目录"
                prop="path"
                show-overflow-tooltip="true"
              />
              <el-table-column align="center" label="最近修改时间" prop="lastMod" />
              <el-table-column align="center" label="是否为目录" prop="dir">
                <template #default="scope">
                  <el-tag v-if="scope.row.dir" type="success">是</el-tag>
                  <el-tag v-else type="info">否</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <pagination
            v-show="showTable && tableTotal > 0"
            v-model:page="tablePage.page"
            v-model:limit="tablePage.pageSize"
            :pager-count="maxCount"
            :total="tableTotal"
            @pagination="listPageForTable"
          />

          <!-- <el-divider></el-divider> -->

          <!-- <div v-if="false">
          <h3>用户列表</h3>
          <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="探索字段" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="探索指标" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          </el-row>
           <el-table :data="userList" @selection-change="handleSelectionChange">
          <el-table-column label="字段名称" key="userName" prop="userName" v-if="columns[1].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="字段类型" key="nickName" prop="nickName" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
           </el-table>
        </div> -->

          <!-- </el-col> -->
        </pane>
      </splitpanes>
    </el-row>
    <div v-if="form.type === 'API'" class="api-info-card">
      <el-descriptions
        title=""
        :column="route?.query?.type === 'API' ? 1 : 3"
        class="card-descriptions"
        :border="false"
        :colon="true"
        size="large"
      >
        <!-- <el-descriptions-item label="数据源名称">{{ form.name }}</el-descriptions-item> -->
        <el-descriptions-item label="取值Key:">{{ preParamKey }}</el-descriptions-item>
      </el-descriptions>
      <div class="card-title">请求参数</div>
      <div class="table-box">
        <el-table height="320px" :data="reqDataList">
          <el-table-column key="prop" label="参数名称" prop="prop" :show-overflow-tooltip="true" />
          <el-table-column
            key="paramPosition"
            label="参数位置"
            prop="paramPosition"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            key="reqParameterLineType"
            label="参数模式"
            prop="reqParameterLineType"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            key="valType"
            label="参数类型"
            prop="valType"
            :show-overflow-tooltip="true"
          />
          <el-table-column key="val" label="参数值" prop="val" :show-overflow-tooltip="true" />
        </el-table>
      </div>
      <div class="card-title">返回参数</div>
      <div class="json-tree-box">
        <div class="json-tree">
          <!-- show result -->
          <div v-for="(item, key) in jsonTreeData" :key="key">
            <json-tree
              :item="item"
              :key-name="key"
              :root="true"
              :original-json="jsonTreeData"
              :parent-json="jsonTreeData"
              :expand-all="expandAll"
            >
            </json-tree>
          </div>
        </div>
      </div>
    </div>
    <el-drawer
      v-model="tableDrawerInfo.show"
      direction="rtl"
      close-on-click-modal
      size="740"
      class="drawer-content-box"
    >
      <template #header>
        <h4>表详情</h4>
      </template>
      <template #default>
        <div class="table-table-box">
          <el-table height="100%" :data="columnInfo">
            <el-table-column align="center" label="序号" width="60" type="index" />
            <el-table-column align="center" label="字段名称" prop="columnName" />
            <el-table-column align="center" label="字段类型" prop="columnType" />
            <el-table-column align="center" label="字段备注" prop="comment" />
          </el-table>
        </div>
        <!-- 分页 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <pagination
              v-show="tableDrawerInfo.total > 0"
              v-model:page="tableDrawerInfo.pageNum"
              v-model:limit="tableDrawerInfo.pageSize"
              :pager-count="tableDrawerInfo.maxCount"
              :total="tableDrawerInfo.total"
              @pagination="listPage"
            />
          </el-col>
        </el-row>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    apiDetail,
    getBucketList,
    getBucketRootObjs,
    getDatabaseList,
    getFieldList,
    getInfoById,
    getTableForPage,
    schemaForGP,
    updateUi,
  } from '@/api/dataSourceManageApi';
  import { onMounted, toRefs, reactive } from 'vue';
  import jsonTree from '@/components/jsonTree/index';
  import { useRoute } from 'vue-router';
  // import { isLeaf } from "element-plus/es/utils";
  // import { tr } from "element-plus/es/locale";
  // import { defineProps } from 'vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '字段名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '字段名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '字段类型不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
    switchLists: [
      {
        label: '追加 Cookies 到响应体',
        data: false,
      },
      {
        label: '追加 Params 到响应体',
        data: false,
      },
      {
        label: '是否跳过 ssl 证书验证',
        data: false,
      },
      {
        label: '追加 Body 到响应体',
        data: false,
      },
      {
        label: '追加 Headers 到响应体',
        data: false,
      },
    ],
    reqDataList: [],
    expandAll: false,
    jsonTreeData: {
      resultCode: 1,
      resultMsg: '调用接口成功',
      results: {
        pageTotal: 162,
        eventList: [
          {
            eventid: 'fc2cda3407f144ef91f1eb2fbca648df',
            eventnum: 'JBSXXA1224012312385',
            eventtitle: 'kkk',
            eventaddress: 'ccccc',
            eventcontent: 'dddddd',
            sjddl: '水务管理',
            sjddl_code: '103',
            eventcategory: '河床淤积生活垃圾、腐质淤泥等',
            eventcategory_code: '103004',
            event_jd: '龙泉街道',
            eventlongitude: '',
            eventlatitude: '',
            eventstatus: '02',
            createtime: '2024-01-23 11:17:37',
            deng_pai: 'cqwbj',
            event_timelimit: '2024-01-30',
            answer_date: '2024-01-23 11:17:37',
            work_acptdep: '龙泉驿区',
            cl_dep: '区测试部门',
            flag1: '3',
            jd_code: '510112001',
            sq_code: '',
            wg_code: '',
            dubantype: 'p_yellow',
          },
          {
            eventid: '5060133f90d9440290b6db794db29a0a',
            eventnum: 'JBSXXA1224012312384',
            eventtitle: 'vvvv',
            eventaddress: 'xxxx',
            eventcontent: 'xxxxxx',
            sjddl: '环境保护',
            sjddl_code: '102',
            eventcategory: '固体危险废物',
            eventcategory_code: '102002',
            event_jd: '龙泉街道',
            eventlongitude: '',
            eventlatitude: '',
            eventstatus: '99',
            createtime: '2024-01-23 11:00:45',
            deng_pai: 'aqbj',
            event_timelimit: '2024-01-31',
            answer_date: '2024-01-23 11:00:45',
            work_acptdep: '龙泉街道',
            cl_dep: '龙泉驿区',
            flag1: '3',
            flag2: '0',
            jd_code: '510112001',
            sq_code: '',
            wg_code: '',
            end_time: '2024-01-23 11:04:32',
          },
          {
            eventid: '261c5a51c1c94271bbfa9b7bd4b5f8ec',
            eventnum: 'JBSXXA1224012312383',
            eventtitle: '测试123',
            eventaddress: 'xxxxx',
            eventcontent: 'xxxxxxx',
            sjddl: '环境保护',
            sjddl_code: '102',
            eventcategory: '建筑工地噪声扰民',
            eventcategory_code: '102003',
            event_jd: '龙泉街道',
            eventlongitude: '104.26196',
            eventlatitude: '30.56541',
            eventstatus: '02',
            createtime: '2024-01-23 10:23:09',
            deng_pai: 'cqwbj',
            event_timelimit: '2024-02-01',
            answer_date: '2024-01-23 10:23:09',
            work_acptdep: '龙泉驿区',
            cl_dep: '区测试部门',
            flag1: '3',
            jd_code: '510112001',
            sq_code: '',
            wg_code: '',
          },
        ],
      },
    },
    preParamKey: '',
  });

  const { form, switchLists, reqDataList, expandAll, jsonTreeData, preParamKey } = toRefs(data);
  const tableDrawerInfo = reactive({
    show: false,
    columnInfo: [],
    pageNum: '',
    pageSize: '',
    total: 0,
    maxCount: 50,
  });
  const route = useRoute();
  const DatasourceId = route.query.DatasourceId;
  const loadingForTree = ref(false);
  // 数据库有模式时，需要该值
  const xuguDB = ref(null);
  // 页面加载时获取数据源的详情
  onMounted(() => {
    if (route.query.type == 'MINIO' || route.query.type == 'S3') {
      getBucketList({
        datasourceId: DatasourceId,
      }).then((res) => {
        dataTree.value = res.data.map((item) => {
          return {
            label: item,
            children: [],
            key: item,
          };
        });
        getInfoById(DatasourceId).then((res) => {
          form.value = res.data;
          form.value.jdbcUrl = JSON.parse(res.data.connectionParams).jdbcUrl;
          form.value.host = JSON.parse(res.data.connectionParams).host;
          form.value.organization = res.data.organization;
          xuguDB.value = JSON.parse(res.data.connectionParams).database;

          if (form?.value.type == 'MINIO' || form?.value.type == 'S3') {
            form.value.access_key = JSON.parse(res.data.connectionParams).accessKey;
            form.value.region = JSON.parse(res.data.connectionParams).region;
            form.value.endpoint = JSON.parse(res.data.connectionParams).endpoint;
            form.value.bucket = JSON.parse(res.data.connectionParams).roots;
          }

          //   dataTree.value[0].label = res.data.name;
          nextTick(() => {
            if (dataTree?.value[0]?.label) {
              document.querySelector('.el-tree-node__content').click();
            }
          });
        });
      });
    } else {
      if (route.query.type != 'API') {
        getInfoById(DatasourceId).then((res) => {
          form.value = res.data;
          form.value.jdbcUrl = JSON.parse(res.data.connectionParams).jdbcUrl;
          form.value.host = JSON.parse(res.data.connectionParams).host;
          form.value.organization = res.data.organization;
          xuguDB.value = JSON.parse(res.data.connectionParams).database;
          dataTree.value[0].label = res.data.name;
          nextTick(() => {
            if (dataTree.value[0].label) {
              document.querySelector('.el-tree-node__content').click();
            }
          });
          // if(res.data.type == 'KAFKA') {
          //   dataTree.value[0].label = res.data.name
          // }
          form.value.user = JSON.parse(res.data.connectionParams).user;
        });
      } else {
        apiDetail(DatasourceId).then((res) => {
          console.log('res', res.data);
          const switchDatas = JSON.parse(res.data.connectionParams);
          const apiData = JSON.parse(res.data.apiData);
          form.value.name = res.data.name;
          form.value.note = res.data.desc;
          form.value.type = res.data.dataSourceType;
          form.value.jdbcUrl = res.data.url;
          form.value.organization = res.data.organization;

          switchLists.value[0].data = switchDatas?.cookies_add;
          switchLists.value[1].data = switchDatas?.params_add;
          switchLists.value[2].data = switchDatas?.skipSsl;
          switchLists.value[3].data = switchDatas?.body_add;
          switchLists.value[4].data = switchDatas?.headers_add;
          reqDataList.value = switchDatas.reqParamLines;
          preParamKey.value = switchDatas.preParamKey;
          jsonTreeData.value = apiData;
        });
      }
    }
  });
  const router = useRouter();

  const defaultProps = {
    children: 'children',
    label: 'label',
  };

  const dataTree = ref([
    {
      label: '',
      children: [],
    },
  ]);

  const loading = ref(false);
  const showInfo = ref(false);
  const showTable = ref(false);
  const showColumn = ref(false);
  const showTopic = ref(false);
  const showMinIO = ref(false);
  const topicInfo = ref([]);
  const detailInfo = ref({});
  const dataInfo = ref([]);
  const dataMinIO = ref([]);
  const titleName = ref(null);
  const columnInfo = ref([]);
  const choseNode = ref({});
  const listPageForTable = () => {
    let tableObj = null;
    if (form.value.type == 'MINIO' || form.value.type == 'S3') {
      tableObj = { bucketName: treeLabel.value, datasourceId: DatasourceId };
    } else {
      // 数据库不一样，所需参数不一样 ac
      if (form.value.type == 'ORACLE') {
        tableObj = { schema: treeLabel.value, datasourceId: DatasourceId };
      } else if (form.value.type == 'XUGU') {
        tableObj = { schema: treeLabel.value, datasourceId: DatasourceId };
      } else if (
        form.value.type == 'DB2' ||
        form.value.type == 'KINGBASE' ||
        form.value.type == 'POSTGRESQL' ||
        form.value.type == 'GREENPLUM'
      ) {
        tableObj = {
          schema: treeLabel.value,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'SQLSERVER' || form.value.type == 'SYBASE') {
        tableObj = {
          schema: treeLabel.value,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'DAMENG') {
        tableObj = {
          schema: treeLabel.value,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'DWS') {
        tableObj = {
          schema: treeLabel.value,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else {
        tableObj = { databaseName: treeLabel.value, datasourceId: DatasourceId };
      }
    }
    tableObj.pageSize = tablePage.value.pageSize;
    tableObj.page = tablePage.value.page;
    loading.value = true;
    getTableForPage(tableObj)
      .then((res) => {
        loading.value = false;
        if (res.data.data.length) {
          showTable.value = true;
          dataInfo.value = res.data.data;
          tableTotal.value = res.data.total;
        } else {
          showTable.value = false;
          data.children = [];
        }
      })
      .catch(() => (loading.value = false));
  };
  const tableTotal = ref(0);
  const tablePage = ref({
    page: 1,
    pageSize: 10,
  });
  // 用于保存分页函数的参数
  const treeLabel = ref(null);
  // 当请求其他表数据时，需要重置分页组织
  watch(treeLabel, () => {
    tablePage.value = {
      page: 1,
      pageSize: 10,
    };
  });

  // 获取详细信息和子级列表
  const handleNodeClick = async (data, node) => {
    // kafka 第二级不能点击
    if (node.level == 2 && form.value.type == 'KAFKA') return false;
    choseNode.value = node;
    let tableObj = null;

    if (form.value.type == 'MINIO' || form.value.type == 'S3') {
      tableObj = { bucketName: data.label, datasourceId: DatasourceId };
      console.log(tableObj);
      const res = await getBucketRootObjs(tableObj);
      showMinIO.value = true;
      dataMinIO.value = res.data;
    } else {
      // 数据库不一样，所需参数不一样 ac
      if (form.value.type == 'ORACLE') {
        tableObj = { schema: data.label, datasourceId: DatasourceId };
      } else if (form.value.type == 'XUGU') {
        tableObj = { schema: data.label, datasourceId: DatasourceId };
      } else if (
        form.value.type == 'DB2' ||
        form.value.type == 'KINGBASE' ||
        form.value.type == 'POSTGRESQL' ||
        form.value.type == 'GREENPLUM'
      ) {
        tableObj = {
          schema: data.label,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'SQLSERVER' || form.value.type == 'SYBASE') {
        tableObj = {
          schema: data.label,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'DAMENG') {
        tableObj = {
          schema: data.label,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else if (form.value.type == 'DWS') {
        tableObj = {
          schema: data.label,
          datasourceId: DatasourceId,
          databaseName: xuguDB.value,
        };
      } else {
        tableObj = { databaseName: data.label, datasourceId: DatasourceId };
      }
    }
    // 判断是否有子分支，如果有则相应的收起列表，如果有切换节点点击，则获取相应节点的数据列表
    if (data?.children?.length) {
      getDataOnNewnode(data, node, tableObj);
      // showTable.value = false
    } else {
      // expandNode(data, node)
      // 树节点为第一级
      if (node.level == 1 && form.value.type != 'MINIO' && form.value.type != 'S3') {
        showColumn.value = false;
        titleName.value = data.label;
        // 获取当前节点的详细信息，在表格中展示
        updateUi({ id: DatasourceId }).then((res) => {
          detailInfo.value = { ...res.data };
          showInfo.value = true;
        });
        // 获取子级列表
        getSecondDataList(data, node);
        return;
      }
      showInfo.value = false;
      // KAFKA 不同于关系型数据库
      if (
        form.value.type != 'KAFKA' &&
        form.value.type != 'TDENGINE' &&
        form.value.type != 'XUGUTSDB'
      ) {
        // 第二级点击 key 是自定义属性，获取表数据
        if (data.key) {
          showTopic.value = false;
          titleName.value = data.label;
          loading.value = true;
          showColumn.value = false;
          if (
            data.key === '库' &&
            form.value.type !== 'MYSQL' &&
            form.value.type !== 'HIVE' &&
            form.value.type !== 'CLICKHOUSE'
          ) {
            schemaForGP({
              databaseName: xuguDB.value,
              datasourceId: DatasourceId,
            }).then((res) => {
              if (res.data.length) {
                res.data.map((i) => {
                  data.children.push({ label: i, children: [], key: '表' });
                  return data.children;
                });
              } else {
                data.children = [];
              }
              loadingForTree.value = false;
              node.expanded = true;
              loading.value = false;
              node.expanded = true;
            });
          } else if (form.value.type != 'MINIO' && form.value.type != 'S3') {
            treeLabel.value = data.label;
            tableObj.pageSize = 10;
            tableObj.page = 1;
            getTableForPage(tableObj).then((res) => {
              if (res.data.data.length) {
                showTable.value = true;
                dataInfo.value = res.data.data;
                tableTotal.value = res.data.total;
                // res.data.map((i) => {
                //   data.children.push({ label: i.tableName, table: '1' });
                //   return data.children;
                // });
              } else {
                dataInfo.value = [];
                tableTotal.value = 0;
                showTable.value = false;
                data.children = [];
              }
              // node.expanded = !node.expanded
            });
            loading.value = false;
            node.expanded = true;
            return;
          }
        }
        showTable.value = false;
        // 第三级点击 table 是自定义属性，获取字段数据
        if (data.table) {
          //   titleName.value = data.label;
          //   showTopic.value = false;
          //   showColumn.value = true;
          //   loading.value = true;
          //   getFieldDataList(data, node);
        }
      } else {
        if (data.key) {
          //   showTopic.value = true;
          //   loading.value = true;
          //   getTableForPage(tableObj)
          //     .then((res) => {
          //       topicInfo.value = res.data;
          //       loading.value = false;
          //       loadingForTree.value = false;
          //     })
          //     .catch(() => {
          //       loading.value = false;
          //       loadingForTree.value = false;
          //     });

          loading.value = true;
          tableObj.pageSize = 10;
          tableObj.page = 1;
          treeLabel.value = data.label;
          getTableForPage(tableObj)
            .then((res) => {
              showTable.value = true;
              dataInfo.value = res.data.data;
              tableTotal.value = res.data.total;
              loading.value = false;
              loadingForTree.value = false;
            })
            .catch(() => {
              loading.value = false;
              loadingForTree.value = false;
            });
        }
      }
    }
  };
  // 数据存在，且没有点击新节点时，不需要发起请求
  /**
   *
   * @param {OBJECT} data 树状结构的 data 对象
   * @param {OBJECT} node 树状结构的 node 对象
   * @param {OBJECT} tableObj 不同数据库的请求参数对象
   */
  const getDataOnNewnode = (data, node, tableObj) => {
    if (node.level == 1) {
      showTopic.value = false;
      showTable.value = false;
      titleName.value = data.label;
      showColumn.value = false;
      loading.value = true;
      updateUi({ id: DatasourceId }).then((res) => {
        showInfo.value = true;
        detailInfo.value = { ...res.data };
        loading.value = false;
      });
      node.expanded = !node.expanded;
    }
    showInfo.value = false;
    if (data.key) {
      showTopic.value = false;
      titleName.value = data.label;
      showInfo.value = false;
      showTable.value = true;
      loading.value = true;
      showColumn.value = false;
      tableObj.pageSize = 10;
      tableObj.page = 1;
      treeLabel.value = data.label;
      // 判断是否是点击新节点，并发起请求
      !data?.children?.length &&
        getTableForPage(tableObj).then((res) => {
          if (res.data.data.length) {
            dataInfo.value = res.data.data;
            tableTotal.value = res.data.total;
          } else {
            dataInfo.value = [];
            showTable.value = false;
            tableTotal.value = 0;
          }
        });
      loading.value = false;
      node.expanded = !node.expanded;
    }
  };
  // 获取树状结构第二级数据列表
  /**
   *
   * @param {OBJECT} data 树状结构的 data 对象
   * @param {OBJECT} node 树状结构的 node 对象
   */
  const getSecondDataList = (data, node) => {
    if (
      // form.value.type == 'HIVE' ||
      //   form.value.type == 'XUGU' ||
      //   form.value.type == 'POSTGRESQL' ||
      //   form.value.type == 'GREENPLUM' ||
      //   form.value.type == 'SQLSERVER' ||
      //   form.value.type == 'DAMENG' ||
      //   form.value.type == 'KINGBASE' ||
      //   form.value.type == 'DB2' ||
      //   form.value.type == 'ORACLE'
      getComputedColumn()
    ) {
      schemaForGP({
        databaseName: xuguDB.value,
        datasourceId: DatasourceId,
      }).then((res) => {
        if (res.data.length) {
          res.data.map((i) => {
            data.children.push({ label: i, children: [], key: '表' });
            return data.children;
          });
        } else {
          data.children = [];
        }
        loadingForTree.value = false;
        node.expanded = true;
      });
    } else {
      getDatabaseList({ datasourceId: DatasourceId })
        .then((res) => {
          if (res.data.length) {
            res.data.map((i) => {
              data.children.push({ label: i, children: [], key: '库' });
              return data.children;
            });
          } else {
            data.children = [];
          }
          loadingForTree.value = false;
          node.expanded = true;
        })
        .catch(() => (loadingForTree.value = false));
    }

    //  else {
    //   getDatabaseList({ datasourceId: DatasourceId })
    //     .then((res) => {
    //       if (res.data.length) {
    //         res.data.map((i) => {
    //           data.children.push({ label: i, children: [], key: '表' });
    //           return data.children;
    //         });
    //       } else {
    //         data.children = [];
    //       }
    //       loadingForTree.value = false;
    //       node.expanded = true;
    //     })
    //     .catch(() => (loadingForTree.value = false));
    // }
  };
  // 获取数据库的字段数据列表
  /**
   *
   * @param {OBJECT} data 树状结构的 data 对象
   * @param {OBJECT} node 树状结构的 node 对象
   */
  const getFieldDataList = (data, node, type) => {
    const req = {
      tableName: data.label || data.tableName,
      datasourceId: DatasourceId,
    };

    // 创建一个映射表，用于根据数据库类型设置req对象的属性
    const dbTypeConfig = {
      POSTGRESQL: { schema: true, databaseName: true },
      GREENPLUM: { schema: true, databaseName: true },
      SQLSERVER: { schema: true, databaseName: true },
      KINGBASE: { schema: true, databaseName: true },
      DB2: { schema: true, databaseName: true },
      SYBASE: { schema: true, databaseName: true },
      MYSQL: { schema: false, databaseName: true },
      HIVE: { schema: false, databaseName: true },
      SPARK: { schema: false, databaseName: true },
      XUGU: { schema: true, databaseName: false },
      ORACLE: { schema: true, databaseName: false },
      DAMENG: { schema: true, databaseName: false },
      DWS: { schema: true, databaseName: true },

      CLICKHOUSE: { schema: false, databaseName: true },
      TDENGINE: { schema: false, databaseName: true },
      XUGUTSDB: { schema: false, databaseName: true },
    };

    const setTypeSpecificProps = (type, node, xuguDB) => {
      const config = dbTypeConfig[form.value.type];
      if (!config) return;

      if (config.schema) {
        req.schema = type === 1 ? node.data.label : node.parent.data.label;
      }
      if (config.databaseName) {
        req.databaseName = config.schema
          ? xuguDB.value
          : type === 1
            ? node.data.label
            : node.parent.data.label;
      }
    };

    // 使用映射表来设置属性
    if (Object.prototype.hasOwnProperty.call(dbTypeConfig, form.value.type)) {
      setTypeSpecificProps(type, node, xuguDB);
    } else {
      req.schema = type === 1 ? node.data.label : node.parent.data.label;
    }

    getFieldList(req)
      .then((res) => {
        if (res.data.length) {
          columnInfo.value = res.data;
        } else {
          columnInfo.value = [];
        }
        loading.value = false;
      })
      .catch(() => (loading.value = false));
  };

  // 表格竖着展示
  const dataForTable = computed(() => {
    return [
      {
        id: 1,
        name: '数据源编号',
        value: detailInfo.value.id,
      },
      {
        id: 2,
        name: '连接名称',
        value: detailInfo.value.name,
      },
      {
        id: 3,
        name: '类型',
        value: detailInfo.value.type,
      },
      {
        id: 4,
        name: '主机',
        value: detailInfo.value.host,
      },
      {
        id: 5,
        name: '登录用户名',
        value: detailInfo.value.userName,
      },
      {
        id: 6,
        name: '创建时间',
        value: detailInfo.value.createTime,
      },
    ];
  });

  // 自定义列背景色
  const columnStyle = (row) => {
    // 设置第一列的样式
    if (row.columnIndex === 0) {
      return { 'background-color': '#f3f6fc' };
    }
    return { 'background-color': '#ffffff' };
  };

  const goBack = () => {
    // console.log('go back')
    router.push('/centralAdmin/dataSourcemanage');
  };

  //   form.value.type == 'HIVE' ||form.value.type == 'DAMENG' ||form.value.type == 'ORACLE'  // 计算属性
  const getComputedColumn = () => {
    if (form.value.type == 'DAMENG' || form.value.type == 'ORACLE') return true;
  };

  // 打开抽屉
  const showDrawer = (scope) => {
    tableDrawerInfo.show = true;
    getFieldDataList(scope, choseNode.value, 1);
  };

  function checkFileSuffix(row) {
    try {
      const fileName = row.objectName;
      const imageTypes = ['.png', '.jpg', '.jpeg'];
      return imageTypes.some((suffix) => fileName?.toLowerCase().endsWith(suffix));
    } catch (error) {
      console.error('Error checking file suffix:', error);
      return false;
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;
    ::v-deep
      .el-descriptions--large
      .el-descriptions__body
      .el-descriptions__table:not(.is-bordered)
      .el-descriptions__cell {
      padding-bottom: 0px;
    }
    .box-card {
      border-radius: 8px;
      margin-bottom: 20px;
      border: none;
      :deep .el-card__body {
        padding: 10px 10px 0px !important;
      }
      .header-box {
        height: 56px;
        border-bottom: 1px solid #f7f8fb;
        .header-box-icon {
          width: 46px;
          height: 46px;
          display: inline-block;
          background: $--base-color-tag-bg url('@/assets/images/databaseIcon/XUGU.png') no-repeat
            center;
          background-size: auto 46px;
          border-radius: 8px;
          &.header-icon-type-SYBASE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SYBASE.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-POSTGRESQL {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/POSTGRESQL.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-HIVE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/HIVE.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-ORACLE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/ORACLE.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-API {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/API.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-DAMENG {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DAMENG.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-DB2 {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DB2.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-GREENPLUM {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/GREENPLUM.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-KAFKA {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/KAFKA.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-MYSQL {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MYSQL.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-SPARK {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SPARK.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-SQLSERVER {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SQLSERVER.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-KINGBASE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/KINGBASE.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-MINIO {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MINIO.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-S3 {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MINIO.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-DWS {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DWS.png') no-repeat
              center;
            background-size: auto 46px;
          }
          &.header-icon-type-CLICKHOUSE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/CLICKHOUSE.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-TDENGINE {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/TDENGINE.png')
              no-repeat center;
            background-size: auto 46px;
          }
          &.header-icon-type-ACTIVEMQ {
            background: $--base-color-tag-bg url('@/assets/images/databaseIcon/ACTIVEMQ.png')
              no-repeat center;
            background-size: auto 46px;
          }
        }
        .header-box-content {
          display: inline-block;
          margin-left: 20px;
          vertical-align: top;
          .header-title {
            font-size: 20px;
            line-height: 20px;
            color: $--base-color-title1;
          }
          .header-title-type {
            height: 18px;
            padding: 0px 4px;
            font-size: 12px;
            line-height: 18px;
            display: inline-block;
            color: $--base-color-primary;
            background: $--base-color-tag-bg2;
          }
        }
      }
      .card-descriptions {
        padding: 10px 0px;
      }
      .api-more-info-box {
        margin-bottom: 10px;
        .api-more-info {
          & span {
            margin-left: 12px;
            color: $--base-color-text1;
            font-size: 14px;
          }
        }
      }
      :deep .el-descriptions__content {
        &:not(.is-bordered-label) {
          font-size: 14px;
          color: $--base-color-text2;
        }
      }
      .el-descriptions--large .el-descriptions__label {
        &:not(.is-bordered-label) {
          font-size: 14px;
          color: $--base-color-text1;
        }
      }
    }
    .api-info-card {
      background: $--base-color-item-light;
      padding: 10px;
      .card-title {
        height: 40px;
        line-height: 40px;
        position: relative;
        padding-left: 12px;
        &::before {
          content: '';
          height: 16px;
          width: 3px;
          background: $--base-color-primary;
          position: absolute;
          top: 12px;
          left: 0;
          border-radius: 4px;
        }
      }
      .table-box {
        background: $--base-color-card-bg;
      }
      .json-tree-box {
        background: $--base-color-box-bg;
        border-radius: 8px;
        padding: 10px;
        :deep .json-reader-tree-property {
          background: transparent;
          border-bottom: none;
          font-size: 12px;
        }
      }
    }
  }
  .head-title {
    font-size: 16px;
    margin-top: 20px;
    // 外边框
    border: 1px solid #f0f0f05d;
    padding: 20px;
    padding-bottom: -20px;
  }

  .head-title > .el-row {
    margin-bottom: 10px;

    //  每个 span 左右间距
    span {
      margin-right: 10px;
    }
  }

  .icon {
    margin-left: -18px;
  }

  .data-icon {
    margin-right: 5px;
    font-size: 12px;

    img {
      position: relative;
      top: 3px;
      left: 0;
      width: 15px;
      height: 15px;
    }
  }

  .box-item {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .more-length-item {
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: bottom;
  }

  :deep .splitpanes.default-theme {
    .splitpanes__pane {
      height: 100%;
      padding: 10px;
      background: $--base-color-item-light;
      border-radius: 8px;
    }
    &.splitpanes--vertical > .splitpanes__splitter,
    &.default-theme .splitpanes--vertical > .splitpanes__splitter {
      width: 20px;
      background: transparent;
      border-left: none;
    }
  }
  :deep(.el-tree-node__content > .el-tree-node__expand-icon) {
    position: absolute;
    opacity: 0;
  }

  :deep(.el-tree-node__content) {
    position: relative;
  }
  .table-box {
    height: 100%;
    &.table-page-box {
      height: calc(100% - 46px) !important;
    }
  }
</style>
