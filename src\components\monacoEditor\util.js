import { toRaw } from 'vue';

export const tips = (v) => {
  range(
    v,
    `
  #--注释--#
        `,
  );
};

export const range = (v, text) => {
  const position = toRaw(v).getPosition();
  toRaw(v).executeEdits('', [
    {
      range: {
        startLineNumber: position.lineNumber,
        startColumn: position.column,
        endLineNumber: position.lineNumber,
        endColumn: position.column,
      },
      text,
    },
  ]);
};

/**
 * 设置自定义右键菜单（添加自定义菜单项）
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} emit - Vue组件的emit函数
 */
export const setupCustomContextMenu = (editor, emit) => {
  if (!editor) return;
  addCustomMenuActions(editor, emit);
};

/**
 * 添加自定义菜单项
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} emit - Vue组件的emit函数
 */
const addCustomMenuActions = (editor, emit) => {
  // 添加自定义菜单项：写到 CDataPath
  editor.addAction({
    id: 'write-to-cdata-path',
    label: '写到 CDataPath',
    keybindings: [],
    contextMenuGroupId: 'custom',
    contextMenuOrder: 1,
    run: (ed) => {
      try {
        const selection = ed.getSelection();
        const selectedText = selection ? ed.getModel().getValueInRange(selection) : '';

        setTimeout(() => {
          emit('writeToField', {
            type: 'CDataPath',
            selectedText,
            position: ed.getPosition(),
            selection,
          });
        }, 0);
      } catch (error) {
        console.error('写到 CDataPath 时出错:', error);
      }
    },
  });

  // 添加自定义菜单项：写到 RowTag
  editor.addAction({
    id: 'write-to-row-tag',
    label: '写到 RowTag',
    keybindings: [],
    contextMenuGroupId: 'custom',
    contextMenuOrder: 2,
    run: (ed) => {
      try {
        const selection = ed.getSelection();
        const selectedText = selection ? ed.getModel().getValueInRange(selection) : '';

        setTimeout(() => {
          emit('writeToField', {
            type: 'RowTag',
            selectedText,
            position: ed.getPosition(),
            selection,
          });
        }, 0);
      } catch (error) {
        console.error('写到 RowTag 时出错:', error);
      }
    },
  });
};
