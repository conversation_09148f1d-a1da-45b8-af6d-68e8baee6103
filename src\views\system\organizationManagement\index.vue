<template>
  <SplitPanes class="rule-container">
    <template #left>
      <div class="rule-left-box">
        <div class="top">
          <div class="title"> 组织目录 </div>
          <div class="btn-box">
            <!-- <el-tooltip class="box-item" effect="light" content="导出目录" placement="top">
              <el-button type="primary" plain> <IconUpload /> </el-button>
            </el-tooltip>
            <el-tooltip class="box-item" effect="light" content="导入目录" placement="top">
              <el-button type="primary" plain @click="dialogVisibleForImport = true">
                <IconDownload />
              </el-button>
            </el-tooltip>
            <el-tooltip class="box-item" effect="light" content="导入导出历史" placement="top">
              <el-button type="primary" plain @click="dialogVisibleForLog = true">
                <IconHistory />
              </el-button>
            </el-tooltip> -->
            <ExportAndImport
              moduleName="OrganizationLabel"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getGroupsTree"
            ></ExportAndImport>
            <el-tooltip effect="light" content="新增" placement="top">
              <el-button type="primary" class="box-item" @click="addGroupBtn()">
                <IconAdd
              /></el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="export-box"> </div>
        <div class="left-box">
          <el-input
            v-model="treeSearchText"
            v-input="searchTree"
            class="tree-search"
            placeholder="请输入搜索内容"
            prefix-icon="Search"
          >
          </el-input>
          <el-scrollbar>
            <el-tree
              ref="treeRef"
              class="left-tree-box"
              :data="allTreeData"
              :props="propsGroupTree"
              :highlight-current="true"
              :filter-node-method="filterNode"
            >
              <template #default="items">
                <div class="tree-item" @click="handleNodeClick(items)">
                  <div class="tree-item-box">
                    <el-icon>
                      <FolderOpened />
                    </el-icon>
                    <el-tooltip :content="items.data.groupName" placement="top">
                      {{
                        items.data.groupName.length > 10
                          ? items.data.groupName.slice(0, 10) + '...'
                          : items.data.groupName
                      }}
                    </el-tooltip>
                  </div>
                  <div class="tree-btn-box">
                    <span
                      style="margin-right: 10px"
                      class="tree-icon"
                      @click.stop="addGroupBtn(items)"
                    >
                      <el-icon>
                        <Plus />
                      </el-icon>
                    </span>
                    <el-popover
                      :ref="`popoverRef${items.data.groupId}`"
                      trigger="click"
                      placement="top"
                    >
                      <div
                        v-if="items.data.groupName"
                        class="tree-icon btnBox"
                        @click.stop="editGroup(items)"
                        style="cursor: pointer; text-align: center; height: 30px; line-height: 30px"
                        :onmouseover="(e) => (e.target.style.background = '#EAEFF5')"
                        :onmouseout="(e) => (e.target.style.background = '')"
                      >
                        <el-icon>
                          <Edit />
                        </el-icon>
                        <span style="margin-left: 10px">编辑</span>
                      </div>
                      <div
                        v-if="!items.data?.children || items.data?.children?.length <= 0"
                        class="tree-icon btnBox"
                        @click.stop="deleteGroup(items)"
                        style="cursor: pointer; text-align: center; height: 30px; line-height: 30px"
                        :onmouseover="(e) => (e.target.style.background = '#EAEFF5')"
                        :onmouseout="(e) => (e.target.style.background = '')"
                      >
                        <el-icon>
                          <Delete />
                        </el-icon>
                        <span style="margin-left: 10px">删除</span>
                      </div>
                      <template #reference>
                        <span class="tree-icon" @click.stop="items.data.visible = true">
                          <el-icon>
                            <MoreFilled />
                          </el-icon>
                        </span>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </template>
    <template #right>
      <div class="rule-right-box">
        <div class="top">
          <div class="title">组织列表</div>
          <div class="search-container">
            <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
              <el-form-item label="组织名称">
                <el-input
                  style="width: 240px"
                  v-model="searchForm.name"
                  placeholder="请输入名称"
                  clearable
                />
              </el-form-item>
              <el-form-item label="组织类型" prop="type">
                <el-select
                  v-model="searchForm.type"
                  style="width: 240px"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="dict in sys_organization_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="margin-right: 5px">
                <span class="table-search-btn">
                  <span class="btn btn1" @click="getList"
                    ><el-icon style="color: #fff"> <Search /> </el-icon
                  ></span>
                  <span class="btn btn2" @click="searchReSet"
                    ><el-icon style="color: #434343"> <Refresh /> </el-icon
                  ></span>
                </span>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="table-container">
          <div class="table-top-box">
            <div>
              <el-button :disabled="isAbleClick" icon="Plus" type="primary" @click="addRule"
                >新增组织</el-button
              >
              <el-button
                :disabled="multiple"
                icon="Delete"
                type="danger"
                @click="deleteOrganizationData"
                >批量删除</el-button
              >
            </div>
            <div><right-toolbar :columns="columns" @query-table="getList"></right-toolbar></div>
          </div>
          <div class="table-box">
            <el-table :data="dataList" height="100%" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" width="60" label="序号">
                <template #default="scope">
                  {{ searchForm.pageSize * (searchForm.pageNum - 1) + (scope.$index + 1) }}
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="name"
                v-if="columns[0].visible"
                label="组织名称"
              >
              </el-table-column>
              <el-table-column v-if="columns[1].visible" label="组织类型">
                <template #default="scope">
                  <span>{{ getLabel(scope.row.type) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                v-if="columns[2].visible"
                prop="root"
                label="根目录"
              >
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                v-if="columns[3].visible"
                prop="remark"
                label="备注"
              >
              </el-table-column>
              <el-table-column v-if="columns[4].visible" prop="createTime" label="创建时间">
              </el-table-column>
              <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
                <template #default="scope">
                  <el-button type="text" @click="editOrganization(scope.row)">编辑</el-button>
                  <el-button type="text" @click="deleteOrganizationData(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <pagination
            v-show="total > 0"
            v-model:page="searchForm.pageNum"
            v-model:limit="searchForm.pageSize"
            :total="total"
            @pagination="getList"
          />
        </div>
      </div>
    </template>
  </SplitPanes>
  <!-- 新增分类 -->
  <el-dialog
    v-model="addGroupDialog"
    :title="addGroupDialogTitle"
    width="650"
    append-to-body
    :draggable="true"
  >
    <OrganizationAddGroup
      v-if="addGroupDialog"
      ref="addGroupRef"
      :form-data="editGroupForm"
      :active-name="activeName"
      groupNameLabel="组织目录名称"
    >
    </OrganizationAddGroup>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="addGroupCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 新增组织 -->
  <el-dialog
    v-model="addRuleDialog"
    :title="addRuleDialogTitle"
    width="650px"
    append-to-body
    :draggable="true"
    @close="closeAddRuleDialog"
  >
    <el-form ref="formRef" :rules="rule" :model="form" label-width="auto" label-position="right">
      <el-form-item label="组织名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入组织名称"
          maxlength="30"
          show-word-limit
          @input="(data) => (form.name = data.replace(/\s/g, ''))"
        ></el-input>
      </el-form-item>
      <el-form-item label="组织类型" prop="type">
        <el-select v-model="form.type" style="width: 100%" placeholder="请选择" clearable>
          <el-option
            v-for="dict in sys_organization_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model.trim="form.remark"
          type="textarea"
          placeholder="请输入内容"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="() => (addRuleDialog = false)">取 消</el-button>
        <el-button type="primary" @click="addOrganizationCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- <ImportDialog
    v-if="dialogVisibleForImport"
    @close="() => (dialogVisibleForImport = false)"
    moduleName="CodeTable"
  ></ImportDialog>
  <ImportExportLog
    v-if="dialogVisibleForLog"
    @close="() => (dialogVisibleForLog = false)"
    moduleName="CodeTable"
  ></ImportExportLog> -->
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserProfile } from '@/api/system/user';
  import {
    getOrganizationTagList,
    addOrganizationTag,
    updateOrganizationTag,
    deleteOrganizationTag,
    getOrganizationList,
    deleteOrganization,
    addOrganization,
    updateOrganization,
  } from '@/api/system/organizationManage';
  import OrganizationAddGroup from '../components/organizationAddGroup';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getCurrentInstance, ref } from 'vue';
  import SplitPanes from '@/components/SplitPanes/index';
  import ExportAndImport from '@/components/exportAndImport/index';

  import {
    IconUpload,
    IconDownload,
    IconHistory,
    IconAdd,
  } from '@arco-iconbox/vue-update-line-icon';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  let userInfo = reactive({});
  const { proxy } = getCurrentInstance();
  const { sys_organization_type } = proxy.useDict('sys_organization_type');

  const getLabel = (data) => {
    const label = sys_organization_type._object.sys_organization_type.filter(
      (res) => res.value == data,
    )[0].label;
    return label;
  };

  const searchForm = ref({ pageSize: 20, pageNum: 1, name: null, type: null, rootId: null });
  const dataList = ref([]);
  const total = ref(0);

  const dialogVisibleForImport = ref(false);
  const dialogVisibleForLog = ref(false);

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `组织名称`, visible: true },
    { key: 1, label: `组织类型`, visible: true },
    { key: 2, label: `根目录`, visible: true },
    { key: 3, label: `备注`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
  ]);

  const multiple = ref(true);
  const ids = ref([]);

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    multiple.value = !selection.length;
  }

  const getList = async () => {
    if (!searchForm.value.rootId) return proxy.$modal.msgError('请先选择一个目录');
    const data = {
      ...searchForm.value,
      workspaceId: workspaceId.value,
    };
    const res = await getOrganizationList(data);
    const tableData = res.rows.map((res) => {
      res.groupName = groupName.value;
      return res;
    });
    dataList.value = tableData;
    total.value = res.total;
  };

  const searchReSet = () => {
    searchForm.value.pageNum = 1;
    searchForm.value.name = null;
    searchForm.value.type = null;
    getList();
  };

  // 获取分组树
  const getGroupsTree = async () => {
    const data = {
      workspaceId: workspaceId.value,
    };
    const resData = await getOrganizationTagList(data);
    allTreeData.value = deepChildren(resData.data);
  };
  // 处理树数据
  const deepChildren = (group) => {
    return group.map((item) => {
      return {
        id: item.id,
        groupName: item.label,
        children: item.children && item.children.length > 0 ? deepChildren(item.children) : [],
      };
    });
  };
  const allTreeData = ref([]);
  const treeSearchText = ref(null);
  const propsGroupTree = reactive({
    value: 'id',
    label: 'groupName',
    children: 'children',
  });

  const isAbleClick = ref(true);
  const groupName = ref(null);
  const handleNodeClick = (item) => {
    if (item.data.children?.length) return;
    isAbleClick.value = false;
    searchForm.value.rootId = item.data.id;
    groupName.value = item.data.groupName;
    getList();
  };

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.groupName.includes(value);
  };

  // 新增组织目录弹出框配置
  const addGroupDialog = ref(false);
  const addGroupRef = ref(null);
  const addGroupDialogTitle = ref('新增组织目录');
  const activeName = ref('first');
  let editGroupForm = reactive({});

  const addGroupBtn = (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      if (item?.data) {
        editGroupForm = JSON.parse(JSON.stringify(item.data));
        editGroupForm.parentName = item.data.groupName;
        editGroupForm.groupName = '';
        addGroupRef.value.setForm(editGroupForm);
      } else {
        activeName.value = 'second';
      }
      addGroupDialogTitle.value = '新增组织目录';
    });
  };
  const closeAddGroupDialog = () => {
    editGroupForm = {};
    activeName.value = 'first';
    addGroupDialogTitle.value = '新增组织目录';
    addGroupDialog.value = false;
  };

  // 编辑分组
  const editGroup = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      editGroupForm.parentName = item.node.parent.data.groupName;
      editGroupForm.parentId = item.node.parent.data.id;
      if (!editGroupForm.parentName) {
        activeName.value = 'second';
      } else {
        activeName.value = 'first';
      }
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑组织目录';
    });
  };

  // 新增组织目录
  const addGroupCommit = async () => {
    const addForm = addGroupRef.value.getForm();
    let res = {};
    let textTitle = '新增';
    const reqData = {
      name: addForm.groupName,
      parentId: addForm.parentId,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      reqData.tenantId = tenantId.value;
    }
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑组织目录') {
      textTitle = '编辑';
      reqData.id = addForm.id;
      res = await updateOrganizationTag(reqData);
    } else {
      res = await addOrganizationTag(reqData);
    }

    if (res.code === 200) {
      isAbleClick.value = true;
      ElMessage.success(textTitle + '成功');
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error(textTitle + '失败：' + res.msg);
    }
  };

  // 删除分组、接口、转发
  const deleteGroup = async (item) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${item.data.groupName}数据项？`);
    if (!confirm) return;
    const res = await deleteOrganizationTag({ id: item.data.id });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      if (item.data.id == searchForm.value.rootId) {
        isAbleClick.value = true;
      } else {
        isAbleClick.value = false;
      }
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };

  //导出目录
  const downloadTree = () => {
    proxy.download(
      'system/role/export',
      {
        ...queryParams.value,
      },
      `role_${new Date().getTime()}.xlsx`,
    );
  };

  const form = ref({
    name: null,
    type: null,
    remark: null,
  });

  const rule = reactive({
    name: [{ required: true, message: '请输入组织名称', trigger: 'change' }],
    type: [{ required: true, message: '请选择组织类型', trigger: 'change' }],
  });

  const addRuleDialog = ref(false);
  const addRuleDialogTitle = ref('新增组织');

  const addRule = () => {
    addRuleDialogTitle.value = '新增组织';
    addRuleDialog.value = true;
  };

  const editOrganization = (row) => {
    for (const key in form.value) {
      form.value[key] = row[key];
    }
    form.value.id = row.id;
    addRuleDialogTitle.value = '编辑组织';
    addRuleDialog.value = true;
  };

  const deleteOrganizationData = async (data) => {
    if (data?.id) {
      const confirm = await proxy.$modal.confirm(`是否确认删除${data.name}组织？`);
      if (!confirm) return;
      const res = await deleteOrganization({ id: data.id });
      if (res.code === 200) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error('删除失败：' + res.msg);
      }
    } else {
      batchDel();
    }
  };

  const batchDel = async () => {
    const confirm = await proxy.$modal.confirm(`是否确认删除这些组织？`);
    if (!confirm) return;
    const id = ids.value.join(',');
    const res = await deleteOrganization({ id });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      getList();
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };

  const addOrganizationCommit = async () => {
    const confirm = await proxy.$refs.formRef.validate((valid) => valid);
    if (!confirm) return;
    const data = {
      name: form.value.name,
      rootId: searchForm.value.rootId,
      type: form.value.type,
      remark: form.value.remark,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    toSendInterFace(data);
  };

  const toSendInterFace = async (data) => {
    if (form.value.id) {
      data.id = form.value.id;
      const resForUpdate = await updateOrganization(data);
      if (resForUpdate.code != 200) return proxy.$modal.msgError(resForUpdate.msg);
      proxy.$modal.msgSuccess(resForUpdate.msg);
      addRuleDialog.value = false;
      getList();
    } else {
      const resForAdd = await addOrganization(data);
      if (resForAdd.code != 200) return proxy.$modal.msgError(resForAdd.msg);
      proxy.$modal.msgSuccess(resForAdd.msg);
      addRuleDialog.value = false;
      getList();
    }
  };

  const closeAddRuleDialog = () => {
    activeName.value = 'first';
    form.value = {
      name: null,
      type: null,
      remark: null,
    };
    proxy.resetForm('formRef');
    addRuleDialog.value = false;
  };

  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    getGroupsTree();
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
    // 重置所有数据状态
    dataList.value = [];
    isAbleClick.value = true;
    total.value = 0;
    searchForm.value = { pageSize: 20, pageNum: 1, name: null, type: null, rootId: null };
  });
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .page-container {
    width: 100%;
    height: 100%;
  }
  .rule-container {
    height: 100%;
    display: flex;
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-left: 10px;
      position: relative;
    }
    .top::before {
      position: absolute;
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      top: 5px;
      left: -10px;
    }

    .rule-left-box {
      //   width: 260px;
      //   margin-right: 20px;
      height: 100%;
      .btn-box {
        .box-item {
          width: 28px;
          height: 28px;
        }
        :deep .export-and-import {
          display: inline-block;
          margin-right: 12px;
        }
      }

      .tree-search {
        margin-bottom: 10px;
        background: $--base-color-item-light;
      }
      .left-box {
        height: calc(100% - 94px);
        background-color: $--base-color-item-light;
        padding: 10px;
        border-radius: 8px;
        ::v-deep .el-scrollbar {
          height: calc(100% - 42px);
        }
      }
      .left-tree-box {
        padding: 5px;
        height: calc(100% - 52px);
      }
    }

    .rule-right-box {
      //   flex: 1;
      //   width: calc(100% - 280px);
      height: 100%;
      .btn {
        cursor: pointer;
        display: inline-block;
        width: 32px;
        height: 32px;
        padding: 0 10px;
        border-radius: 20px;
        &.btn1 {
          background: #1269ff;
          margin-right: 10px;
        }
        &.btn2 {
          background: #dce5f5;
        }
      }

      .table-top-box {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      .table-container {
        height: calc(100% - 70px);
        .table-box {
          height: calc(100% - 102px);
        }
      }
      .pagination-container {
        margin-top: 10px;
      }
    }
  }

  .arrBox {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
  }

  .content-box {
    // width: 610px;
    padding: 10px;
    border-radius: 8px;
    background: #f7f8fb;
    .formList-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .tag-box {
    width: 36px;
    height: 22px;
    padding: 4px 6px;
  }

  .alert-box {
    background: #eaf1ff;
    color: #8c8c8c;
  }

  :deep(.el-alert .el-alert__icon) {
    color: #1269ff;
  }

  .detail-box {
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }
</style>
