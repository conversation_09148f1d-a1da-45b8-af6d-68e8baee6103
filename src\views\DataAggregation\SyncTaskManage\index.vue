<template>
  <div class="app-container">
    <HeadTitle :title="HeadTitleName" :pull-down="true" @update-list="listPage" />
    <!-- <el-row :gutter="20"> -->
    <!-- 工作流 -->

    <!--用户数据-->
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="流程名称">
        <el-input
          v-model="queryParams.searchVal"
          placeholder="请输入流程名称"
          clearable
          style="width: 255px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['operator:flow:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增工作流
        </el-button>
        <!-- <el-button type="primary" plain icon="Plus" @click="ToAddTask" v-hasPermi="['system:user:add']"> -->
        <!--  -->
        <!-- </el-button> -->
      </el-col>
      <!-- </el-row> -->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column align="center" label="序号" width="150">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.currentPage - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="编号" key="userId" prop="userId" v-if="columns[0].visible" /> -->
        <el-table-column
          align="center"
          label="工作流名称"
          prop="flowName"
          :show-overflow-tooltip="true"
        />
        <el-table-column align="center" label="流程类型" prop="flowType">
          <template #default="scope">
            <el-tag
              v-if="
                (scope.row.flowType == 'OFFLINE' && scope.row.flowTaskType == null) ||
                scope.row.flowTaskType == 'Kettle'
              "
              type=" danger"
              >离线</el-tag
            >
            <el-tag
              v-if="scope.row.flowTaskType == 'Kafka' || scope.row.flowTaskType == 'FlinkCDC'"
              type="success"
              >实时</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column align="center" label="版本" prop="version"></el-table-column>
        <el-table-column align="center" label="是否提交" prop="approvalStatus">
          <template #default="scope">
            <el-tag v-if="scope.row.approvalStatus == 0" type="success">已提交</el-tag>
            <el-tag v-if="scope.row.approvalStatus != 0" type="danger">未提交</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="描述"
          prop="description"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
        <el-table-column align="center" label="创建人" prop="createUser" />
        <el-table-column
          align="center"
          label="操作"
          class-name="small-padding fixed-width"
          min-width="200"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              :disabled="scope.row.approvalStatus != 0"
              @click="handleRun(scope.row)"
            >
              运行</el-button
            >
            <el-button
              v-hasPermi="['operator:flow:add']"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
            >
              编辑</el-button
            >
            <el-button
              link
              type="primary"
              :disabled="
                scope.row.approvalStatus != 0 ||
                scope.row.flowTaskType == 'Kafka' ||
                scope.row.flowTaskType == 'FlinkCDC'
              "
              @click="handleAttemper(scope.row)"
            >
              调度设置
            </el-button>
            <el-button
              link
              type="primary"
              :disabled="scope.row.approvalStatus != 0"
              @click="handlesubscribe(scope.row)"
            >
              告警订阅
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.currentPage"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="listPage"
      />
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      v-model="openAttemper"
      title="调度设置"
      width="50%"
      append-to-body
      @close="cancelAttemper()"
    >
      <el-alert
        title="提示"
        type="warning"
        description="请按照以下步骤进行操作：
  1. 选择适当的时间。
  2. 生成相应的 Cron 表达式。
  3. 确保保存所做的更改。
  4. 注意：务必不要忽略选择秒时段。"
      >
      </el-alert>
      <el-row>
        <el-col :span="24" style="margin: 20px 0 20px 0">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-M-D HH:mm:ss"
            :disabled-date="disablesDate"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="24">
          <vue3Cron
            v-if="showCron"
            :project-code-of-ds="projectCodeOfDs"
            :datetimerange="datetimerange"
            @change="handleCronChange"
          />
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAttemper">取 消</el-button>
          <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button>
          <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增流程 -->
    <el-dialog
      v-model="open"
      :title="title"
      width="560px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancel()"
    >
      <el-form
        ref="flowRef"
        :model="form"
        :rules="rules"
        label-width="115px"
        @submit.native.prevent
      >
        <el-form-item label="流程名称" prop="flowName">
          <el-input v-model="form.flowName" maxlength="30"></el-input>
        </el-form-item>
        <!-- <el-form-item label="流程类型" prop="flowType"> -->
        <!-- <el-select width="200px" v-model="form.flowType"> -->
        <!-- <el-option v-for="dict in flow_type" :key="dict.label" :value="dict.value" -->
        <!-- :label="dict.label"></el-option> -->
        <!-- </el-select> -->
        <!-- </el-form-item> -->
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="openWarn"
      title="告警设置"
      width="560px"
      :close-on-click-modal="false"
      append-to-body
      z-index="1000"
      @close="cancelWarn()"
    >
      <div class="part">
        <div class="inform">通知策略</div>
        <div class="selectBox">
          <el-radio-group v-model="radio">
            <el-radio :label="1">成功失败都通知</el-radio>
            <el-radio :label="3">失败通知</el-radio>
            <el-radio :label="2">成功通知</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="part">
        <div class="inform">通知渠道</div>
        <div class="selectBox">
          <el-checkbox-group v-model="checkList" @change="getWays">
            <el-checkbox label="站内信" />
            <el-checkbox :disabled="onCheck" label="邮箱" />
          </el-checkbox-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelWarn">取 消</el-button>
          <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import { getInfo } from '@/api/login';
  import {
    addWorkFlow,
    deleteById,
    getPage,
    runWorkflow,
    SchedulingConfiguration,
  } from '@/api/dataAggregation';
  import { getTenantList } from '@/api/system/user';
  import { addSubscribe } from '@/api/alert/subscribe';

  /// 导入组件HeadTitle
  import HeadTitle from '@/components/HeadTitle';
  import vue3Cron from '@/components/vue3Cron';
  import { watch } from 'vue';
  import { ElMessage } from 'element-plus';

  // 告警订阅功能模块
  const onButton = ref(false);
  const radio = ref(1);
  const mail = ref(false);
  const onCheck = ref(false);
  const openWarn = ref(false);
  const email = ref(null);
  const objForWarn = ref({});
  const checkList = ref(['站内信']);
  const projectCodeOfDs = ref();
  const datetimerange = ref(1);
  const showCron = ref(false);
  const getWays = () => {
    if (checkList.value.length) {
      onButton.value = false;
    } else {
      onButton.value = true;
    }
  };
  const handlesubscribe = (row) => {
    if (!email.value) {
      onCheck.value = true;
      // proxy.$modal.msgWarning('请前往个人用户中心绑定邮箱')
      ElMessage({
        message: '请前往个人用户中心绑定邮箱',
        type: 'warning',
        customClass: 'messageIndex',
      });
    } else {
      onCheck.value = false;
    }
    objForWarn.value.bizId = row.id;
    objForWarn.value.processCode = row.flowIdOfDs;
    objForWarn.value.projectCode = row.projectCodeOfDs;
    objForWarn.value.userId = userId.value;
    objForWarn.value.tenantId = tenantId.value;
    objForWarn.value.workspaceId = queryParams.value.workspaceId;
    objForWarn.value.createProcessUser = row.createUser;
    openWarn.value = true;
  };

  const disablesDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7;
  };

  // 提交设置
  const submitFormWarn = () => {
    if (checkList.value.length == 2) {
      objForWarn.value.alertType = '1,3';
    } else if (checkList.value.length == 1) {
      if (checkList.value.indexOf('站内信') != -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    } else {
      objForWarn.value.alertType = '';
    }

    if (radio.value == 1) {
      objForWarn.value.subscribeType = '1';
    } else if (radio.value == 2) {
      objForWarn.value.subscribeType = '2';
    } else {
      objForWarn.value.subscribeType = '3';
    }

    if (objForWarn.value.processCode == null || objForWarn.value.projectCode == null) {
      proxy.$modal.msgError('请先发布流程调度');
      return;
    }
    addSubscribe(objForWarn.value).then((res) => {
      if (res.code == 200) {
        openWarn.value = false;
        proxy.$modal.msgSuccess('订阅成功，请前往告警中心查看并管理已订阅流程');
      }
    });
  };
  // 取消设置
  const cancelWarn = () => {
    checkList.value = ['站内信'];
    onButton.value = false;
    openWarn.value = false;
  };

  const tenantList = ref([]);
  const isAdmin = ref(true);
  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const processDefinitionCode = ref();
  const cronValue = ref();

  const userId = ref(null);
  onMounted(() => {
    // listPage()
    // 获取租户列表
    getInfo().then((res) => {
      userId.value = res.data.user.userId;
      email.value = res.data.user.email;
      tenantId.value = res.data.user.tenantId;
      if (res.data.user.userType != 'sys_user') {
        isAdmin.value = false;
      } else {
        isAdmin.value = true;
        getTenantList().then((res) => (tenantList.value = res.data));
      }
    });
  });

  const tenantId = ref(null);
  // 获取流程列表
  const listPage = (e) => {
    if (e.selectedWorkspaceId && typeof e.selectedWorkspaceId === 'number') {
      queryParams.value.workspaceId = e.selectedWorkspaceId;
    } else {
      queryParams.value.workspaceId = queryParams.value.workspaceId;
    }

    getPage(queryParams.value)
      .then((res) => {
        if (res.data.records.length) {
          total.value = res.data.totalCount;
          dataList.value = res.data.records;
        } else {
          dataList.value = [];
          total.value = 0;
        }
        loading.value = false;
      })
      .catch((error) => (loading.value = false));
  };

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { operator_flow_add } = proxy.useDict('operator_flow_add');
  const HeadTitleName = ref('工作流');
  const dataList = ref([]);
  const open = ref(false);
  const loading = ref(false);
  const showSearch = ref(true);
  const total = ref(0);
  const title = ref('新增流程');

  const data = reactive({
    form: {
      flowName: '',
      flowType: '',
      description: '',
    },
    queryParams: {
      currentPage: 1,
      pageSize: 20,
      searchVal: null,
    },
    rules: {
      flowName: [
        { required: true, message: '流程名称不能为空', trigger: 'blur' },
        { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
          message: '名称只能包含中文、英文、数字和下划线',
        },
      ],
      flowType: [{ required: true, message: '流程类型不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.currentPage = 1;
    queryParams.value.workspaceId = queryParams.value.workspaceId;
    getPage(queryParams.value)
      .then((res) => {
        if (res.data.records.length) {
          total.value = res.data.totalCount;
          dataList.value = res.data.records;
        } else {
          dataList.value = [];
          total.value = 0;
        }
        loading.value = false;
      })
      .catch((error) => (loading.value = false));
  }
  /** 重置按钮操作 */
  function resetQuery() {
    queryParams.value.searchVal = null;
    handleQuery();
  }

  const { flow_type } = proxy.useDict('flow_type');

  const handleAdd = () => {
    if (queryParams.value.workspaceId) {
      open.value = true;
    } else {
      return proxy.$modal.msgWarning('请先选择工作空间');
    }
  };

  const submitForm = () => {
    form.value.workspaceId = queryParams.value.workspaceId;
    form.value.flowType = 'OFFLINE';
    proxy.$refs.flowRef.validate((valid) => {
      if (valid) {
        addWorkFlow(form.value)
          .then((res) => {
            if (res.code == 200) {
              open.value = false;
              // 构建目标路由
              const targetRoute = {
                path: '/DataAggregation/SyncChange',
                query: {
                  id: res.data.id,
                  name: res.data.flowName,
                },
              };
              // 使用 $router.push() 跳转到目标路由
              router.push(targetRoute);
              // console.log('res', res)
              proxy.$modal.msgSuccess('新增成功');
            }
          })
          .catch((error) => (open.value = false));
      }
    });
  };

  // 获取子组件的值
  function handleCronChange(data) {
    console.log('cronValue', data);
    cronValue.value = data;
    console.log('cronValue', data);
  }

  // 打开调度弹窗
  function handleAttemper(row) {
    processDefinitionCode.value = row.flowIdOfDs;
    projectCodeOfDs.value = row.projectCodeOfDs;
    Attemper.value.name = row.flowName;
    Attemper.value.type = row.flowType;

    openAttemper.value = true;
    showCron.value = true;
    nextTick(() => {
      const currentDate = new Date();
      const tomorrowDate = new Date(currentDate.getTime() + 10 * 365 * 24 * 60 * 60 * 1000);
      value1.value = [format(currentDate), format(tomorrowDate)];
    });
  }

  // 修改时间格式
  const format = (date) => {
    return `${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
  };

  // 保存调度设置
  function submitAttemper(listData) {
    console.log('listData', listData);
    console.log('value1.value', value1.value);
    if (!value1.value) {
      return proxy.$modal.msgError('请选择调度时间');
    }
    if (!cronValue.value) {
      return proxy.$modal.msgError('请选择生成调度表达式');
    }
    if (typeof cronValue.value !== 'string') return false;

    const cronArr = cronValue.value.split(' ');

    if (cronArr[0] == '*' || cronArr[0] == '0/1') {
      return proxy.$modal.msgError('[秒] 必须选择具体的值');
    }

    const query = {
      ...Attemper.value,
      scheduleParam: {
        startTime: value1.value[0],
        endTime: value1.value[1],
        crontab: cronValue.value,
        timezoneId: 'Asia/Shanghai',
      },
      toBeOnline: listData,
      processDefinitionCode: processDefinitionCode.value,
      workspaceId: queryParams.value.workspaceId,
    };
    SchedulingConfiguration(query).then((res) => {
      console.log('res', res);
      if (res.code == 200) {
        openAttemper.value = false;
        showCron.value = false;
        proxy.$modal.msgSuccess('保存成功');
        // 关闭弹窗
        cancelAttemper();
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('您确定删除"' + row.flowName + '"流程吗？注意：一旦删除，该任务将无法恢复')
      .then(() => {
        deleteById(row.id)
          .then(() => {
            proxy.$modal.msgSuccess('删除成功');
            // listPage()
            handleQuery();
          })
          .catch(() => {});
      });
  }
  function cancelAttemper() {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    value1.value = null;
    cronValue.value = null;
  }

  /** 重置操作表单 */
  function reset() {
    form.value = {
      flowName: '',
      flowType: '',
      description: '',
    };
    proxy.resetForm('flowRef');
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    router.push({
      path: '/DataAggregation/SyncChange',
      query: {
        id: row.id,
        name: row.flowName,
        type: 'edit',
        flowTaskType: row.flowTaskType == 'Kettle',
      },
    });
  }
  function handleRun(row) {
    console.log('row', row);
    console.log('row', row.projectCodeOfDs);
    console.log('row', row.flowIdOfDs);
    // const query = {
    //    projectCode: row.projectCode,
    //    code: row.code
    // }
    runWorkflow(row.projectCodeOfDs, row.flowIdOfDs).then((res) => {
      console.log('res.data', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  }

  // watch value1
  watch(
    value1,
    (newValue, oldValue) => {
      // Perform actions when value1 changes
      datetimerange.value = newValue;
      // Assuming you have a method to handle the cron change
      console.log('value1 changed:', datetimerange.value);
      // handleCronChange();
    },
    { immediate: true },
  );
</script>

<style scoped lang="scss">
  .app-container {
    position: relative;

    .title {
      width: 1500px;
      position: absolute;
      top: 20px;
      left: 200px;
      z-index: 10;
    }
  }

  .el-dialog .el-select {
    width: 500px;
  }

  .part {
    margin: 10px;
    font-size: 16px;

    .inform {
      margin-left: 10px;
    }

    .selectBox {
      margin: 24px 0 24px 66px;
    }
  }

  .messageIndex {
    z-index: 99999999 !important;
  }

  .el-message {
    z-index: 99999999 !important;
  }
</style>
