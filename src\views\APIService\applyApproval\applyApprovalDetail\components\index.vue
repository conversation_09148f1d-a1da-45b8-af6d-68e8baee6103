<script setup>
  import TaskHandle from '@/views/flyflow/flyflow/components/task/handler/task.vue';

  import { onMounted, reactive, ref, nextTick } from 'vue';

  import {
    completeTask,
    // deleteProcessInstance,
    queryTodoAndFinishedTaskList,
    getApprovalHistory,
  } from '@/views/flyflow/flyflow/api/task';

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import applyApprovalDetail from '@/views/APIService/applyApproval/applyApprovalDetail';
  import { useRoute } from 'vue-router';
  import pagination from '@/views/flyflow/flyflow/components/pagination.vue';
  import { isNotBlank } from '@/views/flyflow/flyflow/utils/objutil';
  import { ElMessage } from 'element-plus';
  import { getGroupTree, getAllCategoryList } from '@/api/APIService';
  const { proxy } = getCurrentInstance();

  const optionsForAuthType = ref([
    // { value: 'none', label: '无' },
    { value: 'app_code', label: '简单认证' },
    { value: 'app_secret', label: '签名认证' },
  ]);

  // 获取分组
  const groupTreeData = ref(null);
  const getGroupsTree = async () => {
    const resData = await getGroupTree();
    groupTreeData.value = deelTreeSelect(resData.data);
  };

  const categoryList = ref(null);
  // 获取主题
  const getCategory = async () => {
    const resForList = await getAllCategoryList();
    categoryList.value = resForList.data;
  };

  // 处理树 select
  const deelTreeSelect = (data) => {
    const returnData = [];
    if (data && data.length > 0) {
      data.forEach((children) => {
        if (children.groupName && children.groupId) {
          returnData.push({
            label: children.groupName,
            value: children.groupId,
            children: deelTreeSelect(children.children),
          });
        }
      });
    }
    return returnData;
  };

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const showPage = ref(false);
  //   const loading = ref(false);
  //   const flowFrom = ref(null);
  let choseRow = reactive({});
  const flowFormData = reactive({
    apiLevel: 'condition',
    categoryId: '',
    hubGroupId: '',
    authType: '',
  });
  const flowDialog = ref(false);
  const flowDialogType = ref(1);
  const flowDialogTitle = ref('审批通过');
  const flowRules = ref({});
  const flowStatus = ref('1'); // 1正常，2最后一步
  const total = ref(0);
  const statusOptions = ref([
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '待审批',
      value: 2,
    },
    {
      label: '已驳回',
      value: 3,
    },
    {
      label: ' 已撤销',
      value: 4,
    },
  ]);

  //   const copy = (value) => {
  //     copyToBoard(value);
  //   };
  //   const statusList = [
  //     {
  //       label: '待审批',
  //       value: 1,
  //     },
  //     {
  //       label: '已结束',
  //       value: 2,
  //     },
  //     {
  //       label: '已撤销',
  //       value: 3,
  //     },
  //   ];
  const statusResultList = [
    {
      label: '待审批',
      value: 0,
    },
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '已驳回',
      value: 2,
    },
  ];
  const statusList = [
    {
      label: '待审批',
      value: 1,
    },
    {
      label: '已结束',
      value: 2,
    },
    {
      label: '已撤销',
      value: 3,
    },
  ];
  const resultList = [
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '已驳回',
      value: 2,
    },
  ];

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  });

  const roleList = ref();

  //   const dataType = ref('2');
  const dataStatus = ref('2');

  const taskHandler = ref();

  // 表格数据转换 根据 value 转 label
  const tableValueToLabel = (constants, data, isMultiple) => {
    if (data === null || data === undefined) {
      return '';
    }
    if (isMultiple) {
      return constants
        .filter((item) => {
          return data.indexOf(item.value) !== -1;
        })
        .map((item) => {
          return item.label;
        })
        .join(',');
    } else {
      // console.log(data, constants);
      const findItem = constants.find((item) => {
        return data === item.value;
      });
      return findItem !== null && findItem !== undefined ? findItem.label : '';
    }
  };
  /**
   * 点击开始处理
   * @param row
   */
  const deal = (row) => {
    showPage.value = true;
    choseRow = row;
    choseRow.flowType = row.name === '服务申请审批流程' ? 'api_apply_flow' : choseRow.flowType;
    // if (dataType.value === '1' && choseRow.status === 1) {
    //   dataStatus.value = 9;
    // else if (dataType.value === '1' && choseRow.status === 3) {
    //   dataStatus.value = 8;
    // } else {
    //   dataStatus.value = dataType.value;
    // }
    // 刷新列表
    handleQuery();
  };
  //   const deelDialog = (row) => {
  //     getTask(row.taskId).then((res) => {
  //       const d = {
  //         taskId: row.taskId,
  //         processInstanceId: row.processInstanceId,
  //         flowId: row.flowId,
  //       };

  //       taskHandler.value.deal(d);
  //     });
  //   };

  // 流程编码的表格宽度
  const processInstanceBizCodeWidth = ref(200);

  /**
   * 查询
   */
  function handleQuery() {
    // loading.value = true;
    const query = {
      ...searchForm,
      //   workspaceId: workspaceId.value,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      hub: true,
      cancel: searchForm.status === 4 ? true : '',
    };
    // 撤销的内容要用其他接口查询
    if (searchForm.status !== 4) {
      queryTodoAndFinishedTaskList(query)
        .then(({ data }) => {
          for (const itm of data.records) {
            const number = itm.processInstanceBizCode?.length * 12;
            if (number > processInstanceBizCodeWidth.value) {
              processInstanceBizCodeWidth.value = number;
            }
          }
          roleList.value = data.records.map((item) => {
            item.showStatus = tableValueToLabel(statusResultList, item.processInstanceResult || 0);
            item.status = item.processInstanceResult ? 2 : 1;
            item.result = item.processInstanceResult ? item.processInstanceResult : '';
            item.name = item.processName;
            item.username = item.rootUserName;
            // item.taskAssignShow = item.taskName;
            // item.updateTime = item.taskEndTime;
            item.createTime = item.taskCreateTime;
            return item;
          });
          total.value = data.total;
        })
        .finally(() => {
          // loading.value = false;
        });
    } else {
      getApprovalHistory(query)
        .then(({ data }) => {
          for (const itm of data.records) {
            const number = itm.processInstanceBizCode?.length * 12;
            if (number > processInstanceBizCodeWidth.value) {
              processInstanceBizCodeWidth.value = number;
            }
          }
          roleList.value = data.records.map((item) => {
            //   item.showStatus = tableValueToLabel(statusList, item.status);
            item.showStatus =
              item.status === 2
                ? tableValueToLabel(resultList, item.applyResult)
                : tableValueToLabel(statusList, item.status);
            item.name = item.instanceName;
            //   item.name = item.processName;.value
            item.apiname = item.apiName;
            item.username = item.applyUser;
            item.taskAssignShow = item.approvalUser;
            // item.endTime = item.endTime;
            item.createTime = item.applyTime;
            return item;
          });
          total.value = data.total;
        })
        .finally(() => {
          // loading.value = false;
        });
    }
  }

  function reset() {
    searchForm.name = '';
    searchForm.status = 2;
    searchForm.applyUser = '';
    searchForm.instanceName = '';
    handleQuery();
  }
  //   // 切换数据类型
  //   const changeDataType = () => {
  //     Object.assign(searchForm, { name: '', outputValue: '' });
  //     handleQuery();
  //   };

  //   // 删除列
  //   const deleteList = (item) => {};

  // 审批确定
  const flowDialogCommit = () => {
    proxy.$refs.flowFrom.validate(async (valid) => {
      if (valid) {
        const thisParam = {
          apiLevel: flowFormData.apiLevel,
          authType: flowFormData.authType,
          categoryId: flowFormData.categoryId,
          hubGroupId: flowFormData.hubGroupId,
        };
        thisParam[choseRow.flowUniqueId] = '';
        const reqData = {
          approveDesc: flowFormData.proposal,
          approveResult: flowDialogTitle.value !== '审批驳回',
          paramMap: thisParam,
          processInstanceId: choseRow.processInstanceId,
          taskId: choseRow.taskId,
        };
        if (flowFormData.dateRange) {
          reqData.startTime = flowFormData.dateRange[0].slice(0, 11);
          reqData.endTime = flowFormData.dateRange[1].slice(0, 11);
        }
        completeTask(reqData).then((res) => {
          if (res.ok) {
            ElMessage.success('审批处理成功');
            Object.assign(flowFormData, {
              apiLevel: 'condition',
              categoryId: '',
              hubGroupId: '',
              authType: '',
            });
            flowDialog.value = false;
            showPage.value = false;
            handleQuery();
          }
        });
      }
    });
  };

  // 隐藏审批弹出框
  const closeFlowDialog = () => {
    flowDialog.value = false;
  };
  // 审批、驳回弹出框
  const showFlowDialog = (row, type) => {
    Object.assign(flowFormData, {
      apiLevel: 'condition',
      categoryId: '',
      hubGroupId: '',
      authType: '',
    });

    flowDialogType.value = type;
    if (type === 1) {
      flowDialogTitle.value = '审批驳回';
      flowRules.value = {
        proposal: [{ required: true, message: '请输入驳回意见', trigger: 'change' }],
      };
    } else {
      flowDialogTitle.value = '审批同意';
      flowRules.value = {
        hubGroupId: [{ required: true, message: '请选择接口分组', trigger: 'change' }],
        categoryId: [{ required: true, message: '请选择接口主题', trigger: 'change' }],
        apiLevel: [{ required: true, message: '请选择权限级别', trigger: 'change' }],
        authType: [{ required: true, message: '请选择认证方式', trigger: 'change' }],
      };
    }
    flowDialog.value = true;
    nextTick(() => {
      // 由于valid是在渲染后才验证，所以这里先把清空valid拿出任务流，后续有更好的方式再修改
      setTimeout(() => {
        flowFormData.proposal = '';
        flowFormData.categoryId = '';
        flowFormData.hubGroupId = '';
        flowFormData.authType = '';
        flowFormData.dateRange = [
          row.paramMap.startTime + ' 00:00:00',
          row.paramMap.endTime + ' 00:00:00',
        ];
        proxy.$refs.flowFrom.clearValidate();
      });
    });
  };
  //   // 删除列
  //   const deleteProcessInstances = async (row) => {
  //     const reqData = {
  //       processInstanceId: row.processInstanceId,
  //     };
  //     const res = await deleteProcessInstance(reqData);
  //     console.log(res);
  //     if (res.ok) {
  //       ElMessage.success(res.data || '删除成功');
  //     }
  //     handleQuery();
  //   };
  const setFlowStatus = (type) => {
    flowStatus.value = type;
  };

  const route = useRoute();

  onMounted(() => {
    handleQuery();

    const query = route.query;

    if (isNotBlank(query.taskId)) {
      // 跳转过来的
      deal({ taskId: query.taskId });
    }
    getGroupsTree();
    getCategory();
  });

  const searchForm = reactive({
    name: '',
    status: 2,
    instanceName: '',
    applyUser: '',
  });
  //   const getList = () => {
  //     const query = {
  //       ...searchForm,
  //       workspaceId: workspaceId.value,
  //       pageNum: queryParams.value.pageNum,
  //       pageSize: queryParams.value.pageSize,
  //     };
  //   };
  watch(workspaceId, (val) => {
    // getGroupList();
  });

  const handleCallback = () => {
    showPage.value = false;
    choseRow = {};
    handleQuery();
  };
</script>

<template>
  <div v-show="!showPage" class="app-container">
    <!-- <div class="data-type-box">
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio-button label="1">我的申请</el-radio-button>
        <el-radio-button label="2">我的审批</el-radio-button>
      </el-radio-group>
    </div> -->
    <el-form ref="" v-model="searchForm" label-position="right" inline label-width="auto">
      <el-form-item label="审批流名称" prop="instanceName">
        <el-input
          v-model="searchForm.instanceName"
          placeholder="请输入审批流名称"
          style="width: 250px"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="申请人" prop="applyUser">
        <el-input
          v-model="searchForm.applyUser"
          placeholder="请输入申请人"
          style="width: 250px"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="审批状态" prop="status">
        <el-select v-model="searchForm.status" placeholder="请选择" style="width: 250px">
          <el-option
            v-for="(option, index) in statusOptions"
            :key="index"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="roleName">
        <el-button type="primary" icon="Search" :disabled="false" @click="handleQuery">
          <!-- @keyup.enter="handleQuery" -->
          查询
        </el-button>

        <el-button type="primary" icon="Search" :disabled="false" @click="reset">
          <!-- @keyup.enter="handleQuery" -->
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table ref="dataTableRef" :data="roleList" highlight-current-row>
      <el-table-column label="规则ID" prop="id" width="200" />
      <el-table-column label="审批流名称" prop="name" width="200" />
      <!-- <el-table-column -->
      <!-- label="编码" -->
      <!-- prop="processInstanceBizCode" -->
      <!-- :width="processInstanceBizCodeWidth" -->
      <!-- > -->
      <!-- <template #default="scope"> -->
      <!-- <el-text> -->
      <!-- <el-icon @click="copy(scope.row.processInstanceBizCode)"> -->
      <!-- <DocumentCopy /> -->
      <!-- </el-icon> -->
      <!-- {{ scope.row.processInstanceBizCode }} -->
      <!-- </el-text> -->
      <!-- </template> -->
      <!-- </el-table-column> -->
      <el-table-column label="关联" prop="apiname" />
      <el-table-column label="申请人" prop="username" />
      <el-table-column label="审批人" prop="taskAssignShow" />
      <el-table-column label="审批时间" prop="endTime" width="180" />
      <el-table-column label="申请时间" prop="createTime" width="180" />
      <el-table-column label="状态" prop="showStatus" width="100">
        <template #default="scope">
          <span
            :class="`status-span status-span-${scope.row.status} ${scope.row.status === 2 ? 'result-' + scope.row.result : ''}`"
            >{{ scope.row.showStatus }}</span
          >
        </template>
      </el-table-column>
      <!-- <el-table-column label="任务时间" prop="taskCreateTime" width="180" /> -->

      <el-table-column fixed="right" width="100" label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" link @click="deal(scope.row)">
            <el-icon>
              <Edit />
            </el-icon>
            详情
          </el-button>
          <!-- <el-button type="primary" size="small" link @click="deleteProcessInstances(scope.row)">
            <el-icon>
              <Delete />
            </el-icon>
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
    <el-dialog
      v-if="flowDialog"
      v-model="flowDialog"
      :title="flowDialogTitle"
      width="30%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="flowFrom" :model="flowFormData" :rules="flowRules">
        <el-form-item label="使用时间" prop="dateRange">
          <el-date-picker
            v-model="flowFormData.dateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disablesDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见" prop="proposal">
          <el-input
            v-model="flowFormData.proposal"
            placeholder="请输入审批意见"
            type="textarea"
            style="width: 250px"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-form-item>
        <!-- 只能上家待审批，并且审批到最后一个阶段展示-->
        <el-form-item
          v-if="
            choseRow.flowType == 'api_online_flow' && flowDialogType === 2 && flowStatus === '2'
          "
          label="接口分组"
          prop="hubGroupId"
        >
          <el-tree-select
            v-model="flowFormData.hubGroupId"
            :data="groupTreeData"
            :render-after-expand="false"
            check-strictly
          ></el-tree-select>
        </el-form-item>
        <el-form-item
          v-if="
            choseRow.flowType == 'api_online_flow' && flowDialogType === 2 && flowStatus === '2'
          "
          label="接口主题"
          prop="categoryId"
        >
          <el-select v-model="flowFormData.categoryId" placeholder="请选择类型" clearable>
            <el-option
              v-for="option in categoryList"
              :key="option.categoryId"
              :label="option.categoryName"
              :value="option.categoryId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            choseRow.flowType == 'api_online_flow' && flowDialogType === 2 && flowStatus === '2'
          "
          label="权限级别"
          prop="apiLevel"
        >
          <!-- <el-radio v-model="flowFormData.apiLevel" label="public">无条件开放</el-radio> -->
          <el-radio v-model="flowFormData.apiLevel" label="condition">有条件开放</el-radio>
        </el-form-item>
        <el-form-item
          v-if="
            choseRow.flowType == 'api_online_flow' && flowDialogType === 2 && flowStatus === '2'
          "
          label="认证方式"
          prop="authType"
        >
          <el-select v-model="flowFormData.authType" placeholder="请选择类型" clearable>
            <el-option
              v-for="option in optionsForAuthType"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="flowDialogCommit">{{
            flowDialogType === 2 ? '同意' : '驳回'
          }}</el-button>
          <el-button @click="closeFlowDialog">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <task-handle ref="taskHandler" @task-submit-event="handleQuery"></task-handle>
  <applyApprovalDetail
    v-if="showPage"
    :page-row="choseRow"
    :would-revoke="dataStatus"
    detail-type="2"
    @callback="handleCallback"
    @deel-flow="showFlowDialog"
    @set-flow-status="setFlowStatus"
  />
</template>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    padding: 20px;
    background: $--base-color-bg;
    // margin-top: 20px;
    .data-type-box {
      width: 400px;
      margin: 20px auto;
    }
    .status-span {
      color: $--base-color-text2;
      &.status-span-1 {
        color: $--base-color-primary;
      }
      &.status-span-3 {
        color: $--base-color-text2;
      }
      &.result-1 {
        color: $--base-color-green;
      }
      &.result-2 {
        color: $--base-color-red;
      }
    }
  }
</style>
