<template>
  <div ref="chart" :style="{ width: '100%', height: '360px' }"></div>
</template>

<script>
  import * as echarts from 'echarts';

  export default defineComponent({
    name: 'Line<PERSON><PERSON>',
    props: {
      options: {
        type: Object,
        required: true,
      },
    },
    setup(props) {
      const chart = ref(null);
      const chartInstance = ref(null);

      const initChart = () => {
        if (chart.value) {
          chartInstance.value = echarts.init(chart.value);
          chartInstance.value.setOption(props.options);
        }
      };

      onMounted(() => {
        initChart();
        window.addEventListener('resize', () => {
          chartInstance.value?.resize();
        });
      });

      watch(
        () => props.options,
        (newOptions) => {
          if (chartInstance.value) {
            chartInstance.value.setOption(newOptions);
          }
        },
        { deep: true },
      );

      return {
        chart,
      };
    },
  });
</script>

<style scoped lang="scss"></style>
