import * as util from "../../../utils/objutil.js";


export function getValidateRule(item){


	var checkConfig = (rule: any, value: any, callback: any) => {

		if (item.required) {
			if (value==undefined||value.length==0) {
				return callback(new Error("请选择" + item.name))
			}
		}
		if (value==undefined) {
			return callback()
		}


		return callback()


	}
	let ruleArray = [{
		validator: checkConfig, trigger: 'change'
	}];
	if (item.required) {
		ruleArray.push(
			{required: true, message: '请选择' + item.name, trigger: 'change'}
		)
	}
	return ruleArray



}
