<template>
  <div class="login" :style="loginStyle">
    <div class="login-form">
      <div style="margin-bottom: 40px">
        <!-- logo -->
        <!-- width: 66px; 为了让logo以高度适应，现在宽度自适应 -->
        <span class="mb10"><img style="height: 40px" :src="src" alt="" /></span>
        <div class="title">{{ productName }}</div>
        <!-- <div class="line"></div> -->
      </div>
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules">
        <div>
          <div style="margin-bottom: 40px">
            <div style="font-size: 22px; font-weight: 300">HELLO!</div>
            <div style="margin-top: 3px; font-size: 18px; font-weight: 300">欢迎您登录系统</div>
          </div>
          <!-- <div class="" style="margin-bottom: 40px"> -->
          <el-form-item class="user-info-box" prop="username" style="margin-bottom: 40px">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              placeholder="用户账号"
            >
              <template #prefix>
                <!-- <svg-icon icon-class="user" class="el-input__icon input-icon" /> -->
                <IconAdmin />
              </template>
            </el-input>
          </el-form-item>
          <!-- </div> -->
          <div>
            <el-form-item class="user-info-box" prop="password" style="margin-bottom: 40px">
              <el-input
                v-model="loginForm.password"
                :type="showPassword ? 'input' : 'password'"
                size="large"
                auto-complete="off"
                placeholder="密码"
                class="password-box"
                @keyup.enter="handleLogin"
              >
                <template #append>
                  <div class="icon-box" @click="changePasswordInput">
                    <el-icon v-if="!showPassword" class="password-icon"><Hide /></el-icon>
                    <el-icon v-else class="password-icon"><View /></el-icon>
                  </div>
                </template>
                <template #prefix>
                  <IconKey />
                  <!-- <svg-icon icon-class="password" class="el-input__icon input-icon"/> -->
                </template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item
            v-if="captchaEnabled"
            class="user-info-box"
            prop="code"
            style="margin-bottom: 40px"
          >
            <el-input
              v-model="loginForm.code"
              autocomplete="off"
              size="large"
              auto-complete="off"
              placeholder="验证码"
              style="width: calc(100% - 157px)"
              @keyup.enter="handleLogin"
            >
              <!-- <template #prefix
                ><svg-icon icon-class="validCode" class="el-input__icon input-icon"
              /></template> -->
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" class="login-code-img" @click="getCode" />
            </div>
          </el-form-item>
          <div class="bottom-box">
            <div class="remember-check-box">
              <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px"
                >记住密码</el-checkbox
              >
            </div>

            <div v-if="!loading" class="btnLogin">
              <el-button
                :loading="loading"
                size="large"
                type="primary"
                class="myBtn"
                @click.prevent="handleLogin"
              >
                <span>登 录</span>
              </el-button>
            </div>
            <div v-else class="btnLogin">
              <el-button class="myBtn" :loading="loading" size="large" type="primary">
                <span>登 录 中...</span>
              </el-button>
            </div>
            <div v-if="register" style="float: right">
              <router-link class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!-- <div id="circle-org" />
    <div id="circle-blue" /> -->

    <!--  底部  -->
    <div class="el-login-footer">
      <span
        >Copyright © {{ new Date().getFullYear() }} {{ companyName }} All Rights Reserved.</span
      >
    </div>
    <!-- <canvas id="canvas3d" /> -->
  </div>
</template>

<script setup>
  import { ref, nextTick } from 'vue';
  import { getCodeImg, getCompanyInfo } from '@/api/login';
  import useUserStore from '@/store/modules/user';
  import { decrypt, encrypt } from '@/utils/jsencrypt';
  import { IconKey, IconAdmin } from '@arco-iconbox/vue-update-color-icon';
  import backgroundImgLogin from '@/assets/images/bg.jpg';
  import logo from '@/assets/logo/logo.png';
  import Cookies from 'js-cookie';

  const loginStyle = reactive({
    backgroundImage: `url(${backgroundImgLogin})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  });

  document.title = '登录';
  const url = new URL(window.location.origin);

  const productName = ref('productName');
  const companyName = ref('companyName');
  const showPassword = ref(false);
  const getCompanyInfoUtil = async () => {
    const res = await getCompanyInfo();
    productName.value = res?.data?.productName;
    companyName.value = res?.data?.companyName;

    nextTick(() => {
      // 设置登录logo
      src.value = res?.data?.logoOfSystem ? url + '/xugurtp-file/' + res.data.logoOfSystem : logo;
      // 设置登录背景图
      loginStyle.backgroundImage = res?.data?.backgroundImgLogin
        ? `url(${url + '/xugurtp-file/' + res.data.backgroundImgLogin})`
        : `url(${backgroundImgLogin})`;
      // 设置浏览器的 favicon
      const faviconUrl = res.data.icon;
      setFavicon(faviconUrl);
      sessionStorage.setItem('CompanyInfo', JSON.stringify(res.data));
    });
  };

  function setFavicon(url) {
    // 添加时间戳以防止缓存
    const timestamp = new Date().getTime();
    const newUrl = `${url}?v=${timestamp}`;

    // 查找现有的 favicon 链接标签
    let link = document.querySelector("link[rel*='icon']");

    if (link) {
      // 如果存在，则更新 href
      link.href = newUrl;
    } else {
      // 如果不存在，创建新的 favicon 链接标签
      link = document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = newUrl;
      document.head.appendChild(link);
    }
  }

  onMounted(async () => {
    getCompanyInfoUtil();
    // const canvas = document.getElementById('canvas3d');
    // const app = new Application(canvas);
    // let scene = localStorage.getItem('scene');
    // if (!scene) {
    //   const sceneUrl = new URL('../../src/assets/images/scene.code', import.meta.url);
    //   const response = await fetch(sceneUrl);
    //   scene = await response.text();
    //   localStorage.setItem('scene', scene);
    // }
    // app.load(scene);
  });

  const userStore = useUserStore();
  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const loginForm = ref({
    username: '',
    password: '',
    rememberMe: false,
    code: '',
    uuid: '',
  });

  const loginRules = {
    username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
    password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
    code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
  };

  const codeUrl = ref('');
  const loading = ref(false);
  // 验证码开关
  const captchaEnabled = ref(true);
  // 注册开关
  const register = ref(false);
  const redirect = ref(undefined);
  // logo地址由后端返回
  const src = ref();
  function handleLogin() {
    proxy.$refs.loginRef.validate((valid) => {
      if (valid) {
        console.log('loginForm.value', loginForm.value);
        loading.value = true;
        // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          Cookies.set('username', loginForm.value.username, { expires: 30 });
          Cookies.set('password', encrypt(loginForm.value.password), {
            expires: 30,
          });
          Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 });
        } else {
          // 否则移除
          Cookies.remove('username');
          Cookies.remove('password');
          Cookies.remove('rememberMe');
        }
        // 调用 action 的登录方法
        userStore
          .login(loginForm.value)
          .then(() => {
            router.push({ path: redirect.value || '/' });
          })
          .catch(() => {
            loading.value = false;
            // 重新获取验证码
            if (captchaEnabled.value) {
              getCode();
            }
          });
      }
    });
  }

  function getCode() {
    getCodeImg().then((res) => {
      captchaEnabled.value = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
      if (captchaEnabled.value) {
        codeUrl.value = 'data:image/gif;base64,' + res.data.img;
        loginForm.value.uuid = res.data.uuid;
      }
    });
  }

  function getCookie() {
    const username = Cookies.get('username');
    const password = Cookies.get('password');
    const rememberMe = Cookies.get('rememberMe');
    loginForm.value = {
      username: username === undefined ? loginForm.value.username : username,
      password: password === undefined ? loginForm.value.password : decrypt(password),
      rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
    };
  }
  // 改变密码框显示
  const changePasswordInput = () => {
    showPassword.value = !showPassword.value;
  };

  getCode();

  getCookie();
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    background-image: url('../assets/images/bg.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    // justify-content: center;
    // align-items: center;
    height: 100%;
    position: relative;
    // background-image: url("../assets/images/login-background.jpg");
    // background-size: cover;
    // background-color: #abc6f8;
    // background-image:
    //   radial-gradient(closest-side, rgb(181, 245, 175), rgba(235, 105, 78, 0)),
    //   // radial-gradient(closest-side, rgb(243, 203, 222), rgba(243, 11, 164, 0)),
    //   radial-gradient(closest-side, rgb(206, 230, 241), rgba(254, 234, 131, 0)),
    //   radial-gradient(closest-side, rgb(216, 217, 247), rgba(170, 142, 245, 0)),
    //   radial-gradient(closest-side, rgb(217, 239, 245), rgba(248, 192, 147, 0));
    // background-size:
    //   130vmax 130vmax,
    //   80vmax 80vmax,
    //   90vmax 90vmax,
    //   110vmax 110vmax,
    //   90vmax 90vmax;
    // background-position:
    //   -80vmax -80vmax,
    //   60vmax -30vmax,
    //   10vmax 10vmax,
    //   -30vmax -10vmax,
    //   50vmax 50vmax;
    // background-repeat: no-repeat;
    // // background-color: #293245;
    // animation: 10s movement linear infinite;

    .login-form {
      //   margin: 135px 1197px 135px 127px;
      // box-sizing: border-box;
      padding: 70px 60px;
      border-radius: 20px;
      border: 2px solid #fff;
      background: rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(0.5px);
      height: 782px;
      width: 568px;
      box-sizing: border-box;
      border-image-source: radial-gradient(48.9% 48.9% at 50% 33.29%, #ffffff 0%, #ffffff4d 100%);
      box-sizing: border-box;
      background: #ffffff66;
      backdrop-filter: blur(1px);
      position: absolute;
      top: calc(50% - 391px);
      left: 130px;

      .login-code-img {
        width: 100%;
        height: 56px;
        border-radius: 8px;
      }

      .login-code {
        width: 137px;
        height: 56px;
        margin-left: 20px;
        border-radius: 8px;

        img {
          cursor: pointer;
          vertical-align: middle;
        }
      }

      .title {
        white-space: nowrap;
        color: #000;
        font-size: 33px;
        font-weight: 400;
      }

      .line {
        margin-top: 10px;
        width: 152px;
        height: 6px;
        border-radius: 10px;
        background: #000;
      }

      .el-input {
        height: 58px;

        input {
          height: 58px;
        }
      }

      .input-icon {
        height: 39px;
        width: 14px;
        margin-left: 0px;
        margin-right: 14px;
      }

      // :deep .el-checkbox__inner {
      //   border-color: #fff;
      //   background: none;
      // }

      :deep .el-input__wrapper {
        // border-color: #fff;
        // background: none;
      }

      .btnLogin {
        text-align: right;
        .myBtn {
          width: 120px;
          display: inline-flex;
          flex-shrink: 0;
          padding: 14px 20px;
          border-radius: 10px;
          background: #1269ff;
          color: #fff;
          box-shadow: 0px 8px 20px #384aff40;
          &:hover {
            background: #1269ff;
            border-color: none;
          }
          & > span {
            font-size: 20px;
          }
        }
      }

      :deep .el-input__wrapper.is-focus {
        border-radius: 10px;
        border: 1px solid #1269ff;
        background: #fff;
      }
    }

    .el-login-footer {
      height: 40px;
      line-height: 40px;
      position: fixed;
      bottom: 0;
      width: 100vw;
      text-align: center;
      color: #000000;
      font-family: Arial;
      font-size: 12px;
      letter-spacing: 1px;
    }
  }

  // .title {
  //   margin: 0px auto 30px auto;
  //   text-align: center;
  //   font-weight: 900;
  //   color: #000000;

  //   font-family: 'Microsoft YaHei';
  //   font-size: 20px;
  //   letter-spacing: 1px;
  //   text-shadow: 0px 0px 10px #ffffff;
  // }

  // .login-form {
  //   border-radius: 6px;
  //   // background: hsla(0, 0%, 100%, 0.925);
  //   background: #323c3c;

  //   // box-shadow: #f3f4f7 10px 12px 20px 4px;
  //   width: 400px;
  //   padding: 25px 25px 5px 25px;

  //   height: 100%;
  //   background-color: rgba(255, 255, 255, 0.05);
  //   backdrop-filter: blur(30px);
  //   -webkit-backdrop-filter: blur(30px);
  //   border: 0.909091px solid rgba(255, 255, 255, 0.18);
  //   box-shadow: rgba(142, 142, 142, 0.19) 0px 6px 15px 0px;
  //   -webkit-box-shadow: rgba(142, 142, 142, 0.19) 0px 6px 15px 0px;
  //   border-radius: 9px;
  //   -webkit-border-radius: 9px;
  //   color: rgba(255, 255, 255, 0.15);

  //   display: flex;
  //   flex-direction: column;
  //   justify-content: center;

  //   margin-left: 0%;
  //   z-index: 1;

  //   .el-input {
  //     height: 40px;

  //     input {
  //       height: 40px;
  //     }
  //   }

  //   .input-icon {
  //     height: 39px;
  //     width: 14px;
  //     margin-left: 0px;
  //   }

  //   :deep .el-checkbox__inner {
  //     border-color: #fff;
  //     background: none;
  //   }

  //   :deep .el-input__wrapper {
  //     border-color: #fff;
  //     background: none;
  //   }

  //   el-textarea__inner,
  //   .el-input__inner {
  //     background: transparent !important;
  //   }

  //   // :deep el-button {}
  // }

  // .login-tip {
  //   font-size: 13px;
  //   text-align: center;
  //   color: #bfbfbf;
  // }

  // .login-code {
  //   width: 33%;
  //   height: 40px;
  //   float: right;

  //   img {
  //     cursor: pointer;
  //     vertical-align: middle;
  //   }
  // }

  // .el-login-footer {
  //   height: 40px;
  //   line-height: 40px;
  //   position: fixed;
  //   bottom: 0;
  //   width: 100vw;
  //   text-align: center;
  //   color: #000000;
  //   font-family: Arial;
  //   font-size: 12px;
  //   letter-spacing: 1px;
  // }

  // .login-code-img {
  //   height: 40px;
  //   padding-left: 12px;
  // }

  // .btnLogin {
  //   background: linear-gradient(135deg, #ffb566, #f67);
  //   border: none;
  //   border-radius: 20px;
  //   color: #fff;
  //   font-size: 16px;
  //   font-weight: 600;
  //   height: 40px;
  //   line-height: 40px;
  //   margin-top: 20px;
  //   outline: none;
  //   width: 100%;
  //   cursor: pointer;
  //   transition: all 0.3s ease-in-out;

  //   &:hover {
  //     background: linear-gradient(135deg, #ffb566, #f67);
  //     box-shadow: 0px 0px 10px #ffb566;
  //   }
  // }

  // #canvas3d {
  //   position: absolute;
  //   top: 0;
  //   left: -10%;
  //   width: 100vw;
  //   height: 100vh;
  // }

  // #circle-org {
  //   // 距离屏幕的位置
  //   position: relative;
  //   width: 300px;
  //   height: 300px;
  //   border-radius: 50%;
  //   margin-top: 0%;
  //   margin-left: -20%;
  //   background: linear-gradient(135deg, #ffb566, #f67);
  //   animation: bounce-down 2s linear infinite;
  // }

  // #circle-blue {
  //   position: relative;
  //   width: 175px;
  //   height: 175px;
  //   border-radius: 50%;
  //   margin-top: 80vh;
  //   margin-left: 70%;
  //   background: linear-gradient(135deg, #de82ca, #259fac);
  //   animation: bounce-down 5s linear infinite;
  // }

  // @-webkit-keyframes bounce-down {
  //   25% {
  //     -webkit-transform: translateY(-20px);
  //   }

  //   100%,
  //   50% {
  //     -webkit-transform: translateY(0);
  //   }

  //   75% {
  //     -webkit-transform: translateY(20px);
  //   }
  // }
  :deep.el-button:focus,
  .el-button:hover {
    border-color: none;
  }
  .user-info-box {
    height: 60px;
    gap: 10px;
    flex-shrink: 0;
    // padding: 20px 24px;

    .el-input {
      height: 58px;
      border-radius: 10px;
      input {
        border-radius: 10px;
        border: 2px solid #ffffff;
        height: 58px;
        background: #ffffff;
      }
    }

    ::v-deep .el-input__wrapper {
      height: 58px;
      line-height: 58px;
      border-radius: 10px;
      border: none;
      box-shadow: none;
      .el-input__inner {
        background: #ffffff;
      }
    }
    ::v-deep .password-box {
      .el-input__wrapper {
        border-right: none;
        border-radius: 10px 0px 0px 10px;
        padding-right: 0;
      }
      .el-input-group__append {
        border: none;
        box-shadow: none;
        border-radius: 0px 10px 10px 0px;
        background-color: #ffffff;
        cursor: pointer;
        .icon-box {
          width: 100%;
          height: 100%;
          line-height: 58px;
        }
      }
    }
  }
  .bottom-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-button {
      height: 56px;
      line-height: 56px;
    }
    .remember-check-box {
      height: 12px;
      ::v-deep .el-checkbox__input {
        &.is-checked .el-checkbox__inner {
          background-color: #1269ff;
          border-color: #1269ff;
        }
        &.is-checked + .el-checkbox__label {
          color: #1269ff;
        }
      }
    }
  }
</style>
