<template>
  <el-form
    ref="SQLSearchForm"
    class="SQL-search-form"
    :model="searchForm"
    label-position="left"
    inline
    label-width="auto"
  >
    <!-- 临时修改数据不变的问题 -->
    <span style="display: none">{{ searchForm }}</span>

    <el-form-item
      v-for="(item, index) in formItems"
      :key="index"
      :label="item.label"
      :prop="item.props"
    >
      <el-input
        v-if="item.type === 'input'"
        v-model="searchForm[item.prop]"
        placeholder="请输入名称"
        v-bind="item.props"
        v-on="item.listeners"
      />
      <el-select
        v-else-if="item.type === 'select'"
        v-model="searchForm[item.prop]"
        v-bind="item.props"
        :placeholder="item.placeholder"
        v-on="item.listeners"
      >
        <el-option
          v-for="option in item.options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        >
        </el-option>
      </el-select>
      <el-select
        v-else-if="item.type === 'selectGroup'"
        v-model="searchForm[item.prop]"
        :placeholder="item.placeholder"
        v-bind="item.props"
        v-on="item.listeners"
      >
        <el-option-group v-for="group in item.options" :key="group.label" :label="group.label">
          <el-option
            v-for="item in group.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-option-group>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import useSQLSearchFormService from '@/views/APIService/components/SQLSearchForm/useSQLSearchFormService';
  import { defineProps } from 'vue';

  const props = defineProps({
    searchForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formItems: {
      type: Array,
      default: () => {
        return {};
      },
    },
  });

  //   const searchForm = props.searchForm;
  //   const formItems = props.formItems;
  const { searchForm, formItems } = toRefs(props);
  console.log(searchForm.value, formItems.value, 331);

  const { SQLSearchForm } = useSQLSearchFormService();
</script>
<style lang="scss" scoped>
  .SQL-search-form {
    // width: 100%;
    // height: 66px;
    padding: 10px 0px;
    ::v-deep .el-form-item {
      margin-right: 8px !important;
      .el-form-item__content {
        width: 200px;
      }
      .el-form-item__label-wrap {
        margin: 0 !important;
      }
    }
  }
</style>
