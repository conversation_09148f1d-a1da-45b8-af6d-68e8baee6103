<template>
  <section class="Navtoos-box">
    <div class="nav-left-box">
      <b>节点</b>
      <div class="left-size-chose-box">
        <el-tooltip class="box-item" effect="light" content="小" placement="bottom-end">
          <!-- <el-tag effect="plain" size="mini" @click="setDrawSize(0.5)"> 小</el-tag> -->
          <div class="size-btn" :class="{ 'active-btn': nodeSize == 0.5 }" @click="setDrawSize(0.5)"
            >小</div
          >
        </el-tooltip>
        <el-tooltip class="box-item" effect="light" content="中" placement="bottom-end">
          <!-- <el-tag effect="plain" size="mini" @click="setDrawSize(1)">中</el-tag> -->
          <div class="size-btn" :class="{ 'active-btn': nodeSize == 1 }" @click="setDrawSize(1)"
            >中</div
          >
        </el-tooltip>
        <el-tooltip class="box-item" effect="light" content="大" placement="bottom-end">
          <!-- <el-tag effect="plain" size="mini" @click="setDrawSize(1.5)">大</el-tag> -->
          <div class="size-btn" :class="{ 'active-btn': nodeSize == 1.5 }" @click="setDrawSize(1.5)"
            >大</div
          >
        </el-tooltip>
        <!-- <el-radio-group v-model="nodeSize" @change="setDrawSize">
          <el-radio-button label="0.5">小</el-radio-button>
          <el-radio-button label="1">中</el-radio-button>
          <el-radio-button label="1.5">大</el-radio-button>
        </el-radio-group> -->
      </div>
      <div style="margin: 0px 20px">
        <el-tooltip class="box-item" effect="light" content="放大" placement="bottom-end">
          <el-icon>
            <CirclePlus @click="addDraw" />
          </el-icon>
        </el-tooltip>
        <el-tooltip class="box-item" effect="light" content="缩小" placement="bottom-end">
          <el-icon>
            <Minus @click="subDraw" />
          </el-icon>
        </el-tooltip>
        <el-tooltip
          v-if="showFull"
          class="box-item"
          effect="light"
          content="全屏"
          placement="bottom-end"
        >
          <el-icon>
            <FullScreen @click="FullCilck" />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="left-lock-box">
        <el-tooltip
          v-if="!CanvasActions"
          class="box-item"
          effect="light"
          content="锁定编辑"
          placement="bottom-end"
        >
          <el-button
            v-show="isShowButton"
            type="text"
            effect="light"
            icon="Lock"
            plain
            @click="toggleCanvasActions"
          >
            锁定编辑
          </el-button>
        </el-tooltip>
        <el-tooltip
          v-if="CanvasActions"
          class="box-item"
          effect="light"
          content="退出编辑"
          placement="bottom-end"
        >
          <el-button
            v-show="isShowButton"
            type="text"
            effect="light"
            icon="Unlock"
            plain
            @click="toggleCanvasActions"
          >
            退出编辑
          </el-button>
        </el-tooltip>
        <b>
          <!-- {{ CanvasActions ? '退出编辑' : '锁定编辑' }} -->
        </b>
      </div>
    </div>
    <div class="nav-right-box">
      <div>
        <el-button
          v-show="isShowButton"
          :disabled="!CanvasActions"
          style="font-size: 14px"
          icon="Warning"
          @click="openWarnDialog"
          type="light"
          >告警设置</el-button
        >
        <el-tooltip class="box-item" effect="light" content="保存" placement="bottom-end">
          <el-button
            type="text"
            :disabled="!CanvasActions"
            class="icon-btn save-btn"
            plain
            @click="saveDraw"
          >
            <IconSave />
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="box-item"
          effect="light"
          :content="runStatus ? '停止' : '运行'"
          placement="bottom-end"
        >
          <!-- <el-button
            v-show="isShowButton"
            type="text"
            effect="light"
            :icon="runStatus ? 'VideoPause' : 'VideoPlay'"
            :disabled="CanvasActions"
            @click="testRunDraw"
          /> -->
          <el-button
            v-show="isShowButton"
            type="text"
            :disabled="CanvasActions"
            class="icon-btn save-btn"
            plain
            @click="testRunDraw"
          >
            <IconOperation v-if="!runStatus" />
            <IconPause v-else />
          </el-button>
        </el-tooltip>

        <el-tooltip class="box-item" effect="light" content="提交" placement="bottom-end">
          <el-button
            v-show="isShowButton"
            type="primary"
            effect="light"
            class="icon-btn submit-btn"
            :disabled="!CanvasActions"
            @click="putDraw"
          >
            提交 🚀
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="box-item"
          effect="light"
          :content="nodeLogShow ? '关闭' : '查看最新运行实例'"
          placement="bottom-end"
        >
          <el-button
            v-show="isShowButton"
            type="primary"
            effect="light"
            class="icon-btn btn-visible"
            :disabled="CanvasActions"
            @click="viewOrCloseLog"
          >
            <IconUnvisible v-if="nodeLogShow" />
            <IconVisible v-else />
          </el-button>
        </el-tooltip>

        <!-- <el-tooltip class="box-item" effect="light" content="提交并允许他人编辑" placement="bottom-end"> -->
        <!-- <el-button type="text" effect="light" icon="Key" @click="putDrawAndEdit" :disabled="!CanvasActions" /> -->
        <!-- </el-tooltip> -->
        <!-- <el-tooltip class="box-item" effect="light" content="收藏" placement="bottom-end"> -->
        <!-- <!~~ 收藏 ~~> -->
        <!-- <el-icon v-show="isShowButton"> -->
        <!-- <Star @click="starDraw" /> -->
        <!-- </el-icon> -->
        <!-- </el-tooltip> -->
      </div>
    </div>
  </section>
</template>

<script setup>
  import { nextTick } from 'vue';

  import {
    IconSave,
    IconOperation,
    IconVisible,
    IconUnvisible,
  } from '@arco-iconbox/vue-update-color-icon';
  import { IconPause } from '@arco-iconbox/vue-update-line-icon';

  // import { DagreLayout, GridLayout } from '@antv/layout'
  const props = defineProps({
    graph: {
      type: Object,
    },
    model: {
      type: Object,
      default: () => {},
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
    isShowButton: {
      type: Boolean,
      default: true,
    },
    runStatus: {
      type: Boolean,
      default: false,
    },
    showFull: {
      type: Boolean,
      default: true,
    },
    nodeLogShow: {
      type: Boolean,
      default: false,
    },
  });

  const { graph, model, CanvasActions, isShowButton, runStatus } = toRefs(props);
  const emit = defineEmits();
  const nodeSize = ref(1);

  const viewOrCloseLog = () => {
    emit('viewOrCloseLog');
  };

  // const CanvasActions = ref(false)
  const isFull = ref(false);
  // 画布缩放
  function addDraw() {
    graph.value.zoom(0.2); // 将画布缩放级别增加 0.2（默认为 1）
  }
  function subDraw() {
    graph.value.zoom(-0.2); // 将画布缩放级别增加 0.2（默认为 1）
  }
  // 画布位置还原
  function adaptationDraw() {
    graph.value.zoomToFit();
  }
  // 试运行
  function testRunDraw() {
    if (runStatus.value) {
      // 判断运行状态
      emit('stopDraw');
    } else {
      emit('runDraw');
    }
    // alert('试运行')
  }

  // 试运行
  function testStopDraw() {
    emit('stopDraw');
    // alert('试运行')
  }
  // 保存
  function saveDraw() {
    emit('saveWorkFlowUtil');
    // alert('保存')
  }
  // 提交
  function putDraw() {
    emit('putDraw');
    // alert('提交')
  }

  //  提交并允许他人编辑
  function putDrawAndEdit() {
    emit('putDraw');
    // alert('提交并允许他人编辑')
  }

  // 收藏
  function starDraw() {
    // alert('收藏')
    emit('starDraw', true);
  }
  // 删除选中
  function removeDraw() {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.removeCells(cells);
    }
  }

  const openWarnDialog = () => emit('openWarnDialog');

  //  设置为最小
  function setDrawSize(v) {
    nodeSize.value = v;
    graph.value.zoomTo(v);
    // 画布视口回到中心
    graph.value.centerContent();
  }

  // 锁定画布
  function toggleCanvasActions() {
    // CanvasActions.value = !CanvasActions.value
    emit('toggleCanvasActions', !CanvasActions.value);
  }
  // 切换
  function upDataCanvasActions(data) {
    console.log(data);
    CanvasActions.value = data;
  }
  function FullCilck() {
    isFull.value = !isFull.value;
    emit('FullCilck', isFull.value);
  }
  // function toGridLayout() {

  //     const gridLayout = new GridLayout({
  //         type: 'grid',
  //         width: 600,
  //         height: 400,
  //         center: [300, 200],
  //         rows: 4,
  //         cols: 4,
  //     })
  //     console.log('model.value', model.value)
  //     const newModel = gridLayout.layout(model.value)
  //     console.log('newModel', newModel)
  //     graph.value.fromJSON(newModel)
  //     console.log('graph.value.getNodes()', graph.value.getNodes())

  //     // graph.value.centerContent();
  // }

  // 在组件挂载后执行
  onMounted(() => {
    watch(isShowButton, (val) => {
      nextTick(() => {
        isShowButton.value = val;
      });
    });
  });
</script>

<style lang="scss" scoped>
  .Navtoos-box {
    // display: grid;
    // grid-template-columns: auto 1fr 0fr 0.75fr;
    // justify-content: space-between;
    // padding: 0 10px;
    // height: 40px;
    background-color: #fff;
    // border-bottom: 1px solid #ebeef5;
    height: 66px;
    box-shadow: 0px 4px 12px #0166f314;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .nav-left-box {
      padding: 20px;
      margin: 0;
      .left-size-chose-box {
        margin: 0px 20px;
        height: 28px;
        background: linear-gradient(#fbfcff, #fbfcff), #f2f4f8;
        border-radius: 4px;
        padding: 4px;
        position: relative;
        .size-btn {
          font-size: 12px;
          line-height: 20px;
          padding: 0px 10px;
          border-radius: 4px;
          font-weight: 400;
          &.active-btn {
            background: #ffffff;
            color: #1269ff;
          }
        }
        &::after {
          content: '';
          width: 2px;
          height: 18px;
          right: -21px;
          top: 5px;
          background: #dce5f5;
          position: absolute;
        }
      }
      .left-lock-box {
        & > .el-button {
          font-size: 14px;
        }
      }
    }
    .nav-right-box {
      .save-btn {
        padding: 0px 8px;
        //     :deep svg {
        //       & > path {
        //         stroke: #ffffff;
        //         &:first-child {
        //           stroke: #1269ff;
        //         }
        //       }
        //     }
      }
      .icon-btn {
        padding: 0px 8px;
        &.is-plain {
          &.is-disabled {
            opacity: 0.6;
            background: #eaf1ff !important;
          }
        }
        :deep svg {
          &.update-line-icon-icon-Pause {
            & > circle {
              stroke: #1269ff;
            }
            & > path {
              stroke: #eaf1ff;
            }
          }
        }
      }
      .submit-btn {
        font-size: 12px;
        background: #1269ff;
        padding: 0px 12px;

        &.is-disabled {
          opacity: 0.6;
          background: #1269ff !important;
        }
      }
      .btn-visible {
        :deep svg {
          & > path {
            fill: #ffffff;
          }
        }
      }
    }

    div {
      margin: 0 10px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #000000;
      font-weight: 600;
      font-size: 19px;

      // 子元素左右间距 10px
      > * {
        margin: 0 5px;
      }

      b {
        font-weight: normal;
        font-size: 13px;
        // left: 5px;
      }

      :deep .el-tag {
        // margin: 0 5px;
        // border: 1px solid #ebeef5;
        // color: #000000;
        // font-weight: 600;
        font-size: 13px;
        cursor: pointer;
        margin-left: -2px;
      }

      :deep .el-button,
      el-button--text {
        font-size: 18px;
      }
    }
  }
</style>
