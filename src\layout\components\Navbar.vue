<template>
  <div class="navbar">
    <div class="left-menu">
      <div class="menu-title">
        <hamburger
          id="hamburger-container"
          :is-active="appStore.sidebar.opened"
          class="hamburger-container"
          @toggle-click="toggleSideBar"
        />
        <breadcrumb
          v-if="!settingsStore.topNav"
          id="breadcrumb-container"
          class="breadcrumb-container"
        />
      </div>
      <div style="margin-left: 24px; font-size: 12px; font-weight: 400; color: #000">
        {{ date }}
        {{ greetings }}
      </div>
    </div>

    <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <HeadTitleBar
          v-show="isBazaar"
          id="header-search"
          class="right-menu-item"
          :pull-down="true"
        />
        <!-- <header-search id="header-search" class="right-menu-item" /> -->

        <!-- <el-icon v-if="isShow" @click="goWarn"> -->
        <div v-if="isShow" class="bell-container">
          <el-tooltip content="消息中心" effect="dark" placement="bottom">
            <!-- <el-button
              v-if="isShow"
              type="text"
              icon="Bell"
              class="bell"
              @click="goWarn"
              style="background-color: #fff;"
            ></el-button> -->
            <div class="message-icon" @click="goWarn"></div>
          </el-tooltip>
        </div>
        <!-- </el-icon> -->
        <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom"> -->
        <!-- <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" /> -->
        <!-- </el-tooltip> -->
        <!--  -->
        <!-- <el-tooltip content="文档地址" effect="dark" placement="bottom"> -->
        <!-- <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" /> -->
        <!-- </el-tooltip> -->
        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->
        <!-- <el-tooltip :content="nickName" effect="dark" placement="bottom">
          <div v-show="showUserDetails" class="userShow">
            <small class="first-small">
              <b> {{ userName }}</b>
            </small>
            <small class="second-small">
              {{ nickName }}
            </small>
          </div>
        </el-tooltip> -->
      </template>
      <div class="avatar-container">
        <div style="height: 40px; margin: 0">
          <el-dropdown
            class="hover-effect"
            trigger="click"
            @command="handleCommand"
            @mouseover="handleMouseOver"
            @mouseleave="handleMouseLeave"
          >
            <div class="avatar-wrapper">
              <div style="width: 40px">
                <!-- <img :src="userStore.avatar" class="user-avatar" /> -->
                <div class="user-avatar">{{ userName?.slice(0, 1) }}</div>
              </div>

              <div style="margin: 0 10px; font-size: 16px; padding-right: 10px">
                <span> {{ userName }}</span>
              </div>
              <div
                ><el-icon><caret-bottom /></el-icon>
              </div>
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/user/profile">
                  <el-dropdown-item>系统配置</el-dropdown-item>
                </router-link>
                <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                  <span>布局设置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getRouters } from '@/api/menu';
  import { ElMessageBox } from 'element-plus';
  import Breadcrumb from '@/components/Breadcrumb';
  import TopNav from '@/components/TopNav';
  import Hamburger from '@/components/Hamburger';
  import useAppStore from '@/store/modules/app';
  import useUserStore from '@/store/modules/user';
  import useSettingsStore from '@/store/modules/settings';
  import { useRouter } from 'vue-router';
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { getInfo } from '@/api/login';
  import HeadTitleBar from '@/components/HeadTitleBar';
  //   import useHeaderStore from '@/store/modules/header';
  import usePermissionStore from '@/store/modules/permission';
  const appStore = useAppStore();
  const userStore = useUserStore();
  const settingsStore = useSettingsStore();
  const permissionStore = usePermissionStore();
  const userName = ref();
  const nickName = ref();
  const showUserDetails = ref(true);
  function toggleSideBar() {
    appStore.toggleSideBar();
  }

  function handleCommand(command) {
    switch (command) {
      case 'setLayout':
        setLayout();
        break;
      case 'logout':
        logout();
        break;
      default:
        break;
    }
  }
  const isShow = ref(true);
  onMounted(() => {
    // getRouters().then((res) => {
    const showNews = permissionStore.sidebarRouters.find((val) => {
      return val.children?.some((i) => i.path === 'noticeNews');
    });
    if (showNews) {
      isShow.value = true;
    } else {
      isShow.value = false;
    }
    // });
    // getInfo().then((res) => {

    userName.value = userStore.user.userName;
    nickName.value = userStore.user.nickName;
    // console.log('res.data.roles 用户账号', res.data.user.userName)
    // console.log('res.data.roles 用户昵称', res.data.user.nickName)
    // });
  });

  function logout() {
    ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        userStore.logOut().then(() => {
          location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index';
        });
      })
      .catch(() => {});
  }

  const emits = defineEmits(['setLayout']);
  function setLayout() {
    emits('setLayout');
  }
  const router = useRouter();
  const goWarn = () => {
    router.push('/noticeNews');
  };
  const handleMouseOver = () => {
    showUserDetails.value = true;
  };
  const handleMouseLeave = () => {
    // showUserDetails.value = false;
    showUserDetails.value = true;
  };
  const date = ref();
  const getDate = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const week = date.getDay();
    const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const res = `${year}.${month}.${day} ${weekDay[week]}`;
    return res;
  };
  nextTick(() => (date.value = getDate()));

  const dateTime = new Date();

  const greetings = computed(() => {
    if (dateTime.getHours() >= 6 && dateTime.getHours() < 8) {
      return '晨起披衣出草堂，轩窗已自喜微凉🌅！';
    } else if (dateTime.getHours() >= 8 && dateTime.getHours() < 12) {
      return '上午好🌞！';
    } else if (dateTime.getHours() >= 12 && dateTime.getHours() < 18) {
      return '下午好☕！';
    } else if (dateTime.getHours() >= 18 && dateTime.getHours() < 24) {
      return '晚上好🌃！';
    } else if (dateTime.getHours() >= 0 && dateTime.getHours() < 6) {
      return '偷偷向银河要了一把碎星，只等你闭上眼睛撒入你的梦中，晚安🌛！';
    }
  });

  const isBazaar = ref(false);
  // bazaar 集市
  // tenant 租户
  // workspace 空间
  // user 用户
  // role
  watch(
    () => router.currentRoute.value.path,
    (newValue, oldValue) => {
      const keywords = ['bazaar', 'tenant', 'workspace', 'user', 'role'];
      isBazaar.value = !keywords.some((keyword) => newValue.includes(keyword));
    },
    { immediate: true },
  );
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .navbar {
    height: 40px;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // 标签导航 背景色
    // background: rgba(1, 1, 1, 0);
    // box-shadow: 0 1px 1px rgba(0, 21, 41, 0.35);

    // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.959);
    // 底部 box-shadow
    // box-shadow: 0px 1px 0px rgba(119, 167, 255, 0.267);
    .left-menu {
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;

      .menu-title {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 16px;
        border-radius: 20px;
        background: #fff;
        // margin-bottom:20px;
        /* 卡片阴影 */
        // box-shadow:
        //   0px 2px 8px 0px rgba(31, 48, 78, 0.05),
        //   0px 10px 16px 4px rgba(31, 48, 78, 0.04);
        .hamburger-container {
          // line-height: 36px;
          height: 27px;
          // float: left;
          cursor: pointer;
          transition: background 0.3s;
          -webkit-tap-highlight-color: transparent;

          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }

        .breadcrumb-container {
          // float: left;
          font-size: 14px;
          font-weight: 400;
          color: #000;
        }
      }
    }

    .topmenu-container {
      position: absolute;
      left: 50px;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      height: 100%;
      float: right;
      line-height: 50px;
      //   display: flex;
      display: inline-block;

      &:focus {
        outline: none;
      }

      .right-menu-item {
        display: inline-block;
        // padding: 10px 0px 0 0;
        height: 32px;
        color: #5a5e66;
        vertical-align: 6px;
        // margin-right: 14px;
        // margin: 4px 30px 4px 15%;
        :deep .el-form-item {
          margin: -24px 10px 0;
        }

        &.hover-effect {
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }
      }
      .hover-effect {
        height: 40px;
        display: inline-block;
        vertical-align: 7px;
      }

      .avatar-container {
        height: 100%;
        // width: 110px;
        display: inline-block;
        vertical-align: 9px;
        margin-left: 24px;

        .avatar-wrapper {
          height: 100%;
          // margin-top: 0px;
          // position: relative;
          //   display: flex;
          //   justify-content: center;
          //   align-items: center;
          display: inline-block;
          cursor: pointer;

          .user-avatar {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: $--base-btn-primary-plain;
            font-size: 16px;
            line-height: 40px;
            text-align: center;
            color: $--base-color-primary;
          }

          i {
            cursor: pointer;
            position: absolute;
            right: 0;
            top: 12px;
            font-size: 12px;
          }
          & > div {
            display: inline-block;
          }
        }
      }
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      //   margin-top: 0.5%;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
  }

  .bell-container {
    margin: 0px;
    width: 52px;
    height: 32px;
    text-align: center;
    border-radius: 20px;
    background: #fff;
    line-height: 50px;
    // margin-top: 3px;
    display: inline-block;
    vertical-align: 7px;
    cursor: pointer;
    .message-icon {
      width: 52px;
      height: 32px;
      margin: auto;
      background: url('@/assets/images/message.png') no-repeat center;
      background-size: 24px auto;
    }
    // margin-top: -10px;
    /* 卡片阴影 */
    // box-shadow:
    //   0px 2px 8px 0px rgba(31, 48, 78, 0.05),
    //   0px 10px 16px 4px rgba(31, 48, 78, 0.04);
    .bell {
      position: relative;
      left: 0;
      top: -15px;
      width: 24px;
      height: 24px;
      font-size: 20px;
    }
  }

  .userShow {
    margin-top: -15px;
    margin-left: 10px;
    padding: 2px 5px 2px 5px;
    width: 120px;
    // height: 50px;
    background-color: #f9f9f9;
    border-left: 1px solid rgb(236, 236, 236);
    // margin-bottom: -5px;

    .first-small {
      display: block;
      color: #000000b9;
      font-family: Arial, Helvetica, sans-serif;
      font-size: 15px;
      font-weight: 800;
      // 添加阴影
      text-shadow: 0 0px 0.1px #000;
    }

    .second-small {
      color: gray;
      display: block;
      margin-top: -20px;
      // margin-left: -10px;
    }
  }
</style>
