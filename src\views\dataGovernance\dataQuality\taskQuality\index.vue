<template>
  <div class="category-manager">
    <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
    <div v-if="!addInfo.showAdd" class="category-manager-list">
      <div class="category-left-box">
        <div class="category-title">目录</div>
        <el-tooltip class="box-item" content="添加目录" effect="light" placement="top-start">
          <el-button
            icon="Plus"
            type="primary"
            class="box-item"
            @click="treeListener.addGroupBtn"
          ></el-button>
        </el-tooltip>
        <div class="list-tree">
          <el-input
            v-model="treeSearchText"
            v-input="searchTree"
            class="tree-search"
            placeholder="请输入搜索内容"
            suffix-icon="Search"
          >
            <!-- <template #append>
            <el-button icon="plus" @click="addGroupBtn" />
          </template> -->
          </el-input>
          <el-tree
            ref="treeRef"
            class="left-tree-box"
            :data="listInfo.treeData"
            :props="listInfo.propsGroupTree"
            :highlight-current="true"
            :filter-node-method="listInfo.filterNode"
          >
            <template #default="items">
              <div class="tree-item" @click="treeListener.handleNodeClick(items)">
                <div class="tree-item-box">
                  <el-icon>
                    <FolderOpened />
                  </el-icon>
                  <el-tooltip
                    :content="items.data.groupName"
                    placement="top"
                    :disabled="items.data.groupName.length < 10"
                  >
                    {{
                      items.data.groupName.length > 10
                        ? items.data.groupName.slice(0, 10) + '...'
                        : items.data.groupName
                    }}
                  </el-tooltip>
                  <!-- {{ items.data.groupName }} -->
                </div>
                <div class="tree-btn-box">
                  <span class="tree-icon" @click.stop="treeListener.addGroupBtn(items)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </span>
                  <span
                    v-if="items.data.groupName"
                    class="tree-icon"
                    @click.stop="treeListener.editGroup(items)"
                  >
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </span>
                  <span
                    v-if="!items.data?.children || items.data?.children?.length <= 0"
                    class="tree-icon"
                    @click.stop="treeListener.deleteGroup(items)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </span>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="category-right-box">
        <div class="category-title">表列表</div>
        <div class="form-box">
          <el-form
            ref=""
            v-model="searchInfo.searchForm"
            label-position="left"
            inline
            label-width="auto"
          >
            <el-form-item label="质量作业名称" prop="name">
              <el-input v-model="searchInfo.searchForm.name" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="searchInfo.searchForm.createTime"
                type="datetimerange"
                :shortcuts="shortcuts"
                range-separator="To"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
              <el-button
                type="primary"
                icon="Search"
                class="icon-btn"
                @click="tableListener.tableSearch"
              ></el-button>
            </el-tooltip>
            <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
              <el-button icon="Refresh" class="icon-btn"></el-button>
            </el-tooltip>
          </el-form>
        </div>
        <div class="other-btn-box">
          <el-button type="primary" icon="Plus" @click="tableListener.showAddDialog"
            >新增</el-button
          >
          <el-button type="danger" icon="Delete" @click="tableListener.deleteItems"
            >批量删除</el-button
          ></div
        >

        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableInfo.tableData"
            height="100%"
            :header-cell-class-name="addHeaderCellClassName"
            row-class-name="rowClass"
            empty-text="暂无数据"
            @selection-change="tableListener.selectChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              v-for="(item, index) in tableInfo.columns"
              :key="index"
              v-bind="item"
            />
            <el-table-column label="操作" fixed="right" min-width="200" width="240">
              <template #default="scope">
                <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                  运行
                </el-button>
                <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                  启用调度
                </el-button>
                <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                  停止调度
                </el-button>
                <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                  编辑
                </el-button>
                <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- </el-tab-pane> -->

        <div style="margin-bottom: 20px">
          <!-- 分页 -->
          <pagination
            v-show="searchInfo.queryParams.total > 0"
            v-model:page="searchInfo.queryParams.pageNum"
            v-model:limit="searchInfo.queryParams.pageSize"
            :pager-count="searchInfo.queryParams.maxCount"
            :total="searchInfo.queryParams.total"
            @pagination="listPage('ODS')"
          />
        </div>
      </div>
    </div>
    <addStep @callback="tableListener.closeAddDialog" v-else :api-id="addInfo.id"></addStep>
    <!-- </el-tabs> -->
    <!-- 新增表 -->
    <el-dialog v-model="dialogInfo.dialogVisible" title="新增表" width="60%" :draggable="true">
      <div class="form-box">
        <el-form
          ref=""
          v-model="dialogInfo.searchForm"
          label-position="left"
          inline
          label-width="auto"
        >
          <el-form-item label="数据源类型" prop="dataType">
            <el-select
              v-model="dialogInfo.searchForm.dataType"
              placeholder="请选择"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="(option, index) in options.dataTypeOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="database">
            <el-select
              v-model="dialogInfo.searchForm.database"
              placeholder="请选择"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="(option, index) in options.databaseOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="库名" prop="databaseName">
            <el-input
              v-model="dialogInfo.searchForm.databaseName"
              placeholder="请输入库名"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="模式名" prop="modelName">
            <el-input
              v-model="dialogInfo.searchForm.modelName"
              placeholder="请输入模式名"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="表名称" prop="tableName">
            <el-input
              v-model="dialogInfo.searchForm.tableName"
              placeholder="请输入表名称"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
            <el-button
              type="primary"
              icon="Search"
              class="icon-btn"
              @click="dialogListener.tableSearch"
            ></el-button>
          </el-tooltip>
          <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
            <el-button icon="Refresh" class="icon-btn"></el-button>
          </el-tooltip>
        </el-form>
      </div>
      <div class="dialog-table-box">
        <el-table
          ref="tableRef"
          :data="dialogInfo.data"
          height="100%"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
          @selection-change="dialogListener.selectChange"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column v-for="(item, index) in dialogInfo.columns" :key="index" v-bind="item" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新增分类 -->
    <el-dialog
      v-model="addGroupDialogInfo.addGroupDialog"
      :title="addGroupDialogInfo.addGroupDialogTitle"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <CategoryAddGroup
        v-if="addGroupDialogInfo.addGroupDialog"
        ref="addGroupRef"
        :form-data="addGroupDialogInfo.editGroupForm"
        :active-name="addGroupDialogInfo.activeName"
      ></CategoryAddGroup>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="treeListener.closeAddGroupDialog">取 消</el-button>
          <el-button type="primary" @click="treeListener.addGroupCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import useTaskQualityService from '@/views/dataGovernance/dataQuality/taskQuality/useTaskQualityService';
  import CategoryAddGroup from '@/views/dataGovernance/dataSecurity/components/categoryAddGroup';
  import addStep from '@/views/dataGovernance/dataQuality/taskQuality/components/addStep';

  const {
    tableInfo,
    searchInfo,
    dialogInfo,
    addGroupDialogInfo,
    tableListener,
    dialogListener,
    listListener,
    treeListener,
    listInfo,
    options,
    treeSearchText,
    addInfo,
  } = useTaskQualityService();
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .category-manager {
    width: 100%;
    height: 100%;
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;

    .category-manager-list {
      width: 100%;
      height: 100%;
      padding: 15px;
      overflow: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      .category-title {
        font-size: 16px;
        color: $--base-color-title1;
        height: 32px;
        line-height: 32px;
        position: relative;
        padding-left: 10px;
        margin-bottom: 10px;
        &::before {
          content: '';
          width: 2px;
          height: 16px;
          position: absolute;
          background: $--base-color-primary;
          top: 10px;
          left: 0;
        }
      }

      .category-left-box {
        width: 260px;
        height: 100%;
        position: relative;
        .box-item {
          position: absolute;
          top: 0px;
          right: 10px;
        }
        .list-tree {
          height: calc(100% - 56px);
          background: $--base-color-item-light;
          padding: 10px;
          margin-top: 20px;
          .tree-search {
            margin-bottom: 10px;
          }
        }
      }
      .category-right-box {
        width: calc(100% - 280px);
        height: 100%;
        .form-box {
          margin-top: 20px;
          ::v-deep .el-form {
            text-align: right;
          }
        }
        .other-btn-box {
          margin-bottom: 20px;
          // padding: 0 20px;
        }
        .table-box {
          height: calc(100% - 200px);
          background: $--base-color-item-light;
          padding: 10px;
        }

        .pagination-container {
          margin: 0;
          padding: 10px 20px;
        }
      }
    }

    .dialog-table-box {
      height: 500px;
    }
  }
</style>
