<template>
  <div class="App-theme">
    <!-- 返回上一级 -->
    <div class="btn-box">
      <div class="back-btn">
        <el-button type="primary" @click="toBack">返回上一级</el-button>
      </div>

      <!-- 取消 / 确认 按钮 -->
      <div class="btn-t">
        <el-button @click="toBack">取消</el-button>
        <el-button v-if="!rowData" type="primary" @click="handleClickAdd">确认</el-button>
      </div>
    </div>
    <!-- 监控对象 -->
    <div>
      <span class="titleName">监控对象</span>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="auto"
        inline
      >
        <!-- 任务名称 -->
        <!-- 目前又说编辑的时候可以修改任务名称 :disabled="rowData"-->
        <el-form-item label="任务名称" prop="name" class="form-item-half">
          <el-input v-model="form.qualityTaskName" :disabled="rowData" />
        </el-form-item>
        <template v-if="true">
          <el-form-item label="数据源类型" prop="dataSourceType" class="form-item-half">
            <!-- 下拉框 -->
            <el-select
              v-model="form.dataSourceType"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getType"
            >
              <el-option
                v-for="dict in dataSourceTypeList"
                :key="dict"
                :value="dict.value"
                :label="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="dataSource" class="form-item-half">
            <el-select
              v-model="form.dataSource"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getDB"
            >
              <el-option
                v-for="dict in dataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据库" prop="dataSchema" class="form-item-half">
            <el-select
              v-model="form.database"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getDataTable"
            >
              <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="modelType" label="模式" prop="dataSchema" class="form-item-half">
            <el-select
              v-model="form.dataSchema"
              placeholder=""
              clearable
              :disabled="rowData"
              @change="getSourceGP"
            >
              <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="modelType" label="数据表" prop="layering" class="form-item-half">
            <el-select
              v-model="form.datasheet"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getFiled"
            >
              <el-option
                v-for="dict in datasheetList"
                :key="dict.tableName"
                :label="dict.tableName"
                :value="dict.tableName"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="!modelType" label="数据表" prop="layering" class="form-item-half">
            <el-select
              v-model="form.datasheet"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getFiled"
            >
              <el-option
                v-for="dict in datasheetList"
                :key="dict.tableName"
                :label="dict.tableName"
                :value="dict.tableName"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <!-- 监控规则 -->
    <div>
      <span class="titleName">监控规则</span>

      <div>
        <!-- <el-button type="primary" :disabled="!form.datasheet" @click="addMonitoringRule"> -->
        <!-- 新增监控规则 -->
        <!-- </el-button> -->
        <el-button type="primary" :disabled="!dataFieldList?.length" @click="addMonitoringRule">
          新增监控规则
        </el-button>
        <!-- <el-button type="primary" :disabled="!dataFieldList?.length" @click="schedulingStrategy"> -->
        <!-- 调度策略 -->
        <!-- </el-button> -->
        <!-- <el-button type="primary" @click="alarmSettings">告警设置</el-button> -->

        <right-toolbar
          v-model:show-search="showSearch"
          :columns="columns"
          @query-table="listPage"
        />
      </div>

      <el-table :data="useTableData" height="350px" @selection-change="handleSelectionChange">
        <el-table-column
          v-for="(item, index) in columns"
          v-show="item.prop !== 'status'"
          :key="index"
          v-bind="item"
        />
        <!-- 操作 -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <!-- 编辑 删除 -->
            <el-button
              type="text"
              size="small"
              :disabled="scope.row.status == 0"
              @click="revamp(scope)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              :disabled="scope.row.status == 0"
              @click="del(scope)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="listPage"
      />
    </div>

    <!-- 监控规则 -->
    <el-dialog
      v-model="openRule"
      :title="ruleTitle"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelRule()"
    >
      <ruleForm v-if="openRule" ref="qualityRulesRef" :canvas-actions="true" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelRule">取 消</el-button>
          <el-button type="primary" @click="submitFormRule">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 调度设置 -->
    <el-dialog
      v-model="openAttemper"
      title="调度设置"
      width="50%"
      append-to-body
      draggable
      @close="cancelAttemper()"
    >
      <el-alert
        title="提示"
        type="warning"
        description="请按照以下步骤进行操作：
     1. 选择适当的时间。
     2. 生成相应的 Cron 表达式。
     3. 确保保存所做的更改。
     4. 注意：务必不要忽略选择秒时段。"
      >
      </el-alert>

      <el-row>
        <el-col :span="24" style="margin: 20px 0 20px 0">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="24">
          <vue3Cron
            v-if="showCron"
            :project-code-of-ds="projectCodeOfDs"
            :workspace-id="workspaceId"
            :datetimerange="datetimerange"
            :CronData="crontab"
            @change="handleCronChange"
          />
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAttemper">取 消</el-button>
          <!-- <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button> -->
          <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 告警设置 -->
    <el-dialog
      v-model="openWarn"
      title="告警设置"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      draggable
      @close="cancelWarn()"
    >
      <el-form ref="warnFormRef" :model="warnForm" :rules="ruleForWarn">
        <el-form-item label="告警策略" prop="subscribeType">
          <el-radio-group v-model="warnForm.subscribeType" class="radio_group">
            <el-radio label="2">成功通知</el-radio>
            <el-radio label="3">失败通知</el-radio>
            <el-radio label="1">成功失败都通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知策略" prop="checkList">
          <el-checkbox-group v-model="warnForm.checkList">
            <el-checkbox label="站内信" />
            <el-checkbox :disabled="onCheck" label="邮箱" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelWarn">取 消</el-button>
          <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    // getStandardList,
    getTableList,
    schemaForGP,
    tableForGP,
  } from '@/api/datamodel';
  import vue3Cron from '@/components/vue3Cron';
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { formatDimension, formatType, getNameById } from '@/utils/xugu';
  import { reactive, ref } from 'vue';
  import ruleForm from './module/index.vue';

  import {
    addDataQualityTask,
    addFlow,
    delFlow,
    getFlowList,
    updateFlow,
    updateDataQualityTask,
  } from '~/src/api/dataGovernance';
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { rowData } = toRefs(props);
  const tabList = ref([]);
  const initializeForm = (type) => {
    qualityRulesStore.$state.dataObj = [];
    qualityRulesStore.$state.dataObj[0] = {};
    qualityRulesStore.$state.dataObj[0][type] = {};
    return qualityRulesStore.$state.dataObj[0][type];
  };

  // 初始化时调用
  const type = 'source';
  let form = reactive({
    qualityTaskName: '',
    dataSourceType: '',
    dataSource: '',
    database: '',
    dataSchema: '',
    datasheet: '',
  });

  // 表格数据和查询参数（mock 数据）
  const tableData = ref([]);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  });

  const total = ref(0);

  // 列定义
  const columns = ref([
    { prop: 'ruleName', label: '规则名称' },
    { prop: 'ruleTypeName', label: '规则类型' },
    { prop: 'ruleDimensionName', label: '维度' },
    { prop: 'filtrate', label: '过滤条件' },
  ]);

  // 模拟获取数据
  const listPage = () => {
    if (rowData.value) {
      getFlowListUtil();
    } else {
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );

      total.value = tableData.value?.length;
    }
  };

  // 按钮操作逻辑

  //   MULTIPLE_RULES("0","单表多规则"),
  //   MULTIPLE_TABLES("1","多表单规则"),

  //   ENABLE("0","启用"),
  //   DISABLE("1","停用");

  // 取消按钮
  const emits = defineEmits(['toBack']);
  const toBack = async () => {
    try {
      await proxy.$confirm('你所做的更改可能未保存', '是否离开当前页面？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      // 返回的时候制空所有存在数据
      tableData.value = [];
      queryParams.pageNum = 1;
      queryParams.pageSize = 10;
      total.value = 0;
      tabList.value = [];

      emits('toBack');
    } catch {
      console.log('取消');
    }
  };
  // 检查是否填写源表检测列 如果无数据提示
  //   const detectSourceTable = () => {
  //     return tableData.value.every((item) => !!item.src_field);
  //   };
  const handleClickAdd = async (type = 0) => {
    // if (!detectSourceTable())
    //   return proxy.$modal.msgWarning('请检查是否所有规则都填写了源表检测列');
    if (tableData.value.length < 1) return proxy.$modal.msgWarning('暂无数据添加数据后在提交');
    // const result = await addDataQualityTask(qualityTask.value);

    // if (result.code !== 200) return proxy.$modal.msgError(result.msg);
    // proxy.$modal.msgSuccess(result.msg);
    // emits('toBack');

    if (rowData.value) {
      // 如果是修改
      await updateDataQualityTaskUtil({ qualityTaskName: form.qualityTaskName });
    } else {
      // 如果是新增
      const result = await addDataQualityTask(qualityTask.value);
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      emits('toBack');
    }
  };

  // 编辑操作
  const scopeRow = ref();
  const revamp = (scope) => {
    ruleTitle.value = '编辑规则';
    scopeRow.value = scope;
    const { row } = scopeRow.value;
    if (!row.ruleDefinition) {
      const transformedRow = {};
      const transformedTargetDefinition = {};
      const typeRuleDefinition = {};
      for (const key in row) {
        if (key.startsWith('src_')) {
          typeRuleDefinition[key.slice(4)] = row[key];
        } else if (key.startsWith('target_')) {
          transformedTargetDefinition[key.slice(7)] = row[key];
        } else if (key === 'mapping_columns') {
          console.log('row[key]', row[key]);
          if (row[key]) {
            transformedRow[key] = JSON.parse(row[key]);
          }
        } else {
          transformedRow[key] = row[key];
        }
      }
      qualityRulesStore.$state.dataObj = [
        { ...transformedRow, source: typeRuleDefinition, target: transformedTargetDefinition },
      ];
      qualityRulesStore.$state.dataObj[0][type].dataFieldList = dataFieldList.value;
      // qualityRulesStore.$state.dataObj[0][type].field = transformedRow.field;
      // qualityRulesStore.$state.dataObj[0][type].filter = transformedRow.filter;
      if (row.qualityTaskName) {
        form.qualityTaskName = JSON.parse(JSON.stringify(row.qualityTaskName));
      }
    } else {
      if (typeof row.ruleDefinition === 'string') {
        if (typeof row.ruleDefinition === 'string') {
          const parsedRuleDefinition = JSON.parse(row.ruleDefinition);
          const transformedRuleDefinition = {};
          const transformedTargetDefinition = {};
          const typeRuleDefinition = {};
          for (const key in parsedRuleDefinition) {
            if (key.startsWith('src_')) {
              typeRuleDefinition[key.slice(4)] = parsedRuleDefinition[key];
            } else if (key.startsWith('target_')) {
              transformedTargetDefinition[key.slice(7)] = parsedRuleDefinition[key];
            } else if (key === 'mapping_columns') {
              if (parsedRuleDefinition[key]) {
                transformedRuleDefinition[key] = JSON.parse(parsedRuleDefinition[key]);
              }
            } else {
              transformedRuleDefinition[key] = parsedRuleDefinition[key];
            }
          }
          qualityRulesStore.$state.dataObj = [
            {
              ...transformedRuleDefinition,
              source: typeRuleDefinition,
              target: transformedTargetDefinition,
            },
          ];
        }
      } else {
        console.log('2', row.ruleDefinition);

        qualityRulesStore.$state.dataObj = [{ ...row.ruleDefinition, [type]: {} }];
      }

      qualityRulesStore.$state.dataObj[0][type].dataSourceType = form.dataSourceType;
      qualityRulesStore.$state.dataObj[0][type].dataSource = form.dataSource;
      qualityRulesStore.$state.dataObj[0][type].database = form.database;
      qualityRulesStore.$state.dataObj[0][type].dataSchema = form.dataSchema;
      qualityRulesStore.$state.dataObj[0][type].datasheet = form.datasheet;

      qualityRulesStore.$state.dataObj[0][type].dataFieldList = dataFieldList.value;

      qualityRulesStore.$state.dataObj[0][type].qualityTaskName = JSON.parse(
        JSON.stringify(form.qualityTaskName),
      );

      form.qualityTaskName = JSON.parse(JSON.stringify(form.qualityTaskName));
    }
    assignFormValues(row);
    openRule.value = true;
  };

  // 删除操作
  const del = async ({ row, $index }) => {
    const ref = await proxy.$modal.confirm('是否确认删除数据项？');
    if (!ref) return;

    if (!row.ruleDefinition) {
      tableData.value = tableData.value.filter((item, index) => index !== $index);
      //   qualityRulesStore.$state.dataObj = tableData.value;
      const qualityTaskRules = tableData.value.map((item) => createRuleObject(item));
      qualityTask.value = {
        qualityTaskName: form.qualityTaskName,
        qualityTaskType: 0,
        //   status:  form.status,
        tenantId: tenantId.value,
        workspaceId: workspaceId.value,
        qualityTaskRules,
        //   startTime:  form.createTime[0],
        //   endTime:  form.createTime[1],
      };
      proxy.$modal.msgSuccess('删除成功');
      total.value = tableData.value?.length;
      // 需要重新给tablist赋值
      if (!rowData.value) {
        tabList.value = tableData.value.slice(
          (queryParams.pageNum - 1) * queryParams.pageSize,
          queryParams.pageNum * queryParams.pageSize,
        );
      }
      listPage();
      return;
    }

    const query = { taskRuleId: row.id };
    const res = await delFlow(query);
    if (res.code !== 200) return;
    proxy.$modal.msgSuccess('删除成功');
    getFlowListUtil();
  };

  const data = reactive({
    rules: {
      code: [
        { required: true, message: '请输入', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      dwDatabase: [{ required: true, message: '请选择储存库', trigger: 'change' }],
      isFromDatasource: [{ required: true, message: '请选择建模方式', trigger: 'change' }],
      isExternal: [{ required: true, message: '请选择存储类型', trigger: 'change' }],
      type: [{ required: true, message: '', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
  });
  const { rules } = toRefs(data);

  // -------------------------------------------------------------------------------------- 数据源
  // #region
  //   const modelType = computed(() => {
  //     return (
  //       form.dataSourceType &&
  //       form.dataSourceType !== 'MYSQL' &&
  //       form.dataSourceType !== 'HIVE' &&
  //       form.dataSourceType !== 'SPARK'
  //       //   &&  form.dataSourceType !== 'ORACLE'
  //     );
  //   });
  const modelType = ref(true);

  const dataSourceTypeList = ref([
    { label: 'MYSQL', value: 'MYSQL' },
    { label: 'POSTGRESQL', value: 'POSTGRESQL' },
    { label: 'HIVE', value: 'HIVE' },
    { label: 'ORACLE', value: 'ORACLE' },
    { label: 'SQLSERVER', value: 'SQLSERVER' },
    { label: 'DB2', value: 'DB2' },
    { label: 'XUGU', value: 'XUGU' },
    // { label: 'DAMENG', value: 'DAMENG' },
    // { label: 'GREENPLUM', value: 'GREENPLUM' },
    // { "label": "KAFKA", "value": "KAFKA" },
  ]);
  const dataSourceList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const datasheetList = ref([]);

  const getType = async () => {
    // 改变数据 先清空已有数据
    form.dataSource = '';
    form.database = '';
    form.dataSchema = '';
    form.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];
    dataFieldList.value = [];

    if (!form.dataSourceType) {
      form.dataSource = '';
      form.database = '';
      form.dataSchema = '';
      form.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: form.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据源不存在');
    dataSourceList.value = res.data;

    // 监听有问题 ，改成手动修改
    modelType.value =
      form.dataSourceType &&
      form.dataSourceType !== 'MYSQL' &&
      form.dataSourceType !== 'HIVE' &&
      form.dataSourceType !== 'SPARK';
  };

  const getDB = async () => {
    // 改变数据 先清空已有数据
    form.database = '';
    form.dataSchema = '';
    form.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];
    dataFieldList.value = [];

    if (!form.dataSource) {
      form.database = '';
      form.dataSchema = '';
      form.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }

    const res = await getDatabaseList({ datasourceId: form.dataSource });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据库不存在');
    databaseList.value = res.data;
  };

  //   const standardList = ref();
  //   const getStandardListUtil = async () => {
  //     const query = {
  //       //   workspaceId: workspaceId.value,
  //     };
  //     const res = await getStandardList(query);
  //     if (res.code === 200) {
  //       standardList.value = res.data;
  //     }
  //   };

  const getDataTable = async () => {
    // 改变数据 先清空已有数据
    form.dataSchema = '';
    form.datasheet = '';
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];
    dataFieldList.value = [];

    if (!form.database) {
      form.dataSchema = '';
      form.datasheet = '';
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    // 根据不同数据源获取不同的接口
    if (
      form.dataSourceType &&
      (form.dataSourceType == 'MYSQL' ||
        form.dataSourceType == 'HIVE' ||
        form.dataSourceType == 'SPARK')
    ) {
      const objForOr = {
        datasourceId: form.dataSource,
        databaseName: form.database,
      };
      const res = await getTableList(objForOr);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('表不存在');
      datasheetList.value = res.data;
    } else {
      const obj = {
        datasourceId: form.dataSource,
        databaseName: form.database,
      };
      const res = await schemaForGP(obj);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据模式不存在');
      dataSchemaList.value = res.data;
    }
  };

  //   const createObj = () => ({
  //   datasourceId: form.dataSource,
  //   databaseName: form.database,
  // });

  // const handleResponse = (res, warningMessage, targetList) => {
  //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //   if (!res.data || !res.data.length) return proxy.$modal.msgWarning(warningMessage);
  //   targetList.value = res.data;
  // };

  // let res;
  // if (['MYSQL', 'HIVE', 'SPARK'].includes(form.dataSourceType)) {
  //   res = await getTableList(createObj());
  //   handleResponse(res, '表不存在', datasheetList);
  // } else {
  //   res = await schemaForGP(createObj());
  //   handleResponse(res, '数据模式不存在', dataSchemaList);
  // }

  const getSourceGP = async (data) => {
    // 改变数据 先清空已有数据
    form.datasheet = '';
    datasheetList.value = [];
    tableData.value = [];

    if (!form.dataSchema) {
      form.datasheet = '';
      datasheetList.value = [];
      return;
    }

    if (data) {
      const obj = {
        datasourceId: form.dataSource,
        databaseName: form.database,
        schemaName: data,
      };

      await tableForGP(obj).then((res) => {
        if (res.data && res.data?.length) {
          datasheetList.value = res.data;
        } else {
          datasheetList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const sourceFieldList = ref([]);
  const targetFieldList = ref([]);
  const dataFieldList = ref([]);
  const getFiled = async () => {
    // 改变数据 先清空已有数据
    sourceFieldList.value = [];
    targetFieldList.value = [];
    tableData.value = [];
    dataFieldList.value = [];
    if (!form.datasheet) {
      sourceFieldList.value = [];
      targetFieldList.value = [];
      return;
    }

    const params = {};
    // 不同的数据源需要不同的参数
    if (modelType.value) {
      params.tableName = form.datasheet;
      params.datasourceId = form.dataSource;
      params.schema = form.dataSchema;
      params.databaseName = form.database;
    } else {
      params.tableName = form.datasheet;
      params.databaseName = form.database;
      params.datasourceId = form.dataSource;
    }

    const res = await getFieldList(params);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('字段不存在');
    dataFieldList.value = res.data;
  };

  watch(dataFieldList, () => {
    form.dataFieldList = dataFieldList.value;
  });
  // #endregion

  // -------------------------------------------------------------------------------------- 规则
  // #region

  const openRule = ref(false);
  const ruleTitle = ref('监控规则');

  const cancelRule = () => {
    console.log(
      'qualityRulesStore.$state.dataObj[0][type]',
      qualityRulesStore.$state.dataObj[0][type],
    );
    const copyForm = JSON.parse(JSON.stringify(qualityRulesStore.$state.dataObj[0][type]));
    console.log(copyForm);
    const thisForm = initializeForm(type);
    Object.assign(form, thisForm);

    if (copyForm.qualityTaskName) {
      form.qualityTaskName = JSON.parse(JSON.stringify(copyForm.qualityTaskName));
    }

    assignFormValues(copyForm);
    debugger;
    form.dataFieldList = dataFieldList.value;
    scopeRow.value = null;
    openRule.value = false;
  };
  const assignFormValues = (source) => {
    if (source.srcDatasourceType || source.dataSourceType) {
      form.dataSourceType = source.srcDatasourceType || source.dataSourceType;
      form.dataSource = source.srcDatasourceId || source.dataSource;
      form.database = source.srcDatabaseName || source.database;
      form.dataSchema = source.srcSchemaName || source.dataSchema;
      form.datasheet = source.srcTableName || source.datasheet;
    }
  };
  const dataSourceTypeMap = new Map([
    ['MYSQL', 0],
    ['POSTGRESQL', 1],
    ['HIVE', 2],
    ['SPARK', 3],
    ['CLICKHOUSE', 4],
    ['ORACLE', 5],
    ['SQLSERVER', 6],
    ['DB2', 7],
    ['PRESTO', 8],
    ['H2', 9],
    ['REDSHIFT', 10],
    ['ATHENA', 11],
    ['XUGU', 12],
  ]);

  const submitFormRule = async () => {
    const valid = await proxy.$refs.qualityRulesRef.checkSubmit();
    if (!valid) return;
    // proxy.$modal.msgWarning('请填写完整的规则');

    console.log(form);
    const roleRowData = qualityRulesStore.$state.dataObj.map((item, index) => {
      const { source, target, ...rest } = item;
      return {
        ...rest,
        // 名称
        ruleName: getNameById(item.ruleId),
        // 类型
        ruleTypeName: formatType(item.ruleId),
        // 维度
        ruleDimensionName: formatDimension(item.ruleId),
        // 过滤条件
        targetFilter: source.filter,
        filtrate: source.filter,
        mapping_columns: item?.mapping_columns ? JSON.stringify(item?.mapping_columns) : '',

        src_dataType: dataSourceTypeMap.get(form.dataSourceType), // 数据类型
        src_dataSources: form.dataSource, // 数据源
        src_database: form.database, // 数据库
        src_schema: form.dataSchema, // 数据模式
        src_dataTable: form.datasheet, // 数据表
        src_field: source?.field, // 字段
        src_filter: source?.filter, // 过滤条件
        src_name: source?.name, // 值名
        src_execute_sql: source?.execute_sql, // 值 SQL

        target_dataType: target?.dataType, // 数据类型
        target_dataSources: target?.dataSources, // 数据源
        target_database: target?.database, // 数据库
        target_schema: target?.schema, // 数据模式
        target_dataTable: target?.dataTable, // 数据表
        target_field: target?.field, // 字段
        target_filter: target?.filter, // 过滤条件
        target_name: target?.name, // 值名
        target_execute_sql: target?.execute_sql, // 值 SQL
      };
    });

    const query = roleRowData.map((item) => createRuleObject(item, true));
    debugger;
    if (rowData.value) {
      if (scopeRow.value?.row?.id) {
        scopeRow.value.row.ruleDefinition = query[0].ruleDefinition;
        scopeRow.value.row.ruleId = query[0].ruleId;
        scopeRow.value.row.targetFilter = query[0].targetFilter;
        scopeRow.value.row.ruleName = query[0].ruleName;
        scopeRow.value.row.ruleDimensionName = query[0].ruleDimensionName;
        scopeRow.value.row.ruleTypeName = query[0].ruleTypeName;

        updateFlowUtil(scopeRow.value.row);
      } else {
        addFlowUtil(query);
      }
    } else {
      if (ruleTitle.value === '编辑规则') {
        updateFlowForeUtil(roleRowData);
      } else {
        console.log('roleRowData', roleRowData);
        addFlowForeUtil(roleRowData);
      }
    }

    openRule.value = false;
  };

  const addMonitoringRule = () => {
    console.log(dataFieldList);
    debugger;
    ruleTitle.value = '监控规则';
    if (rowData.value) {
      form.qualityTaskName = JSON.parse(JSON.stringify(form.qualityTaskName));
      assignFormValues(flowList.value[0]);
    }
    openRule.value = true;
    qualityRulesStore.$state.dataObj[0][type].dataFieldList = dataFieldList.value;
    // qualityRulesStore.$state.dataObj[0][type].field = transformedRow.field;
    // qualityRulesStore.$state.dataObj[0][type].filter = transformedRow.filter;
    console.log('qualityRulesStore.$state.dataObj', qualityRulesStore.$state.dataObj);
  };

  const qualityTask = ref();

  const createRuleObject = (item, includeQualityTaskId = false) => {
    const baseObject = {
      ruleName: item.ruleName,
      ruleTypeName: item.ruleTypeName,
      ruleDimensionName: item.ruleDimensionName,
      ruleId: item.ruleId,
      targetFilter: item.filtrate,

      srcDatasourceType: form.dataSourceType,
      srcDatasourceId: form.dataSource,
      srcDatabaseName: form.database,
      srcSchemaName: form.dataSchema,
      srcTableName: form.datasheet,

      ruleDefinition: JSON.stringify(item),
    };

    if (includeQualityTaskId) {
      baseObject.qualityTaskId = rowData.value
        ? flowList.value
          ? flowList.value[0]?.qualityTaskId
          : null
        : null;
    }

    return baseObject;
  };

  const updateFlowForeUtil = async (datas) => {
    tableData.value[scopeRow.value.$index] = datas[0];
    const data = tableData.value[scopeRow.value.$index];
    tableData.value[scopeRow.value.$index].targetFilter = data?.source?.filter || data.src_filter;
    tableData.value[scopeRow.value.$index].filtrate = data?.source?.filter || data.src_filter;
    // tableData.value[scopeRow.value.$index].ruleDefinition = undefined
    // 需要重新给tablist赋值
    if (!rowData.value) {
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );
    }

    const qualityTaskRules = tableData.value.map((item) => createRuleObject(item));
    qualityTask.value = {
      qualityTaskName: form.qualityTaskName,
      qualityTaskType: 0,
      //   status:  form.status,
      tenantId: tenantId.value,
      workspaceId: workspaceId.value,
      qualityTaskRules,
      //   startTime:  form.createTime[0],
      //   endTime:  form.createTime[1],
    };
    proxy.$modal.msgSuccess('操作成功');
  };

  const addFlowForeUtil = async (roleRowData) => {
    if (tableData.value.length >= 50) return proxy.$modal.msgError('最多只能添加50条规则');
    tableData.value.push(...roleRowData);
    console.log('tableData.value', tableData.value);
    const qualityTaskRules = tableData.value.map((item) => createRuleObject(item));
    qualityTask.value = {
      qualityTaskName: form.qualityTaskName,
      qualityTaskType: 0,
      //   status:  form.status,
      tenantId: tenantId.value,
      workspaceId: workspaceId.value,
      qualityTaskRules,
      //   startTime:  form.createTime[0],
      //   endTime:  form.createTime[1],
    };
    console.log('qualityTaskRules', qualityTaskRules);
    // 需要重新给tablist赋值
    if (!rowData.value) {
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );
    }
    proxy.$modal.msgSuccess('操作成功');
    listPage();
  };

  const addFlowUtil = async (query) => {
    const res = await addFlow(...query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    // 需要重新给tablist赋值
    if (!rowData.value) {
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );
    }
    getFlowListUtil();
  };

  const updateFlowUtil = async (query) => {
    const res = await updateFlow(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    // 需要重新给tablist赋值
    if (!rowData.value) {
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );
    }
    getFlowListUtil();
  };

  // #endregion

  // --------------------------------------------------------------------------------------  调度
  // #region
  const cronValue = ref();
  const projectCodeOfDs = ref();
  const datetimerange = computed(() => value1.value);
  const showCron = ref(false);
  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const crontab = ref();

  // const processDefinitionCode = ref()
  // ...
  const schedulingStrategy = () => {
    if (rowData.value) {
      const startTime = new Date();
      const formattedStartTime = startTime.toISOString().slice(0, 19).replace('T', ' ');
      const endTime = new Date(startTime);
      endTime.setDate(endTime.getDate() + 7);
      const formattedEndTime = endTime.toISOString().slice(0, 19).replace('T', ' ');
      nextTick(() => {
        value1.value = [formattedStartTime, formattedEndTime];
        crontab.value = rowData.value.cron ? rowData.value.cron : cronValue.value;
      });
    }

    openAttemper.value = true;
    showCron.value = true;
  };
  // ...

  function handleCronChange(data) {
    cronValue.value = data;
  }

  const cancelAttemper = () => {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    // value1.value = null
    // cronValue.value = null
  };
  // 保存调度设置
  function submitAttemper() {
    if (!value1.value) return proxy.$modal.msgWarning('请选择调度时间');
    if (!cronValue.value) return proxy.$modal.msgWarning('请选择生成调度表达式');
    if (typeof cronValue.value !== 'string') return proxy.$modal.msgWarning('请选择生成调度表达式');

    if (rowData.value) {
      //   updateDataQualityTaskUtil(cronValue.value);
      updateDataQualityTaskUtil({ cron: cronValue.value });
      openAttemper.value = false;
      showCron.value = false;
    } else {
      qualityTask.value.cron = cronValue.value;
      qualityTask.value.startTime = value1.value[0];
      qualityTask.value.endTime = value1.value[1];
      qualityTask.value.timezoneId = 'Asia/Shanghai';
      openAttemper.value = false;
      showCron.value = false;
    }
  }
  // #endregion

  // -------------------------------------------------------------------------------------- 告警
  // #region

  //   const alarmSettings = () => {
  //     openWarn.value = true;
  //   };

  const warnForm = ref({
    subscribeType: '1',
    checkList: ['站内信'],
  });
  const onCheck = ref(false);
  const openWarn = ref(false);
  //   const email = ref(null);
  const objForWarn = ref({});
  const ruleForWarn = ref({
    subscribeType: [{ required: true, message: '请选择告警策略', trigger: 'change' }],
    checkList: [{ required: true, message: '请选择通知策略', trigger: 'change' }],
  });
  // 提交设置
  const submitFormWarn = async () => {
    const valid = await proxy.$refs.warnFormRef.validate((valid) => valid);
    if (!valid) return;
    if (warnForm.value.checkList.length === 2) {
      objForWarn.value.alertType = '1,3';
    } else if (warnForm.value.checkList.length === 1) {
      if (warnForm.value.checkList.indexOf('站内信') !== -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    }
    objForWarn.value.subscribeType = warnForm.value.subscribeType;
    if (objForWarn.value.processCode == null || objForWarn.value.projectCode == null) {
      proxy.$modal.msgError('请先发布流程调度');
      //   return;
    }
    // addSubscribe(objForWarn.value).then((res) => {
    //   if (res.code === 200) {
    //     openWarn.value = false;
    //     proxy.$modal.msgSuccess('订阅成功,可以前往告警中心查看并管理已订阅流程');
    //   }
    // });
  };

  // 取消设置
  const cancelWarn = () => {
    warnForm.value = {
      subscribeType: '1',
      checkList: ['站内信'],
    };
    proxy.resetForm('warnFormRef');
    openWarn.value = false;
  };
  // #endregion

  const useTableData = computed(() => {
    if (rowData.value) {
      return tableData.value;
    } else {
      return tabList.value;
    }
  });

  // 组件加载时获取数据
  onMounted(() => {
    form = initializeForm(type);
    if (rowData.value) {
      init();
    }
  });

  const init = async () => {
    await getFlowListUtil();
    console.log('flowList.value', flowList.value);
    form.qualityTaskName = JSON.parse(JSON.stringify(rowData.value.qualityTaskName));
    form.dataSourceType = flowList.value[0].srcDatasourceType;
    form.dataSourceType && getType();

    form.dataSource = flowList.value[0].srcDatasourceId;
    form.dataSource && getDB();

    form.database = flowList.value[0].srcDatabaseName;
    form.database && getDataTable();

    form.dataSchema = flowList.value[0].srcSchemaName;
    form.dataSchema && getSourceGP(form.dataSchema);

    form.datasheet = flowList.value[0].srcTableName;
    form.datasheet && getFiled();

    tableData.value = flowList.value.map((item) => {
      return {
        ...item,
        filtrate: item.targetFilter,
      };
    });
  };

  const flowList = ref([]);
  const getFlowListUtil = async () => {
    const query = {
      qualityTaskId: rowData.value.id,
      ...queryParams,
      workspaceId: workspaceId.value,
      //   orderByColumn: 2,
      //   isAsc: true,
    };
    const res = await getFlowList(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    // qualityRulesStore.$state.dataObj = JSON.parse(res.rows[0].ruleDefinition);
    nextTick(() => {
      flowList.value = res.rows;

      tableData.value = flowList.value.map((item) => {
        return {
          ...item,
          filtrate: item.targetFilter,
        };
      });

      total.value = res.total;

      //   tabList.value = tableData.value;
    });
  };

  const updateDataQualityTaskUtil = async ({ cron, qualityTaskName }) => {
    const query = {
      id: rowData.value.id,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      //   ...(cron && {
      //     cron,
      //     startTime: value1.value[0],
      //     endTime: value1.value[1],
      //     timezoneId: 'Asia/Shanghai',
      //   }),
      ...(qualityTaskName && { qualityTaskName }),
    };

    const res = await updateDataQualityTask(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .btn-box {
    display: flex;
    justify-content: flex-end;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .titleName {
    padding-left: 10px;
    font-size: 15px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;

    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .form-item-half {
    width: 48%;
    margin-right: 2%;
  }
  .form-item-half:nth-child(2n) {
    margin-right: 0;
  }
  .rule-form {
    margin-bottom: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;
  }
</style>
