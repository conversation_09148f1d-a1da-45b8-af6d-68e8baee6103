<template>
  <div>
    <div class="TitleName">
      <el-row :gutter="20">
        <el-col :span="20">安全设置</el-col>
        <el-col :span="1.5">
          <el-button type="primary" @click="submit"><IconSave />&nbsp;保存</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="reset"><IconRefresh />&nbsp;重置</el-button>
        </el-col>
      </el-row>
    </div>
    <div style="padding: 0 25%">
      <el-form ref="pwdRef" label-position="top" :model="user" :rules="rules" label-width="80px">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model="user.oldPassword"
            placeholder="请输入旧密码"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="user.newPassword"
            placeholder="请输入新密码"
            type="password"
            show-password
          />
        </el-form-item>
        <span
          style="
            color: rgb(140, 140, 140);
            margin: 10px;
            display: block;
            font-size: 12px;
            font-weight: 500;
          "
        >
          密码长度不小于8位,必须包含大写字母、小写字母、数字和特殊字符!@#$%^&*0_+-=中的至少三种
        </span>
        <el-form-item label="确定密码" prop="confirmPassword">
          <el-input
            v-model="user.confirmPassword"
            placeholder="请确定新密码"
            type="password"
            show-password
          />
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="primary" @click="submit">保存</el-button>
          <el-button type="danger" @click="close">关闭</el-button>
        </el-form-item> -->
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { updateUserPwd } from '@/api/system/user';
  import { IconRefresh, IconSave } from '@arco-iconbox/vue-update-line-icon';

  const { proxy } = getCurrentInstance();

  const user = reactive({
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined,
  });

  const equalToPassword = (rule, value, callback) => {
    if (user.newPassword !== value) {
      callback(new Error('两次输入的密码不一致'));
    } else {
      callback();
    }
  };
  //   const validatePasswordComplexity = (rule, value, callback) => {
  //     const SPECIAL_CHARACTERS = '!@#$%^&*()_+-=';

  //     if (value.length < 8) {
  //       callback(new Error('密码长度至少为 8 位'));
  //     } else if (!/(?=.*[a-z])/.test(value)) {
  //       callback(new Error('密码必须包含至少一个小写字母'));
  //     } else if (!/(?=.*[A-Z])/.test(value)) {
  //       callback(new Error('密码必须包含至少一个大写字母'));
  //     } else if (!/(?=.*\d)/.test(value)) {
  //       callback(new Error('密码必须包含至少一个数字'));
  //     } else if (!SPECIAL_CHARACTERS.split('').some((char) => value.includes(char))) {
  //       callback(new Error('密码必须包含至少一个特殊字符'));
  //     } else {
  //       callback();
  //     }
  //   };
  const validatePasswordComplexity = (rule, value, callback) => {
    const SPECIAL_CHARACTERS = '!@#$%^&*()_+-=';
    let count = 0;
    if (/[a-z]/.test(value)) count++;
    if (/[A-Z]/.test(value)) count++;
    if (/\d/.test(value)) count++;
    if (SPECIAL_CHARACTERS.split('').some((char) => value.includes(char))) count++;
    if (count >= 3) {
      callback();
    } else {
      let errorMsg =
        '密码至少满足三个条件中的三个：小写字母、大写字母、数字和特殊字符。当前密码不满足要求。';
      if (count > 0) {
        const conditions = [];
        if (/[a-z]/.test(value)) conditions.push('小写字母');
        if (/[A-Z]/.test(value)) conditions.push('大写字母');
        if (/\d/.test(value)) conditions.push('数字');
        if (SPECIAL_CHARACTERS.split('').some((char) => value.includes(char)))
          conditions.push('特殊字符');
        errorMsg = `密码不满足要求，还需满足更多条件。当前密码包含${conditions.join(', ')}。`;
      }
      callback(new Error(errorMsg));
    }
  };

  const rules = ref({
    oldPassword: [{ required: true, message: '旧密码不能为空', trigger: 'blur' }],
    newPassword: [
      { required: true, message: '新密码不能为空', trigger: 'blur' },
      { min: 8, message: '密码长度不少于 8', trigger: 'blur' },
      { required: true, validator: validatePasswordComplexity, trigger: 'blur' },
    ],
    confirmPassword: [
      { required: true, message: '确定密码不能为空', trigger: 'blur' },
      { required: true, validator: equalToPassword, trigger: 'blur' },
    ],
  });

  /** 提交按钮 */
  function submit() {
    proxy.$refs.pwdRef.validate((valid) => {
      if (valid) {
        updateUserPwd(user.oldPassword, user.newPassword).then(() => {
          proxy.$modal.msgSuccess('修改成功');
        });
      }
    });
  }
  /** 关闭按钮 */
  function close() {
    proxy.$tab.closePage();
  }
  const reset = () => {
    user.oldPassword = '';
    user.newPassword = '';
    user.confirmPassword = '';
    proxy.$refs.pwdRef.resetFields();
  };
</script>

<style lang="scss" scoped>
  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
  }
</style>
