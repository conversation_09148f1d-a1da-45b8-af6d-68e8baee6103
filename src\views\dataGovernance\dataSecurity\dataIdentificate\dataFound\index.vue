<template>
  <div class="rule-container">
    <div class="rule-right-box">
      <div class="top" style="text-align: right">
        <div class="search-container">
          <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
            <el-form-item label="任务名称">
              <el-input style="width:200px" v-model="searchForm.taskName" placeholder="请输入名称" clearable />
            </el-form-item>
            <el-form-item label="任务状态">
              <el-select style="width:200px" v-model="searchForm.status" placeholder="请选择" clearable>
                <el-option
                  v-for="dict in taskStatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="margin-right: 5px">
              <span class="table-search-btn">
                <span @click="getList" class="btn btn1"
                  ><el-icon style="color: #fff"><Search /></el-icon
                ></span>
                <span @click="searchReSet" class="btn btn2"
                  ><el-icon style="color: #434343"><Refresh /></el-icon
                ></span>
              </span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="table-container">
        <div class="table-top-box">
          <div>
            <el-button @click="addTask" icon="Plus" type="primary">新增数据发现任务</el-button>
          </div>
          <div><right-toolbar :columns="columns" @query-table="getList"></right-toolbar></div>
        </div>
        <el-table :data="dataList">
          <el-table-column type="index" width="60" label="序号">
            <template #default="scope">
              {{ searchForm.pageSize * (searchForm.pageNum - 1) + (scope.$index + 1) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[0].visible"
            prop="taskName"
            label="任务名称"
          ></el-table-column>
          <el-table-column v-if="columns[1].visible" prop="rangeType" label="数据范围">
            <template #default="scope">
              <div>
                {{ showRangeType(scope.row.rangeType) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120" v-if="columns[3].visible" prop="status" label="运行状态">
            <template #default="scope">
              <div class="table-status">
                <span :class="`task-status-content task-status-${scope.row.status}`">
                  {{ showTaskStatus(scope.row.status) }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[4].visible"
            prop="createTime"
            label="创建时间"
          ></el-table-column>
          <el-table-column
            label="操作"
            class-name="small-padding fixed-width"
            fixed="right"
            width="320"
          >
            <template #default="scope">
              <el-button :disabled="scope.row.status == '1'" @click="runTask(scope.row)" type="text"
                >启动</el-button
              >
              <!-- <el-button
                v-if="scope.row.status == '1'"
                @click="editTask(scope.row)"
                icon="switch-button"
                type="text"
                >停止</el-button
              > -->
              <el-button
                :disabled="scope.row.status == '1'"
                @click="editTask(scope.row)"
                type="text"
                >编辑</el-button
              >
              <el-button :disabled="scope.row.status == '0'" @click="logTask(scope.row)" type="text"
                >日志</el-button
              >
              <el-button :disabled="scope.row.status == '1'" @click="delTask(scope.row)" type="text"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          v-model:page="searchForm.pageNum"
          v-model:limit="searchForm.pageSize"
          :total="total"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 新增数据发现任务 -->
    <el-dialog
      v-model="addTaskDialog"
      :title="addTaskDialogTitle"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeAddTaskDialog"
    >
      <el-form ref="formRef" :rules="rule" :model="form" label-width="auto" label-position="right">
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model.trim="form.taskName"
            @input="(data) => (form.taskName = data.replace(/\s/g, ''))"
            placeholder="请输入任务名称"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            v-model.trim="form.remark"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="数据范围" prop="rangeType">
          <el-radio-group v-model="form.rangeType" @change="getTreeData">
            <el-radio label="0">技术资产</el-radio>
            <el-radio label="1">业务资产</el-radio>
          </el-radio-group>
          <div v-if="form.rangeType == '0'" class="tree-container">
            <el-tree
              ref="techRef"
              class="tree-border"
              :data="dataTreeForGroup"
              show-checkbox
              node-key="nodeKey"
              :check-strictly="false"
              empty-text="暂无数据"
              :props="{ label: 'label', children: 'children' }"
            ></el-tree>
          </div>
          <div v-else class="tree-container">
            <el-tree
              ref="categoryRef"
              class="tree-border"
              :data="dataTreeForGroup"
              show-checkbox
              node-key="id"
              :check-strictly="false"
              empty-text="暂无数据"
              :props="{ label: 'label', children: 'children' }"
            ></el-tree>
          </div>
        </el-form-item>

        <el-form-item style="margin-top: 20px" label="敏感数据类型分组" prop="sensitiveGroupId">
          <div class="tree-container group-box">
            <el-tree
              ref="groupRef"
              class="tree-border"
              :data="groupOptions"
              show-checkbox
              node-key="id"
              :check-strictly="false"
              empty-text="加载中，请稍候"
              :props="{ label: 'groupName', children: 'children' }"
            ></el-tree>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAddTaskDialog">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="addTaskCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 日志框 -->
    <el-drawer
      :close-on-click-modal="false"
      v-model="logDialog"
      title="日志"
      direction="rtl"
      size="50%"
      @close="openLogClose"
    >
      <div class="log-container">
        <el-table :data="logData">
          <el-table-column type="index" width="60" label="序号">
            <template #default="scope">
              {{ logParam.pageSize * (logParam.pageNum - 1) + (scope.$index + 1) }}
            </template>
          </el-table-column>
          <el-table-column label="开始时间" prop="startTime" />
          <el-table-column label="结束时间" prop="endTime" />
          <el-table-column label="执行结果">
            <template #default="scope">
              <div :class="`log-status-content log-status-${scope.row.result}`">
                {{ showTaskStatus(scope.row.result) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发现敏感字段数量" prop="discoveriesCount"></el-table-column>
        </el-table>

        <pagination
          v-show="logTotal > 0"
          v-model:page="logParam.pageNum"
          v-model:limit="logParam.pageSize"
          :total="logTotal"
          @pagination="getLogList(logId)"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
  import { getUserProfile } from '@/api/system/user';
  import { getCatalogTree } from '@/api/datamodel';
  import {
    getSensitiveGroup,
    getTechnologyTree,
    getSensitiveTask,
    addSensitiveTask,
    updateSensitiveTask,
    deleteSensitiveTask,
    getSensitiveTaskInfo,
    runSensitiveTask,
    logSensitiveTask,
  } from '@/api/dataGovernance';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getCurrentInstance } from 'vue';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();
  const categoryRef = ref(null);
  const techRef = ref(null);
  const groupRef = ref(null);
  // const props = defineProps({
  //   title:{
  //     type:String,
  //     default:''
  //   }
  // })

  const searchForm = ref({ pageSize: 20, pageNum: 1, taskName: null, status: null });
  const dataList = ref([]);
  const total = ref(0);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `任务名称`, visible: true },
    { key: 1, label: `数据范围`, visible: true },
    { key: 3, label: `运行状态`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
    { key: 5, label: `备注`, visible: true },
  ]);

  const getList = async () => {
    const data = {
      ...searchForm.value,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const res = await getSensitiveTask(data);
    dataList.value = res.rows;
    total.value = res.total;
  };

  const showTaskStatus = (type) => {
    return taskStatus.find((item) => item.value === type)?.label;
  };

  const showRangeType = (type) => {
    return rangeTypeList.find((item) => item.value === type)?.label;
  };

  const taskStatus = [
    { label: '未启动', value: '0' },
    {
      label: '运行中',
      value: '1',
    },
    {
      label: '成功',
      value: '2',
    },
    {
      label: '失败',
      value: '3',
    },
  ];

  const rangeTypeList = [
    { label: '技术资产', value: '0' },
    {
      label: '业务资产',
      value: '1',
    },
  ];

  const searchReSet = () => {
    searchForm.value.pageNum = 1;
    searchForm.value.taskName = null;
    searchForm.value.status = null;
    getList();
  };

  const form = ref({
    taskName: null,
    remark: null,
    rangeType: '0',
  });

  const validateTreeForRangeType = (rule, value, callback) => {
    let arr;
    if (categoryRef.value) {
      arr = groupRef.value.getCheckedKeys();
    } else {
      arr = techRef.value.getCheckedKeys();
    }
    if (arr.length == 0 || !arr) {
      callback(new Error('请勾选资产数据'));
    } else {
      callback();
    }
  };

  const validateTreeForGroup = (rule, value, callback) => {
    let arr = groupRef.value.getCheckedKeys();
    if (arr.length == 0 || !arr) {
      callback(new Error('请勾选分组'));
    } else {
      callback();
    }
  };
  const rule = reactive({
    taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
    rangeType: [{ required: true, validator: validateTreeForRangeType, trigger: 'blur' }],
    sensitiveGroupId: [{ required: true, validator: validateTreeForGroup, trigger: 'blur' }],
  });

  const addTaskDialog = ref(false);
  const addTaskDialogTitle = ref('新增任务');

  const closeAddTaskDialog = () => {
    if (categoryRef.value != undefined) {
      categoryRef.value.setCheckedKeys([]);
    }
    if (techRef.value != undefined) {
      techRef.value.setCheckedKeys([]);
    }
    if (groupRef.value != undefined) {
      groupRef.value.setCheckedKeys([]);
    }
    proxy.resetForm('formRef');
    dataTreeForGroup.value = [];
    groupOptions.value = [];
    loading.value = false;
    addTaskDialog.value = false;
  };
  const addTask = async () => {
    addTaskDialogTitle.value = '新增任务';
    await getGroupsTree();
    await getTechTree();
    form.value.taskName = null;
    form.value.remark = null;
    form.value.sensitiveGroupId = null;
    form.value.id = null;
    form.value.rangeType = '0';
    addTaskDialog.value = true;
  };

  const editTask = async (row) => {
    addTaskDialogTitle.value = '编辑任务';
    await getGroupsTree();
    const resForInfo = await getSensitiveTaskInfo({ id: row.id });
    for (let key in form.value) {
      form.value[key] = resForInfo.data[key];
    }
    form.value.id = row.id;
    form.value.status = row.status;
    await getTreeData(resForInfo.data.rangeType);
    addTaskDialog.value = true;
    nextTick(() => {
      // 回显 tree 的勾选数据
      resForInfo.data.assets.forEach((v) => {
        nextTick(() => {
          if (resForInfo.data.rangeType == '0') {
            if (v.databaseName) {
              techRef.value.setChecked(`${v.assetId}-${v.databaseName}`, true, false);
            } else {
              techRef.value.setChecked(v.assetId, true, false);
            }
          } else {
            categoryRef.value.setChecked(v.assetId, true, false);
          }
        });
      });
      resForInfo.data.sensitiveGroupId.forEach((v) => {
        nextTick(() => {
          groupRef.value.setChecked(v, true, false);
        });
      });
    });
  };

  const delTask = async (row) => {
    proxy
      .$confirm('任务删除后，对应的执行记录和结果都将删除', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(async () => {
        const res = await deleteSensitiveTask({ id: row.id });
        if (res.code != 200) return;
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(() => {});
  };

  const runTask = async (row) => {
    const res = await runSensitiveTask({ id: row.id });
    if (res.code != 200) return;
    proxy.$modal.msgSuccess('启动成功');
    getList();
  };

  const loading = ref(false);

  const addTaskCommit = async () => {
    const confirm = await proxy.$refs.formRef.validate((valid) => valid);
    if (!confirm) return;
    loading.value = true;
    // 获取tree选中的叶节点的key数组
    let assetIds;
    let paramForIds;
    if (form.value.rangeType == '0') {
      assetIds = techRef.value.getCheckedNodes(true);
      paramForIds = assetIds.map((res) => {
        if (res.level == 'database') {
          return { assetId: res.datasourceId, databaseName: res.label };
        } else {
          return { assetId: res.datasourceId };
        }
      });
    } else {
      assetIds = categoryRef.value.getCheckedKeys(true);
      paramForIds = assetIds.map((res) => {
        return { assetId: res };
      });
    }

    const sensitiveGroupId = groupRef.value.getCheckedKeys(true);
    const data = {
      taskName: form.value.taskName,
      rangeType: form.value.rangeType,
      remark: form.value.remark,
      workspaceId: workspaceId.value,
      assetIds: paramForIds,
      sensitiveGroupId: sensitiveGroupId,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    try {
      if (form.value.id) {
        data.id = form.value.id;
        data.status = form.value.status;
        const resForUpdate = await updateSensitiveTask(data);
        if (resForUpdate.code != 200) return proxy.$modal.msgError(resForUpdate.msg);
        proxy.$modal.msgSuccess('修改成功');
        addTaskDialog.value = false;
        getList();
      } else {
        const resForAdd = await addSensitiveTask(data);
        if (resForAdd.code != 200) return proxy.$modal.msgError(resForAdd.msg);
        proxy.$modal.msgSuccess('新增成功');
        addTaskDialog.value = false;
        getList();
      }
    } catch {
      loading.value = false;
    }
  };

  const logDialog = ref(false);
  const logData = ref([]);
  const logTotal = ref(0);
  const logParam = ref({
    pageSize: 20,
    pageNum: 1,
  });

  // 日志
  const logId = ref(null);
  const logTask = async (row) => {
    logParam.value.pageNum = 1;
    logId.value = row.id;
    await getLogList(row.id);
    logDialog.value = true;
  };

  const getLogList = (id) => {
    const data = {
      pageSize: logParam.value.pageSize,
      pageNum: logParam.value.pageNum,
      taskId: id,
    };
    logSensitiveTask(data)
      .then((res) => {
        logData.value = res.rows;
        logTotal.value = res.total;
      })
      .catch(() => {});
  };

  /**
   * 关闭日志
   */
  const openLogClose = () => {
    logParam.value.pageNum = 1;
    logTotal.value = 0;
    logData.value = [];
    logDialog.value = false;
  };

  const getTreeData = async (data) => {
    if (categoryRef.value != undefined) {
      categoryRef.value.setCheckedKeys([]);
    }
    if (techRef.value != undefined) {
      techRef.value.setCheckedKeys([]);
    }
    if (data == '1') {
      await getCatalogTreeUtil();
    } else {
      await getTechTree();
    }
  };

  const dataTreeForGroup = ref([]);
  // 业务资产
  const getCatalogTreeUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      themeMenuFlg: true,
    };
    const res = await getCatalogTree(query);
    dataTreeForGroup.value = res.data;
  };
  //技术资产
  const getTechTree = async () => {
    let data = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resData = await getTechnologyTree(data);

    dataTreeForGroup.value = jsonToTree(resData.data);
  };

  const jsonToTree = (data) => {
    let treeData;
    if (data instanceof Object) {
      treeData = data.map((res) => {
        let objData;
        // 后端返回 库名列表 没有 层级就展示到数据源名称
        if (res.databaseNameList?.length) {
          objData = res.databaseNameList.map((k) => {
            return {
              label: k,
              children: [],
              datasourceId: res.datasourceId,
              level: 'database',
              nodeKey: `${res.datasourceId}-${k}`,
            };
          });
        } else {
          objData = [];
        }
        return {
          label: res.datasourceType ? res.datasourceType : res.datasourceName,
          children: res.children.length ? jsonToTree(res.children) : objData,
          datasourceId: res.datasourceId,
          nodeKey: res.datasourceId ?? res.datasourceType,
        };
      });
      return treeData;
    } else {
      return [];
    }
  };

  const groupOptions = ref([]);
  // 获取分组树
  const getGroupsTree = async () => {
    let data = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resData = await getSensitiveGroup(data);
    groupOptions.value = resData.data;
  };

  let userInfo = reactive({});
  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    getList();
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .rule-container {
    padding: 0px 20px;
    .btn {
      cursor: pointer;
      display: inline-block;
      width: 32px;
      height: 32px;
      padding: 0 10px;
      border-radius: 20px;
      line-height: 36px;
      &.btn1 {
        background: #1269ff;
        margin-right: 10px;
      }
      &.btn2 {
        background: #dce5f5;
      }
    }
    .table-top-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .table-status {
      position: relative;
      padding-left: 18px;
      height: 24px;
      // width: 48px;
      // height: 24px;
      & > span {
        height: 20px;
        line-height: 1;
        color: $--base-color-green;
        background-color: $--base-color-green-disable;
        display: inline-block;
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
        &.task-status-0 {
          color: $--base-color-text2;
          background: $--base-color-tag-bg;
          &::before {
            border: 3px solid $--base-color-text2;
          }
        }
        &.task-status-1 {
          color: $--base-color-primary;
          background-color: $--base-color-tag-primary;
          &::before {
            border: 3px solid $--base-color-primary;
          }
        }
        &.task-status-2 {
          color: $--base-color-green;
        }
        &.task-status-3 {
          color: $--base-btn-red-text;
          background-color: $--base-btn-red-bg;
          &::before {
            border: 3px solid $--base-btn-red-text;
          }
        }
        &::before {
          content: '';
          width: 12px;
          height: 12px;
          border: 3px solid $--base-color-green;
          border-radius: 6px;
          position: absolute;
          top: calc(50% - 6px);
          left: 0px;
        }
      }
    }
  }
  :deep(.el-tree.tree-border) {
    border: none !important;
  }
  .tree-container {
    width: 500px;
    padding: 10px;
    padding: 10px;
    border-radius: 8px;
    background: #f7f8fb;
  }

  :deep(.el-drawer__header) {
    padding: 20px 12px !important;
    background: #f7f8fb !important;
  }

  .log-container {
    // height: 800px;
    // padding: 20px;
    // border-radius: 8px;
    // background: #f7f8fb;
    .log-status-content {
      &.log-status-2 {
        color: $--base-color-green;
      }
      &.log-status-3 {
        color: $--base-btn-red-text;
      }
    }
  }
</style>
