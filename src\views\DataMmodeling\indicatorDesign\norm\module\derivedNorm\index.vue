<template>
  <div class="container">
    <el-row>
      <el-col :span="24">
        <el-button type="text" @click="toBack"> {{ '<' }} 返回上一层</el-button>
      </el-col>
    </el-row>
    <div class="demo-tabs">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="指标配置" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
          >
            <el-form-item label="原子指标" prop="targetId">
              <!-- 下拉框 -->
              <el-select
                v-model="form.targetId"
                placeholder="请选择"
                clearable
                @change="onChangeTarget(form.targetId)"
              >
                <el-option
                  v-for="item in TargetList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间限定" prop="limitTimeField">
              <!-- 下拉框 -->
              <el-select v-model="form.presetLimitTime" placeholder="请选择时间限定">
                <el-option
                  v-for="item in TimeRestrictList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>

              <el-select
                v-model="form.limitTimeField"
                style="margin-top: 15px"
                placeholder="请选择关联字段"
              >
                <el-option
                  v-for="item in Relatedfields"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="修饰限定" prop="limitEmbellishId">
              <createRule
                :object="object"
                :decorate-list="decorateList"
                :field-list="RelatedfieldsCopy"
              >
              </createRule>
            </el-form-item>

            <el-form-item label="所属主题" prop="timeUnit">
              {{ nodeClick?.label }}
            </el-form-item>
            <el-form-item label="衍生指标名称" prop="code">
              <!-- 输入框 -->
              <el-input v-model="form.code" type="text" show-word-limit></el-input>
            </el-form-item>

            <el-form-item label="指标中文名称" prop="name">
              <el-input v-model="form.name" type="text" show-word-limit></el-input>
            </el-form-item>

            <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <div class="bont">
          <!-- 分割线 -->
          <el-divider></el-divider>
          <el-button type="primary" @click="fulfill"> 完成</el-button>
          <el-button plain @click="toBack">关闭</el-button>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
  import {
    getDwDatabaseList,
    getFieldsByModel,
    getFiledType,
    getGlobalVarListByModel,
    getListEmbell,
    getTargetListPlus,
    previewSql,
  } from '@/api/datamodel';
  import createRule from '@/components/createRule';

  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { nodeClick, workspaceId, rowData } = toRefs(props);
  const { proxy } = getCurrentInstance();
  const { time_limited_lists } = proxy.useDict('time_limited_lists');

  const object = ref({
    rules: 'and',
    list: [],
  });
  console.log(nodeClick.value);
  console.log(workspaceId.value);
  console.log(rowData.value);
  const emit = defineEmits();

  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      targetId: [{ required: true, message: '请选择原子指标', trigger: 'blur' }],
      limitTimeField: [{ required: true, message: '请选择时间限定', trigger: 'blur' }],
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],

      code: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
        // 校验
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      store: [{ required: true, message: '请选择储存库', trigger: 'blur' }],
      isExternal: [{ required: true, message: '请选择建模方式', trigger: 'blur' }],
      type: [{ required: true, message: '请选择存储类型', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });
  const dwDatabaseList = ref([]);
  const codeData = ref();
  const drawer = ref(false);
  const getDwDatabaseListUtil = async (data) => {
    const res = await getDwDatabaseList({ catalogId: data });
    dwDatabaseList.value = res.data.map((item) => item);
  };

  const { form, rules, queryParams } = toRefs(data);
  const toBack = () => {
    emit('toBack', true);
  };

  const timeunitList = ref([
    { value: 'year', label: '按 年' },
    { value: 'month', label: '按 月' },
    { value: 'day', label: '按 日' },
    { value: 'hour', label: '按 时' },
    { value: 'minute', label: '按 分' },
    { value: 'second', label: '按 秒' },
  ]);
  const codetableField = ref([
    {
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: null,
      notNul: null,
      partitionField: true,
    },
  ]);

  const activeName = ref('first');
  const tableData = ref([]);

  const beforeleave = (tab, oldTab) => {
    console.log(tab, oldTab);
    return new Promise((resolve) => {
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.name &&
        form.value.code &&
        form.value.dwDatabase &&
        form.value.isExternal &&
        form.value.isFromDatasource
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const handleClick = (tab, event) => {};
  const addStep = async () => {
    const res = await beforeleave();
    if (!res) {
      return proxy.$modal.msgWarning('请先完善基本配置');
    }
    activeName.value = 'second';
  };
  const delStep = () => {
    console.log(1);
    activeName.value = 'first';
  };
  const fulfill = async () => {
    console.log(object.value);

    const res = await proxy.$refs.formRef.validate((valid) => valid);

    console.log(res);

    if (!res) return;

    const data = {
      targetId: form.value.targetId,
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      presetLimitTime: form.value.presetLimitTime,
      limitEmbellishId: form.value.limitEmbellishId,

      limitTimeField: form.value.limitTimeField,
      limitEmbellishField: form.value.limitEmbellishField,
      embellishLimitRule: object.value,
    };

    console.log(data);
    console.log(object.value);

    console.log(rowData.value);
    if (rowData.value && rowData.value.name) {
      emit('fulfill', data, 'derivedEdit');
    } else {
      emit('fulfill', data, 'derived');
    }
  };

  const delTableData = (row) => {
    tableData.value.splice(tableData.value.indexOf(row), 1);
  };
  const addTableData = (e) => {
    console.log(e);
    tableData.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: false,
      partitionField: false,
      notNul: false,
      workspaceId: workspaceId.value,
    });
  };

  const handleEdit = (row) => {
    console.log(row);
  };

  const delCodetableField = (row) => {
    codetableField.value.splice(codetableField.value.indexOf(row), 1);
  };

  const quickSelect = ref();
  const customTimeOne = ref();
  const customTimeTwo = ref();

  const cascaderOptions = ref([]);
  // watch form.datasheet
  watch(
    () => tableData.value,
    (val) => {
      cascaderOptions.value = tableData.value.map((item) => {
        return {
          value: item.columnName,
          label: item.columnName,
        };
      });
    },
  );

  watch(
    () => nodeClick.value,
    (val) => {
      nextTick(() => {
        getDwDatabaseListUtil(val.key);
      });
    },
    {
      deep: true,
    },
  );

  const partitionCodeList = ref([]);

  const handleChange = (e) => {
    console.log(e);
  };

  const addCodetableField = () => {
    codetableField.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: null,
      partitionField: true,
      notNul: null,
    });
  };

  const customerIdList = ref([]);
  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'hive',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  const open = async () => {
    drawer.value = true;

    const data = {
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      dwLevel: form.value.dwLevel,

      isFromDatasource: form.value.isFromDatasource == 1,

      dwDatabase: form.value.dwDatabase,

      databaseName: form.value.database,
      datasourceId: form.value.dataSource,
      isExternal: form.value.isExternal == 1,
      tableName: form.value.datasheet,
      fieldBoList: form.value.datasheet ? [] : tableData.value,
      workspaceId: workspaceId.value,
    };
    await previewSqlUtil(data);
  };
  const previewSqlUtil = async (data) => {
    codeData.value = '暂无数据';
    const res = await previewSql(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    codeData.value = res.data;
  };
  const getGlobalVarListByModelUtil = async () => {
    const res = await getGlobalVarListByModel();
    // console.log(res)
    partitionCodeList.value = res.data.map((item) => {
      return {
        value: item.code,
        label: item.name,
        ...item,
      };
    });
  };

  /** 原子指标列表 */
  const TargetList = ref([]);

  /** 获取原子指标列表 */
  const getTargetListUtil = async () => {
    console.log(999999999999999);
    const res = await getTargetListPlus({
      workspaceId: workspaceId.value,
      catalogId: nodeClick.value.key,
      themeId: nodeClick.value.key,
    });
    console.log(res);

    TargetList.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.code + '(' + item.name + ')',
        ...item,
      };
    });
    console.log(TargetList.value);
  };

  /** 时间限定列表 */
  const TimeRestrictList = ref(time_limited_lists);
  /** 获取时间限定列表 */
  const getTimeRestrictListUtil = async () => {
    // const res = await getTimeRestrictVoList({
    //     workspaceId: workspaceId.value
    // })
    // TimeRestrictList.value = res.data.map(item => {
    //     return {
    //         value: item.id,
    //         label: item.name
    //     }
    // });
  };
  /** 修饰限定列表 */
  const decorateList = ref();

  /** 获取修饰限定列表 */
  const getEmbellishRestrictListUtil = async () => {
    const res = await getListEmbell({
      workspaceId: workspaceId.value,
      catalogId: nodeClick.value.key,
    });
    console.log(res);
    decorateList.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.name + '(' + item.code + ')',
      };
    });
  };

  /** 时间 - 修饰 关联字段 */
  const Relatedfields = ref();
  const RelatedfieldsCopy = ref();
  const onChangeTarget = async (data) => {
    console.log(data);
    if (!data) return;
    // 使用 data 找到 TargetList 里的 modelId
    console.log(TargetList.value);
    const modelId = TargetList.value.find((item) => item.id == data)?.modelId;
    console.log(modelId);

    const res = await getFieldsByModel({ modelId, whichType: 'timeField' });
    const re = await getFieldsByModel({ modelId });
    console.log(res);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (re.code !== 200) return proxy.$modal.msgError(re.msg);
    Relatedfields.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.name + '(' + item.code + ')',
      };
    });
    RelatedfieldsCopy.value = re.data.map((item) => {
      return {
        value: item.id,
        label: item.name + '(' + item.code + ')',
      };
    });
  };

  onMounted(async () => {
    await getTargetListUtil();
    await getTimeRestrictListUtil();
    await getEmbellishRestrictListUtil();
    if (rowData.value && rowData.value.name) {
      form.value.targetId = rowData.value.targetId;
      onChangeTarget(rowData.value.targetId);
      form.value.name = rowData.value.name;
      form.value.code = rowData.value.code;
      form.value.remark = rowData.value.remark;
      form.value.presetLimitTime = rowData.value.presetLimitTime;
      form.value.limitEmbellishId = rowData.value.limitEmbellishId;
      form.value.limitTimeField = rowData.value.limitTimeField.id;
      form.value.limitEmbellishField = rowData.value.limitEmbellishField;
      object.value = rowData.value?.embellishLimitRule
        ? rowData.value.embellishLimitRule
        : object.value;
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 10px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    margin: 5px;
  }

  .demo-tabs {
    padding: 20px 0;
    :deep .el-tabs__content {
      padding: 20px;
      background: #ffffff;
      border-radius: 8px;
    }
  }
  .bont {
    text-align: right;
    padding: 10px 0;
  }

  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .codeCs {
    padding: 10px;
    white-space: pre;
    font-family: 'Courier New', monospace;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .rgroup {
    // border-top: 1px solid #0400ff3f;
    // border-left: 1px solid #E6E6E6;
    // border-right: 1px solid #E6E6E6;
    // border-bottom: 1px solid #E6E6E6;
    padding: 10px;
  }

  .boxLimit {
    display: flex;
    flex-direction: column;

    .el-select {
      margin-bottom: 10px;
    }

    .el-divider {
      margin: 10px 0;
    }

    .el-button {
      margin-bottom: 10px;
    }
  }
</style>
