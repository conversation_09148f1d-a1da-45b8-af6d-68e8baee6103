<template>
  <div class="app-container" @contextmenu.prevent="$event.preventDefault()">
    <!-- 调度任务管理 -->
    <HeadTitle :title="HeadTitleName" />

    <el-row gutter="20">
      <el-col :span="4"></el-col>
      <el-col :span="20">
        <el-row gutter="20">
          <el-col :span="10">
            <el-button type="primary">新增</el-button>
            <el-button type="primary">编辑</el-button>
            <el-button type="primary">删除</el-button>
          </el-col>
          <el-col :span="14">
            <el-form-item label="算子名称" prop="userName">
              <el-input
                v-model="HeadTitleName"
                placeholder="请输入算子名称"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="tree-container">
        <div class="head-container" @contextmenu.prevent="$event.preventDefault()">
          <el-tree
            ref="treeRef"
            :data="dataTree"
            :props="defaultProps"
            node-key="id"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span v-if="node.level == 1" @contextmenu="showContextMenu($event, data, node)">
                🌖
                {{ data.label }}
              </span>
              <span v-if="node.level == 2" @contextmenu="showContextMenu($event, data, node)">
                🌗
                {{ data.label }}
              </span>
              <span v-if="node.level == 3" @contextmenu="showContextMenu($event, data, node)">
                🌘
                {{ data.label }}
              </span>
            </template>
          </el-tree>
          <div
            v-if="showMenu"
            class="custom-menu"
            :style="{ top: `${menuY}px`, left: `${menuX}px` }"
          >
            <a @click="append">Append</a>
            <a @click="remove">Delete</a>
            <a @click="rename">Rename</a>
          </div>
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <!--  表单信息 -->
        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            key="userName"
            label="算子名称"
            prop="userName"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <el-tag @click="toDataList(scope.row)">{{ scope.row.userName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column key="nickName" label="流程类型" prop="nickName" />
          <el-table-column
            key="deptName"
            label="创建人"
            prop="dept.deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="启用状态" width="120">
            <template #default="scope">
              <el-switch v-model="value1" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120" />
          <el-table-column label="更新时间" width="120" />
          <el-table-column label="操作" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button type="text">测试连接</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <!-- <el-row :gutter="10" class="mb8"> -->
        <!-- <el-col :span="1.5"> -->
        <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" -->
        <!-- v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
        <!-- </el-col> -->
        <!-- </el-row> -->
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { listUser } from '@/api/system/user';
  import HeadTitle from '@/components/HeadTitle';
  const HeadTitleName = '调度任务管理';
  const userList = ref([]);
  const loading = ref(true);
  const { proxy } = getCurrentInstance();
  const dateRange = ref([]);
  const total = ref(0);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '用户名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const dataTree = ref([
    {
      label: 'Level one 1',
      children: [
        {
          label: 'Level two 1-1',
          children: [
            {
              label: 'Level three 1-1-1',
            },
          ],
        },
      ],
    },
    {
      label: 'Level one 2',
      children: [
        {
          label: 'Level two 2-1',
          children: [
            {
              label: 'Level three 2-1-1',
            },
          ],
        },
        {
          label: 'Level two 2-2',
          children: [
            {
              label: 'Level three 2-2-1',
            },
          ],
        },
      ],
    },
    {
      label: 'Level one 3',
      children: [
        {
          label: 'Level two 3-1',
          children: [
            {
              label: 'Level three 3-1-1',
            },
          ],
        },
        {
          label: 'Level two 3-2',
          children: [
            {
              label: 'Level three 3-2-1',
            },
          ],
        },
      ],
    },
  ]);

  /** 查询用户列表 */
  function getList() {
    loading.value = true;
    listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then((res) => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    });
  }

  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  getList();
</script>

<style lang="scss" scoped></style>
