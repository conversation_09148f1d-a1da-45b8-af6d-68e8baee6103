<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" /> -->

    <el-row :gutter="20">
      <!--部门数据-->
      <!-- <el-col :span="4" :xs="24"> -->
      <!-- <div class="head-container"> -->
      <!-- <el-input v-model="deptName" placeholder="请输入部门名称" clearable prefix-icon="Search" -->
      <!-- style="margin-bottom: 20px" /> -->
      <!-- </div> -->
      <!-- <div class="head-container"> -->
      <!-- <el-tree :data="deptOptions" :props="{ label: 'label', children: 'children' }" :expand-on-click-node="false" -->
      <!-- :filter-node-method="filterNode" ref="deptTreeRef" node-key="id" highlight-current default-expand-all -->
      <!-- @node-click="handleNodeClick" /> -->
      <!-- </div> -->
      <!-- </el-col> -->
      <!--用户数据-->
      <!-- <el-col :span="20" :xs="24"> -->
      <el-col :span="24" :xs="24">
        <el-form
          v-show="showSearch"
          ref="queryRef"
          class="search-box"
          :model="queryParams"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入用户名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item v-if="user_type == 'sys_user'" label="租户" prop="phonenumber">
            <el-select v-model="queryParams.tenantId" placeholder="请选择" style="width: 240px">
              <el-option
                v-for="dict in tenementList"
                :key="dict.tenantId"
                :label="dict.tenantName"
                :value="dict.tenantId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item />
          <el-form-item label="创建时间" style="width: 308px">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              :disabled-date="disablesDate"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:add']"
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              >新增用户</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" -->
            <!-- v-hasPermi="['system:user:edit']">修改</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:remove']"
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              >批量删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button type="info" plain icon="Upload" @click="handleImport" -->
            <!-- v-hasPermi="['system:user:import']">导入</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button type="warning" plain icon="Download" @click="handleExport" -->
            <!-- v-hasPermi="['system:user:export']">导出</el-button> -->
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            :columns="columns"
            @query-table="getList"
          ></right-toolbar>
        </el-row>

        <div class="table-box">
          <el-table
            v-loading="loading"
            :data="userList"
            height="100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" label="序号" width="60">
              <template #default="scope">
                {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns[0].visible"
              key="userName"
              label="用户账号"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
              width="220"
            />
            <el-table-column
              v-if="columns[1].visible"
              key="nickName"
              label="用户昵称"
              prop="nickName"
              :show-overflow-tooltip="true"
              width="220"
            />
            <el-table-column
              v-if="user_type == 'sys_user'"
              key="tenantName"
              label="租户"
              prop="tenantName"
              :show-overflow-tooltip="true"
              width="220"
            />
            <el-table-column
              v-if="columns[2].visible"
              key="tenantName"
              label="用户类型"
              prop="userType"
              :show-overflow-tooltip="true"
              width="220"
            >
              <template #default="scope">
                <el-tag :class="tagClass(scope.row.userType)">{{
                  userTypeText(scope.row.userType)
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="columns[3].visible" key="status" label="状态" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  active-icon=""
                  inactive-icon=""
                  style="--el-switch-on-color: #13ce66"
                  :disabled="scope.row.userName == userInfo.userName"
                  @change="handleStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns[4].visible"
              label="创建时间"
              align="center"
              prop="createTime"
              :show-overflow-tooltip="true"
              width="220"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns[5].visible"
              label="更新时间"
              align="center"
              prop="updateTime"
              :show-overflow-tooltip="true"
              width="220"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="columns[6].visible"
              key="userName"
              label="邮箱"
              align="center"
              prop="email"
              :show-overflow-tooltip="true"
              width="220"
            />
            <el-table-column
              v-if="columns[7].visible"
              key="userName"
              label="备注"
              align="center"
              prop="remark"
              :show-overflow-tooltip="true"
              width="220"
            />
            <el-table-column
              label="操作"
              align="center"
              min-width="220"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template #default="scope">
                <!-- <el-tooltip content="修改" placement="top"> -->
                <el-button
                  v-hasPermi="['system:user:edit']"
                  link
                  type="primary"
                  :disabled="
                    scope.row.userType == 'tenant_admin_user' && user_type == 'tenant_admin_user'
                  "
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <!-- </el-tooltip> -->
                <!-- <el-tooltip content="删除" placement="top"> -->
                <el-button
                  v-if="scope.row.userId !== 1"
                  v-hasPermi="['system:user:remove']"
                  link
                  type="danger"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
                <!-- </el-tooltip> -->
                <!-- <el-tooltip content="重置密码" placement="top" > -->
                <el-button
                  v-if="scope.row.userId !== 1"
                  v-hasPermi="['system:user:resetPwd']"
                  link
                  type="primary"
                  @click="handleResetPwd(scope.row)"
                  >重置密码</el-button
                >
                <!-- </el-tooltip> -->
                <!-- <el-tooltip content="分配角色" placement="top" v-if="scope.row.userId !== 1"> -->
                <!-- <el-button link type="primary" icon="CircleCheck" @click="handleAuthRole(scope.row)" -->
                <!-- v-hasPermi="['system:user:edit']"></el-button> -->
                <!-- </el-tooltip> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" width="700px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px" class="formDialog">
        <el-row>
          <el-col v-if="form.userId == undefined" :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户账号" prop="userName">
              <el-input
                v-model="form.userName"
                placeholder="由6-30位字母与数字组成"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input
                v-model="form.nickName"
                placeholder="由中文、字母、数字、下划线1-16位"
                maxlength="16"
              />
              <!-- <small>仅可使用中文、字母、数字、下划线</small> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="form.userId == undefined" :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请填写密码"
                type="password"
                show-password
              />
              <!-- <small style="color: gray">(若不填写，默认密码为 xugu123456) </small> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="user_type != 'tenant_admin_user'">
          <el-col :span="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select
                v-model="form.userType"
                placeholder="请选择"
                :disabled="form.userId"
                size="mini"
              >
                <!--  -->
                <el-option
                  v-for="dict in sys_user_type"
                  v-if="user_type == 'sys_user'"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />

                <el-option
                  v-for="dict in sysUserType"
                  v-else
                  :key="'' + dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
                <!--  -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userType !== 'sys_user'" label="选择租户" prop="tenantId">
              <el-select v-model="form.tenantId" placeholder="请选择" :disabled="form.userId">
                <el-option
                  v-for="dict in tenementList"
                  :key="dict.tenantId"
                  :label="dict.tenantName"
                  :value="dict.tenantId"
                />

                <!-- <el-option v-for="dict in tenementListMock" :key="'' + dict.tenantId" :label="dict.tenantName" -->
                <!-- :value="dict.tenantId" v-else /> -->
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设置角色" prop="roleIds">
              <el-select v-model="form.roleIds" multiple placeholder="请选择">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-row> -->
        <!-- <el-col :span="12"> -->
        <!-- <el-form-item label="岗位"> -->
        <!-- <el-select v-model="form.postIds" multiple placeholder="请选择"> -->
        <!-- <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName" -->
        <!-- :value="item.postId" :disabled="item.status == 1"></el-option> -->
        <!-- </el-select> -->
        <!-- </el-form-item> -->
        <!-- </el-col> -->
        <!-- </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
            </div>
            <span>仅允许导入 xls、xlsx 格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="resetPwdDialogVisible"
      title="重置密码"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-card style="background-color: #f0f4f9; margin-bottom: 20px">
        <div>
          <b>用户名称:{{ resetPwdForm.userName }}</b>
          |
          <el-tag :class="tagClass(resetPwdForm.userType)">{{
            userTypeText(resetPwdForm.userType)
          }}</el-tag>
        </div>
        <small>{{ resetPwdForm.email }}</small>
      </el-card>
      <el-form
        ref="resetPwdFormRef"
        :model="resetPwdForm"
        :rules="resetPwdRules"
        label-width="100px"
      >
        <!-- <el-form-item label="用户名"> -->
        <!-- <el-input v-model="resetPwdForm.userName" disabled></el-input> -->
        <!-- </el-form-item> -->
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPwdForm.newPassword"
            type="password"
            show-password
            placeholder=" 请输入密码 密码不少于 6 位"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPwdForm.confirmPassword"
            type="password"
            show-password
            placeholder=" 请输入密码 密码不少于 6 位"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPwdDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitResetPwd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import {
    addUser,
    changeUserStatus,
    delUser,
    getTenantList,
    getTenantRoles,
    getUser,
    getUserProfile,
    listUser,
    resetUserPwd,
    updateUser,
  } from '@/api/system/user';
  import HeadTitle from '@/components/HeadTitle';
  import { getToken } from '@/utils/auth';
  import { ElMessage } from 'element-plus';
  const HeadTitleName = ref('用户管理');
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_type } = proxy.useDict(
    'sys_normal_disable',
    'sys_user_sex',
    'sys_user_type',
  );
  const user_type = ref();
  const tenementList = ref([]);

  const sysUserType = ref([
    {
      label: '普通用户',
      value: 'normal_user',
    },
  ]);
  const userList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const deptName = ref('');
  const initPassword = ref(undefined);
  const postOptions = ref([]);
  const roleOptions = ref([]);
  /** * 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData',
  });
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `账号`, visible: true },
    { key: 1, label: `昵称`, visible: true },
    { key: 2, label: `类型`, visible: true },
    { key: 3, label: `状态`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
    { key: 6, label: `邮箱`, visible: true },
    { key: 7, label: `备注`, visible: true },
  ]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
      tenantId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '用户账号名称不能为空', trigger: 'blur' },
        { min: 6, max: 30, message: '用户名称长度必须介于 6 和 30 之间', trigger: 'blur' },
        // 仅可使用中文、字母、数字、下划线
        {
          pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
          message: '仅可使用字母、数字、下划线',
          trigger: 'blur',
        },
      ],
      nickName: [
        { required: true, message: '用户昵称不能为空', trigger: 'blur' },
        // 仅可使用中文、字母、数字、下划线
        {
          pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
          message: '仅可使用中文、字母、数字、下划线',
          trigger: 'blur',
        },
      ],
      userType: [{ required: true, message: '用户类型不能为空', trigger: 'blur' }],
      roleIds: [{ required: true, message: '角色不能为空', trigger: 'blur' }],
      role: [{ required: true, message: '角色不能为空', trigger: 'blur' }],
      tenantId: [{ required: true, message: '租户不能为空', trigger: 'blur' }],
      password: [
        {
          required: true,
          message: '用户密码不能为空',
          trigger: 'blur',
        },
        {
          min: 6,
          message: '用户密码长度不少于 6',
          trigger: 'blur',
        },
        // {
        //   validator: (rule, value, callback) => {
        //     const SPECIAL_CHARACTERS = '!@#$%^&*()_+-=';

        //     if (!/(?=.*[a-z])/.test(value)) {
        //       callback(new Error('密码必须包含至少一个小写字母'));
        //     } else if (!/(?=.*[A-Z])/.test(value)) {
        //       callback(new Error('密码必须包含至少一个大写字母'));
        //     } else if (!/(?=.*\d)/.test(value)) {
        //       callback(new Error('密码必须包含至少一个数字'));
        //     } else if (!SPECIAL_CHARACTERS.split('').some((char) => value.includes(char))) {
        //       callback(new Error('密码必须包含至少一个特殊字符'));
        //     } else {
        //       callback();
        //     }
        //   },
        //   trigger: 'blur',
        // },
      ],

      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  // 使用 watch 判断 form.userType  如果 form.value.userType == sys_user 则请求接口   getUser().then(response => {
  // roleOptions.value = response.data.roles;
  // });
  watch(
    () => form.value.userType,
    (newUserType, oldUserType) => {
      if (newUserType !== oldUserType && title.value !== '修改用户') {
        // 添加 title.value 的判断
        form.value.tenantId = undefined;
        form.value.roleIds = undefined;
        tenementList.value = [];
        roleOptions.value = [];

        console.log('newUserType', newUserType);
        if (newUserType !== 'normal_user') {
          getUser().then((response) => {
            roleOptions.value = response.data.roles;
          });

          // 默认情况下调用 getTenantList
          let tenantListPromise = getTenantList();

          if (newUserType === 'tenant_admin_user') {
            // 在需要的条件下调用 getTenantList('true')
            tenantListPromise = getTenantList({ hasAdmin: true });
          }

          tenantListPromise.then((res) => {
            tenementList.value = res.data;
          });
        } else if (newUserType == 'normal_user') {
          getTenantList().then((res) => {
            tenementList.value = res.data;
            form.value.tenantId = tenementList.value[0].tenantId;
          });

          if (newUserType.tenantId !== undefined) {
            getTenantRoles(newUserType.tenantId).then((response) => {
              roleOptions.value = response.data.roles;
            });
          }
        }
      }
    },
    { deep: true },
  );

  watch(
    () => form.value.tenantId,
    (newUserType, oldUserType) => {
      console.log('newUserType', newUserType);
      if (newUserType !== undefined && form.value.userType != 'tenant_admin_user') {
        getTenantRoles(newUserType).then((response) => {
          console.log('response', response);
          roleOptions.value = response.data.roles;
        });
      }
    },
  );

  // 时间禁用
  // const disablesDate = (time) => {
  //    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7 //最小时间可选前七天
  //    const _maxTime = Date.now() - 24 * 60 * 60 * 1000 * 1 //最大时间可选今天
  //    return time.getTime() <= _maxTime || time.getTime() > _minTime
  // }

  // 时间禁用，但不限制查看过去时间
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  /** 根据名称筛选部门树 */
  watch(deptName, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  /** 查询部门下拉树结构 */
  function getDeptTree() {
    // deptTreeSelect().then(response => {
    //    deptOptions.value = response.data;
    // });
  }
  /** 查询用户列表 */
  function getList() {
    loading.value = true;
    listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then((res) => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    });
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    console.log('data', data);

    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.deptId = undefined;
    queryParams.value.tenantId = undefined;
    proxy.$refs.deptTreeRef?.setCurrentKey(null);
    handleQuery();
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const userIds = row.userId || ids.value;
    console.log('userIds', userIds);
    proxy.$modal
      .confirm(
        ids.value.length > 1
          ? `是否确定删除勾选共 [ ${ids.value.length} ] 的用户？注意：一旦删除，该用户的未提交信息将不会保留。`
          : `您确定删除用户账号为   [ ${row.userName} ]  用户昵称为 [ ${row.nickName} ]  的用户吗？ 
   注意：一旦删除，该用户的未提交信息将不会保留。`,
      )
      .then(function () {
        return delUser(userIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  /** 用户状态修改  */
  function handleStatusChange(row) {
    const text = row.status === '0' ? '启用' : '停用';
    proxy.$modal
      .confirm('确定要"' + text + '""' + row.userName + '"用户吗？')
      .then(function () {
        return changeUserStatus(row.userId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + '成功');
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0';
      });
  }
  /** 更多操作 */
  // function handleCommand(command, row) {
  //    switch (command) {
  //       case "handleResetPwd":
  //          handleResetPwd(row);
  //          break;
  //       case "handleAuthRole":
  //          handleAuthRole(row);
  //          break;
  //       default:
  //          break;
  //    }
  // };

  /** 重置密码按钮操作 */
  //   function handleResetPwd(row) {
  //     proxy
  //       .$prompt('请输入"' + row.userName + '"的新密码', '提示', {
  //         confirmButtonText: '确定',
  //         cancelButtonText: '取消',
  //         closeOnClickModal: false,
  //         inputPattern: /^.{5,20}$/,
  //         inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
  //         inputType: 'password',
  //       })
  //       .then(({ value }) => {
  //         resetUserPwd(row.userId, value).then(() => {
  //           proxy.$modal.msgSuccess('修改成功，新密码是：' + value);
  //         });
  //       })
  //       .catch(() => {});
  //   }

  const resetPwdDialogVisible = ref(false);
  const resetPwdFormRef = ref(null);
  const resetPwdForm = reactive({
    userId: '',
    userName: '',
    newPassword: '',
    confirmPassword: '',
  });

  const resetPwdRules = {
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 5, message: '密码长度不少于 6', trigger: 'blur' },
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== resetPwdForm.newPassword) {
            callback(new Error('两次输入的密码不一致'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
  };
  function handleResetPwd(row) {
    resetPwdForm.userId = row.userId;
    resetPwdForm.userName = row.userName;
    resetPwdForm.userType = row.userType;
    resetPwdForm.email = row.email;
    resetPwdForm.newPassword = '';
    resetPwdForm.confirmPassword = '';
    proxy.$refs.resetPwdFormRef?.resetFields();
    resetPwdDialogVisible.value = true;
  }

  function submitResetPwd() {
    resetPwdFormRef.value.validate((valid) => {
      if (valid) {
        resetUserPwd(resetPwdForm.userId, resetPwdForm.newPassword)
          .then((res) => {
            proxy.$modal.msgSuccess(res.msg);
            resetPwdDialogVisible.value = false;
          })
          .catch((error) => {
            proxy.$modal.msgError(error.msg);
          });
      } else {
        return false;
      }
    });
  }
  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
  }
  /** 文件上传中处理 */
  const handleFileUploadProgress = () => {
    upload.isUploading = true;
  };
  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs.uploadRef.handleRemove(file);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      '导入结果',
      { dangerouslyUseHTMLString: true },
    );
    getList();
  };
  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs.uploadRef.submit();
  }
  /** 重置操作表单 */
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: [],
    };
    proxy.resetForm('userRef');
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    // getUser().then(response => {
    //    // roleOptions.value = response.data.roles;
    // });
    open.value = true;
    title.value = '新增用户';
    form.value.password = initPassword.value;
    getTenantList().then((res) => {
      console.log('res', res);
      tenementList.value = res.data;
      form.value.tenantId = tenementList.value[0].tenantId;
      form.value.userType = sysUserType.value[0].value;
    });
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const userId = row.userId || ids.value;
    getUser(userId).then((response) => {
      console.log('response.data.user.tenantId', response);
      form.value = response.data.user;
      postOptions.value = response.data.posts;
      roleOptions.value = response.data.roles;
      form.value.postIds = response.data.postIds;
      form.value.roleIds = response.data.roleIds;
      form.value.tenantId = response.data.user.tenantId;
      open.value = true;
      title.value = '修改用户';
      form.value.password = '';
    });
    // getTenantList().then((res) => {
    //    console.log('res.data', res.data)
    //    tenementList.value = res.data
    // }
    // )
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        if (form.value.userId != undefined) {
          updateUser(form.value).then(() => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          });
        } else {
          const FormValue = {
            ...form.value,
            nickName: form.value.nickName ? form.value.nickName : form.value.userName,
          };
          addUser(FormValue).then(() => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  const userInfo = ref();
  function TOgetInfo() {
    getUserProfile().then((res) => {
      userInfo.value = res.data.user;
      user_type.value = res.data.user.userType;
      console.log('user_type.value', user_type.value);
    });
  }
  getDeptTree();
  getList();
  TOgetInfo();
  onMounted(() => {
    getTenantList().then((res) => {
      tenementList.value = res.data;
    });
  });
  const userTypeText = (userType) => {
    const typeMap = {
      normal_user: '普通用户',
      sys_user: '平台级用户',
      tenant_admin_user: '租户管理员',
      api_hub: 'API用户',
    };
    return typeMap[userType] || '未知用户类型';
  };

  const tagClass = (userType) => {
    const classMap = {
      normal_user: 'user-type-normal',
      sys_user: 'user-type-system',
      tenant_admin_user: 'user-type-admin',
      api_hub: 'user-type-api',
    };
    return ['user-type-tag', classMap[userType]];
  };
</script>
<style scope lang="scss">
  .formDialog {
    .el-input {
      width: 113%;
    }
  }
</style>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    & > .el-row {
      height: 100%;
      .el-col {
        height: 100%;
      }
    }
    .search-box {
      margin: 0;
      text-align: right;
      .el-form-item--default {
        margin-bottom: 20px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .table-box {
      height: calc(100% - 170px);
    }
    .mb8 {
      margin-bottom: 20px;
    }
  }

  .user-type-tag {
    //   font-weight: bold;
    width: 80px;
    text-align: center;
    border-color: transparent;
  }
  .user-type-normal {
    background-color: #f0f5ff;
    color: #7f97f3;
  }
  .user-type-system {
    background-color: #e6f7ff;
    color: #409eff;
  }
  .user-type-admin {
    background-color: #fff7e6;
    color: #f5bf52;
  }
  .user-type-api {
    background-color: #c0f8f3;
    color: #01b3a4;
  }
</style>
