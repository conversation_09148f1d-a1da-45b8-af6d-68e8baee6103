<script setup lang="ts">
  import { EditPen } from '@element-plus/icons-vue';
  import { computed, nextTick, ref } from 'vue';
  import { placeholderList } from '../../../utils/const';

  const input = ref(false);

  const props = defineProps({
    nodeConfig: {
      type: Object,
      default: () => {},
    },
  });

  const defaultText = computed(() => {
    return placeholderList[props.nodeConfig.type];
  });

  const titleInputBlurEvent = () => {
    input.value = false;
    props.nodeConfig.nodeName = props.nodeConfig.nodeName || defaultText;
  };
  const titleInputRef = ref();
  const titleTextClickEvent = () => {
    input.value = true;
    nextTick(() => {
      titleInputRef.value.focus();
    });
  };
</script>

<template>
  <div>
    <el-text
      v-if="!input"
      style="cursor: pointer"
      tag="b"
      size="large"
      @click="titleTextClickEvent"
    >
      {{ nodeConfig.nodeName }}
      <el-icon>
        <EditPen />
      </el-icon>
    </el-text>
    <el-input
      v-if="input"
      ref="titleInputRef"
      v-model="nodeConfig.nodeName"
      maxlength="10"
      @blur="titleInputBlurEvent"
    ></el-input>
  </div>
</template>

<style scoped lang="less"></style>
