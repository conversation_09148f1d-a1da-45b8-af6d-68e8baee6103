<template>
  <div class="configure-data-source-box">
    <div class="configure-top">
      <el-tooltip class="box-item" content="返回" effect="light" placement="top-start">
        <el-button type="" icon="DArrowLeft" @click="callBack"></el-button>
      </el-tooltip>
      <div style="display: flex; justify-content: space-between; align-items: center; gap: 50px">
        <div v-if="isOpenDbSync">
          是否开启整库同步 :
          <el-switch
            v-model="isDbSync"
            :disabled="!CanvasActions"
            name="1"
            active-icon=""
            inactive-icon=""
            style="margin-left: 8px; --el-switch-on-color: #13ce66"
            @change="isDbSyncChange"
          />
        </div>
        <!-- <el-button
          class="save-btn"
          plain
          type="primary"
          :disabled="!CanvasActions"
          @click="sumBitAllParams"
          >确定</el-button
        > -->
        <el-tooltip class="box-item" effect="light" content="保存" placement="bottom-end">
          <el-button
            type="text"
            :disabled="!CanvasActions"
            class="icon-btn save-btn"
            plain
            @click="sumBitAllParams"
          >
            <IconSave />
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="configure-content">
      <div class="configure-title">
        数据源配置
        <!-- <el-divider></el-divider> -->
      </div>
      <el-col class="config" :span="12">
        <p>源数据源</p>
        <el-form
          ref="dataSourceRef"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="auto"
        >
          <el-form-item label="源数据源类型" prop="sourceDataType">
            <el-select
              v-model="form.sourceDataType"
              placeholder="源数据源类型"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getType"
            >
              <el-option
                v-for="dict in jdbc_input_datasource_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="源数据源" prop="sourceDataSource">
            <el-select
              v-model="form.sourceDataSource"
              placeholder="源数据源"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getSourceDB"
            >
              <el-option
                v-for="dict in sourceDataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="源数据库" prop="sourceDatabase">
            <el-select
              v-model="form.sourceDatabase"
              placeholder="源数据库"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getSourceTable"
            >
              <el-option
                v-for="dict in sourceDatabaseList"
                :key="dict"
                :label="dict"
                :value="dict"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-else label="源模式" prop="sourceDatabase">
            <el-select
              v-model="form.sourceDatabase"
              placeholder="源模式"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getSourceTable"
            >
              <el-option
                v-for="dict in sourceDatabaseList"
                :key="dict"
                :label="dict"
                :value="dict"
              />
            </el-select>
          </el-form-item> -->

          <el-form-item v-if="isShowSchemaSource()" label="源模式" prop="status">
            <!-- {{ form.sourceDataType }} -->
            <el-select
              v-model="form.sourceTable"
              placeholder="源模式"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getSourceGP"
            >
              <el-option v-for="dict in sourceTableList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col style="margin-left: 120px" class="config config-target" :span="12">
        <p class="target-title">目标数据源</p>
        <el-form
          ref="dataSourceRef"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="auto"
        >
          <el-form-item label="目标数据源类型" prop="aimDataType">
            <el-select
              v-model="form.aimDataType"
              placeholder="目标数据源类型"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getTarget"
            >
              <el-option
                v-for="dict in jdbc_output_datasource_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="目标数据源" prop="aimDataSource">
            <el-select
              v-model="form.aimDataSource"
              placeholder="目标数据源"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getTargetDB"
            >
              <el-option
                v-for="dict in aimDataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="目标数据库" prop="aimDatabase">
            <el-select
              v-model="form.aimDatabase"
              placeholder="目标数据库"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getTargetTable"
            >
              <el-option
                v-for="dict in aimDatabaseList"
                :key="dict.value"
                :label="dict.name"
                :value="dict"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-else label="目标模式" prop="aimDatabase">
            <el-select
              v-model="form.aimDatabase"
              placeholder="目标模式"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getTargetTable"
            >
              <el-option
                v-for="dict in aimDatabaseList"
                :key="dict.value"
                :label="dict.name"
                :value="dict"
              />
            </el-select>
          </el-form-item> -->

          <el-form-item v-if="isShowSchemaAim()" label="目标模式" prop="status">
            <el-select
              v-model="form.aimTable"
              placeholder="目标模式"
              clearable
              style="width: 100%"
              :popper-append-to-body="false"
              popper-class="my-select"
              :disabled="!CanvasActions"
              @change="getGPTable"
            >
              <el-option v-for="dict in aimTableList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>

      <div v-show="!treeNodeShow" v-loading="!treeNodeShow" class="srcAndTrg-loading"></div>

      <div v-show="srcAndTrg" class="srcAndTrg">
        <div class="configure-title"> 配置字段映射 </div>
        <!-- <el-divider></el-divider> -->

        <!-- <el-row :gutter="20" class="configCP"> -->
        <splitpanes class="default-theme">
          <pane min-size="16" max-size="24" size="16">
            <!-- <b>选择源表</b> -->
            <el-input
              v-model="filterText"
              placeholder="源数据库表名搜索"
              clearable
              prefix-icon="Search"
              style="margin-bottom: 20px"
              :disabled="!CanvasActions"
            />
            <el-tree
              ref="deptTreeRef"
              :data="sourceDataTableList"
              :props="defaultProps"
              :expand-on-click-node="false"
              node-key="tableName"
              default-expand-all
              show-checkbox
              style="max-height: 260px; overflow-y: auto; overflow-x: none"
              :filter-node-method="filterNode"
              :class="{ 'disabled-tree': !CanvasActions }"
              @check="getDeptTreeRef"
            />
          </pane>

          <pane>
            <el-table
              :data="tableList"
              height="300"
              style="min-height: 260px"
              :class="{ 'disabled-tree': isDbSync }"
            >
              <el-table-column
                align="center"
                label="序号"
                type="index"
                width="80"
              ></el-table-column>
              <el-table-column
                align="center"
                label="已选择源表"
                prop="tableName"
                width="400"
                show-overflow-tooltip
              />
              <el-table-column align="center" label="目标表" show-overflow-tooltip>
                <template #default="scope">
                  <el-tooltip
                    :content="getSealName(scope)"
                    :disabled="!scope.row.target"
                    placement="top-start"
                  >
                    <el-select
                      v-model="scope.row.target"
                      :remote-method="remoteMethod"
                      remote
                      filterable
                      clearable
                      :popper-append-to-body="false"
                      :disabled="!CanvasActions"
                      @change="changeTarget(scope.row)"
                    >
                      <el-option
                        v-for="table in optionList"
                        :key="table.tableName"
                        :label="table.tableName"
                        :value="table.tableName"
                        :autosize="{ minRows: 2, maxRows: 6 }"
                      ></el-option>
                    </el-select>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="字段映射"
                class-name="small-padding fixed-width"
                width="150"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    :disabled="!CanvasActions"
                    @click="editField(scope.row)"
                    >编辑</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="其他配置"
                class-name="small-padding fixed-width"
                width="150"
              >
                <template #default="scope">
                  <el-button link type="primary" :disabled="!CanvasActions" @click="edit(scope.row)"
                    >编辑</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </pane>
        </splitpanes>
        <!-- </el-row> -->
      </div>
    </div>

    <!-- 字段映射 -->
    <el-dialog
      v-model="open"
      title="字段映射"
      width="780px"
      :close-on-click-modal="false"
      style="height: auto"
      append-to-body
      @close="cancelEditField()"
    >
      <!-- <el-button>排序</el-button> -->
      <div v-loading="loadingForField" class="fieldBox">
        <div class="btnGruop">
          <el-button @click="sameNameConnect">同名连接</el-button>
          <el-button @click="peerConnect">同行连接</el-button>
          <el-button @click="cancelAllConnection">移除所有连接</el-button>
        </div>
        <div class="field-container-box">
          <div class="fieldContainer">
            <div class="list left">
              <div v-for="sourceField in fieldList" :key="sourceField.id" class="listItem">
                <div
                  :id="`${sourceField.columnName}*leftItem`"
                  class="listItenInner leftItem"
                  @click="addPoint"
                >
                  <!-- <el-tooltip effect="dark" :content="sourceField.isPartitionField" placement="top">
                  <div>{{ sourceField.isPartitionField }}</div>
                </el-tooltip> -->

                  <div>
                    <el-tooltip effect="dark" :content="sourceField.columnName" placement="top">
                      <span class="inner-content">{{ sourceField.columnName }}</span>
                    </el-tooltip>
                    <el-tooltip
                      :disabled="true"
                      effect="dark"
                      :content="sourceField.columnType"
                      placement="top"
                    >
                      <el-tag style="margin-left: 5px" size="mini">
                        <span>{{ sourceField.columnType }}</span>
                      </el-tag>
                    </el-tooltip>
                  </div>
                  <div v-if="sourceField.comment">
                    <el-tooltip
                      :disabled="!sourceField.comment"
                      effect="dark"
                      :content="sourceField.comment"
                      placement="top"
                    >
                      <div class="inner-div-content">{{ sourceField.comment }}</div>
                    </el-tooltip>
                  </div>
                  <div v-else style="opacity: 0">
                    <div>暂无数据</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="list right">
              <div v-for="targetField in newTrgFieldList" :key="targetField.id" class="listItem">
                <div
                  :id="targetField.columnName + '*rightItem/' + targetField.sourceTableName"
                  class="listItenInner rightItem"
                  @click="addPoint"
                >
                  <!-- <el-tooltip effect="dark" :content="targetField.isPartitionField" placement="top">
                  <div>{{ targetField.isPartitionField }}</div>
                </el-tooltip> -->
                  <div>
                    <el-tooltip effect="dark" :content="targetField.columnName" placement="top">
                      <span class="inner-content">{{ targetField.columnName }}</span>
                    </el-tooltip>
                    <el-tooltip
                      :disabled="true"
                      effect="dark"
                      :content="targetField.columnType"
                      placement="top"
                    >
                      <el-tag style="margin-left: 5px" size="mini">
                        <span>{{ targetField.columnType }}</span>
                      </el-tag>
                    </el-tooltip>
                  </div>
                  <div v-if="targetField.comment">
                    <el-tooltip
                      :disabled="!targetField.comment"
                      effect="dark"
                      :content="targetField.comment"
                      placement="top"
                    >
                      <div class="inner-div-content">{{ targetField.comment }}</div>
                    </el-tooltip>
                  </div>
                  <div v-else style="opacity: 0">
                    <div>暂无数据</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <el-table v-loading="loadingForField" :data="fieldList">
            <el-table-column align="center" label="源表字段及类型">
                <template #default="scope">
                    <div>字段名：{{ scope.row.columnName }}</div>
                    <div>字段类型：{{ scope.row.columnType }}</div>
                </template>
            </el-table-column>
            <el-table-column align="center" label="目标表字段">
                <template #default="scope">
                    <el-select clearable v-model="scope.row.field">
                        <el-option v-for="field in scope.row.trgFieldList" :key="field.columnName" :label="field.columnName"
                            :value="field.columnName"></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table> -->

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEditField">取 消</el-button>
          <el-button type="primary" @click="submitFormField">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 其他配置 -->
    <el-dialog
      v-model="otherSetting"
      title="其他配置"
      width="1200px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelEditSet()"
    >
      <el-form ref="setRef" :model="settingObj" :rules="rulesOther">
        <div v-if="form.aimDataType == 'HIVE' || form.sourceDataType == 'HIVE'">
          <b class="bTitle">HIVE 属性</b>
          <el-divider></el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <!-- hiveReaderParameter -->
              <el-form-item label="HIVE读">
                <el-input
                  v-model="settingObj.rDefaultFS"
                  placeholder="hadoop集群DefaultFs地址"
                ></el-input>
                <br />
                <el-input v-model="settingObj.rTempDatabase" placeholder="临时数据库"></el-input>
                <br />
                <el-input
                  v-model="settingObj.rTempDatabasePath"
                  placeholder="临时数据存储路径"
                ></el-input>
                <br />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- hiveWriterParameter -->
              <el-form-item label="HIVE写">
                <el-input v-model="settingObj.partitions" placeholder="分割"></el-input>
                <br />
                <el-input
                  v-model="settingObj.wDefaultFS"
                  placeholder="hadoop集群DefaultFs地址"
                ></el-input>
                <br />
                <el-input v-model="settingObj.wTempDatabase" placeholder="临时数据库"></el-input>
                <br />
                <el-input
                  v-model="settingObj.wTempDatabasePath"
                  placeholder="临时数据存储路径"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <b class="bTitle">设置过滤条件</b>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="过滤条件" prop="whereSql">
              <el-input
                v-model="settingObj.whereSql"
                type="textarea"
                placeholder="如：where id = 1"
                :autosize="{ minRows: 10, maxRows: 10 }"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="切割键" prop="splitPk"> -->
            <!-- <el-input -->
            <!-- v-model="settingObj.splitPk" -->
            <!-- type="text" -->
            <!-- placeholder="根据配置的字段进行数据分片,实现并发读取" -->
            <!-- ></el-input> -->
            <!-- </el-form-item> -->
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="后置SQL" prop="postSql"> -->
            <!-- <el-input -->
            <!-- v-model="settingObj.postSql" -->
            <!-- type="text" -->
            <!-- placeholder="选填,请根据数据源类型对应的SQL语法填写SQL" -->
            <!-- ></el-input> -->
            <!-- </el-form-item> -->
            <el-form-item label="写入模式" prop="writeMode">
              <el-select v-model="settingObj.writeMode" style="width: 640px">
                <el-option
                  v-for="pattern in patternList"
                  :key="pattern"
                  :label="pattern.label"
                  :value="pattern.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="前置SQL" prop="preSql">
              <el-input
                v-model="settingObj.preSql"
                type="textarea"
                placeholder="选填,请根据数据源类型对应的SQL语法填写SQL"
                :autosize="{ minRows: 8, maxRows: 8 }"
                :disabled="settingObj.writeMode !== 'CUSTOM_PROCESSING'"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <b class="bTitle">通道控制</b>
        <el-divider></el-divider>
        <!-- <el-row> -->
        <el-form-item label="是否开启 UPSERT" prop="">
          <el-switch
            v-model="settingObj.isUpsert"
            active-text=""
            inactive-text=""
            :disabled="!CanvasActions"
            name="1"
            @change="isUpsertChange"
          />
        </el-form-item>
        <el-form-item label="主键" prop="">
          <!-- 下拉框 -->
          <el-select v-model="settingObj.primaryKeys" placeholder="请选择" :multiple="true">
            <el-option
              v-for="num in otherFieldList"
              :key="num"
              :label="num.columnName"
              :value="num.columnName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="期待最大并发数" prop="">
          <el-select v-model="settingObj.parallel" placeholder="请选择">
            <el-option v-for="num in numList" :key="num" :label="num" :value="num"></el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="脏数据设置 " prop="errorLimit"> -->
        <!-- 脏数据占比超过 -->
        <!-- <el-input v-model="settingObj.errorLimit" style="width: 50px"></el-input> -->
        <!-- 时停止 -->
        <!-- </el-form-item> -->
        <!--  -->
        <el-form-item label="(batchSize) 单次批处理大小" prop="batchSize">
          <el-input v-model="settingObj.batchSize"></el-input>
        </el-form-item>
        <!-- <el-form-item label="(record) 行限制" prop="record">
                    <el-input v-model="settingObj.record"></el-input>
                </el-form-item>
                <el-form-item label="(byte) 字节限制" prop="byteS">
                    <el-input v-model="settingObj.byteS"></el-input>
                </el-form-item> -->

        <!-- <el-form-item label="启动参数" prop="jvmOpt"> -->
        <!-- <el-input v-model="settingObj.jvmOpt"></el-input> -->
        <!-- </el-form-item> -->
        <!-- </el-row> -->
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEditSet">取 消</el-button>
          <el-button type="primary" @click="submitFormSet">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    createOrUpdate,
    getByNodeId,
    getDatabaseList,
    getFieldMapList,
    list as getList,
    getTableList,
    schemaForGP,
    tableForGP,
    getByTypeTableList,
  } from '@/api/dataSourceManageApi';

  import { IconSave } from '@arco-iconbox/vue-update-color-icon';
  import jsPlumb from 'jsplumb';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { getFieldList } from '@/api/DataDev';
  import { ref } from 'vue';

  const { proxy } = getCurrentInstance();

  const isShowSchemaSource = () => {
    return !['SPARK', 'MYSQL', 'HIVE', 'TDENGINE', 'XUGUTSDB', 'CLICKHOUSE'].includes(
      form.value.sourceDataType,
    );
  };
  const isShowSchemaAim = () => {
    return !['SPARK', 'MYSQL', 'HIVE', 'TDENGINE', 'XUGUTSDB', 'CLICKHOUSE'].includes(
      form.value.aimDataType,
    );
  };
  // 组件接收传参
  const props = defineProps({
    NodeData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
    workspaceId: {
      type: String,
    },
    CanvasActions: {
      type: Boolean,
      default: () => true,
    },
    abc: {
      type: Object,
      default: () => {},
    },
  });

  const { NodeData, workFlowType, workspaceId, CanvasActions } = toRefs(props);
  const openWorkFlowData = ref();
  console.log('openWorkFlowData.value', NodeData.value);
  console.log('workspaceId.value', workspaceId.value);

  // 使用计算属性
  const getSealName = (scope) => {
    console.log('scope', scope);
    for (const e of optionList.value) {
      if (e.tableName == scope.row.target) {
        return e.tableName;
      }
    }
  };

  // jsplumb 实例
  const plumbIns = ref(null);
  // 连线数据的左侧集合
  const leftElList = ref(null);
  // 连线数据的右侧集合
  const rightElList = ref(null);
  const { jdbc_input_datasource_type, jdbc_output_datasource_type } = proxy.useDict(
    'jdbc_input_datasource_type',
    'jdbc_output_datasource_type',
  );
  // const { table_type, model_search_type } = proxy.useDict('table_type', 'model_search_type');
  // 源数据源类型
  // const sourceDataTypeList = ref([
  //   'MYSQL',
  //   'ORACLE',
  //   'XUGU',
  //   'SQLSERVER',
  //   'POSTGRESQL',
  //   'DAMENG',
  //   'GREENPLUM',
  //   'HIVE',
  //   // 'KAFKA', 'API'
  // ]);
  // const sourceDataTypeList = proxy.useDict('jdbc_input_datasource_type').map(res => {
  //   return {
  //     label:res.label,
  //       value:res.value
  //   }
  // });
  // 目标数据源类型
  // const aimDataTypeList = ref([
  //   'MYSQL',
  //   'ORACLE',
  //   'XUGU',
  //   'SQLSERVER',
  //   'POSTGRESQL',
  //   'DAMENG',
  //   'GREENPLUM',
  //   // "HIVE"
  //   // 'KAFKA', 'API'
  // ]);
  // 源数据源
  const sourceDataSourceList = ref();
  // 源数据库
  const sourceDatabaseList = ref();

  // 源表
  const sourceDataTableList = ref();
  // 源模式
  const sourceTableList = ref();

  // 目标数据源
  const aimDataSourceList = ref();
  // 目标数据库
  const aimDatabaseList = ref();

  // 目标表
  // const aimDataTableList = ref()

  // 目标模式
  const aimTableList = ref();

  const defaultProps = {
    children: 'children',
    label: 'tableName',
  };
  const data = reactive({
    form: {
      sourceDataType: '',
      sourceDataSource: '',
      sourceDatabase: '',
      sourceTable: '',
      aimDataType: '',
      aimDataSource: '',
      aimDatabase: '',
      aimTable: '',
    },
    settingObj: {
      sourceTableName: '',
      newColumns: [],
      parallel: '1',
      incrementColumn: null,
      radioVal: '',
      writeMode: 'APPEND_DATA',
      preSql: '',
      postSql: '',
      errorLimit: '',
      whereSql: '',
      splitPk: '',
      jvmOpt: '--jvm="-Xms1G -Xmx1G"',
      byteS: -1,
      record: -1,
      batchSize: 5000,
    },
    rules: {
      sourceDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      sourceDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      sourceDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],

      aimDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      aimDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      aimDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],
    },

    rulesOther: {
      whereSql: [{ required: false, message: '过滤条件不能为空', trigger: 'blur' }],
      splitPk: [{ required: false, message: '切割键不能为空', trigger: 'blur' }],
      preSql: [{ required: false, message: '前置 SQL 不能为空', trigger: 'blur' }],
      postSql: [{ required: false, message: '后置 SQL 不能为空', trigger: 'blur' }],
      writeMode: [{ required: false, message: '写入模式不能为空', trigger: 'change' }],
      parallel: [{ required: true, message: '期待最大并发数不能为空', trigger: 'change' }],
      errorLimit: [{ required: false, message: '脏数据设置不能为空', trigger: 'blur' }],
      batchSize: [{ required: false, message: '批次大小不能为空', trigger: 'blur' }],
      jvmOpt: [{ required: true, message: '启动参数不能为空', trigger: 'blur' }],
    },
  });
  const { form, settingObj, rules, rulesOther } = toRefs(data);

  // 字段映射的显影
  const srcAndTrg = ref(false);
  const treeNodeShow = ref(true);

  // 树状结构 Ref
  const deptTreeRef = ref();
  // 字段映射树状结构
  const targetTableList = ref([]);
  // 字段映射表格数据
  const tableList = ref([]);

  // 字段映射 弹窗 loading
  const loadingForField = ref(false);
  // 弹窗字段映射 弹窗
  const open = ref(false);
  // 弹窗字段映射 table 列表
  const fieldList = ref([]);

  // 其他设置弹窗
  const otherSetting = ref(false);

  const columnsMapping = ref([]);

  // 搜索字段参数
  const filterText = ref('');
  // 监听输入框的值
  watch(filterText, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  // 对输入框的值进行模糊查询
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.tableName.toLowerCase().includes(value.toLowerCase());
  };

  const optionList = ref([]);
  // 目标表 select 框下拉搜索事件
  const remoteMethod = (query) => {
    if (query) {
      optionList.value = targetTableList.value.filter((item) => {
        return item.tableName.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      optionList.value = targetTableList.value;
    }
  };

  // 最大并发
  const numList = ref(['1', '2', '3', '4', '5', '6', '7', '8']);
  //   const columnSet = ref(null);
  // let incrementColumnList = ref(null)

  // 写入模式
  const patternList = ref([
    { label: 'append(追加数据)', value: 'APPEND_DATA' },
    { label: 'truncate(先清空表,后追加数据)', value: 'DROP_DATA' },
    { label: 'pre_sql(自定义存储模式，需配合高级选项使用)', value: 'CUSTOM_PROCESSING' },
  ]);

  // 增量配置选择时，给 setting 中的 incrementColumn 赋值
  // const getIncrementColumn = (data) => {
  //     if(data && data !='无' && incrementColumnList.value) {
  //         settingObj.value.incrementColumn.columnName = incrementColumnList.value.filter(item => item.columnName == data)[0].columnName
  //         settingObj.value.incrementColumn.columnType = incrementColumnList.value.filter(item => item.columnName == data)[0].columnType
  //     } else {
  //         settingObj.value.incrementColumn = null
  //     }

  // }
  // 获取源数据源
  const getType = async (data) => {
    // 改变数据 先清空已有数据
    form.value.sourceDataSource = null;
    sourceDataSourceList.value = [];

    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];

    sourceDataTableList.value = [];
    isDbSync.value = false;
    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      // form.value.sourceDataSource = null
      await getList({
        type: data,
        workSpaceId: workspaceId.value,
      }).then((res) => {
        sourceDataSourceList.value = res.data;
      });
    } else {
      sourceDataSourceList.value = [];
    }
  };

  // 获取源数据库
  const getSourceDB = async (data) => {
    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    sourceDataTableList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];
    isDbSync.value = false;

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      getDatabaseList({ datasourceId: data }).then((res) => {
        console.log(res);
        sourceDatabaseList.value = res.data;
      });
      // if (form.value.sourceDataType == 'DAMENG' || form.value.sourceDataType == 'ORACLE') {
      //   const obj = {};
      //   (obj.datasourceId = form.value.sourceDataSource),
      //     (obj.databaseName = form.value.sourceDatabase);
      //   const res = await schemaForGP(obj);
      //   const resRe = await getDatabaseList({ datasourceId: form.value.sourceDataSource });
      //   console.log(resRe);
      //   sourceDatabaseList.value = res.data;
      // } else {

      // }
    } else {
      sourceDatabaseList.value = [];
    }
  };

  // 获取目标数据源
  const getTarget = async (data) => {
    // 改变数据 先清空已有数据
    form.value.aimDataSource = null;
    aimDataSourceList.value = [];

    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];
    isDbSync.value = false;

    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }
    if (data) {
      getList({ type: data, workSpaceId: workspaceId.value }).then((res) => {
        aimDataSourceList.value = res.data;
      });
    } else {
      aimDataSourceList.value = [];
    }
  };

  // 获取目标数据库
  const getTargetDB = async (data) => {
    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];
    isDbSync.value = false;

    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }
    if (data) {
      getDatabaseList({ datasourceId: data }).then((res) => {
        aimDatabaseList.value = res.data;
      });
      // if (form.value.aimDataType == 'DAMENG' || form.value.aimDataType == 'ORACLE') {
      //   const obj = {};
      //   (obj.datasourceId = form.value.aimDataSource), (obj.databaseName = form.value.aimDatabase);
      //   const res = await schemaForGP(obj);
      //   aimDatabaseList.value = res.data;
      // } else {

      // }
    } else {
      aimDatabaseList.value = [];
      // form.value.aimDatabase = ''
    }
  };
  // 获取源数据源表数据，如果存在模式则获取相应的模式列表
  const getSourceTable = async (data) => {
    form.value.sourceTable = null;
    sourceTableList.value = [];
    isDbSync.value = false;

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      if (!isShowSchemaSource()) {
        const objForOr = {};
        (objForOr.datasourceId = form.value.sourceDataSource),
          (objForOr.databaseName = form.value.sourceDatabase);
        getTableList(objForOr).then((res) => {
          // proxy.$modal.loading('正在加载...');
          if (res.data && res.data.length) {
            sourceDataTableList.value = res.data;
            console.log('sourceDataTableList.value', sourceDataTableList.value);
            // console.log('sourceDataTableList.value', sourceDataTableList.value)
          } else {
            sourceDataTableList.value = [];
            proxy.$modal.msgWarning('源数据源当前数据库下没有表');
          }
          treeIsshow();
          console.log(' 2222', 21);
          // proxy.$modal.closeLoading();
        });
      } else {
        const obj = {};
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase);
        // if(form.value.sourceDataType == 'XUGU') {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.schema = form.value.sourceDatabase
        // } else {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.databaseName = form.value.sourceDatabase
        // }
        schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            sourceTableList.value = res.data;
          } else {
            sourceTableList.value = [];
          }
        });
      }
    } else {
      form.value.sourceTable = null;
      // getSourceGP(data)
    }
  };
  // 源数据源存在模式情况，获取模式下的表
  const getSourceGP = async (data) => {
    isDbSync.value = false;

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      const obj = {};
      if (form.value.sourceDataType == 'XUGU') {
        (obj.datasourceId = form.value.sourceDataSource), (obj.schema = data);
      } else {
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase),
          (obj.schema = data);
      }
      tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          sourceDataTableList.value = res.data;
          treeIsshow();
        } else {
          sourceDataTableList.value = [];
          //   proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      sourceDataTableList.value = [];
    }
  };
  // 当配置数据源的数据发生变化时，判断是否展示树状结构
  // 两种情况，是否包含模式
  const treeIsshow = () => {
    if (!isShowSchemaSource()) {
      if (sourceDataTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy?.$refs?.deptTreeRef?.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    } else {
      if (sourceTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy.$refs.deptTreeRef.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    }
  };
  // 如果存在模式。则获取模式
  const getTargetTable = async (data) => {
    form.value.aimTable = null;
    aimTableList.value = [];
    //
    targetTableList.value = [];
    // 库发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    // treeNodeShow.value = false;

    // proxy.$modal.loading('正在加载....');
    optionList.value = [];
    if (!data) return;
    if (!isShowSchemaAim()) {
      console.log(form.value);
      aimTableList.value = [];
      const obj = {};
      (obj.datasourceId = form.value.aimDataSource), (obj.databaseName = form.value.aimDatabase);
      const res = await getTableList(obj);
      if (res.data && res.data.length) {
        targetTableList.value = res.data;
      } else {
        targetTableList.value = [];
        // proxy.$modal.msgWarning('源数据源当前模式下没有表');
      }
      treeNodeShow.value = true;
      console.log('*-* ');
    } else {
      const obj = {};
      (obj.datasourceId = form.value.aimDataSource), (obj.databaseName = form.value.aimDatabase);
      schemaForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          aimTableList.value = res.data;
          console.log(aimTableList.value);
          // proxy.$modal.closeLoading();
          treeNodeShow.value = true;
        } else {
          aimTableList.value = [];
          //   proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    }
    // else {
    //   optionList.value = [];
    //   // form.value.aimTable = ''
    //   // aimTableList.value = []
    //   targetTableList.value = [];
    //   // proxy.$modal.closeLoading();
    // }
    treeNodeShow.value = true;
  };
  // 获取目标数据源模式下的表
  const getGPTable = async (data) => {
    // 模式发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    optionList.value = [];
    // if (data) {
    //   // targetTableList.value = []
    //   const obj = {};
    //   if (form.value.aimDataType == 'XUGU') {
    //     (obj.datasourceId = form.value.aimDataSource), (obj.schema = data);
    //   } else {
    //     (obj.datasourceId = form.value.aimDataSource),
    //       (obj.databaseName = form.value.aimDatabase),
    //       (obj.schema = data);
    //   }

    //   const res = await tableForGP(obj);
    //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    //   if (res.data && res.data.length) {
    //     targetTableList.value = res.data;
    //   } else {
    //     targetTableList.value = [];
    //     // proxy.$modal.msgWarning('目标数据源当前模式下不存在表')
    //   }
    // } else {
    //   optionList.value = [];
    //   targetTableList.value = [];
    // }
    if (!data) return;

    let obj = {
      datasourceId: form.value.aimDataSource,
    };

    const conditionalPart =
      form.value.aimDataType === 'XUGU'
        ? { schema: data }
        : { databaseName: form.value.aimDatabase, schema: data };

    obj = { ...obj, ...conditionalPart };

    const res = await tableForGP(obj);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (res.data && res.data.length) {
      targetTableList.value = res.data;
    } else {
      targetTableList.value = [];
      // proxy.$modal.msgWarning('目标数据源当前模式下不存在表')
    }
  };
  // 树状结构点击勾选事件
  const getDeptTreeRef = (data, checked) => {
    console.log(data, checked.checkedNodes);
    // 添加后续用到的参数（字段映射）
    if (checked.checkedNodes.length) {
      // 勾选表
      console.log(checked.checkedNodes.some((item) => item.tableName == data.tableName));
      if (checked.checkedNodes.some((item) => item.tableName == data.tableName)) {
        // 首次勾选则直接赋值，之后 push 添加元素
        // 还需判断是增加勾选还是取消勾选
        if (tableList.value.length) {
          data.trgFieldList = [];
          data.tableMapping = [];
          data.columnsMapping = [];
          data.connectData = [];
          tableList.value.push(data);
        } else {
          checked.checkedNodes.map((i) => {
            i.trgFieldList = [];
            i.tableMapping = [];
            i.columnsMapping = [];
            i.connectData = [];
            return checked.checkedNodes;
          });
          tableList.value = checked.checkedNodes;
        }
        // if (!targetTableList.value.length || targetTableList.value.every(res => res.tableName != data.tableName)) {
        //     targetTableList.value.unshift({ tableName: data.tableName })
        // }
        // tableList.value.filter(item => item.tableName == data.tableName)[0].target = data.tableName
      }
      // 取消表选择
      else {
        if (tableList.value.length) {
          // tableList.value.filter(i => i.tableName == data.tableName)[0].field = ''
          tableList.value.filter((i) => i.tableName == data.tableName)[0].target = null;
          tableList.value = tableList.value.filter((i) => i.tableName != data.tableName);
        } else {
          checked.checkedNodes.map((i) => {
            i.trgFieldList = [];
            i.tableMapping = [];
            i.columnsMapping = [];
            return checked.checkedNodes;
          });
          tableList.value = checked.checkedNodes;
        }
      }
      console.log('tableList.value', tableList.value);
    } else {
      tableList.value = [];
    }
  };
  const newTrgFieldList = ref([]);
  // 连线规则
  const connectLimit = ref([]);
  const editField = async (row) => {
    // 字段映射获取字段信息
    if (row.target) {
      open.value = true;
      loadingForField.value = true;
      const queryObj = {};

      queryObj.srcDatasourceId = form.value.sourceDataSource;
      queryObj.srcDatabaseName = form.value.sourceDatabase;
      queryObj.srcSchemaName = form.value.sourceTable;

      if (
        form.value.sourceDataType == 'MYSQL' ||
        form.value.sourceDataType == 'HIVE' ||
        form.value.sourceDataType == 'SPARK'
      ) {
        queryObj.srcSchemaName = null;
      }

      queryObj.srcTableName = row.tableName;
      queryObj.destDatasourceId = form.value.aimDataSource;
      queryObj.destDatabaseName = form.value.aimDatabase;
      queryObj.destSchemaName = form.value.aimTable;
      if (
        form.value.aimDataType == 'MYSQL' ||
        form.value.aimDataType == 'HIVE' ||
        form.value.aimDataType == 'SPARK'
      ) {
        queryObj.destSchemaName = null;
      }

      queryObj.destTableName = row.target;
      console.log(queryObj);
      getFieldMapList(queryObj)
        .then((res) => {
          if (res.data && res.code == 200) {
            fieldList.value = res.data.srcTable.columns;
            console.log(JSON.parse(fieldList.value[0].otherOptions));
            fieldList.value.forEach((i) => {
              i.sourceTableName = row.tableName;
            });
            res.data.destTable.columns.forEach((item) => (item.sourceTableName = row.tableName));
            newTrgFieldList.value = res.data.destTable.columns;
            connectLimit.value = res.data.columnTypeMap;
          } else {
            connectLimit.value = [];
          }
          loadingForField.value = false;
        })
        .then(() => {
          leftElList.value = document.querySelectorAll('.leftItem');
          rightElList.value = document.querySelectorAll('.rightItem');
          // 回显连线数据
          if (
            tableList.value.filter((res) => res.tableName == row.tableName)[0].connectData?.length
          ) {
            tableList.value
              .filter((res) => res.tableName == row.tableName)[0]
              .connectData.forEach((item) => {
                plumbIns.value.ready(() => {
                  plumbIns.value.connect({
                    // 连线起点
                    source: item.source,
                    // 连线终点
                    target: item.target,
                    anchor: ['Left', 'Right'],
                    connector: ['Straight'],
                    endpoint: 'Blank',
                    overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
                    // 添加样式
                    paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
                  });
                });
              });
          }
        });
      nextTick(() => {
        plumbIns.value = jsPlumb.jsPlumb.getInstance({ Container: 'content' });
      });
    } else if (isDbSync.value) {
      return proxy.$modal.msgWarning('请选择目标表');
    }
    // if (form.value.aimDatabase || form?.value.aimTable) {
    //     if (row.target) {
    //         // tableListId.value = row.$treeNodeId
    //         open.value = true
    //         let schemaSource = ''
    //         let schemaTarget = ''
    //         if (form.value.sourceTable) {
    //             schemaSource = form.value.sourceTable
    //         } else {
    //             schemaSource = ''
    //         }
    //         if (form.value.aimTable) {
    //             schemaTarget = form.value.aimTable
    //         } else {
    //             schemaTarget = ''
    //         }
    //         let sourceForXugu = {}
    //         if (form.value.sourceDataType == 'XUGU') {
    //             sourceForXugu.tableName = row.tableName,
    //                 sourceForXugu.datasourceId = form.value.sourceDataSource,
    //                 sourceForXugu.schema = schemaSource
    //         } else {
    //             if (form.value.sourceDataType == 'ORACLE') {
    //                 sourceForXugu.tableName = row.tableName,
    //                     sourceForXugu.schema = form.value.sourceDatabase,
    //                     sourceForXugu.datasourceId = form.value.sourceDataSource
    //             } else {
    //                 sourceForXugu.tableName = row.tableName,
    //                     sourceForXugu.databaseName = form.value.sourceDatabase,
    //                     sourceForXugu.datasourceId = form.value.sourceDataSource
    //             }

    //         }
    //         let aimForXugu = {}
    //         if (form.value.aimDataType == 'XUGU') {
    //             aimForXugu.tableName = row.target,
    //                 aimForXugu.datasourceId = form.value.aimDataSource,
    //                 aimForXugu.schema = schemaTarget
    //         } else {
    //             if (form.value.aimDataType == 'ORACLE') {
    //                 aimForXugu.tableName = row.target,
    //                     aimForXugu.schema = form.value.aimDatabase,
    //                     aimForXugu.datasourceId = form.value.aimDataSource
    //             } else {
    //                 aimForXugu.tableName = row.target,
    //                     aimForXugu.databaseName = form.value.aimDatabase,
    //                     aimForXugu.datasourceId = form.value.aimDataSource
    //             }
    //         }
    //         // if ( workFlowType.value == 'edit'||!(columnsMapping.value.length) || !columnsMapping.value.some(i => i.sourceTableName == row.tableName)) {
    //         loadingForField.value = true
    //         getFieldList(sourceForXugu).then(res => {
    //             if (res.data && res.data.length) {
    //                 fieldList.value = res.data
    //                 fieldList.value.forEach(i => {
    //                     // i.show == '-';
    //                     i.field = null
    //                     i.sourceTableName = row.tableName
    //                 })
    //                 getFieldList(aimForXugu).then(res => {
    //                     if (res.data && res.data.length) {
    //                         res.data.forEach(item => item.sourceTableName = row.tableName)
    //                         newTrgFieldList.value = res.data
    //                         // res.data.forEach(item => item.columnType = `字段 ${item.columnName},类型 ${item.columnType}`)
    //                         // row.trgFieldList = res.data
    //                         // fieldList.value.forEach(i => i.trgFieldList = res.data);
    //                         // let b = null
    //                         // if (row.trgFieldList.length > fieldList.value.length) {
    //                         //     b = row.trgFieldList.slice(0, fieldList.value.length)
    //                         // } else {
    //                         //     b = row.trgFieldList
    //                         // }
    //                         // 如果已经对字段映射数据进行选择，则回显已选择的数据
    //                         // 否则渲染默认值

    //                         // } else {
    //                         //     b.forEach((i, index) => {
    //                         //         fieldList.value[index].field = i.columnName
    //                         //     })
    //                         // }
    //                     } else {
    //                         proxy.$modal.msgWarning('重新选择目标表')
    //                     }
    //                 }).then(() => {
    //                     leftElList.value = document.querySelectorAll('.leftItem');
    //                     rightElList.value = document.querySelectorAll('.rightItem');
    //                     // 回显连线数据
    //                     if (tableList.value.filter(res => res.tableName == row.tableName)[0].connectData?.length) {
    //                         tableList.value.filter(res => res.tableName == row.tableName)[0].connectData.forEach(item => {
    //                             plumbIns.value.ready(() => {
    //                                 plumbIns.value.connect({
    //                                     // 连线起点
    //                                     source: item.source,
    //                                     // 连线终点
    //                                     target: item.target,
    //                                     anchor: ['Left', 'Right'],
    //                                     connector: ['Straight'],
    //                                     endpoint: 'Blank',
    //                                     overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
    //                                     // 添加样式
    //                                     paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
    //                                 })
    //                             })
    //                         })
    //                     }
    //                 })
    //                 nextTick(() => {
    //                     plumbIns.value = jsPlumb.jsPlumb.getInstance({ Container: 'content' })
    //                 })
    //             } else {
    //                 proxy.$modal.msgWarning('重新选择目标数据库或模式')
    //             }
    //             loadingForField.value = false
    //         })
    //         // }
    //     } else {
    //         return proxy.$modal.msgWarning('请选择目标表')
    //     }
    // } else {
    //     return proxy.$modal.msgWarning('请选择目标库或者模式')
    // }
  };
  // 目标表选中变化时，
  const changeTarget = (data) => {
    console.log(data);
    if (data.target) {
      if (data.tableMapping.length && data.tableMapping[0].columnsMapping.length) {
        data.tableMapping[0].columnsMapping = [];
      }
    } else {
      data.tableMapping = [];
    }
    data.trgFieldList = [];
    data.columnsMapping = [];
  };
  const edit = (row) => {
    console.log(row);
    if (row.target) {
      let schema = '';
      if (form.value.aimDataType != 'MYSQL' && form.value.aimDataType != 'HIVE') {
        schema = form.value.aimTable;
      } else {
        schema = form.value.aimDatabase;
      }
      const params = {
        tableName: row.target,
        databaseName: form.value.aimDatabase,
        datasourceId: form.value.aimDataSource,
        schema,
      };

      getFieldListUtil(params);
      // 每次打开弹窗前，清空数据
      reset();
      // 确定是新增工作流还是编辑工作流
      // 编辑任务时则先回显数据
      if (openWorkFlowData.value) {
        console.log('penWorkFlowData.value', openWorkFlowData.value);
        if (
          openWorkFlowData.value.tableMapping.filter((i) => i.sourceTableName == row.tableName)
            .length
        ) {
          const obj = openWorkFlowData.value.tableMapping.filter(
            (i) => i.sourceTableName == row.tableName,
          )[0];
          settingObj.value.parallel = obj.parallel;
          settingObj.value.writeMode = obj.writeMode;
          settingObj.value.splitPk = obj.splitPk;
          settingObj.value.whereSql = obj.whereSql;
          settingObj.value.errorLimit = obj.errorLimit;
          settingObj.value.preSql = obj.preSql;
          settingObj.value.postSql = obj.postSql;
          settingObj.value.newColumns = obj.newColumns;
          settingObj.value.jvmOpt = obj.jvmOpt;
          settingObj.value.byteS = -1;
          settingObj.value.record = -1;
          settingObj.value.batchSize = obj.batchSize;
          settingObj.value.isUpsert = obj.isUpsert;
          settingObj.value.primaryKeys = obj.primaryKeys;

          if (form.value.sourceDataType == 'HIVE' || form.value.aimDataType == 'HIVE') {
            console.log(JSON.parse(obj.hiveReaderParameter));
            console.log(JSON.parse(obj.hiveWriterParameter));

            settingObj.value.rDefaultFS = JSON.parse(obj.hiveReaderParameter)?.defaultFS;
            settingObj.value.rTempDatabase = JSON.parse(obj.hiveReaderParameter)?.tempDatabase;
            settingObj.value.rTempDatabasePath = JSON.parse(
              obj.hiveReaderParameter,
            )?.tempDatabasePath;

            settingObj.value.partitions = JSON.parse(obj.hiveWriterParameter)?.partitions;
            settingObj.value.wDefaultFS = JSON.parse(obj.hiveWriterParameter)?.defaultFS;
            settingObj.value.wTempDatabase = JSON.parse(obj.hiveWriterParameter)?.tmpDatabase;
            settingObj.value.wTempDatabasePath = JSON.parse(
              obj.hiveWriterParameter,
            )?.tmpDatabasePath;
          }

          console.log('*/*/*/*/*/******************/', settingObj.value);
        }
      }
      // 判断是新增其他编辑还是修改已经存在的其他编辑
      if (!row.tableMapping.length) {
        row.tableMapping.push({
          columnsMapping: [],
          sourceTableName: row.tableName,
          sourceDatabase: form.value.sourceDatabase,
          destTableName: row.target,
          destDatabase: form.value.aimDatabase,
          newColumns: [],
          parallel: '1',
          isUpsert: false,
          primaryKeys: null,
          incrementColumn: null,
          radioVal: '',
          writeMode: 'APPEND_DATA',
          preSql: '',
          postSql: '',
          errorLimit: '',
          splitPk: '',
          jvmOpt: '--jvm="-Xms1G -Xmx1G"',
          byteS: -1,
          record: -1,
          batchSize: 5000,
        });
        console.log(row.tableMapping);
      } else {
        const obj = row.tableMapping.filter((i) => i.sourceTableName == row.tableName)[0];
        settingObj.value.primaryKeys = obj.primaryKeys;
        settingObj.value.isUpsert = obj.isUpsert;
        settingObj.value.parallel = obj.parallel;
        settingObj.value.writeMode = obj.writeMode;
        settingObj.value.splitPk = obj.splitPk;
        settingObj.value.whereSql = obj.whereSql;
        settingObj.value.errorLimit = obj.errorLimit;
        settingObj.value.preSql = obj.preSql;
        settingObj.value.postSql = obj.postSql;
        settingObj.value.newColumns = obj.newColumns;
        settingObj.value.jvmOpt = obj.jvmOpt;
        settingObj.value.byteS = -1;
        settingObj.value.record = -1;
        settingObj.value.batchSize = obj.batchSize;
        // settingObj.value.incrementColumn = obj.incrementColumn

        if (form.value.sourceDataType == 'HIVE' || form.value.aimDataType == 'HIVE') {
          settingObj.value.rDefaultFS = JSON.parse(obj.hiveReaderParameter)?.defaultFS;
          settingObj.value.rTempDatabase = JSON.parse(obj.hiveReaderParameter)?.tempDatabase;
          settingObj.value.rTempDatabasePath = JSON.parse(
            obj.hiveReaderParameter,
          )?.tempDatabasePath;

          settingObj.value.partitions = JSON.parse(obj.hiveWriterParameter)?.partitions;
          settingObj.value.wDefaultFS = JSON.parse(obj.hiveWriterParameter)?.defaultFS;
          settingObj.value.wTempDatabase = JSON.parse(obj.hiveWriterParameter)?.tmpDatabase;
          settingObj.value.wTempDatabasePath = JSON.parse(obj.hiveWriterParameter)?.tmpDatabasePath;
        }

        console.log(settingObj.value);
      }
      settingObj.value.sourceTableName = row.tableName;
      console.log(row.tableMapping);
      otherSetting.value = true;
    } else if (isDbSync.value) {
      //   return proxy.$modal.msgWarning('请选择目标表');
    }
  };
  // 存放端点数组
  const point = ref([]);
  // 连线样式
  const pointStyle = {
    // 端点的颜色样式
    paintStyle: { stroke: 'black' },
    // 设置端点的类型，大小、css 类名、浮动上去的 css 类名
    endpoint: ['Dot', { radius: 5, cssClass: 'initial_endpoint', hoverClass: 'hover_endpoint' }],
  };
  /**
   * e.srcElement.id - DOM 节点对应的 id 名
   * @param {String} e -点击节点对应的 DOM 属性
   */
  const addPoint = (e) => {
    console.log(e);
    console.log(point.value);
    // 点击左侧 dom 时，判断是否该节点已存在端点，存在的话需要删除当前的端点和连线
    if (
      point.value.length &&
      point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id).length
    ) {
      // 删除当前 DOM 的连线
      plumbIns.value.deleteConnectionsForElement(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0].anchor
          .elementId,
      );
      // 删除当前 DOM 的端点
      plumbIns.value.deleteEndpoint(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0],
      );
      // 更新当前存在的端点数据
      point.value = point.value.filter((res) => res.elementId != e.srcElement.offsetParent.id);
    } else {
      // 右边的 dom 不需要增加端点
      if (!e.srcElement.offsetParent.className.includes('rightItem')) {
        point.value.push(
          plumbIns.value.addEndpoint(
            e.srcElement.offsetParent.id,
            {
              anchors: ['Right'],
            },
            pointStyle,
          ),
        );
      }
    }
    console.log(point.value);
    // 左侧每个 DOM 只能有一条连线
    if (allData.value.some((data) => data.sourceId == point.value.slice(-1)[0]?.anchor.elementId)) {
      return;
    }
    // 右侧 DOM 只能有一个箭头
    if (allData.value.some((data) => data.targetId == e.srcElement.offsetParent.id)) {
      return;
    }
    // 点击右边的 DOM 时，才触发连线操作
    if (e.srcElement.offsetParent.className.includes('rightItem')) {
      // 端点数组可能没有数据，需要判断一下
      point.value.length &&
        plumbIns.value.ready(() => {
          plumbIns.value.connect({
            // 连线起点
            source: point.value.slice(-1)[0].anchor.elementId,
            // 连线终点
            target: e.srcElement.offsetParent.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 存放所有的连接信息
  const allData = ref([]);
  // 删除所有连线
  const cancelAllConnection = () => {
    allData.value = [];
    point.value = [];
    plumbIns.value.deleteEveryConnection();
    plumbIns.value.deleteEveryEndpoint();
  };
  // 同行连接
  const peerConnect = () => {
    cancelAllConnection();
    // 根据长度判断循环连线的数据
    if (leftElList.value.length <= rightElList.value.length) {
      leftElList.value.forEach((res, index) => {
        plumbIns.value.connect({
          // 连线起点
          source: res.id,
          // 连线终点
          target: rightElList.value[index].id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
      });
    } else if (leftElList.value.length > rightElList.value.length) {
      rightElList.value.forEach((res, index) => {
        plumbIns.value.connect({
          // 连线起点
          source: leftElList.value[index].id,
          // 连线终点
          target: res.id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
      });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 同名连接
  const sameNameConnect = () => {
    cancelAllConnection();
    leftElList.value.forEach((res) => {
      rightElList.value.forEach((key) => {
        // 将 res.id 和 key.id.split('*')[0] 都转换为小写，然后进行比较
        if (res.id.split('*')[0].toLowerCase() == key.id.split('*')[0].toLowerCase()) {
          plumbIns.value.connect({
            // 连线起点
            source: res.id,
            // 连线终点
            target: key.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        }
      });
    });
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 保存字段映射
  const submitFormField = () => {
    // 获取所有的连接信息
    const item = plumbIns.value.getAllConnections();

    // 过滤目标为空的字段
    // let item = fieldList.value.filter(i => i.field != null && i.field != '')

    console.log(item[0].targetId.split('/')[1]);
    // columnsMapping.value = []
    // console.log(tableList.value.filter(res => res.tableName == item[0].sourceTableName));
    if (item.length) {
      // 将连接的数据进行保存，用于回显连线
      if (
        tableList.value.filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
          .connectData?.length
      ) {
        // 每次赋值前，清空之前选择的数据
        tableList.value.filter(
          (res) => res.tableName == item[0].targetId.split('/')[1],
        )[0].connectData = [];
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .connectData.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId,
              target: i.targetId,
            });
        });
      } else {
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .connectData.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId,
              target: i.targetId,
            });
        });
      }
      // 连线数据
      // 判断是否是第一次添加表格中的字段映射数据
      if (
        tableList.value.filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
          .columnsMapping?.length
      ) {
        // 每次赋值前，清空之前选择的数据
        tableList.value.filter(
          (res) => res.tableName == item[0].targetId.split('/')[1],
        )[0].columnsMapping = [];
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .columnsMapping.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId.split('*')[0],
              target: i.targetId.split('*')[0],
            });
        });
      } else {
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].targetId.split('/')[1])[0]
            .columnsMapping.push({
              sourceTableName: i.sourceTableName,
              source: i.sourceId.split('*')[0],
              target: i.targetId.split('*')[0],
            });
        });
      }
    } else {
      proxy.$modal.msgWarning('字段映射不能为空');
    }

    open.value = false;
  };
  const cancelEditField = () => {
    cancelAllConnection();
    open.value = false;
  };
  const submitFormSet = () => {
    console.log(settingObj.value.RdefaultFS);

    const item = tableList.value.filter((i) => i.tableName == settingObj.value.sourceTableName)[0];
    const obj = item?.tableMapping?.filter(
      (i) => i.sourceTableName == settingObj.value.sourceTableName,
    )[0];
    obj.primaryKeys = settingObj.value.primaryKeys;
    obj.isUpsert = settingObj.value.isUpsert;
    obj.parallel = settingObj.value.parallel;
    obj.writeMode = settingObj.value.writeMode;
    obj.splitPk = settingObj.value.splitPk;
    obj.whereSql = settingObj.value.whereSql;
    obj.errorLimit = settingObj.value.errorLimit;
    obj.preSql = settingObj.value.preSql;
    obj.postSql = settingObj.value.postSql;
    obj.newColumns = settingObj.value.newColumns;
    obj.incrementColumn = settingObj.value.incrementColumn;
    obj.jvmOpt = settingObj.value.jvmOpt;
    obj.byteS = -1;
    obj.record = -1;
    obj.batchSize = settingObj.value.batchSize;

    obj.hiveReaderParameter = JSON.stringify({
      defaultFS: settingObj.value.rDefaultFS,
      tempDatabase: settingObj.value.rTempDatabase,
      tempDatabasePath: settingObj.value.rTempDatabasePath,
    });
    obj.hiveWriterParameter = JSON.stringify({
      partitions: settingObj.value.partitions,
      defaultFS: settingObj.value.wDefaultFS,
      tmpDatabase: settingObj.value.wTempDatabase,
      tmpDatabasePath: settingObj.value.wTempDatabasePath,
    });

    console.log(tableList.value);
    // 关闭弹窗
    otherSetting.value = false;
  };
  const cancelEditSet = () => {
    otherSetting.value = false;
  };
  // 重置其他编辑中的数据
  const reset = () => {
    settingObj.value = {
      sourceTableName: '',
      newColumns: [],
      parallel: '1',
      writeMode: 'APPEND_DATA',
      primaryKeys: null,
      isUpsert: false,
      incrementColumn: null,
      radioVal: '',
      preSql: '',
      postSql: '',
      errorLimit: '',
      whereSql: '',
      splitPk: '',
      jvmOpt: '--jvm="-Xms1G -Xmx1G"',
      byteS: -1,
      record: -1,
      batchSize: 5000,
      rDefaultFS: fieldList?.value[0]?.otherOptions
        ? JSON.parse(fieldList.value[0].otherOptions).defaultFS
        : undefined,
      partitions: fieldList?.value[0]?.otherOptions
        ? JSON.parse(fieldList.value[0].otherOptions).partitionKeys
        : undefined,
      wDefaultFS: fieldList?.value[0]?.otherOptions
        ? JSON.parse(fieldList.value[0].otherOptions).defaultFS
        : undefined,
    };
    // 解除 writeMode 非 mysql的限制
    // if (form.value.aimDataType == 'MYSQL') {
    //   settingObj.value.writeMode = 'APPEND_DATA';
    // } else {
    //   settingObj.value.writeMode = null;
    // }
  };
  const determineType = (type, aim) => {
    if (aim == 'aim') {
      return form.value.aimTable;
    } else {
      return form.value.sourceTable;
    }
  };
  const emit = defineEmits();
  // 返回按钮
  const callBack = () => {
    emit('tab-remove-click', NodeData.value.id);
  };
  const sumBitAllParams = () => {
    if (!isDbSync.value) {
      if (tableList.value.some((res) => res?.columnsMapping?.length == 0)) {
        return proxy.$modal.msgWarning('请确定所有的字段映射配置');
      }
      if (tableList.value.some((res) => res?.tableMapping?.length == 0)) {
        return proxy.$modal.msgWarning('请确定所有的其他配置');
      }
    }

    const colLen = tableList.value?.length;
    console.log(tableList.value);
    // return
    if (colLen && !isDbSync.value) {
      tableList?.value.forEach((i) => {
        i.tableMapping[0].columnsMapping = [];
      });
      console.log(tableList.value);
      tableList?.value?.forEach((i) => {
        i.tableMapping[0].columnsMapping = i.columnsMapping;
        i.tableMapping[0].destTableName = i.target;
        i.tableMapping[0].sourceTableName = i.tableName;
      });
      let allParams = [];
      // 拼接所有字段
      tableList.value?.forEach((i) => {
        allParams = allParams.concat(i.tableMapping);
      });
      console.log(allParams);

      // 增加模式字段并处理源和目标数据库/模式
      allParams.forEach((i) => {
        i.destDatabase = form.value.aimDatabase;
        i.sourceDatabase = form.value.sourceDatabase;
        i.destSchemaName = form.value.aimTable;
        i.sourceSchemaName = form.value.sourceTable;
      });

      // 解除 非 mysql 的限制
      // mysql writeMode 的值为 null
      // if (form.value.aimDataType != 'MYSQL') {
      //   allParams.forEach((i) => {
      //     i.writeMode = null;
      //   });
      // }
      let tableIndex = null;
      allParams.map((i, index) => {
        if (!i.columnsMapping.length) {
          return (tableIndex = index + 1);
        }
      });
      if (tableIndex) {
        return proxy.$modal.msgWarning(`列表中第${tableIndex}行字段映射配置未确定`);
      }

      allParams.forEach((i) => {
        console.log('i.columnsMapping', i.destDatabase);
        // target: 'ID*rightItem/CREATE TABLE',
        i.columnsMapping.forEach((item) => {
          if (item.target.includes('*')) {
            item.source = item.source.split('*')[0];
            item.target = item.target.split('*')[0];
          }
        });
      });

      createOrUpdate({
        flowId: NodeData.value?.flowId,
        nodeId: NodeData.value?.id,
        sourceDatasourceType: form.value.sourceDataType,
        destDatasourceType: form.value.aimDataType,
        sourceDataSourceId: form.value.sourceDataSource,
        destDataSourceId: form.value.aimDataSource,
        tableMapping: allParams,
        isDbSync: isDbSync.value,
      }).then((res) => {
        if (res.code !== 200) return proxy.$modal.msgError(res.msg);
        proxy.$modal.msgSuccess(res.msg);
      });
    } else if (isDbSync.value) {
      const allParams = [];
      // 拼接所有字段
      // destDatabase destSchemaName destTableName
      // sourceDatabase destSchemaName sourceTableName
      console.log('form.value', form.value);

      tableList.value?.forEach((i) => {
        allParams.push({
          destDatabase: form.value.aimDatabase,
          destSchemaName: determineType(form.value.aimDataType, 'aim'),
          destTableName: i.target,
          sourceDatabase: form.value.sourceDatabase,
          sourceSchemaName: determineType(form.value.sourceDataType, 'source'),
          sourceTableName: i.tableName,
        });
      });

      createOrUpdate({
        flowId: NodeData.value?.flowId,
        nodeId: NodeData.value?.id,
        sourceDatasourceType: form.value.sourceDataType,
        destDatasourceType: form.value.aimDataType,
        sourceDataSourceId: form.value.sourceDataSource,
        destDataSourceId: form.value.aimDataSource,
        tableMapping: allParams,
        isDbSync: isDbSync.value,
      }).then((res) => {
        if (res.code !== 200) return proxy.$modal.msgError(res.msg);
        proxy.$modal.msgSuccess(res.msg);
      });
    } else {
      return proxy.$modal.msgWarning('请选择需要同步的源表和目标表');
    }
  };
  const getByNodeIdUtil = async (nodeData) => {
    if (nodeData) {
      console.log('nodeData', nodeData);
      await getByNodeId(nodeData?.id).then((res) => {
        if (res.code == 200) {
          console.log('res.data', res.data);
          openWorkFlowData.value = res.data;
          if (openWorkFlowData.value == null) {
            getType();
          }
          console.log('openWorkFlowData.value', openWorkFlowData.value);
        }
      });
    } else {
      await getByNodeId(NodeData.value?.id).then((res) => {
        if (res.code == 200) {
          console.log('res.data', res.data);
          openWorkFlowData.value = res.data;
          console.log('openWorkFlowData.value', openWorkFlowData.value);
        }
      });
    }
  };
  const editUtil = async () => {
    if (openWorkFlowData.value) {
      // 回显 源和目标 数据下拉
      form.value.sourceDataType = openWorkFlowData.value?.sourceDatasourceType;
      await getType(form.value.sourceDataType);
      form.value.sourceDataSource = openWorkFlowData.value?.sourceDataSourceId;
      await getSourceDB(form.value.sourceDataSource);
      form.value.sourceDatabase = openWorkFlowData.value?.tableMapping[0].sourceDatabase;
      await getSourceTable(form.value.sourceDatabase);
      form.value.sourceTable = openWorkFlowData.value?.tableMapping[0].sourceSchemaName;
      await getSourceGP(form.value.sourceTable);

      form.value.aimDataType = openWorkFlowData.value?.destDatasourceType;
      await getTarget(form.value.aimDataType);
      form.value.aimDataSource = openWorkFlowData.value?.destDataSourceId;
      await getTargetDB(form.value.aimDataSource);
      form.value.aimDatabase = openWorkFlowData.value?.tableMapping[0].destDatabase;
      await getTargetTable(form.value.aimDatabase);
      form.value.aimTable = openWorkFlowData.value?.tableMapping[0].destSchemaName;
      await getGPTable(form.value.aimTable);

      const sourceTableNames = openWorkFlowData.value?.tableMapping.map(
        (item) => item.sourceTableName,
      );
      const destTableNames = openWorkFlowData.value?.tableMapping.map((item) => item.destTableName);
      isDbSync.value = openWorkFlowData.value?.isDbSync;

      nextTick(() => {
        treeIsshow();
        // 树结构勾选
        deptTreeRef.value?.setCheckedKeys(sourceTableNames, false);
        // 表数据渲染
        tableList.value = sourceTableNames.map((sourceTableName, index) => ({
          tableName: sourceTableName,
          trgFieldList: [],
          tableMapping: [],
          columnsMapping: [],
          // 回显所需数据
          connectData: [],
          target: destTableNames[index],
        }));
        // 回显其他配置中的字段
        tableList.value.forEach((resp) => {
          edit(resp);
          submitFormSet();
        });
        // 获取字段映射配置
        openWorkFlowData.value?.tableMapping.forEach((item) => {
          tableList.value.forEach((res, index) => {
            if (res.tableName == item.sourceTableName) {
              item.columnsMapping.forEach((i) => (i.sourceTableName = item.sourceTableName));
              tableList.value[index].columnsMapping = item.columnsMapping;
              // 回显连线需要数据
              tableList.value[index].connectData = item.columnsMapping;
            }
          });
        });
        tableList.value.forEach((res) => {
          res.connectData.forEach((item) => {
            item.source = `${item.source}*leftItem`;
            item.target = `${item.target}*rightItem/${item.sourceTableName}`;
          });
        });
        console.log(tableList.value, '--------------------------------------------------');
      });
    }
  };
  onMounted(async () => {
    await getByNodeIdUtil();
    await editUtil();
  });

  watch(NodeData, (newVal, oldVal) => {
    if (newVal) {
      console.log('newVal', newVal);
      getByNodeIdUtil(newVal);
      getType();

      editUtil();
    }
  });

  const isDbSync = ref(false);
  const isDbSyncChange = (data) => {
    if (data) {
      // 勾选全部，但禁用不是 TABLE 类型的节点
      sourceDataTableList.value.forEach((item) => {
        if (item.tableType !== 'TABLE') {
          item.disabled = true;
        }
      });
    } else {
      // 取消所有勾选，并清除禁用状态
      sourceDataTableList.value.forEach((item) => {
        item.disabled = false;
      });
    }

    proxy.$refs.deptTreeRef.setCheckedKeys(
      data
        ? sourceDataTableList.value
            .filter((item) => item.tableType === 'TABLE')
            .map((item) => item.tableName)
        : [],
    );

    tableList.value = data
      ? sourceDataTableList.value.filter((item) => item.tableType === 'TABLE')
      : [];
  };

  const isUpsertChange = (data) => {
    console.log(data);
  };
  const otherFieldList = ref([]);
  const getFieldListUtil = async (data) => {
    // if (data) {
    // sourceFieldList.value = res.data;
    //   console.log(data);
    await getFieldList(data).then((res) => {
      if (res.data && res.data.length) {
        otherFieldList.value = res.data;
      } else {
        // proxy.$modal.msgWarning('源数据源当前模式下没有表');
      }
    });
  };

  // 仅以下源到xugu支持，选择其他数据源时，屏蔽整库同步按钮
  // MySQL、Xugu、Oracle、PostgreSQL、SQL server、DaMeng、GreenPlum。
  const isOpenDbSync = computed(() => {
    const supportedSourceTypes = [
      'MYSQL',
      'XUGU',
      'ORACLE',
      'POSTGRESQL',
      'SQLSERVER',
      'DAMENG',
      'GREENPLUM',
    ];
    return (
      form.value.aimDataType === 'XUGU' && supportedSourceTypes.includes(form.value.sourceDataType)
    );
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .bTitle {
    font-weight: 800;
    color: #138280;
  }
</style>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .my-select {
    .el-select-dropdown__item {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        overflow: visible;
        white-space: normal;
      }
    }
  }

  .btnGruop {
    text-align: center;
    margin-bottom: 25px;
  }
  .field-container-box {
    height: calc(100vh - 440px);
    overflow: scroll;
    .fieldContainer {
      height: auto;
      overflow: auto;
      position: relative;
      padding: 10px;

      .list {
        float: left;

        .listItem {
          height: 60px;
          text-align: left;
          line-height: 25px;
          padding-top: 5px;
          box-sizing: border-box;
          cursor: pointer;
          min-width: 270px;
          max-width: 270px;
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .listItenInner {
            position: relative;
            border: 1px solid #d8dade;
            color: #4e5370;
            border-radius: 2px;
            height: 52px;
            padding: 5px 10px;

            .inner-content {
              display: inline-block;
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #434343;
            }

            .inner-div-content {
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #8c8c8c;
            }
          }
        }
      }

      .left {
        margin-right: 160px;
      }
    }
  }

  .configCP {
    // display: inline-block;
    border: 1px solid #dfe3e8;
    border-top: 3px solid #138280;
    padding: 20px;
    z-index: 999;
    width: 70vw;
    max-height: 400px;
  }

  .srcAndTrg {
    margin-top: 20px;
    max-height: 400px;
  }

  .srcAndTrg-loading {
    height: 200px;
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }
  .default-theme {
    margin-top: 20px;
    height: calc(100% - 20px);
  }

  .configure-data-source-box {
    // min-width: 100%;
    height: calc(100vh - 147px);
    width: 100%;
    background-color: $--base-color-card-bg2;
    overflow: auto;
    .configure-top {
      height: 66px;
      box-shadow: 0px 4px 12px #0166f314;
      text-align: right;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
    }
    .configure-content {
      height: calc(100% - 106px);
      padding: 20px;
      overflow: auto;
      .configure-title {
        color: $--base-color-title1;
        position: relative;
        font-size: 16px;
        line-height: 20px;
        padding-left: 12px;

        &::before {
          content: '';
          width: 3px;
          height: 16px;
          position: absolute;
          top: 2px;
          left: 0px;
          background-color: $--base-color-primary;
          border-radius: 4px;
        }
      }
      // 配置数据源
      .config {
        width: calc(50% - 60px);
        display: inline-block;
        vertical-align: top;
        //   border: 1px solid #dfe3e8;
        //   border-top: 3px solid #138280;
        padding: 20px;
        & > p {
          padding-left: 32px;
          margin-top: 10px;
          line-height: 20px;
          height: 20px;
          position: relative;
          &::before {
            content: '';
            width: 32px;
            height: 32px;
            position: absolute;
            top: -12px;
            left: 0px;
            background: url('@/assets/images/dataAggregation/processDesign/configure-data-source.png')
              no-repeat center;
            background-size: 32px auto;
          }
          &.target-title {
            &::before {
              background: url('@/assets/images/dataAggregation/processDesign/configure-data-target.png')
                no-repeat center;
              background-size: 32px auto;
            }
          }
        }
        ::v-deep .el-form-item__label {
          font-weight: normal;
        }
        &.config-target {
          position: relative;
          &::before {
            content: '';
            width: 40px;
            height: 40px;
            position: absolute;
            top: calc(50% - 20px);
            left: -80px;
            background: url('@/assets/images/dataAggregation/processDesign/configure-data-arrow.png')
              no-repeat center;
            background-size: 40px auto;
          }
        }
      }
    }
  }
</style>
