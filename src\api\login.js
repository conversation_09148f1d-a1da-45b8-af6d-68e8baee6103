import { encrypt } from '@/utils/jsencrypt'; // 加密 解密
import request from '@/utils/request';
// 登录方法
export function login(username, password, code, uuid) {
  // 密码未加密，加密
  // if (password.length < 20) {
  password = encrypt(password);
  // }
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
    },
    method: 'post',
    data: { username, password, code, uuid },
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get',
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete',
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  });
}

// /system//sysCompanyInfo

// 获取公司信息
export function getCompanyInfo() {
  return request({
    url: '/system/sysCompanyInfo',
    method: 'get',
  });
}
export function uploadImg(query) {
  return request({
    url: '/system/sysCompanyInfo/editImg',
    method: 'get',
    params: query,
  });
}

// /sysCompanyInfo/editInfo

// 修改公司信息
export function editCompanyInfo(data) {
  return request({
    url: '/system/sysCompanyInfo/editInfo',
    method: 'put',
    data,
  });
}

//   /user/reNewLicense
export function updateLicense(key) {
  return request({
    url: '/system/user/reNewLicense',
    method: 'post',
    data: key,
  });
}

// /getLicense
export function getLicense(key) {
  return request({
    url: '/system/user/getLicense',
    method: 'get',
    params: {
      key,
    },
  });
}
