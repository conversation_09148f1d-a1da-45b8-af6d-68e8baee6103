<template>
  <el-form ref="dataSourceRef" :model="busKeyTableForm" label-width="100px">
    <el-form-item
      :label="busKeyTableForm.formItemO"
      prop="tableData"
      :rules="[{ required: true, message: '请选择' }]"
    >
      <el-table
        ref="tableRef"
        :data="busKeyTableForm.tableData"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        border="1"
        size="mini"
        height="260px"
        empty-text="暂无数据"
        style="min-height: 150px"
      >
        <el-table-column v-for="(item, index) in busKeyCol" :key="index" v-bind="item">
          <template #default="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.' + item.prop"
              :rules="busKeyRules[item.prop]"
            >
              <el-select
                v-if="item.prop === 'field'"
                v-model="scope.row[item.prop]"
                placeholder="请选择字段"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option
                  v-for="items in fieldList"
                  :key="items.columnName"
                  :label="items.columnName"
                  :value="items.columnName"
                />
              </el-select>
              <el-select
                v-if="item.prop === 'operate'"
                v-model="scope.row[item.prop]"
                placeholder="请选择操作"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option
                  v-for="items in operateList"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                />
              </el-select>
              <el-button
                v-if="item.prop === 'compareValue'"
                v-show="operateType(scope.row)"
                type="primary"
                :text="true"
                size="mini"
                bg
                :disabled="!CanvasActions"
                @click="open(scope.row)"
              >
                <el-tooltip
                  v-if="scope.row[item.prop]?.value == undefined"
                  effect="dark"
                  content="配置"
                  placement="top"
                >
                  配置
                  <el-icon>
                    <setting />
                  </el-icon>
                </el-tooltip>

                <el-tooltip
                  v-else
                  effect="dark"
                  :content="scope.row[item.prop]?.value"
                  placement="top"
                >
                  {{
                    scope.row[item.prop]?.value?.length > 4
                      ? scope.row[item.prop]?.value?.slice(0, 4) + '...'
                      : scope.row[item.prop]?.value
                  }}
                </el-tooltip>
                <template
                  v-for="tag in [
                    { type: 'STRING', tagType: 'success', tagText: 'S' },
                    { type: 'NUMBER', tagType: 'warning', tagText: 'N' },
                    { type: 'EXPRESSION', tagType: 'danger', tagText: 'E' },
                    { type: 'FIELD', tagType: 'info', tagText: 'F' },
                  ]"
                >
                  <el-tag
                    v-if="tag.type === scope.row[item.prop]?.type"
                    :key="tag.type"
                    :type="tag.tagType"
                    size="mini"
                  >
                    {{ tag.tagText }}
                  </el-tag>
                </template>
              </el-button>
              <el-select
                v-if="item.prop === 'conditionalRelation'"
                v-model="scope.row[item.prop]"
                placeholder="请选择条件关系"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option
                  v-for="items in conditionalRelationList"
                  :key="items.value"
                  :label="items.label"
                  :value="items.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              type="text"
              icon="Delete"
              :disabled="!CanvasActions"
              @click="deleteSyncChange(scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>
      <el-button
        type="text"
        style="margin-left: 40%"
        :disabled="!CanvasActions"
        @click="addSyncChange"
        >添加一行</el-button
      >
    </el-form-item>
    <el-form-item v-if="false" :label="busKeyTableForm.formItemT">
      <el-input
        v-model="tableAliases"
        :placeholder="busKeyTableForm.tableAliasesPlaceholder"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <!-- 配置 -->
  <el-dialog
    v-model="dialogVisible"
    title="配置"
    width="560px"
    :close-on-click-modal="false"
    append-to-body
    :draggable="true"
    @close="cancelEditField()"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="自定义">
        <el-input v-model="input3" placeholder="Please input" class="input-with-select" size="mini">
          <template #prepend>
            <el-select
              v-model="selectName"
              placeholder="Select"
              style="width: 115px"
              size="mini"
              @change="onChangeinput"
            >
              <el-option
                v-for="item in selectNameValueList"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :disabled="item.value === 'EXPRESSION' && selectNameDis"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div div class="dialog-footer">
        <el-button @click="cancelEditField">取 消</el-button>
        <el-button type="primary" @click="submitEditField">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getNodeData } from '@/api/DataDev';

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const tableData = ref([]);

  // 字段列表
  const fieldList = ref([]);
  // 操作列表
  const operateList = ref([
    /**
     * 等于、不等于、大于、大于等于、小于、小于等于、包含、不包含、开头为、结尾为、为 Null、不为 Null、为空（包含为 Null）、不为空（前提是不为 Null）
     */
    //  EQ("="),
    // NE("<>"),
    // GT(">"),
    // GE(">="),
    // LT("<"),
    // LE("<="),
    // CONTAIN("contain"),
    // NOT_CONTAIN("notContains"),
    // START_WITH("startWith"),
    // END_WITH("endWith"),
    // NULL("null"),
    // NONNULL("nonNull"),
    // EMPTY("empty"),
    // NONEMPTY("nonEmpty"),

    // { value: '=', label: '=' },
    // { value: '!=', label: '!=' },
    // { value: '>', label: '>' },
    // { value: '>=', label: '>=' },
    // { value: '<', label: '<' },
    // { value: '<=', label: '<=' },
    // { value: '包含', label: '包含' },
    // { value: '不包含', label: '不包含' },
    // { value: '开头为', label: '开头为' },
    // { value: '结尾为', label: '结尾为' },
    // { value: '为 Nul', label: '为 Nul' },
    // { value: '不为 Nu 川', label: '不为 Nu 川' },
    // { value: '为空', label: '为空' },
    // { value: '不为空', label: '不为空' },
    {
      value: 'EQ',
      label: '=',
    },
    {
      value: 'NE',
      label: '<>',
    },
    {
      value: 'GT',
      label: '>',
    },
    {
      value: 'GE',
      label: '>=',
    },
    {
      value: 'LT',
      label: '<',
    },
    {
      value: 'LE',
      label: '<=',
    },
    {
      value: 'CONTAIN',
      label: '包含',
    },
    {
      value: 'NOT_CONTAIN',
      label: '不包含',
    },
    {
      value: 'START_WITH',
      label: '开头为',
    },
    {
      value: 'END_WITH',
      label: '结尾为',
    },
    {
      value: 'NULL',
      label: '为 Null',
    },
    {
      value: 'NONNULL',
      label: '不为 Null',
    },
    {
      value: 'EMPTY',
      label: '为空',
    },
    {
      value: 'NONEMPTY',
      label: '不为空',
    },
  ]);

  // 条件关系列表
  const conditionalRelationList = ref([
    { value: 'AND', label: 'AND' },
    { value: 'OR', label: 'OR' },
  ]);

  const dialogVisible = ref(false);
  const selectName = ref('STRING');
  const input3 = ref('');
  const tableAliases = ref('');

  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      tableAliases: '',
    },
    busKeyTableForm: {
      tableData: [],
    },
  });

  const { form, busKeyTableForm } = toRefs(data);
  const rowKey = ref('');
  const selectNameDis = ref(false);
  const open = (row) => {
    const allowedValues = [
      'CONTAIN',
      'NOT_CONTAIN',
      'START_WITH',
      'END_WITH',
      'NULL',
      'NONNULL',
      'EMPTY',
      'NONEMPTY',
    ];

    if (allowedValues.includes(row.operate)) {
      selectNameDis.value = true;
      console.log('898989', selectNameDis.value);
    }

    selectNameTypeChange(row);

    rowKey.value = row.key;
    dialogVisible.value = true;
    // 首先看当前的有值没有 有值先用之前的 没值 用 新的
    const index = busKeyTableForm.value.tableData.findIndex((item) => item.key === row.key);
    console.log(index);
    if (busKeyTableForm.value.tableData[index].compareValue) {
      selectName.value = busKeyTableForm.value.tableData[index].compareValue.type;
      input3.value = busKeyTableForm.value.tableData[index].compareValue.value;
      //   selectNameType.value = selectName.value !== 'FIELD' ? 2 : 1;
    } else {
      selectName.value = 'STRING';
      input3.value = '';
    }
  };
  const cancelEditField = () => {
    dialogVisible.value = false;
    input3.value = '';
    selectName.value = 'STRING';
    selectNameDis.value = false;
  };
  const submitEditField = () => {
    dialogVisible.value = false;
    // 找到 当前的 tableData 把 input3 和 selectName 赋值给 compareValue
    const compareValue = {
      //   type: selectNameType.value === 1 ? 'FIELD' : selectName.value,
      type: selectName.value,
      value: input3.value,
    };
    // 使用 rowKey.value 去 tableData 中找到对应的数据
    const index = busKeyTableForm.value.tableData.findIndex((item) => item.key === rowKey.value);
    busKeyTableForm.value.tableData[index].compareValue = compareValue;
    selectNameTypeShow.value = false;
  };

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };

  const { proxy } = getCurrentInstance();
  const submitDrawer = async () => {
    const r = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!r) return;
    const res = await DataProcessing();

    NodeData.value.inputProperties[0].value = res;
    NodeData.value.inputProperties[1].value = tableAliases.value;

    emit('submitDrawer', NodeData.value);
  };

  function DataProcessing() {
    const JSon = busKeyTableForm.value.tableData.map((item) => ({
      field: item.field,
      operate: item.operate,
      compareValue: item.compareValue,
      conditionalRelation: item.conditionalRelation,
      key: item.key,
    }));
    return JSON.stringify(JSon);
  }

  const init = async () => {
    busKeyTableForm.value.tableData = [];
    tableAliases.value = '';
    busKeyTableForm.value.formItemO = NodeData.value.inputProperties[0].displayName;

    busKeyTableForm.value.formItemT = NodeData.value.inputProperties[1].displayName;
    busKeyTableForm.value.tableAliasesPlaceholder = NodeData.value.inputProperties[1].description;
    await getNodeDataUtil();
    // 回显
    if (NodeData.value.inputProperties[0].value) {
      const tableDataData = JSON.parse(NodeData.value.inputProperties[0].value);
      busKeyTableForm.value.tableData = tableDataData;
    }
    if (NodeData.value.inputProperties[1].value) {
      tableAliases.value = NodeData.value.inputProperties[1].value;
    }
  };

  function deleteSyncChange(index) {
    busKeyTableForm.value.tableData.splice(index, 1);
  }

  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    console.log(res);
    if (res.data && res.data.metadata.length) {
      const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
      if (currentIndex > 0) {
        const previousNode = res.data.metadata[currentIndex - 1];
        fieldList.value = previousNode ? previousNode.columns : [];
      } else if (res.data.metadata.length === 1) {
        const currentNode = res.data.metadata[0];
        fieldList.value = currentNode ? currentNode.columns : [];
      } else {
        fieldList.value = res.data.metadata[res.data.metadata.length - 1].columns;
      }
    }
  };

  const operateType = (syncChange) => {
    // 检查 operate 是否不是这四个特定的值之一
    if (
      syncChange.operate !== 'EMPTY' &&
      syncChange.operate !== 'NONEMPTY' &&
      syncChange.operate !== 'NULL' &&
      syncChange.operate !== 'NONNULL'
    ) {
      // 如果不是这四个值之一，返回 true
      return true;
    } else {
      // 如果是这四个值之一，返回 false
      return false;
    }
  };
  const selectNameTypeShow = ref(false);
  const selectNameTypeChange = (row) => {
    console.log(row);
    if (
      row.operate === 'CONTAIN' ||
      row.operate === 'NOT_CONTAIN' ||
      row.operate === 'START_WITH' ||
      row.operate === 'END_WITH'
    ) {
      selectNameTypeShow.value = true;
    } else {
      selectNameTypeShow.value = false;
    }
  };

  const selectNameValueList = ref([
    {
      label: '字符串',
      value: 'STRING',
    },
    {
      label: '数字',
      value: 'NUMBER',
    },
    {
      label: '表达式',
      value: 'EXPRESSION',
    },
  ]);
  const validatePreviousRow = (rule, value, callback) => {
    busKeyTableForm.value.tableData.forEach((item, index) => {
      if (index !== busKeyTableForm.value.tableData.length - 1) {
        if (!item.conditionalRelation) {
          callback(new Error('条件关系未通过校验'));
        }
      }
    });
    callback();
  };

  const busKeyCol = [
    { prop: 'field', label: '字段', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'operate', label: '操作', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'compareValue', label: '操作内容', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'conditionalRelation', label: '条件关系', minWidth: 150, tooltip: true, width: 120 },
  ];
  const busKeyRules = {
    field: [{ required: true, message: '请输入', trigger: 'change' }],
    operate: [{ required: true, message: '请选择', trigger: 'change' }],
    compareValue: [{ required: false, message: '请输入', trigger: 'change' }],
    conditionalRelation: [
      { required: false, message: '请选择', trigger: 'change' },
      {
        validator: validatePreviousRow,
        trigger: 'change',
      },
    ],
  };
  const addHeaderCellClassName = (params) => {
    const { columnIndex } = params;
    if (columnIndex === 0 || columnIndex === 1) {
      return 'required';
    }
  };

  const addSyncChange = () => {
    // 初始化数据...
    const newSyncChange = {
      field: null,
      operate: null,
      compareValue: null,
      conditionalRelation: null,
    };
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;
    busKeyTableForm.value.tableData.push(newSyncChange);
    nextTick(() => {
      scrollToBottomOfTable();
    });
  };

  const generateUniqueKey = () => {
    return Math.random().toString(36).substr(2, 9);
  };
  const scrollToBottomOfTable = () => {
    setTimeout(() => {
      const lastIndex = busKeyTableForm.value.tableData.length - 1;
      const newRow = proxy.$refs.tableRef.$el.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
      );
      newRow.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
    }, 100);
  };

  onMounted(() => {
    init();
  });

  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .table-bordered {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
    color: #606266;

    th {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      padding: 10px;
    }

    el-button {
      margin-left: 10px;
      border: 1px solid #ebeef5;
    }
  }

  .container {
    display: grid;
    justify-content: start;
    gap: 10px;
    grid-template-columns: 0.8fr 0.8fr 1fr 1fr 0.2fr;
    border: 1px solid #ebeef5;
  }

  :deep .el-form-item__content {
    padding: 5px;
  }

  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      background-color: red;
      transform: translate(40px, 5px);
      color: red;
    }
  }
  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }
  .el-form-item .el-form-item {
    margin-bottom: 1.8rem;
  }
</style>
