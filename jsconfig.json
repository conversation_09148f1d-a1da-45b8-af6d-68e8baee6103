// https://code.visualstudio.com/docs/languages/jsconfig
{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "baseUrl": "./",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "paths": {
      // 解决项目中使用@作为路径别名，导致vscode无法跳转文件的问题
      "~/*": ["*"],
      "@/*": ["src/*"],
      "#/*": ["types/*"],
      "@vab/*": ["library/*"],
      "@gp/*": ["library/plugins/vab"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  //提高 IDE 性能
  "exclude": ["node_modules", "dist"]
}
