<template>
  <div class="app-container">
    <div style="margin-bottom: 20px">
      <h2 style="text-align: center">服务 分组</h2>
      <el-input
        v-model="queryParams.keyword"
        prefix-icon="Search"
        placeholder="请输入服务名称"
        clearable
      >
        <template #append>
          <el-button type="primary" @click="handleNodeClick">搜索</el-button>
        </template>
      </el-input>
    </div>
    <splitpanes class="default-theme">
      <pane min-size="16" max-size="25" size="16" class="App-theme">
        <el-row class="head-title-tree">
          <el-col :span="20">
            <span>服务 分组</span>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-tree-v2
              :data="groupList"
              :props="props"
              :height="700"
              highlight-current="true"
              @node-click="handleNodeClick"
            />
          </el-col>
        </el-row>
      </pane>
      <pane>
        <div
          style="
            padding: 20px;
            border-bottom: 1px solid #ddd;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
          "
        >
          <el-row :gutter="20">
            <el-col :span="22"> 共有{{ total }}个 数据目录 </el-col>
            <el-col :span="1.5">
              <!-- <el-button type="primary" @click="onSearch">目录下载</el-button> -->
            </el-col>

            <el-col :span="24">
              主题：
              <el-radio-group v-model="queryParams.dataFilter">
                <el-radio v-for="option in radioOptions" :key="option.label" :label="option.label">
                  {{ option.text }}
                </el-radio>
              </el-radio-group>
            </el-col>

            <el-col :span="24">
              开放类型:
              <el-radio-group v-model="queryParams.open">
                <el-radio v-for="option in openOptions" :key="option.label" :label="option.label">
                  {{ option.text }}
                </el-radio>
              </el-radio-group>
            </el-col>

            <el-col :span="24" style="margin-bottom: 20px">
              已选条件：
              <el-tag
                v-for="tag in selectedTags"
                :key="tag.name"
                closable
                :type="tag.type"
                @close="removeTag(tag)"
              >
                {{ tag.name }}
              </el-tag>
            </el-col>

            <el-col :span="18">
              <el-input v-model="filterText" prefix-icon="Search" />
            </el-col>

            <el-col :span="6">
              <el-check-tag
                v-for="tag in tagsType"
                :key="tag.key"
                :checked="tag.checked"
                :type="tag.type"
                @change="onChange(tag.key)"
              >
                <el-icon><Sort /></el-icon>
                {{ tag.label }}
              </el-check-tag>
            </el-col>
          </el-row>
        </div>

        <el-main style="max-height: 700px; overflow-y: auto" class="scroll-container">
          <el-card v-for="(rowData, index) in tableData" :key="index" class="bodyStyleCard">
            <el-row :gutter="20">
              <el-col :span="20">
                <el-button type="text" @click="drawerOpen">{{ rowData.apiName }}</el-button>
              </el-col>
              <el-col :span="4">
                <div
                  style="
                    text-align: right;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <span>
                    <el-icon><Download /></el-icon> {{ 1222 }}
                  </span>
                  <span>
                    <el-icon><View /></el-icon>{{ 1222 }}
                  </span>
                </div>
              </el-col>
              <el-col :span="24"> {{ rowData.remark }} </el-col>
              <el-col :span="24">
                <el-descriptions title="" :border="false" :colon="true" size="large">
                  <el-descriptions-item label="主题"> </el-descriptions-item>
                  <el-descriptions-item label="发布时间">
                    {{ rowData.releaseTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="更新时间">
                    {{ rowData.updateTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="数据量"> </el-descriptions-item>
                  <el-descriptions-item label="开放条件"> </el-descriptions-item>
                  <el-descriptions-item label="格式">
                    <el-tag v-for="tag in tags" :key="tag.name" :type="tag.type">
                      {{ tag.name }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </el-col>
            </el-row>
          </el-card>
        </el-main>
        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :pager-count="maxCount"
          :total="total"
          @pagination="determine"
        />
      </pane>
    </splitpanes>

    <el-drawer
      v-model="drawerVisible"
      title="查看详情"
      direction="btt"
      size="85%"
      @close="drawerClose"
    >
      <el-card>
        <el-row :gutter="20">
          <el-col :span="22">
            <span>name</span>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" @click="onAsk">申请</el-button>
          </el-col>
        </el-row>

        <el-descriptions title="" :border="false" :colon="true" size="large">
          <el-descriptions-item
            v-for="(item, index) in descriptionsData"
            :key="index"
            :label="item.label"
          >
            <template v-if="item.label === '接口地址'">
              <!-- {{ formatResponseTime(item.value) }} -->
              {{ item.value }}
              <el-link
                v-copyText="item.value"
                v-copyText:callback="copyTextSuccess"
                icon="DocumentCopy"
                :underline="false"
              />
            </template>
            <template v-else>
              {{ item.value }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card style="margin-top: 20px">
        <!-- 请求参数 -->
        <div class="TitleName">请求参数</div>
        <el-table
          ref="request"
          :data="requestData"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="300"
        >
          <el-table-column v-for="(item, index) in reqColumns" :key="index" v-bind="item" />
        </el-table>
        <!-- 响应参数 -->
        <div class="TitleName">响应参数</div>
        <el-table
          ref="respond"
          :data="respondData"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="300"
        >
          <el-table-column v-for="(item, index) in resColumns" :key="index" v-bind="item" />
        </el-table>
        <!-- 结果样例 -->
        <div class="TitleName">结果样例</div>
        <Codemirror
          v-model="codeDataSql"
          style="width: 100%; height: 95%; min-height: 300px"
          :disabled-type="true"
        />

        <!-- 错误码说明 -->
        <div class="TitleName">错误码说明</div>
        <el-table
          ref="errorCode"
          :data="errorCodeData"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="300"
        >
          <el-table-column v-for="(item, index) in errorCodeColumns" :key="index" v-bind="item" />
        </el-table>
      </el-card>
    </el-drawer>
    <el-dialog v-model="dialogVisible" title="申请" width="40%" append-to-body @close="dialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="服务名称" prop="apiName">
          <!-- radio-g -->
          <el-radio-group v-model="form.apiName">
            <el-radio :label="1">API</el-radio>
            <el-radio :label="2">Group</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请说明" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入申请原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="onApply">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import Codemirror from '@/components/Codemirror';
  import { useWorkFLowStore } from '@/store/modules/workFlow';

  import { getApiList, getGroupListTree, getGroupTree, getCategoryList } from '@/api/APIService';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';

  const store = useWorkFLowStore();
  const { proxy } = getCurrentInstance();

  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());

  const data = reactive({
    form: {},
    rules: {
      groupName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],

      parentId: [{ required: true, message: '请选择菜单', trigger: 'change' }],
      categoryName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const props = {
    value: 'groupId',
    label: 'groupName',
    children: 'children',
  };

  const tags = ref([
    {
      name: '标签一',
      type: '',
    },
    {
      name: '标签二',
      type: 'success',
    },
    {
      name: '标签三',
      type: 'warning',
    },
    {
      name: '标签四',
      type: 'danger',
    },
  ]);
  const tableData = ref([]);

  const onSearch = () => {
    console.log('search');
  };

  const tagsType = ref([
    { label: '更新时间', checked: false, type: 'primary', key: 'checked1' },
    { label: '访问量', checked: false, type: 'primary', key: 'checked2' },
    { label: '下载量', checked: false, type: 'primary', key: 'checked3' },
  ]);

  const onChange = (key) => {
    const tag = tagsType.value.find((tag) => tag.key === key);
    if (tag) {
      tag.checked = !tag.checked;
    }
  };

  const total = ref();
  const loading = ref(false);
  const hasMoreData = ref(true);
  const getApiListUtil = async () => {
    if (queryParams.value.pageNum !== 1) {
      if (loading.value || !hasMoreData.value) return; // 防止重复加载或没有更多数据时加载
    }
    loading.value = true;

    const res = await getApiList({
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      keyword: queryParams.value.keyword,
      // 其他参数
    });

    loading.value = false;

    if (res.code !== 200) {
      proxy.$modal.msgError(res.msg);
      return;
    }

    if (queryParams.value.pageNum === 1) {
      tableData.value = res.rows;
    } else {
      tableData.value.push(...res.rows);
    }

    total.value = res.total;
    hasMoreData.value = res.rows.length >= queryParams.value.pageSize; // 判断是否还有更多数据
  };

  const handleScroll = (event) => {
    const container = event.target;
    const bottomOfContainer =
      container.scrollHeight - container.scrollTop <= container.clientHeight;

    if (bottomOfContainer && !loading.value && hasMoreData.value) {
      queryParams.value.pageNum += 1;
      getApiListUtil();
    }
  };

  const openOptions = ref([
    { label: '全部', text: '全部' },
    { label: '公开', text: '公开' },
    { label: '私有', text: '私有' },
  ]);
  const radioOptions = ref([
    { label: '1', text: '全部' },
    { label: '2', text: 'XLS' },
    { label: '3', text: 'XML' },
    { label: '4', text: 'JSON' },
    { label: '5', text: 'CSV' },
    { label: '6', text: 'RDF' },
    { label: '7', text: '接口' },
    { label: '8', text: '链接' },
    { label: '9', text: '其他' },
  ]);
  const selectedTags = computed(() => {
    const tags = [];

    if (queryParams.value.dataFilter) {
      const dataFilterOption = radioOptions.value.find(
        (option) => option.label === queryParams.value.dataFilter,
      );
      if (dataFilterOption) {
        tags.push({ name: dataFilterOption.text, type: 'info', key: 'dataFilter' });
      }
    }

    if (queryParams.value.open) {
      const openOption = openOptions.value.find(
        (option) => option.label === queryParams.value.open,
      );
      if (openOption) {
        tags.push({ name: openOption.text, type: 'success', key: 'open' });
      }
    }

    if (queryParams.value.groupId) {
      tags.push({ name: treeDataInfo.value.groupName, type: 'primary', key: 'groupId' });
    }

    return tags;
  });

  const removeTag = (tag) => {
    queryParams.value[tag.key] = '';
  };

  const groupList = ref([
    {
      value: '',
      categoryName: '全部',
    },
  ]);

  const getGroupListTreeUtil = async () => {
    try {
      const res = await getGroupListTree({
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });

      if (res.code !== 200) {
        proxy.$modal.msgError(res.msg);
        return;
      }

      // Assuming res.data is an array of group items
      groupList.value = [
        {
          value: '',
          categoryName: '全部',
        },
        ...res.data,
      ];

      console.log(groupList.value);
    } catch (error) {
      proxy.$modal.msgError('An error occurred while fetching the group list');
      console.error(error);
    }
  };
  const treeDataInfo = ref([]);
  const handleNodeClick = (data) => {
    // console.log(data);
    treeDataInfo.value = data;
    queryParams.value.pageNum = 1;
    queryParams.value.groupId = data.groupId;
    getApiListUtil();
  };

  const descriptionsData = ref([
    {
      label: '接口编号',
      value: '接口名称',
    },
    {
      label: '认证方式',
      value: '接口路径',
    },
    {
      label: '版本号',
      value: '接口描述',
    },
    {
      label: '接口地址',
      value: '接口描述',
    },
    {
      label: '上线时间',
      value: '接口描述',
    },
    {
      label: '接口备注',
      value: '接口描述',
    },
  ]);
  const drawerVisible = ref(false);
  const drawerOpen = () => {
    drawerVisible.value = true;
    requestDataUtil();
    respondDataUtil();
    codeDataSqlUtil();
    errorCodeDataUtil();
  };

  const drawerClose = () => {
    drawerVisible.value = false;
  };
  const requestData = ref([]);
  const respondData = ref([]);
  const errorCodeData = ref([]);
  const resColumns = ref([
    { key: 0, label: `参数名称`, visible: true, prop: 'categoryId' },
    { key: 1, label: `参数类型`, visible: true, prop: 'categoryId' },
    { key: 2, label: `是否必穿`, visible: true, prop: 'categoryId' },
    { key: 3, label: `备注`, visible: true, prop: 'categoryId' },
    { key: 4, label: `示例值`, visible: true, prop: 'categoryId' },
  ]);
  const reqColumns = ref([
    { key: 0, label: `参数名称`, visible: true, prop: 'paramName' },
    { key: 1, label: `参数类型`, visible: true, prop: 'paramType' },
    { key: 2, label: `是否必穿`, visible: true, prop: 'categoryId' },
    { key: 3, label: `备注`, visible: true, prop: 'categoryId' },
    { key: 4, label: `示例值`, visible: true, prop: 'categoryId' },
  ]);
  const errorCodeColumns = ref([
    { key: 0, label: `错误码`, visible: true, prop: 'categoryId' },
    { key: 1, label: `说明`, visible: true, prop: 'categoryId' },
  ]);

  const codeDataSql = ref('');
  const dialogVisible = ref(false);
  const onAsk = () => {
    dialogVisible.value = true;
  };
  const dialogClose = () => {
    dialogVisible.value = false;
  };
  const onApply = () => {
    dialogVisible.value = false;
  };
  function copyTextSuccess() {
    proxy.$modal.msgSuccess('复制成功');
  }

  // 请求参数
  const requestDataUtil = async (apiId) => {
    try {
      console.log(1);
    } catch (error) {}
  };

  //   响应参数
  const respondDataUtil = async (apiId) => {
    try {
      console.log(1);
    } catch (error) {}
  };

  //   结果样例
  const codeDataSqlUtil = async (apiId) => {
    try {
      console.log(1);
    } catch (error) {}
  };
  const errorCodeDataUtil = async (apiId) => {
    try {
      console.log(1);
    } catch (error) {}
  };

  const getGroupTreeUtil = async () => {
    try {
      const res = await getGroupTree({
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });

      if (res.code !== 200) return proxy.$modal.msgError(res.msg);

      groupList.value = [
        {
          value: '',
          groupName: '全部',
        },
        ...res.data,
      ];
    } catch (error) {
      proxy.$modal.msgError('An error occurred while fetching the group list');
      console.error(error);
    }
  };

  const getCategoryListUtil = async () => {
    try {
      const res = await getCategoryList({
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
        pageNum: 1,
        pageSize: 20,
      });

      if (res.code !== 200) return proxy.$modal.msgError(res.msg);

      radioOptions.value = [
        {
          label: '全部',
          text: '全部',
        },
        ...res.rows.map((item) => ({
          label: item.categoryId,
          text: item.categoryName,
        })),
      ];

      console.log(radioOptions.value);
    } catch (error) {
      proxy.$modal.msgError('An error occurred while fetching the category list');
      console.error(error);
    }
  };
  onMounted(async () => {
    await nextTick();
    getApiListUtil();
    // getGroupListTreeUtil();
    getGroupTreeUtil();
    getCategoryListUtil();
    queryParams.value.open = '全部';
    queryParams.value.dataFilter = '全部';
    const elMain = document.querySelector('.scroll-container');
    if (elMain) {
      elMain.addEventListener('scroll', handleScroll);
    }
  });

  // 移除滚动事件监听，防止内存泄漏
  onUnmounted(() => {
    const elMain = document.querySelector('.scroll-container');
    if (elMain) {
      elMain.removeEventListener('scroll', handleScroll);
    }
  });
  watch(workspaceId, (val) => {
    getApiListUtil();
    getGroupListTreeUtil();
  });
</script>

<style lang="scss" scoped>
  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  .head-title-tree {
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 15px;
  }

  .bodyStyleCard {
    margin-bottom: 10px;
  }
  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
  }
</style>
