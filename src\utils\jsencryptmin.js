import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDPkJpwSSuEdVAhAajuLEgWgbfJmKVi3PHaZyslLDpH5RXGvfWOKXdaVTcg6Pfe+gDThsvWsPSu0Q4YYOH16tM5Mjuhu25+XXof8pljT5fXuymEvCsdb/I9pjjcyUkV5vdSU7FPoeH4ALs42vM8gCpA7Ojdla66DcUIDFn2GCkqAwIDAQAB';

// 加密
export function encryptMin(txt) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey); // 设置公钥
  return encryptor.encrypt(txt); // 对数据进行加密
}
