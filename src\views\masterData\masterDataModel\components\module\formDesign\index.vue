<template>
  <div class="form-design-container">
    <!-- 视图选择区域 -->
    <div class="form-selector">
      <el-form :inline="true" class="selector-form">
        <el-form-item label="选择视图：">
          <el-select
            v-model="selectedViews"
            placeholder="请选择视图"
            multiple
            @change="handleViewChange"
            style="width: 300px"
          >
            <el-option
              v-for="item in availableViews"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 视图设计区域 -->
    <div class="design-area" v-if="viewList.length > 0">
      <el-tabs
        v-model="activeName"
        type="card"
        editable
        @edit="handleTabEdit"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          v-for="item in viewList"
          :key="item.id"
          :label="item.name"
          :name="item.id"
        >
          <!-- 在这里放置表单设计器 -->
          <ng-form-design
            :ref="el => setFormDesignRef(el, item.id)"
            :key="item.id"
            :view-id="item.id"
            @on-change="(config) => handleFormConfigChange(config, item.id)"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <el-empty description="请先选择视图开始设计" />
    </div>
  </div>
</template>

<script setup>
import NgFormDesign from '@/views/ngForm/packages/form-design/index.vue'
import { ref, reactive, computed, onMounted, provide } from 'vue'
import { ElMessage } from 'element-plus'
import { viewOptions, fieldOptions } from '@/views/masterData/masterDataCoding/mockdata.js'

// 字段选择功能相关的响应式数据 - 改为基于viewId的键值对结构
const enableFieldSelect = ref(false)
const fieldOptionsMap = reactive({}) // 存储每个viewId对应的字段选项
const currentViewId = ref('')

// 为 NgFormDesign 组件提供所需的注入
provide('$ngofrm_components', [])
provide('$config', {
  $ngofrm_components: [],
  $ngofrm_config: {}
})

// 提供字段选择功能的依赖注入 - 使用新的数据结构
provide('enableFieldSelect', enableFieldSelect)
provide('fieldOptionsMap', fieldOptionsMap)
provide('currentViewId', currentViewId)

// 响应式数据
const selectedViews = ref([])
const activeName = ref('')
const formDesignRefs = ref(new Map())

// 默认表单ID为客户主数据模型
const formId = 'customer_model'

// 可用视图选项（固定为客户主数据模型的视图）
const availableViews = computed(() => {
  return viewOptions[formId] || []
})

// 已选择的视图列表
const viewList = computed(() => {
  return selectedViews.value.map(viewValue => {
    const viewOption = availableViews.value.find(v => v.value === viewValue)
    return {
      id: viewValue,
      name: viewOption?.label || viewValue,
      value: viewValue
    }
  })
})

// 表单配置存储（每个视图对应一个配置）
const formConfigs = reactive({})

// 更新字段选项数据 - 改为基于viewId的键值对存储
const updateFieldOptions = (viewId) => {
  console.log('updateFieldOptions 被调用, viewId:', viewId)
  console.log('当前 fieldOptionsMap:', fieldOptionsMap)

  // 获取字段选项
  let options = fieldOptions[viewId] || []

  if (options.length === 0) {
    console.log('使用默认字段选项')
    options = [
      { label: '客户编号(customer_code)', value: 'customer_code' },
      { label: '客户名称(customer_name)', value: 'customer_name' },
      { label: '客户类型(customer_type)', value: 'customer_type' },
      { label: '联系人(contact_person)', value: 'contact_person' },
      { label: '联系电话(contact_phone)', value: 'contact_phone' },
      { label: '邮箱地址(email)', value: 'email' },
      { label: '注册地址(address)', value: 'address' },
      { label: '创建时间(create_time)', value: 'create_time' },
      { label: '更新时间(update_time)', value: 'update_time' },
      { label: '状态(status)', value: 'status' },
      { label: '备注(remark)', value: 'remark' }
    ]
  }

  console.log('设置字段选项 for viewId:', viewId, options)
  // 将字段选项存储到对应的viewId键下 - 总是更新以确保响应式
  fieldOptionsMap[viewId] = options

  console.log('更新后的 fieldOptionsMap:', fieldOptionsMap)
}



// 设置表单设计器引用
const setFormDesignRef = (el, viewId) => {
  if (el) {
    formDesignRefs.value.set(viewId, el)
  }
}

// 处理视图选择变化
const handleViewChange = (newSelectedViews) => {
  console.log('选择的视图:', newSelectedViews)

  // 如果有新增的视图，设置第一个为激活状态
  if (newSelectedViews.length > 0 && !activeName.value) {
    activeName.value = newSelectedViews[0]
  }

  // 移除未选择视图的配置和字段选项
  Object.keys(formConfigs).forEach(viewId => {
    if (!newSelectedViews.includes(viewId)) {
      delete formConfigs[viewId]
      // 同时删除对应的字段选项
      delete fieldOptionsMap[viewId]
    }
  })

  // 启用字段选择功能并设置当前视图ID
  if (newSelectedViews.length > 0) {
    console.log('启用字段选择功能，选择的视图:', newSelectedViews);

    const viewId = activeName.value || newSelectedViews[0]

    // 更新依赖注入的数据
    enableFieldSelect.value = true
    currentViewId.value = viewId

    // 为每个选中的视图初始化字段选项
    newSelectedViews.forEach(vid => {
      updateFieldOptions(vid)
    })
  } else {
    console.log('禁用字段选择功能')
    // 禁用字段选择功能
    enableFieldSelect.value = false
    currentViewId.value = ''
    // 清空字段选项映射
    Object.keys(fieldOptionsMap).forEach(key => {
      delete fieldOptionsMap[key]
    })
  }
}

// 处理tab编辑（删除）
const handleTabEdit = (targetName, action) => {
  if (action === 'remove') {
    // 从选择的视图中移除
    const index = selectedViews.value.indexOf(targetName)
    if (index > -1) {
      selectedViews.value.splice(index, 1)
    }

    // 删除对应的表单配置和字段选项
    delete formConfigs[targetName]
    delete fieldOptionsMap[targetName]

    // 如果删除的是当前激活的tab，切换到其他tab
    if (activeName.value === targetName) {
      const newActiveTab = selectedViews.value.length > 0 ? selectedViews.value[0] : ''
      activeName.value = newActiveTab
      // 更新当前视图ID
      if (newActiveTab) {
        currentViewId.value = newActiveTab
        updateFieldOptions(newActiveTab)
      } else {
        currentViewId.value = ''
      }
    }


  }
}

// 处理tab点击
const handleTabClick = (tab) => {
  console.log('=== 切换到tab ===')
  console.log('tab.name:', tab.name)
  console.log('切换前 currentViewId:', currentViewId.value)
  console.log('切换前 fieldOptionsMap:', fieldOptionsMap)

  // 更新当前视图ID
  currentViewId.value = tab.name
  console.log('切换后 currentViewId:', currentViewId.value)

  // 确保该视图的字段选项已初始化
  updateFieldOptions(tab.name)

  console.log('=== tab切换完成 ===')
}

// 处理表单配置变化
const handleFormConfigChange = (formConfig, viewId) => {
  console.log(`视图 ${viewId} 的表单配置已更改:`, formConfig)
  formConfigs[viewId] = formConfig
  // 这里可以添加自动保存逻辑
}

// 获取指定视图的表单配置
const getFormConfig = (viewId) => {
  if (viewId) {
    const ref = formDesignRefs.value.get(viewId)
    if (ref) {
      return ref.getFormConfig()
    }
  }
  return null
}

// 获取所有视图的表单配置
const getAllFormConfigs = () => {
  const configs = {}
  formDesignRefs.value.forEach((ref, viewId) => {
    if (ref) {
      configs[viewId] = ref.getFormConfig()
    }
  })
  return configs
}

// 设置指定视图的表单配置
const setFormConfig = (viewId, config) => {
  const ref = formDesignRefs.value.get(viewId)
  if (ref) {
    ref.setFormConfig(config)
  }
}

// 保存所有表单配置
const saveAllConfigs = () => {
  const allConfigs = getAllFormConfigs()
  console.log('保存所有表单配置:', allConfigs)

  // 这里可以调用API保存配置
  ElMessage.success('表单配置保存成功')

  return allConfigs
}




// 组件挂载时初始化
onMounted(() => {
  console.log('formDesign 组件已挂载')
  console.log('初始状态:')
  console.log('- selectedViews:', selectedViews.value)
  console.log('- enableFieldSelect:', enableFieldSelect.value)
  console.log('- currentViewId:', currentViewId.value)
  console.log('- fieldOptionsMap:', fieldOptionsMap)

  // 如果有默认视图，初始化字段选择功能
  if (selectedViews.value.length > 0) {
    const defaultViewId = selectedViews.value[0]
    enableFieldSelect.value = true
    currentViewId.value = defaultViewId
    updateFieldOptions(defaultViewId)

    console.log('初始化完成后:')
    console.log('- enableFieldSelect:', enableFieldSelect.value)
    console.log('- currentViewId:', currentViewId.value)
    console.log('- fieldOptionsMap:', fieldOptionsMap)
  }
})





// 暴露方法给父组件使用
defineExpose({
  getFormConfig,
  getAllFormConfigs,
  setFormConfig,
  saveAllConfigs
})
</script>

<style lang="scss" scoped>
.form-design-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;

  .form-selector {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .selector-form {
      margin: 0;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 24px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .design-area {
    flex: 1;
    min-height: 500px;

    .el-tabs {
      height: 100%;

      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        padding: 16px 0;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }

  // 确保表单设计器占满容器
  :deep(.ng-form-design) {
    height: 100%;
  }
}
</style>