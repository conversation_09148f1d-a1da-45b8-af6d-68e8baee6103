<template>
  <el-row :gutter="20" style="margin-left: 4%">
    <el-col :span="4" :xs="12">
      <el-upload
        v-model:file-list="fileList"
        :limit="limit"
        class="upload-demo"
        :action="uploadFileUrl"
        multiple
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :on-exceed="handleExceed"
        :before-upload="handleBeforeUpload"
        :on-success="handleUploadSuccess"
        :headers="headers"
        :show-file-list="false"
        accept=".ktr, .kjb, .zip,"
      >
        <el-button type="primary" link plain :disabled="!CanvasActions"> 上传文件</el-button>
        <template #tip>
          <!-- <div class="el-upload__tip">只能上传不超过 2 个文件，且不超过 10MB</div> -->
        </template>
      </el-upload>
    </el-col>
    <el-button type="danger" link plain :disabled="!CanvasActions" @click="handleDelete">
      {{ uploadList.length >= 1 ? '批量删除' : '' }}
      <!-- 删除 -->
    </el-button>
    <el-col :span="24" :xs="12">
      <div class="el-upload__tip">
        提示:
        我们建议在上传Kettle文件时,仅上传一个kjb文件作为任务入口使用、和一个zip文件包含所有要执行的任务
      </div>
    </el-col>
  </el-row>

  <el-table
    :data="uploadList"
    height="350"
    style="margin-left: 4%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="KETTLE文件名" align="center" prop="name" width="155" />
    <el-table-column label="文件后缀" align="center" prop="fileSuffix" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <el-row style="margin-top: 20px; margin-left: 4%">
    <el-col :span="15">
      <el-form-item label="任务运行最大内存">
        <el-input v-model="maxMemory" placeholder="" suffix="MB" :disabled="!CanvasActions">
          <template #suffix>
            <el-icon class="el-input__icon" style="font-style: normal; margin-right: 10px"
              >MB</el-icon
            >
          </template>
        </el-input>
      </el-form-item>
    </el-col>
    <el-divider></el-divider>

    <el-col :span="24">
      <div class="dialog-footer">
        <el-button @click="cancelDrawer">取 消</el-button>
        <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  const { proxy } = getCurrentInstance();
  // 组件接收传参
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    workspaceId: {
      type: String,
      default: () => '',
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
  });
  const { NodeData, workspaceId } = toRefs(props);
  const emit = defineEmits();

  const limit = ref(2);
  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  const headers = ref({ Authorization: 'Bearer ' + getToken(), workspaceId });
  const uploadList = ref([]);
  const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
  const maxMemory = ref();
  const ids = ref([]);
  const INames = ref();
  const single = ref(true);
  const multiple = ref(true);
  const data = reactive({
    form: {},
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      describe: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
      syncTaskName: [{ required: true, message: '工作流名称不能为空', trigger: 'blur' }],
      cron: [{ required: true, message: 'cron不能为空', trigger: 'blur' }],
    },
  });
  const fileType = ref(['ktr', 'kjb', 'zip']);
  const { form } = toRefs(data);

  const fileList = ref([]);

  // 文件上传成功
  const handleRemove = (file, uploadFiles) => {
    console.log(file, uploadFiles);
  };
  // 文件预览
  const handlePreview = (uploadFile) => {
    console.log(uploadFile);
  };

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${limit.value} 个!`);
  }

  // 删除文件之前的钩子
  const beforeRemove = (uploadFile, uploadFiles) => {};

  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (fileType.value.length) {
      const fileName = file.name.split('.');
      const fileExt = fileName[fileName.length - 1];
      const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
        return false;
      }
    }
    return true;
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.data.fileName,
        url: res.data.url,
        fileSuffix: res.data.fileName.split('.').pop(),
        ossId: res.data.ossId,
      });
    }
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.ossId);
    INames.value = selection.map((item) => item.name);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const ossIds = row.ossId || ids.value;
    proxy.$modal.confirm('是否确定删除文件名称为"' + INames.value + '"的数据项?').then(function () {
      // 创建一个过滤后的新数组，其中排除了要删除的行
      const updatedUploadList = uploadList.value.filter((item) => !ossIds.includes(item.ossId));
      fileList.value = fileList.value.filter((item) => !ossIds.includes(item.response.data.ossId));

      console.log('fileList.value', fileList.value);
      if (updatedUploadList.length < uploadList.value.length) {
        // 至少有一行被删除
        uploadList.value = updatedUploadList;
        proxy.$modal.msgSuccess('删除成功');
      } else {
        // 没有找到匹配的行
        proxy.$modal.msgError('未找到要删除的行');
      }
    });
  }

  // 数据处理
  const DataProcessing = () => {
    if (uploadList.value.length < 1) return proxy.$modal.msgError('文件不能为空');
    return true;
  };
  // 取消按钮
  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  //  确定按钮
  const submitDrawer = async () => {
    const res = await DataProcessing();
    if (!res) return;
    NodeData.value.inputProperties[0].value = JSON.stringify(uploadList.value);
    NodeData.value.inputProperties[1].value = maxMemory.value;
    await emit('submitDrawer', NodeData.value);
  };

  const init = () => {
    uploadList.value = NodeData.value.inputProperties[0].value
      ? JSON.parse(NodeData.value.inputProperties[0].value)
      : [];
    maxMemory.value = NodeData.value.inputProperties[1].value;
  };
  onMounted(() => {
    init();
  });
  watch(NodeData, (val) => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }
</style>
