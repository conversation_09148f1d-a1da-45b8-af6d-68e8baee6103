<template>
  <div class="rules-manager">
    <div v-if="pageType === 'list' && databaseInfo.data.length > 0" class="card-box">
      <div
        v-for="(card, databaseIndex) in databaseInfo.data"
        :key="databaseIndex"
        class="metadata-card"
      >
        <div class="database-card-left">
          <div class="database-icon" :class="card.name"></div>
          <span class="database-title">{{ card.name }}</span>
          <p class="database-remark">{{ card.remark }}</p>
        </div>
        <div class="database-card-right">
          <div
            v-if="card.total === 0"
            class="database-add"
            @click="listeners.turnTo('/centralAdmin/dataSourcemanage', card)"
            >+</div
          >
          <div v-else class="database-charts-box">
            <div class="database-charts">
              <CircleCanvas
                :num="((card.collectNum / card.total) * 100).toFixed(1)"
                :size="{ width: 66, height: 66 }"
                :text="22"
                :gradient="'-51.9'"
              ></CircleCanvas>
              <span class="database-charts-text">
                <span class="database-charts-text-num">{{ card.collectNum }}</span>
                <span class="database-charts-all-num">/{{ card.total }}</span>
              </span>
            </div>
            <el-button class="database-btn" @click="detailListener.showDetail(card)"
              >查看详情</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-else-if="databaseInfo.data.length <= 0" class="no-data"></div> -->
    <el-empty v-else-if="databaseInfo.data.length <= 0" description="暂无数据" />
    <div v-else class="rules-manager-detail">
      <el-button
        type="primary"
        icon="DArrowLeft"
        class="call-back-btn"
        @click="detailListener.callback"
        >返回</el-button
      >
      <span class="detail-title">{{ detailInfo.title }}</span>
      <div class="data-type-box">
        <el-radio-group v-model="dataType" @change="listeners.changeDataType">
          <el-radio-button label="1">已采集</el-radio-button>
          <el-radio-button label="2">未采集</el-radio-button>
          <el-radio-button label="3">采集记录</el-radio-button>
        </el-radio-group>
      </div>

      <div class="detail-"></div>

      <div class="form-box">
        <el-form
          ref=""
          v-model="detailInfo.searchInfo.searchForm"
          label-position="left"
          inline
          label-width="auto"
        >
          <el-form-item label="数据源名称" prop="name">
            <el-input
              v-model="detailInfo.searchInfo.searchForm.name"
              placeholder="请输入数据源名称"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
          <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
            <el-button
              type="primary"
              icon="Search"
              class="icon-btn"
              @click="tableListener.tableSearch"
            ></el-button>
          </el-tooltip>
          <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
            <el-button icon="Refresh" class="icon-btn" @click="tableListener.refresh"></el-button>
          </el-tooltip>
        </el-form>
      </div>

      <div v-if="dataType === '1' || dataType === '3'" class="other-btn-box">
        <el-button type="danger" icon="Delete" @click="tableListener.deleteItems"
          >批量删除</el-button
        ></div
      >

      <div class="table-box">
        <el-table
          ref="tableRef"
          :data="detailInfo.tableInfo.tableData"
          height="100%"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
          @selection-change="tableListener.selectChange"
        >
          <el-table-column
            v-if="dataType === '1' || dataType === '3'"
            type="selection"
            width="55"
          />
          <el-table-column type="index" label="序号" width="60">
            <template #default="scope">
              {{
                detailInfo.searchInfo.queryParams.pageSize *
                  (detailInfo.searchInfo.queryParams.pageNum - 1) +
                (scope.$index + 1)
              }}
            </template>
          </el-table-column>

          <el-table-column
            v-for="(item, index) in detailInfo.tableInfo.columns"
            :key="index"
            v-bind="item"
          >
            <template v-if="item.prop === 'findTable'" #default="scope">
              <el-button
                v-if="scope.row.findTable >= 0"
                type="text"
                size="small"
                :disabled="scope.row.findTable === 0"
                @click="tableListener.showTableDialog(scope)"
              >
                {{ scope.row.findTable }}
              </el-button>
            </template>
            <template v-else-if="item.prop === 'status'" #default="scope">
              <div class="table-status">
                <span :class="`status-${scope.row.status}`">{{ scope.row.statusLabel }}</span>
              </div>
            </template>
            <template v-else-if="item.prop === 'connectionInfoLabel'" #default="scope">
              <div class="table-connectionInfoLabel" v-html="scope.row.connectionInfoLabel"> </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button
                v-if="dataType === '1'"
                type="text"
                size="small"
                @click="tableListener.run(scope)"
                >运行</el-button
              >
              <el-button
                v-if="dataType === '1'"
                type="text"
                size="small"
                @click="tableListener.edit(scope)"
                >编辑</el-button
              >
              <el-button
                v-if="dataType === '2'"
                type="text"
                size="small"
                @click="tableListener.showCollectDialog(scope)"
                >元数据采集</el-button
              >
              <!-- <el-button
                v-if="dataType === '3'"
                type="text"
                size="small"
                @click="tableListener.restart(scope)"
                >重跑</el-button
              >
              <el-button
                v-if="dataType === '3'"
                type="text"
                size="small"
                @click="tableListener.showDetail(scope)"
                >停止</el-button
              > -->
              <el-button
                v-if="dataType === '3'"
                type="text"
                size="small"
                @click="tableListener.showLogDetail(scope)"
                >日志</el-button
              >
              <el-button
                v-if="dataType === '3'"
                type="text"
                size="small"
                :disabled="scope.row.statusLabel !== '运行中'"
                @click="tableListener.stopCollection(scope)"
                >停止采集</el-button
              >
              <el-button
                v-if="dataType === '1' || dataType === '3'"
                type="text"
                size="small"
                @click="tableListener.deleteItem(scope)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="margin-bottom: 20px">
        <!-- 分页 -->
        <pagination
          v-show="detailInfo.searchInfo.queryParams.total > 0"
          v-model:page="detailInfo.searchInfo.queryParams.pageNum"
          v-model:limit="detailInfo.searchInfo.queryParams.pageSize"
          :pager-count="detailInfo.searchInfo.queryParams.maxCount"
          :total="detailInfo.searchInfo.queryParams.total"
          @pagination="tableListener.tableSearch"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogInfo.dialogVisible"
      :title="dialogInfo.title"
      width="860px"
      :draggable="true"
      class="req-dialog"
    >
      <!-- 运行发现表 -->
      <div v-if="dialogInfo.type === 'findTable'" class="dialog-table-box">
        <el-table
          ref="findTableRef"
          :data="dialogInfo.data"
          height="500px"
          header-cell-class-name="dialog-header-cell-class-name"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in dialogInfo.columns" :key="index" v-bind="item">
            <template v-if="item.prop === 'tableName'" #default="scope">
              <el-button type="text" size="small" @click="dialogListener.turnTable(scope)">
                {{ scope.row.tableName }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="dialogInfo.type === 'findTable'" style="margin-bottom: 20px">
        <!-- 分页 -->
        <pagination
          v-show="dialogInfo.searchInfo.queryParams.total > 0"
          v-model:page="dialogInfo.searchInfo.queryParams.pageNum"
          v-model:limit="dialogInfo.searchInfo.queryParams.pageSize"
          :pager-count="dialogInfo.searchInfo.queryParams.maxCount"
          :total="dialogInfo.searchInfo.queryParams.total"
          @pagination="dialogListener.tableSearch"
        />
      </div>

      <!-- 配置采集计划 -->
      <el-form
        v-if="dialogInfo.type === 'collectPlan'"
        ref="collectPlanFrom"
        :model="dialogInfo.planFromInfo.reqFrom"
        label-position="right"
        :rules="formRules"
        label-width="auto"
      >
        <span class="form-title">采集配置</span>
        <el-alert
          title="采集规则：系统会根据数据源中数据的变更进行同步修改、新增、删除操作"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        />
        <el-form-item width="100%" label="告警" prop="isWaring">
          <el-radio-group v-model="dialogInfo.planFromInfo.reqFrom.isWaring">
            <el-radio v-for="(radio, index) in ALARM_TYPE" :key="index" :label="radio.value">{{
              radio.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="dialogInfo.planFromInfo.reqFrom.isWaring"
          label="通知方式"
          prop="methodType"
        >
          <el-checkbox-group v-model="dialogInfo.planFromInfo.reqFrom.methodType">
            <el-checkbox
              v-for="(checkbox, checkboxI) in METHOD_TYPE"
              :key="checkboxI"
              :label="checkbox.value"
              :value="checkbox.value"
            >
              {{ checkbox.label }}
            </el-checkbox>
          </el-checkbox-group>
          <!-- <el-radio-group v-model="dialogInfo.planFromInfo.reqFrom.methodType">
            <el-radio v-for="(radio, index) in METHOD_TYPE" :key="index" :label="radio.value">{{
              radio.label
            }}</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="采集计划" prop="plan">
          <el-radio-group
            v-model="dialogInfo.planFromInfo.reqFrom.plan"
            class="no-border"
            @change="dialogListener.formListener.changeDataType"
          >
            <el-radio v-for="type in PLAN_TYPE" :key="type.value" :label="type.value">{{
              type.label
            }}</el-radio>
            <!-- <el-radio :label="0">手动采集</el-radio>
            <el-radio :label="1">每月</el-radio>
            <el-radio :label="2">每周</el-radio>
            <el-radio :label="3">每天</el-radio> -->
            <!-- <el-radio :label="5">每小时</el-radio> -->
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="dialogInfo.planFromInfo.reqFrom.plan !== 0"
          label="时间范围"
          prop="timeInterval"
        >
          <el-date-picker
            v-model="dialogInfo.planFromInfo.reqFrom.timeInterval"
            type="datetimerange"
            align="right"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 59, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-alert
          v-if="dialogInfo.planFromInfo.reqFrom.plan === 1"
          title="请谨慎选择月末日期，部分月份不含29、30、31"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        />
        <el-form-item v-if="dialogInfo.planFromInfo.reqFrom.plan === 1" label="日期" prop="date">
          <el-checkbox-group v-model="dialogInfo.planFromInfo.reqFrom.date">
            <el-checkbox
              v-for="(checkbox, checkboxI) in dialogInfo.planFromInfo.dateInfo"
              :key="checkboxI"
              :label="checkbox.value"
              :value="checkbox.value"
            >
              {{ checkbox.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="dialogInfo.planFromInfo.reqFrom.plan === 2" label="日期" prop="week">
          <el-checkbox-group v-model="dialogInfo.planFromInfo.reqFrom.week">
            <el-checkbox
              v-for="(checkbox, checkboxI) in dialogInfo.planFromInfo.weekInfo"
              :key="checkboxI"
              :label="checkbox.value"
              :value="checkbox.value"
            >
              {{ checkbox.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- <el-form-item v-if="dialogInfo.planFromInfo.reqFrom.plan === 4" label="分钟" prop="minute">
          <el-checkbox-group v-model="dialogInfo.planFromInfo.reqFrom.minute">
            <el-checkbox
              v-for="(checkbox, checkboxI) in dialogInfo.planFromInfo.timeInfo"
              :key="checkboxI"
              :label="checkbox.label"
              :value="checkbox.value"
            />
          </el-checkbox-group>
        </el-form-item> -->
        <el-form-item
          v-if="
            dialogInfo.planFromInfo.reqFrom.plan === 1 ||
            dialogInfo.planFromInfo.reqFrom.plan === 2 ||
            dialogInfo.planFromInfo.reqFrom.plan === 3
          "
          label="时间"
          prop="time"
        >
          <el-time-picker
            v-model="dialogInfo.planFromInfo.reqFrom.time"
            format="HH:mm"
            placeholder="请选择采集时间"
          />
        </el-form-item>
      </el-form>

      <template v-if="dialogInfo.type === 'collectPlan'" #footer>
        <span class="dialog-footer">
          <el-button @click="dialogListener.cancel">取 消</el-button>
          <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
        </span>
      </template>
      <div v-if="dialogInfo.type === 'log'" class="log-box">
        <!-- <div class="log-left-box">
          <p class="log-title">日志列表</p>
          <div class="log-list">
            <div
              v-for="(logItem, logIndex) in dialogInfo.logInfo"
              :key="logIndex"
              class="log-list-item"
              @click="dialogListener.changeLogDetail(logItem)"
            >
              <el-icon><Document /></el-icon>
              <span class="log-time">{{ logItem.time }}</span>
            </div>
          </div>
        </div> -->
        <div class="log-right-box">
          <!-- <p class="log-title">日志详情</p> -->
          <div class="log-detail">
            <monacoEditor
              ref="monacoEdit"
              :need-tips="false"
              :need-time="false"
              height="415"
              :value="dialogInfo.logContent"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import useMetadataCollectionService from '@/views/dataGovernance/dataMap/metadataCollection/useMetadataCollectionService';
  import {
    ALARM_TYPE,
    METHOD_TYPE,
    PLAN_TYPE,
  } from '@/views/dataGovernance/dataMap/metadataCollection/common/constants';

  import monacoEditor from '@/components/monacoEditor/index.vue';
  import CircleCanvas from './components/CircleCanvas';

  const {
    databaseInfo,
    detailInfo,
    dialogInfo,
    detailListener,
    tableListener,
    dialogListener,
    dataType,
    listeners,
    formRules,
    pageType,
  } = useMetadataCollectionService();
  const setImg = (name) => {
    const thisImg = `url(../../../../../../assets/images/dataGovernance/dataMap/metadataCollection/${name}.png)`;
    return thisImg;
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  @mixin responsive1520() {
    @media (max-width: 1519px) and (min-width: 1280px) {
      @content;
    }
  }
  @mixin responsive1280() {
    @media (max-width: 1279px) {
      @content;
    }
  }
  @mixin responsiveM1520() {
    @media (min-width: 1520px) {
      @content;
    }
  }

  .rules-manager {
    padding: 15px;
    height: 100%;
    overflow: auto;
    background: $--base-color-bg;
    .el-empty {
      width: 100%;
      height: 100%;
    }
    .card-box {
      display: flex;
      justify-content: left;
      flex-wrap: wrap;
      //   padding: 20px;
      padding: 0px;
      .metadata-card {
        width: calc(20% - 16px);
        height: 126px;
        background-color: $--base-color-card-bg2;
        margin-bottom: 20px;
        margin-right: 20px;
        border: 1px solid $--base-color-card-bg2;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        .database-card-left {
          width: calc(100% - 66px);
          height: 100%;
          .database-icon {
            width: 46px;
            height: 46px;
            display: inline-block;
            background-color: #333;
            background: $--base-color-tag-bg
              url('@/assets/images/dataGovernance/dataMap/metadataCollection/XUGU.png') no-repeat
              center;
            background-size: 100% auto;
            &.XUGU {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/XUGU.png') no-repeat
                center;
              background-size: 100% auto;
            }
            &.DAMENG {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/DAMENG.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.DB2 {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/DB2.png') no-repeat
                center;
              background-size: 100% auto;
            }
            &.GREENPLUM {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/GREENPLUM.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.HIVE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/HIVE.png') no-repeat
                center;
              background-size: 100% auto;
            }
            &.KINGBASE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/KINGBASE.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.MYSQL {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/MYSQL.png') no-repeat
                center;
              background-size: 100% auto;
            }
            &.ORACLE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/ORACLE.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.POSTGRESQL {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/POSTGRESQL.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.SQLSERVER {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/SQLSERVER.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.SYBASE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/SYBASE.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.DWS {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/DWS.png') no-repeat
                center;
              background-size: 100% auto;
            }
            &.CLICKHOUSE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/CLICKHOUSE.png')
                no-repeat center;
              background-size: 100% auto;
            }
            &.TDENGINE {
              background: $--base-color-tag-bg
                url('@/assets/images/dataGovernance/dataMap/metadataCollection/TDENGINE.png')
                no-repeat center;
              background-size: 100% auto;
            }
          }
          .database-title {
            width: calc(100% - 46px);
            height: 46px;
            line-height: 46px;
            display: inline-block;
            font-size: 16px;
            color: $--base-color-text1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: no-wrap;
            padding-left: 20px;
          }
          .database-remark {
            width: 100%;
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            color: $--base-color-text5;
            margin: 14px 0 0 0;
          }
        }
        .database-card-right {
          width: 66px;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          transition: 0.5s;
          margin-top: -6px;
          .database-charts-box {
            width: 66px;
            height: 100%;
            position: relative;
            // text-align: center;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-wrap: wrap;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // flex-wrap: wrap;
            .database-charts {
              width: 66px;
              height: 66px;
              margin: auto 0;
              position: relative;
              //   .circle-progress__bar {
              //     position: absolute;
              //     top: 0;
              //     left: 0;
              //     width: 100%;
              //     height: 100%;
              //     border-radius: 50%;
              //     transition: clip-path 0.5s ease-in-out;
              //     background: conic-gradient(#a2e570 var(--percent), #ffffff var(--percent2));

              //     &::before {
              //       content: '';
              //       position: absolute;
              //       inset: 6px;
              //       background-color: $--base-color-card-bg2;
              //       width: 52px;
              //       height: 52px;
              //       text-align: center;
              //       line-height: 64px;
              //       border-radius: 50%;
              //     }
              //   }
              .database-charts-text {
                width: 44px;
                height: 44px;
                position: absolute;
                top: 11px;
                left: 11px;
                border-radius: 22px;
                background-color: $--base-color-card-bg2;
                line-height: 42px;
                text-align: center;
                .database-charts-text-num {
                  font-size: 20px;
                  color: $--base-color-green;
                }
                .database-charts-all-num {
                  font-size: 12px;
                }
              }
            }
            .database-btn {
              height: 18px;
              background: $--base-color-tag;
              padding: 0 8px;
              color: $--base-color-primary;
              border-radius: 2px;
              font-size: 12px;
              margin-bottom: 10px;
              display: none;
              margin-top: 6px;
            }
          }
          .database-add {
            width: 60px;
            height: 60px;
            line-height: 58px;
            text-align: center;
            background: $--base-color-tag-primary;
            color: $--base-color-primary;
            border-radius: 30px;
            font-size: 22px;
            opacity: 0;
          }
        }
        &:hover {
          //   border-color: $--base-color-primary;
          box-shadow: 0px 6px 18px 0px rgba(1, 102, 243, 0.18);
          .database-card-right {
            .database-charts-box {
              .database-btn {
                display: inline-block;
              }
            }
            .database-add {
              opacity: 1;
              cursor: pointer;
            }
          }
        }
        // &:nth-child(5n) {
        //   margin-right: 0;
        // }

        @include responsiveM1520() {
          &:nth-child(5n) {
            margin-right: 0;
          }
        }
        @include responsive1520() {
          width: calc(25% - 15px);
          &:nth-child(4n) {
            margin-right: 0;
          }
        }
        @include responsive1280() {
          width: calc(33.33% - 14px);
          &:nth-child(3n) {
            margin-right: 0;
          }
        }
      }
    }
    .table-box {
      background: $--base-color-item-light;
      padding: 10px;
      height: calc(100% - 220px);
      .table-status {
        position: relative;
        padding-left: 18px;
        height: 24px;
        & > span {
          height: 20px;
          line-height: 1;
          color: $--base-color-green;
          background-color: $--base-color-green-disable;
          display: inline-block;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
          &.status-0 {
            color: $--base-btn-red-text;
            background-color: $--base-btn-red-bg;
            &::before {
              border: 3px solid $--base-btn-red-text;
            }
          }
          &.status-2 {
            color: $--base-color-primary;
            background-color: $--base-color-tag-primary;
            &::before {
              border: 3px solid $--base-color-primary;
            }
          }
          &::before {
            content: '';
            width: 12px;
            height: 12px;
            border: 3px solid $--base-color-green;
            border-radius: 6px;
            position: absolute;
            top: calc(50% - 6px);
            left: 0;
          }
        }
      }
    }
    .rules-manager-detail {
      height: 100%;
      background: $--base-color-bg;
      position: relative;
      padding: 0;
      .detail-title {
        font-size: 20px;
        height: 32px;
        line-height: 32px;
        font-weight: bold;
        color: $--base-color-title1;
        display: inline-block;
        vertical-align: top;
        margin-left: 16px;
      }
      .data-type-box {
        width: 30%;
        position: absolute;
        top: 20px;
        left: 35%;
      }
      .form-box {
        margin-top: 20px;
        ::v-deep .el-form {
          text-align: right;
        }
      }
      .other-btn-box {
        margin-bottom: 20px;
        // padding: 0 20px;
      }
      .pagination-container {
        margin: 0;
      }
    }
    .log-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .log-title {
        width: 100%;
        font-size: 16px;
        height: 20px;
        line-height: 20px;
        margin-bottom: 20px;
        position: relative;
        padding-left: 10px;
        margin-top: 0;
        &::before {
          content: '';
          width: 3px;
          height: 16px;
          background: $--base-color-primary;
          position: absolute;
          top: 3px;
          left: 0px;
          border-radius: 4px;
        }
      }
      .log-left-box {
        width: 260px;
        height: 455px;
        .log-list {
          height: calc(100% - 40px);
          background: $--base-color-box-bg;
          padding: 10px;
          border-radius: 8px;
          .log-list-item {
            width: 100%;
            height: 36px;
            line-height: 36px;
            padding: 0px 10px;
            .el-icon {
              display: inline-block;
              vertical-align: middle;
            }
            .log-time {
              width: calc(100% - 40px);
              height: 36px;
              line-height: 36px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              display: inline-block;
              vertical-align: middle;
              padding-left: 8px;
              cursor: pointer;
            }
            &:hover {
              background-color: $--base-color-tag-bg;
            }
          }
        }
      }
      .log-right-box {
        // width: calc(100% - 280px);
        width: 100%;
        height: 100%;
        .log-detail {
          width: 100%;
          height: 415px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
    ::v-deep .req-dialog {
      .el-dialog__body {
        padding: 20px !important;
      }
      .form-title {
        font-size: 14px;
        line-height: 20px;
        color: $--base-color-title1;
        margin-bottom: 10px;
        display: inline-block;
      }
    }
  }
</style>
