import request from '@/utils/request';

// 查询租户管理列表
export function listTenant(query) {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query,
  });
}

// 查询租户管理详细
export function getTenant(tenantId) {
  return request({
    url: '/system/tenant/' + tenantId,
    method: 'get',
  });
}

// 新增租户管理
export function addTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'post',
    data,
  });
}

// 修改租户管理
export function updateTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'put',
    data,
  });
}

// 删除租户管理
export function delTenant(tenantId) {
  return request({
    url: '/system/tenant/' + tenantId,
    method: 'delete',
  });
}

// 获取当前租户下所有用户
export function getAllTenantUser(tenantId) {
  return request({
    url: '/system/tenant/getAllTenantUser/' + tenantId,
    method: 'get',
  });
}
