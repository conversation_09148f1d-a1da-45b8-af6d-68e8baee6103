import autoprefixer from 'autoprefixer';
import path from 'path';
import postCssPxToRem from 'postcss-pxtorem';
import { defineConfig, loadEnv } from 'vite';
import createVitePlugins from './vite/plugins';
import poscssPresetEnv from 'postcss-preset-env';

const hash = Math.floor(Math.random() * 90000) + 10000;
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的 URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.Xugu.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.Xugu.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      hmr: true,
      proxy: {
        '/operator/websocket': {
          target: 'ws://************:9000',
          changeOrigin: true,
          ws: true,
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api': {
          //   target: 'http://************:9380',
          //   target: 'http://************:8080',
          target: 'http://************:9001/prod-api', // 服务器
          // target: 'http://************:9001/prod-api', // 服务器
          //   target: 'http://**********:8080', // lij
          // target: 'http://**********:8080', // zx
          //   target: 'http://************:9480', // lps
          //   target: 'http://************:8080', //
          //   target: 'http://************:9580', // zj
          //   target: 'http://************:8080', // luo j
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, ''),
        },
      },
    },
    // fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          postCssPxToRem({
            // 自适应，px>rem 转换
            rootValue: 16, // 1rem 的大小
            propList: ['*'], // 需要转换的属性，这里选择全部都进行转换
          }),
          autoprefixer({
            overrideBrowserslist: [
              'Android 4.1',
              'iOS 7.1',
              'Chrome > 31',
              'ff > 31',
              'ie >= 8',
              '> 1%',
            ],
            grid: true,
          }),
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              },
            },
          },
          poscssPresetEnv(),
        ],
      },
      //   preprocessorOptions: {
      //     scss: {
      //         additionalData: `@use "src/assets/styles/element/index.scss" as *;`,
      //       }
      //   },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境时移除 console
          drop_console: true,
          drop_debugger: true,
        },
      },
      rollupOptions: {
        output: {
          entryFileNames: `[name]` + hash + `.js`,
          chunkFileNames: `[name]` + hash + `.js`,
          assetFileNames: `[name]` + hash + `.[ext]`,
        },
      },
    },
    preview: {
      port: 5000,
      open: true,
      hmr: true,
      proxy: {
        '/prod-api': {
          target: 'http://10.28.25.158:39001/prod-api', // 服务器
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/prod-api/, ''),
        },
      },
    },
  };
});
