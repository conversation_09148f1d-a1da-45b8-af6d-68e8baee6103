<!-- 饼图 -->
<template>
  <!-- <el-card v-if="topic == '接口访问量'">
    <template #header> 接口访问量 </template>
    <div ref="chart" :style="{ width: width, height: height }" />
  </el-card> -->
  <div ref="chart" :style="{ width: width, height: height }" />
</template>

<script>
  import * as echarts from 'echarts';

  export default defineComponent({
    name: 'PieChart',
    props: {
      id: {
        type: String,
        default: 'pieChart',
      },
      className: {
        type: String,
        default: '',
      },
      width: {
        type: String,
        default: '400px',
        // required: true,
      },
      height: {
        type: String,
        default: '250px',
        // required: true,
      },
      options: {
        type: Object,
        default: () => {},
      },
    },
    setup(props) {
      const chart = ref(null);
      const chartInstance = ref(null);

      const initChart = () => {
        if (chart.value) {
          chartInstance.value = echarts.init(chart.value);
          chartInstance.value.setOption(props.options);
        }
      };

      onMounted(() => {
        // console.log(props.options);
        initChart();
        window.addEventListener('resize', () => {
          chartInstance.value?.resize();
        });
      });

      watch(
        () => props.options,
        (newOptions) => {
          if (chartInstance.value) {
            chartInstance.value.setOption(newOptions);
          }
        },
        { deep: true },
      );

      return {
        chart,
      };
    },
  });
</script>

<style scoped lang="scss"></style>

<!-- 饼图 -->
<!-- <template>
  <el-card>
    <template #header> 接口访问量 </template>
    <div :id="id" :class="className" :style="{ height, width }" />
  </el-card>
</template>

<script setup lang="ts">
  import * as echarts from 'echarts';

  const props = defineProps({
    id: {
      type: String,
      default: 'pieChart',
    },
    className: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '400px',
      required: true,
    },
    height: {
      type: String,
      default: '250px',
      required: true,
    },
    options: {
      type: Object,
      default: () => {},
    },
  });

  onMounted(() => {
    init()
  });
  const init = () => {
    const chart = echarts.init(document.getElementById(props.id) as HTMLDivElement);
    chart.setOption(props.options);
    window.addEventListener('resize', () => {
      chart.resize();
    });
  }
  watch(
        () => props.options,
        (newOptions) => {
          const chart = echarts.init(document.getElementById(props.id) as HTMLDivElement)
          if (chart) {
            chart.setOption(newOptions);
          }
        },
        { deep: true },
      );
</script> -->
