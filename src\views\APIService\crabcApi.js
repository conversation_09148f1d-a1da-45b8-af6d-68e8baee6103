// api.js
const myHeaders = new Headers();
myHeaders.append('Accept', '*/*');
myHeaders.append('Host', 'app.crabc.cn');
myHeaders.append('Connection', 'keep-alive');
myHeaders.append('Content-Type', 'application/json');
// myHeaders.append('Cookie', 'token={...略}');
myHeaders.append(
  'Authorization',
  'bearer eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4f45gXYo9q1KdxRcJ6bl3iO9w5xJTgUmn8tHoqWZ-AtaTG7wTKFbzmxsLKz8ltLAGEMbpg6vGGrfw3D_oRGLhQ',
);

const fetchData = async (url, method = 'GET', body = null) => {
  const requestOptions = {
    method,
    headers: myHeaders,
    redirect: 'follow',
  };

  if (body) {
    requestOptions.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, requestOptions);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
};

export const getData = (url) => fetchData(url);
export const postData = (url, body) => fetchData(url, 'POST', body);
export const putData = (url, body) => fetchData(url, 'PUT', body);
export const deleteData = (url, body = null) => fetchData(url, 'DELETE', body);
