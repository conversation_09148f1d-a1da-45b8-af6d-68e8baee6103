<template>
  <SplitPanes class="rule-container">
    <template #left>
      <div class="rule-left-box">
        <div class="top">
          <div class="title"> 目录 </div>
          <div>
            <ExportAndImport
              moduleName="SensitiveData"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getGroupsTree"
            ></ExportAndImport>
            <el-tooltip content="新增目录" placement="top">
              <el-button
                style="width: 28px; height: 28px"
                icon="Plus"
                type="primary"
                @click="addGroupBtn()"
              ></el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="left-box">
          <el-input
            v-model="treeSearchText"
            v-input="searchTree"
            class="tree-search"
            placeholder="请输入搜索内容"
            prefix-icon="Search"
          >
          </el-input>
          <el-tree
            ref="treeRef"
            class="left-tree-box"
            :data="allTreeData"
            :props="propsGroupTree"
            :highlight-current="true"
            :filter-node-method="filterNode"
          >
            <template #default="items">
              <!-- <div v-if="items.node.level == 1" class="tree-item" @click="handleNodeClick(items)">
            <div class="tree-item-box">
              <el-icon>
                <FolderOpened />
              </el-icon>
              <el-tooltip
                :content="items.data.groupName"
                placement="top"
                :disabled="items.data.groupName?.length < 10"
              >
                {{
                  items.data.groupName?.length > 10
                    ? items.data.groupName.slice(0, 10) + '...'
                    : items.data.groupName
                }}
              </el-tooltip>
            </div>
            <div class="tree-btn-box">
              <span class="tree-icon" @click.stop="addGroupBtn(items)">
                <el-icon>
                  <Plus />
                </el-icon>
              </span>
              <span v-if="items.data.groupName" class="tree-icon" @click.stop="editGroup(items)">
                <el-icon>
                  <Edit />
                </el-icon>
              </span>
              <span
                v-if="
                  (items.data.groupName && items.data.children?.length === 0)
                "
                class="tree-icon"
                @click.stop="deleteGroup(items)"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </span>
            </div>
          </div> -->
              <div class="tree-item" @click="handleNodeClick(items)">
                <div class="tree-item-box">
                  <el-icon>
                    <FolderOpened />
                  </el-icon>
                  <el-tooltip
                    :content="items.data.groupName"
                    placement="top"
                    :disabled="items.data.groupName.length < 10"
                  >
                    {{
                      items.data.groupName.length > 10
                        ? items.data.groupName.slice(0, 10) + '...'
                        : items.data.groupName
                    }}
                  </el-tooltip>
                </div>
                <div class="tree-btn-box">
                  <span class="tree-icon" @click.stop="addGroupBtn(items)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </span>
                  <span class="tree-icon" @click.stop="editGroup(items)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </span>
                  <span
                    v-if="items.data.groupName && items.data.children?.length === 0"
                    class="tree-icon"
                    @click.stop="deleteGroup(items)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </span>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <template #right>
      <div class="rule-right-box">
        <div class="top">
          <div class="title">数据识别规则</div>
          <div class="search-container">
            <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
              <el-form-item label="敏感字段类型">
                <el-input style="width:200px" v-model="searchForm.sensitiveName" placeholder="请输入名称" clearable />
              </el-form-item>
              <el-form-item style="margin-right: 5px">
                <span class="table-search-btn">
                  <span class="btn btn1" @click="getList"
                    ><el-icon style="color: #fff"> <Search /> </el-icon
                  ></span>
                  <span class="btn btn2" @click="searchReSet"
                    ><el-icon style="color: #434343"> <Refresh /> </el-icon
                  ></span>
                </span>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="table-container">
          <div class="table-top-box">
            <div>
              <el-button :disabled="isAbleClick" icon="Plus" type="primary" @click="addRule"
                >新增敏感字段类型</el-button
              >
            </div>
            <div><right-toolbar :columns="columns" @query-table="getList"></right-toolbar></div>
          </div>
          <div class="table-box">
            <el-table :data="dataList" height="100%">
              <el-table-column type="index" width="60" label="序号">
                <template #default="scope">
                  {{ searchForm.pageSize * (searchForm.pageNum - 1) + (scope.$index + 1) }}
                </template>
              </el-table-column>
              <el-table-column v-if="columns[0].visible" label="敏感字段类型">
                <template #default="scope">
                  <el-button type="text" size="small" @click="viewDetail(scope.row)">
                    {{ scope.row.sensitiveName }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                v-if="columns[1].visible"
                prop="groupName"
                label="分类"
              ></el-table-column>
              <el-table-column v-if="columns[2].visible" prop="securityLevelCode" label="密级">
                <template #default="scope">
                  {{ getLabel(scope.row.securityLevelCode) }}
                </template>
              </el-table-column>
              <el-table-column v-if="columns[3].visible" prop="status" label="状态">
                <template #default="scope">
                  <el-tag
                    v-if="scope.row.showStatus"
                    class="tag-box"
                    effect="light"
                    :round="false"
                    type="primary"
                    >启用</el-tag
                  >
                  <el-tag v-else class="tag-box" effect="light" :round="false" type="info"
                    >停用</el-tag
                  >
                  <el-switch
                    v-model="scope.row.showStatus"
                    :loading="loadingForStatus"
                    active-icon=""
                    inactive-icon=""
                    style="
                      width: 28px;
                      height: 16px;
                      margin-left: 8px;
                      --el-switch-on-color: #13ce66;
                    "
                    @change="changeStatus(scope.row)"
                  >
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
                <template #default="scope">
                  <el-button
                    :disabled="scope.row.status == '0'"
                    type="text"
                    @click="editRule(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    :disabled="scope.row.status == '0'"
                    type="text"
                    @click="deleteRule(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>

          <pagination
            v-show="total > 0"
            v-model:page="searchForm.pageNum"
            v-model:limit="searchForm.pageSize"
            :total="total"
            @pagination="getList"
          />
        </div>
      </div>
    </template>
  </SplitPanes>
  <!-- 新增分类 -->
  <el-dialog
    v-model="addGroupDialog"
    :title="addGroupDialogTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <CategoryAddGroup
      v-if="addGroupDialog"
      ref="addGroupRef"
      :form-data="editGroupForm"
      :active-name="activeName"
    >
    </CategoryAddGroup>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="addGroupCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 新增识别规则 -->
  <el-dialog
    v-model="addRuleDialog"
    :title="addRuleDialogTitle"
    width="40%"
    append-to-body
    :draggable="true"
    @close="closeAddRuleDialog"
  >
    <el-form ref="formRef" :rules="rule" :model="form" label-width="auto" label-position="right">
      <el-form-item label="敏感字段类型" prop="sensitiveName">
        <el-input
          v-model.trim="form.sensitiveName"
          placeholder="请输入敏感字段类型"
          maxlength="30"
          show-word-limit
          @input="(data) => (form.sensitiveName = data.replace(/\s/g, ''))"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属分类" prop="sensitiveGroupId">
        <el-input v-model="form.sensitiveGroupId" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="所属密级" prop="securityLevelCode">
        <el-select
          v-model="form.securityLevelCode"
          style="width: 100%"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in security_level_code"
            :key="dict.value"
            :label="`${dict.value}/${dict.label}`"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model.trim="form.remark"
          type="textarea"
          placeholder="请输入内容"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
      <div style="margin: 10px 0; font-size: 14px; font-weight: 600; color: #000">配置规则</div>
      <el-alert
        class="alert-box"
        title="数据满足任一条件即可被识别为敏感数据，至少需保留一条规则"
        type="warning"
        show-icon
        :closable="false"
      />
      <div class="arrBox">
        <div style="color: #000; font-weight: 400">字段名称识别</div>
        <div>
          <el-button
            style="background: #eaf1ff; color: #1269ff; width: 26px; height: 26px"
            icon="plus"
            @click="addColumn"
          ></el-button>
        </div>
      </div>
      <div v-if="columnFormList && columnFormList.length" class="content-box">
        <div v-for="(data, index) in columnFormList" :key="index" class="formList-box">
          <div>
            <span style="color: #434343">字段名称：</span>
          </div>
          <div>
            <el-input v-model="data.ruleName" maxlength="30" show-word-limit></el-input>
          </div>
          <div style="margin-left: 10px">
            <el-icon style="cursor: pointer; color: #1269ff" @click="delCol(index)">
              <Delete />
            </el-icon>
            <!-- <el-button  icon="delete"></el-button> -->
          </div>
        </div>
      </div>
      <div class="arrBox">
        <span style="color: #000; font-weight: 400">字段注释识别</span>
        <span>
          <el-button
            style="background: #eaf1ff; color: #1269ff; width: 26px; height: 26px"
            icon="plus"
            @click="addRemark"
          ></el-button>
        </span>
      </div>
      <div v-if="remarkFormList && remarkFormList.length" class="content-box">
        <div v-for="(data, index) in remarkFormList" :key="index" class="formList-box">
          <div>
            <span style="color: #434343">字段注释名称：</span>
          </div>
          <div>
            <el-input v-model="data.ruleName" maxlength="30" show-word-limit></el-input>
          </div>
          <div style="margin-left: 10px">
            <el-icon style="cursor: pointer; color: #1269ff" @click="delRem(index)">
              <Delete />
            </el-icon>
            <!-- <el-button @click="delRem(index)" icon="delete"></el-button> -->
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddRuleDialog">取 消</el-button>
        <el-button type="primary" @click="addRuleCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 字段详情 -->
  <el-dialog
    v-model="detailDialog"
    :title="detailDialogTitle"
    width="40%"
    append-to-body
    :draggable="true"
    @close="closeDetailDialog"
  >
    <el-form ref="detailFormRef" :model="detailForm" label-width="auto" label-position="right">
      <el-form-item label="敏感字段类型">
        <span class="detail-box">{{ detailForm.sensitiveName }}</span>
      </el-form-item>
      <el-form-item label="所属分类">
        <span class="detail-box">{{ groupName }}</span>
      </el-form-item>
      <el-form-item label="所属密级">
        <span class="detail-box">{{ getLabel(detailForm.securityLevelCode) }}</span>
      </el-form-item>
      <el-form-item label="备注">
        <span class="detail-box">{{ detailForm.remark }}</span>
      </el-form-item>
      <div style="margin: 10px 0; font-size: 14px; font-weight: 600; color: #000">配置规则</div>
      <div class="arrBox">
        <div style="color: #000; font-weight: 400">字段名称识别</div>
      </div>
      <div v-if="columnFormList && columnFormList.length" class="content-box">
        <div v-for="(data, index) in columnFormList" :key="index" class="formList-box">
          <div>
            <span style="color: #434343">字段名称：</span>
          </div>
          <div>
            <span class="detail-box">{{ data.ruleName }}</span>
          </div>
        </div>
      </div>
      <div class="arrBox">
        <span style="color: #000; font-weight: 400">字段注释识别</span>
      </div>
      <div v-if="remarkFormList && remarkFormList.length" class="content-box">
        <div v-for="(data, index) in remarkFormList" :key="index" class="formList-box">
          <div>
            <span style="color: #434343">字段中文名称：</span>
          </div>
          <div>
            <span class="detail-box">{{ data.ruleName }}</span>
          </div>
        </div>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserProfile } from '@/api/system/user';
  import {
    getSensitiveGroup,
    addSensitiveGroup,
    updateSensitiveGroup,
    deleteSensitiveGroup,
    getListForRule,
    addDiscoveryRule,
    updateDiscoveryRule,
    deleteDiscoveryRule,
    updateStatus,
  } from '@/api/dataGovernance';
  import CategoryAddGroup from '../../components/categoryAddGroup';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getCurrentInstance, ref } from 'vue';
  import SplitPanes from '@/components/SplitPanes/index';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  let userInfo = reactive({});
  const { proxy } = getCurrentInstance();
  const { security_level_code } = proxy.useDict('security_level_code');
  const getLabel = (data) => {
    const label = security_level_code._object.security_level_code.filter(
      (res) => res.value == data,
    )[0].label;
    return `${data}/${label}`;
  };

  const loadingForStatus = ref(false);
  const changeStatus = async (row) => {
    console.log(row);
    loadingForStatus.value = true;
    const message = {
      title: '',
    };

    if (!row.showStatus) {
      message.title = '停用当前规则，停用后相关任务会受到影响，确认继续吗？';
    } else {
      message.title = '是否启用该规则？';
    }
    proxy
      .$confirm(`${message.title}`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(async () => {
        const resData = await updateStatus({
          id: row.id,
          status: row.showStatus ? '0' : '1',
        });
        if (resData.code === 200) {
          ElMessage.success('操作成功');
          getList();
        } else {
          row.showStatus = !row.showStatus;
          ElMessage.error('操作失败，请稍后重试');
        }
        loadingForStatus.value = false;
      })
      .catch(() => {
        row.showStatus = !row.showStatus;
        loadingForStatus.value = false;
      });
  };

  const searchForm = ref({ pageSize: 20, pageNum: 1, sensitiveName: null, groupId: null });
  const dataList = ref([]);
  const total = ref(0);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `敏感字段类型`, visible: true },
    { key: 1, label: `分类`, visible: true },
    { key: 2, label: `密级`, visible: true },
    { key: 3, label: `状态`, visible: true },
  ]);

  const getList = async () => {
    if (!searchForm.value.groupId) return proxy.$modal.msgError('请先选择一个目录');
    const data = {
      ...searchForm.value,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const res = await getListForRule(data);
    const tableData = res.rows.map((res) => {
      res.groupName = groupName.value;
      res.showStatus = res.status != '1';
      return res;
    });
    dataList.value = tableData;
    total.value = res.total;
  };

  const searchReSet = () => {
    searchForm.value.pageNum = 1;
    searchForm.value.sensitiveName = null;
    getList();
  };

  // 获取分组树
  const getGroupsTree = async () => {
    const data = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resData = await getSensitiveGroup(data);
    allTreeData.value = resData.data;
  };
  const allTreeData = ref([]);
  const treeSearchText = ref(null);
  const propsGroupTree = reactive({
    value: 'id',
    label: 'groupName',
    children: 'children',
  });

  const isAbleClick = ref(true);
  const groupName = ref(null);
  const handleNodeClick = (item) => {
    if (item.data.children?.length) return;
    isAbleClick.value = false;
    searchForm.value.groupId = item.data.id;
    groupName.value = item.data.groupName;
    getList();
  };

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.groupName.includes(value);
  };

  // 添加分组弹出框配置
  const addGroupDialog = ref(false);
  const addGroupRef = ref(null);
  const addGroupDialogTitle = ref('添加分组');
  const activeName = ref('first');
  let editGroupForm = reactive({});

  const addGroupBtn = (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      if (item?.data) {
        editGroupForm = JSON.parse(JSON.stringify(item.data));
        editGroupForm.parentName = item.data.groupName;
        editGroupForm.groupName = '';
        addGroupRef.value.setForm(editGroupForm);
      } else {
        activeName.value = 'second';
      }
      addGroupDialogTitle.value = '新增分组';
    });
  };
  const closeAddGroupDialog = () => {
    editGroupForm = {};
    activeName.value = 'first';
    addGroupDialogTitle.value = '添加分组';
    addGroupDialog.value = false;
  };

  // 编辑分组
  const editGroup = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      editGroupForm.parentName = item.node.parent.data.groupName;
      if (!editGroupForm.parentName) {
        activeName.value = 'second';
      } else {
        activeName.value = 'first';
      }
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑分组';
    });
  };

  // 添加分组
  const addGroupCommit = async () => {
    const addForm = addGroupRef.value.getForm();
    let res = {};
    let textTitle = '添加';
    const reqData = {
      ...addForm,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      reqData.tenantId = tenantId.value;
    }
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑分组') {
      textTitle = '编辑';

      res = await updateSensitiveGroup(reqData);
    } else {
      res = await addSensitiveGroup(reqData);
    }

    if (res.code === 200) {
      isAbleClick.value = true;
      ElMessage.success(textTitle + '成功');
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error(textTitle + '失败：' + res.msg);
    }
  };

  // 删除分组、接口、转发
  const deleteGroup = async (item) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${item.data.groupName}数据项？`);
    if (!confirm) return;
    const res = await deleteSensitiveGroup({ id: item.data.id });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      if (item.data.id == searchForm.value.groupId) {
        isAbleClick.value = true;
      } else {
        isAbleClick.value = false;
      }
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };

  const form = ref({
    sensitiveName: null,
    sensitiveGroupId: null,
    securityLevelCode: null,
    remark: null,
  });

  const rule = reactive({
    sensitiveName: [{ required: true, message: '请输入敏感字段类型', trigger: 'change' }],
    securityLevelCode: [{ required: true, message: '请选择密级', trigger: 'change' }],
    sensitiveGroupId: [{ required: true, message: '请选择密级', trigger: 'change' }],
  });

  const addRuleDialog = ref(false);
  const addRuleDialogTitle = ref('新增敏感字段类型');

  const addRule = () => {
    form.value.sensitiveGroupId = groupName.value;
    addRuleDialogTitle.value = '新增敏感字段类型';
    addRuleDialog.value = true;
  };

  const editRule = (row) => {
    for (const key in form.value) {
      form.value[key] = row[key];
    }
    form.value.sensitiveGroupId = groupName.value;
    form.value.id = row.id;
    columnFormList.value = row.rules.filter((res) => res.discoveriesType == '0');
    remarkFormList.value = row.rules.filter((res) => res.discoveriesType == '1');
    addRuleDialogTitle.value = '编辑敏感字段类型';
    addRuleDialog.value = true;
  };

  const deleteRule = async (data) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${data.sensitiveName}敏感字段？`);
    if (!confirm) return;
    const res = await deleteDiscoveryRule({ id: data.id });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      getList();
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };

  const addRuleCommit = async () => {
    const confirm = await proxy.$refs.formRef.validate((valid) => valid);
    if (!confirm) return;
    const isColExist =
      columnFormList.value?.length && columnFormList.value.some((res) => res.ruleName);
    const isRemarkExist =
      remarkFormList.value?.length && remarkFormList.value.some((res) => res.ruleName);
    if (!isColExist && !isRemarkExist) return proxy.$modal.msgError('至少配置一条规则');
    const ruleForCol = columnFormList.value.map((res) => {
      return {
        discoveriesType: '0',
        ruleName: res.ruleName,
      };
    });
    const ruleForRemark = remarkFormList.value.map((res) => {
      return {
        discoveriesType: '1',
        ruleName: res.ruleName,
      };
    });
    const data = {
      sensitiveName: form.value.sensitiveName,
      sensitiveGroupId: searchForm.value.groupId,
      securityLevelCode: form.value.securityLevelCode,
      remark: form.value.remark,
      workspaceId: workspaceId.value,
      rules: [...ruleForCol, ...ruleForRemark],
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    toSendInterFace(data);
  };

  const toSendInterFace = async (data) => {
    if (form.value.id) {
      data.id = form.value.id;
      const resForUpdate = await updateDiscoveryRule(data);
      if (resForUpdate.code != 200) return proxy.$modal.msgError(resForUpdate.msg);
      proxy.$modal.msgSuccess(resForUpdate.msg);
      addRuleDialog.value = false;
      getList();
    } else {
      const resForAdd = await addDiscoveryRule(data);
      if (resForAdd.code != 200) return proxy.$modal.msgError(resForAdd.msg);
      proxy.$modal.msgSuccess(resForAdd.msg);
      addRuleDialog.value = false;
      getList();
    }
  };

  const closeAddRuleDialog = () => {
    columnFormList.value = [];
    remarkFormList.value = [];
    activeName.value = 'first';
    form.value = {
      sensitiveName: null,
      sensitiveGroupId: null,
      securityLevelCode: null,
      remark: null,
    };
    proxy.resetForm('formRef');
    addRuleDialog.value = false;
  };

  const columnFormList = ref([]);
  const addColumn = () => {
    // 如果数据为空，则重置
    if (columnFormList.value == null) {
      columnFormList.value = new Array();
    }
    const obj = {
      ruleName: null,
    };
    columnFormList.value.push(obj);
  };

  const delCol = (index) => {
    columnFormList.value.splice(index, 1);
  };

  const remarkFormList = ref([]);
  const addRemark = () => {
    // 如果数据为空，则重置
    if (remarkFormList.value == null) {
      remarkFormList.value = new Array();
    }
    const obj = {
      ruleName: null,
    };
    remarkFormList.value.push(obj);
  };
  const delRem = (index) => {
    remarkFormList.value.splice(index, 1);
  };

  const detailForm = ref({});
  const detailDialog = ref(false);
  const detailDialogTitle = ref('详情');

  const viewDetail = (row) => {
    detailForm.value = { ...row };
    columnFormList.value = row.rules.filter((res) => res.discoveriesType == '0');
    remarkFormList.value = row.rules.filter((res) => res.discoveriesType == '1');
    detailDialog.value = true;
  };

  const closeDetailDialog = () => {
    columnFormList.value = [];
    remarkFormList.value = [];
    detailDialog.value = false;
  };

  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    getGroupsTree();
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
    // 重置所有数据状态
    dataList.value = [];
    isAbleClick.value = true;
    total.value = 0;
    searchForm.value = { pageSize: 20, pageNum: 1, sensitiveName: null, groupId: null };
  });
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .page-container {
    width: 100%;
    height: 100%;
  }
  .rule-container {
    height: 100%;
    display: flex;
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-left: 10px;
      position: relative;
      .right-btn-box {
        text-align: right;
        .right-btn-add {
          width: 28px;
          height: 28px;
        }
      }
      .export-and-import {
        display: inline-block;
        margin-right: 10px;
      }
    }
    .top::before {
      position: absolute;
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: #1269ff;
      top: 5px;
      left: -10px;
    }

    .rule-left-box {
      //   width: 260px;
      //   margin-right: 20px;
      height: 100%;

      .tree-search {
        margin-bottom: 10px;
        background: $--base-color-item-light;
      }
      .left-box {
        height: calc(100% - 60px);
        background: $--base-color-item-light;
        border-radius: 8px;
        padding: 10px;
      }
      .left-tree-box {
        padding: 5px;
        height: calc(100% - 52px);
      }
    }

    .rule-right-box {
      //   flex: 1;
      //   width: calc(100% - 280px);
      height: 100%;
      .btn {
        cursor: pointer;
        display: inline-block;
        width: 32px;
        height: 32px;
        padding: 0 10px;
        border-radius: 20px;
        line-height: 36px;
        &.btn1 {
          background: #1269ff;
          margin-right: 10px;
        }
        &.btn2 {
          background: #dce5f5;
        }
      }

      .table-top-box {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      .table-container {
        height: calc(100% - 70px);
        .table-box {
          height: calc(100% - 102px);
        }
      }
      .pagination-container {
        margin-top: 10px;
      }
    }
  }

  .arrBox {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
  }

  .content-box {
    // width: 610px;
    padding: 10px;
    border-radius: 8px;
    background: #f7f8fb;
    .formList-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .tag-box {
    width: 36px;
    height: 22px;
    padding: 4px 6px;
  }

  .alert-box {
    background: #eaf1ff;
    color: #8c8c8c;
  }

  :deep(.el-alert .el-alert__icon) {
    color: #1269ff;
  }

  .detail-box {
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }
</style>
