<template>
  <div
    class="xg-card"
    :class="{
      'is-checked': cardData.isChecked,
      'is-selected': cardData.selected,
      'is-default': cardData.default,
    }"
  >
    <div v-if="cardData.type === 'imgCard'" class="card-header">
      <header-slot v-if="$slots.cardTop">
        <slot name="cardTop"></slot>
      </header-slot>
      <div v-else class="img-card">
        <img :src="cardData.img" />
        <div class="card-title-box">
          <div class="card-title">{{ cardData.title }}</div>
          <div class="card-title-remark">{{ cardData.remark }}</div>
        </div>
      </div>
    </div>
    <div v-else-if="cardData.type === 'normalCard'" class="card-header">
      <header-slot v-if="$slots.cardTop">
        <slot name="cardTop"></slot>
      </header-slot>
      <div v-else class="card-title">{{ cardData.title }}</div>
    </div>
    <div class="card-content">
      <div v-if="cardData.content" class="card-content-text">
        {{ cardData.content }}
      </div>
      <slot v-else name="cardContent"></slot>
    </div>
    <div class="card-bottom">
      <slot name="cardBottom"></slot>
    </div>
    <div v-if="cardData.placement" class="card-placement-box" :class="cardData.placement">
      <slot name="cardPlacement"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'XgCard',
    props: {
      cardData: {
        type: Object,
        default: () => {
          return {
            isChecked: false,
            selected: false,
            default: false,
          };
        },
      },
    },
    data() {
      return {};
    },
    computed: {},
    mounted() {},
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .xg-card {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    border: 1.5px solid #fff;
    background: #fff;
    position: relative;
    > div {
      box-sizing: border-box;
    }
    .card-header {
      width: 100%;
      height: 66px;
      padding: 20px 20px 0;
      .img-card {
        display: flex;
        justify-content: space-between;
        > img {
          width: 46px;
          height: 46px;
        }
        .card-title-box {
          width: calc(100% - 62px);
          height: 46px;
          text-align: left;
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          .card-title {
            width: 100%;
            overflow: hidden;
            color: var(--Neutral-9, #434343);
            text-overflow: ellipsis;
            font-family: 'PingFang SC';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 14px; /* 87.5% */
          }
          .card-title-remark {
            height: 14px;
            padding: 4px 6px;
            border-radius: 4px;
            background: #f0f4f9;
            color: var(--character-secondary-45, rgba(0, 0, 0, 0.45));
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 14px; /* 100% */
          }
        }
      }
    }
    .card-content {
      width: 100%;
      padding: 10px 20px 20px;
    }
    .card-bottom {
      width: 100%;
      height: 46px;
    }
    &.is-selected {
      border-color: $--base-color-primary;
    }
    &.is-default {
      filter: grayscale(100%);
      cursor: no-drop;
    }
    &.is-checked {
      &::before {
        content: '√';
        width: 26px;
        height: 26px;
        line-height: 26px;
        background: #52c41a;
        color: #f0f4f9;
        position: absolute;
        top: 0;
        right: 0;
        border-radius: 0 12px 0 0;
      }
    }
    &:hover {
      //   border-color: $--base-color-primary;
      box-shadow: 0px 6px 18px 0px rgba(1, 102, 243, 0.18);
    }
  }
</style>
