<template>
  <div class="rule-right-box">
    <div class="search-container" style="text-align: right">
      <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
        <el-form-item label="表名称" label-width="60">
          <el-input style="width:200px" v-model="searchForm.tableName" placeholder="请输入表名称" clearable />
        </el-form-item>
        <el-form-item label="字段名称" label-width="80">
          <el-input style="width:200px" v-model="searchForm.columnName" placeholder="请输入字段名称" clearable />
        </el-form-item>
        <el-form-item label="敏感字段类型">
          <el-select style="width:200px" v-model="searchForm.sensitiveId" placeholder="请选择" clearable>
            <el-option
              v-for="dict in sensitiveDataList"
              :key="dict.id"
              :label="dict.sensitiveName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="密级" label-width="60">
          <el-select style="width:200px" v-model="searchForm.securityLevelCode" placeholder="请选择" clearable>
            <el-option
              v-for="dict in security_level_code"
              :key="dict.value"
              :label="dict.value"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-right: 5px">
          <span class="table-search-btn">
            <span @click="getList" class="btn btn1"
              ><el-icon style="color: #fff"><Search /></el-icon
            ></span>
            <span @click="searchReSet" class="btn btn2"
              ><el-icon style="color: #434343"><Refresh /></el-icon
            ></span>
          </span>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-top-box">
      <div></div>
      <div><right-toolbar :columns="columns" @query-table="getList"></right-toolbar></div>
    </div>
    <el-table :data="dataList">
      <el-table-column type="index" width="60" label="序号">
        <template #default="scope">
          {{ searchForm.pageSize * (searchForm.pageNum - 1) + (scope.$index + 1) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[0].visible"
        label="数据源类型/数据源名称/数据库名称"
        prop="datasourceInfoName"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="columns[1].visible" label="模式" prop="schemaName"></el-table-column>
      <el-table-column
        v-if="columns[2].visible"
        label="表名称"
        prop="tableName"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        v-if="columns[3].visible"
        label="字段名称"
        prop="columnName"
      ></el-table-column>
      <el-table-column v-if="columns[4].visible" label="密级" prop="securityLevelCode">
        <template #default="scope">
          <span :class="`status-span status-span-${scope.row.securityLevelCode}`">
            {{ scope.row.securityLevelCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="敏感字段类型">
        <template #default="scope">
          <el-select
            @change="(data) => updateSensitive(data, scope.row)"
            v-model="scope.row.sensitiveId"
            placeholder="请选择"
          >
            <el-option
              v-for="dict in sensitiveDataList"
              :key="dict.id"
              :label="dict.sensitiveName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
        <template #default="scope">
          <el-button @click="toAssets(scope.row)" type="text">血缘分析</el-button>
          <span style="margin: 0 4px; width: 1px; height: 12px; color: #d9d9d9">|</span>
          <el-button @click="openDialog(scope.row)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="searchForm.pageNum"
      v-model:limit="searchForm.pageSize"
      :total="total"
      @pagination="getList"
    />

    <el-dialog
      v-model="shelvesModal"
      :title="shelvesTitle"
      width="400px"
      append-to-body
      :draggable="true"
      @close="shelvesCancel"
    >
      <div style="text-align: center">
        <div style="font-weight: 500; font-size: 16px; color: #434343">是否确认删除该敏感字段</div>
        <div style="font-size: 14px; color: #8c8c8c">字段删除后，将无法恢复</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shelvesCancel">取 消</el-button>
          <el-button type="primary" @click="shelvesCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getUserProfile } from '@/api/system/user';
  import {
    getAllSensitiveList,
    getSensitiveDataList,
    updateSensitiveData,
    deleteSensitiveData,
  } from '@/api/dataGovernance';
  import { useRouteDataStore } from '@/store/modules/dataAssets';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getCurrentInstance } from 'vue';
  import { useRouter } from 'vue-router';
  const store = useWorkFLowStore();
  const storeForRoute = useRouteDataStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();
  const { security_level_code } = proxy.useDict('security_level_code');
  const total = ref(0);
  const searchForm = ref({ pageSize: 20, pageNum: 1 });
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `数据源`, visible: true },
    { key: 1, label: `模式`, visible: true },
    { key: 2, label: `表字段`, visible: true },
    { key: 3, label: `字段名称`, visible: true },
    { key: 4, label: `密级`, visible: true },
  ]);

  const updateSensitive = async (sensitiveId, row) => {
    const data = {
      columnId: row.columnId,
      sensitiveId,
    };
    const res = await updateSensitiveData(data);
    if (res.code != 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess('修改成功');
    getList();
  };

  const shelvesModal = ref(false);
  const shelvesTitle = ref('操作确认');
  const shelvesForm = ref({});
  const openDialog = (row) => {
    shelvesForm.value.columnId = row.columnId;
    shelvesModal.value = true;
  };

  const shelvesCancel = () => {
    shelvesModal.value = false;
  };

  const shelvesCommit = async () => {
    const res = await deleteSensitiveData(shelvesForm.value);
    if (res.code != 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess('删除成功');
    shelvesModal.value = false;
    getList();
  };

  const sensitiveDataList = ref([]);
  const dataList = ref([]);

  const searchReSet = () => {
    for (let key in searchForm.value) {
      searchForm.value[key] = null;
    }
    searchForm.value.pageNum = 1;
    getList();
  };

  const getList = async () => {
    let data = {
      ...searchForm.value,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const res = await getSensitiveDataList(data);
    dataList.value = res.rows;
    total.value = res.total;
  };
  let userInfo = reactive({});
  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    let data = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resForList = await getAllSensitiveList(data);
    sensitiveDataList.value = resForList.data;
    getList();
  };
  init();
  watch(workspaceId, () => {
    init();
  });

  const router = useRouter();
  const toAssets = (row) => {
    const assetData = {
      catalog: row.databaseName,
      datasourceId: row.datasourceId,
      schema: row.schemaName,
      tableName: row.tableName,
      workspaceId: row.workspaceId,
      id: row.assetId,
      activeName: 'third',
    };
    storeForRoute.setRouteData(assetData);
    router.push({
      name: 'DataAssets',
    });
  };
</script>

<style lang="scss" scoped>
  .rule-right-box {
    padding: 0 20px;
  }
  .btn {
    cursor: pointer;
    display: inline-block;
    width: 32px;
    height: 32px;
    padding: 0 10px;
    border-radius: 20px;
    line-height: 36px;
    &.btn1 {
      background: #1269ff;
      margin-right: 10px;
    }
    &.btn2 {
      background: #dce5f5;
    }
  }

  .table-top-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .status-span {
    display: inline-block;
    width: 37px;
    height: 24px;
    &.status-span-L0 {
      background: #dce5f5;
      color: #434343;
    }
    &.status-span-L1 {
      background: #eaf1ff;
      color: #1269ff;
    }
    &.status-span-L2 {
      background: #e6fffb;
      color: #36cfc9;
    }
    &.status-span-L3 {
      background: #fff7e6;
      color: #faad14;
    }
    &.status-span-L4 {
      background: #f9f0ff;
      color: #722ed1;
    }
    &.status-span-L5 {
      background: #fff0f6;
      color: #eb2f96;
    }
    &.status-span-L6 {
      background: #fbeae9;
      color: #f84031;
    }
  }
</style>
