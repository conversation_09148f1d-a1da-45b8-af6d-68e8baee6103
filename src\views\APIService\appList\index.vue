<template>
  <div class="App-theme">
    <el-form
      ref=""
      label-position="left"
      class="search-form"
      label-width="auto"
      @submit.prevent="determine"
    >
      <el-form-item :label="formItem" prop="roleName">
        <el-row :gutter="20">
          <el-col :span="15">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入名称"
              clearable
              style="width: 250px"
            />
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="Search" @click="determine">查询</el-button>
            <!--  -->
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button icon="Plus" @click="jumpTo">新建</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button icon="Download" @click="listExport">导出</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button icon="Upload" @click="listImport">上传</el-button> -->
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div class="table-box">
      <el-table
        ref="tableRef"
        :data="tableData"
        height="100%"
        row-class-name="rowClass"
        empty-text="暂无数据"
      >
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
          <!-- appName -->
          <template v-if="item.prop === 'enabled'" #default="scope">
            <el-tag
              v-if="scope.row.enabled == 1"
              size="small"
              class="tag-box"
              type="primary"
              effect="light"
              :round="false"
            >
              启用
            </el-tag>
            <el-tag v-else size="small" class="tag-box" effect="light" :round="false" type="info">
              禁用
            </el-tag>
            <el-switch
              v-model="scope.row.enabled"
              :active-value="1"
              :inactive-value="0"
              style="margin-left: 8px; --el-switch-on-color: #13ce66"
              @change="updateAppStateUtil(scope)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200" width="300" flex="right">
          <template #default="scope">
            <!-- <el-button -->
            <!-- type="text" -->
            <!-- size="small" -->
            <!-- :disabled="scope.row.enabled !== 1" -->
            <!-- @click="relating(scope)" -->
            <!-- >授权 API</el-button -->
            <!-- > -->
            <el-button type="text" size="small" @click="revamp(scope)">
              <IconEdit />
              编辑
            </el-button>
            <el-button type="text" size="small" @click="delAppUtil(scope)">删除</el-button>
            <!-- <el-button type="text" size="small" @click="updateAppStateUtil(scope)"> -->
            <!-- {{ scope.row.enabled == 1 ? '禁用' : '启用' }} -->
            <!-- </el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div>
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="determine"
      />
    </div>

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="auto">
        <el-form-item label="应用名称" prop="appName">
          <el-input v-model="form.appName" placeholder="请输入" @input="limitInputLength" />
        </el-form-item>
        <!-- AppCode -->
        <el-form-item label="AppCode" prop="appCode">
          <el-input v-model="form.appCode" placeholder="请输入" :maxlength="32">
            <template #suffix>
              <el-button type="text" icon="el-icon-refresh" @click="refreshAppCode('appCode')">
                刷新
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <!-- appKey -->
        <el-form-item label="AppKey" prop="appKey">
          <el-input v-model="form.appKey" placeholder="请输入" :maxlength="32" />
        </el-form-item>
        <!-- AppSecret -->
        <el-form-item label="AppSecret" prop="appSecret">
          <el-input v-model="form.appSecret" placeholder="请输入" :maxlength="32">
            <template #suffix>
              <el-button type="text" icon="el-icon-refresh" @click="refreshAppCode('appSecret')">
                刷新
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <!-- IP 白名单 -->
        <el-form-item label="IP 访问限制" prop="ips">
          <el-select
            v-model="form.strategyType"
            placeholder="请选择"
            clearable
            @change="changeStrategy"
          >
            <el-option
              v-for="option in restrictionsList"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="IP " prop="ips">
          <el-input v-model="form.ips" placeholder="多个 IP 用分号分隔" />
        </el-form-item>
        <el-form-item label="应用状态" prop="enabled">
          <el-tag v-if="form.enabled" class="tag-box" type="primary" effect="light" :round="false">
            启用
          </el-tag>
          <el-tag v-else class="tag-box" effect="light" :round="false" type="info">禁用</el-tag>
          <el-switch
            v-model="form.enabled"
            :active-value="1"
            :inactive-value="0"
            style="margin-left: 8px; --el-switch-on-color: #13ce66"
          />
        </el-form-item>
        <el-form-item label="应用描述" prop="appDesc">
          <el-input
            v-model="form.appDesc"
            placeholder="请输入"
            type="textarea"
            :maxlength="255"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="apiVisible" title="关联服务" width="40%" append-to-body :draggable="true">
      <!-- <el-tree-transfer
        v-model="valueTree"
        :data="dataTree"
        :to-data="toData"
        :children-is-empty="true"
        :show-filter="true"
        :node-props="nodeProps"
      /> -->
      <el-transfer
        v-model="valueTree"
        :data="dataTree"
        :props="nodeProps"
        filterable
        class="api-visible-transfer"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeApi">取 消</el-button>
          <el-button type="primary" @click="submitApi">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    addApp,
    getAppList,
    getCategoryList,
    updateApp,
    updateAppState,
    setChoosedList,
  } from '@/api/APIService';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { ref, reactive } from 'vue';
  import CryptoJS from 'crypto-js';
  import { ElMessage } from 'element-plus';

  // 生成随机字符串的函数
  function generateRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  // 生成随机 MD5 哈希值的函数
  function generateRandomMD5Hash() {
    const randomString = generateRandomString(32); // 你可以根据需要调整字符串的长度
    const hash = CryptoJS.MD5(randomString).toString();
    return hash;
  }
  const refreshAppCode = (type) => {
    if (type === 'appCode') {
      form.value.appCode = generateRandomMD5Hash();
    } else if (type === 'appSecret') {
      form.value.appKey = generateRandomMD5Hash();
      form.value.appSecret = generateRandomMD5Hash();
    }
  };
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      groupName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],

      //   parentId: [{ required: true, message: '请选择菜单', trigger: 'change' }],
      categoryName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],

      appName: [{ required: true, message: '请输入应用名称', trigger: 'change' }],
      appCode: [{ required: true, message: '请输入应用编码', trigger: 'change' }],
      appKey: [{ required: true, message: '请输入应用密钥', trigger: 'change' }],
      appSecret: [{ required: true, message: '请输入应用密钥', trigger: 'change' }],
      appDesc: [{ required: false, message: '请输入应用描述', trigger: 'change' }],
      ips: [{ required: false, message: '请输入 IP 白名单', trigger: 'change' }],
      enabled: [{ required: true, message: '请选择应用状态', trigger: 'change' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    enabled: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
    restrictionsList: [
      {
        label: '黑名单',
        value: 'black',
      },
      {
        label: '白名单',
        value: 'white',
      },
    ],
  });

  const { form, rules, queryParams, restrictionsList } = toRefs(data);

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref();

  const activeName = ref('first');

  const formItem = ref('应用名称');

  const maxCount = ref(5);
  const total = ref();
  const getAppListUtil = async () => {
    closeSpatial();

    columns.value = [
      {
        key: 0,
        label: `应用 ID`,
        visible: true,
        prop: 'appId',
        width: '100px',
        showOverflowTooltip: true,
      },
      {
        key: 1,
        label: `应用名称`,
        visible: true,
        prop: 'appName',
        width: '150px',
        showOverflowTooltip: true,
      },
      {
        key: 2,
        label: `应用描述`,
        visible: true,
        prop: 'appDesc',
        width: '200px',
        showOverflowTooltip: true,
      },
      {
        key: 3,
        label: `修改时间`,
        visible: true,
        prop: 'updateTime',
        width: '200px',
        showOverflowTooltip: true,
      },
      { key: 4, label: `状态`, visible: true, prop: 'enabled' },
      //   { key: 4, label: `appSecret`, visible: true, prop: 'appSecret' },
    ];

    const res = await getAppList({
      ...queryParams.value,
      //   workspaceId: workspaceId.value,
    });

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.data.list;
    total.value = res.data.total;
  };

  const groupName = ref();

  const addAppUtil = async () => {
    const res = await addApp({
      ...form.value,
      userId: '',
      tenantId: tenantId.value,
      workspaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getAppListUtil();
  };

  //   const delAppUtil = async (row) => {
  //     const confirm = await proxy.$modal.confirm(
  //       `是否确认删除应用名称为${row.row.appName}的数据项？`,
  //     );
  //     if (!confirm) return;
  //     // const res = await delApp(row.row.appId);
  //     const res = await delApp({
  //       appId: row.row.appId,
  //     });
  //     if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //     proxy.$modal.msgSuccess(res.msg);
  //     getAppListUtil();
  //   };
  const updateAppStateUtil = async ({ row }) => {
    const res = await updateAppState({
      ...row,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getAppListUtil();
  };

  const updateAppUtil = async () => {
    const res = await updateApp({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getAppListUtil();
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
    proxy.$refs.formRef?.resetFields();
  };

  const submitSpatial = async () => {
    const ref = await proxy.$refs.formRef.validate((valid) => valid);
    if (spatialTitle.value === '编辑应用' && ref) {
      updateAppUtil();
    } else if (spatialTitle.value === '新增应用' && ref) {
      addAppUtil();
    }
  };

  const revamp = (row) => {
    spatialVisible.value = true;
    spatialTitle.value = '编辑应用';
    form.value = {
      ...row.row,
    };
  };

  const getCategoryListUtil = async () => {
    closeSpatial();

    const ref = await getCategoryList({
      keyword: groupName.value,
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });

    columns.value = [
      { key: 0, label: `分类 ID`, visible: true, prop: 'categoryId' },
      { key: 1, label: `分类名称`, visible: true, prop: 'categoryName' },
      { key: 2, label: `分类说明`, visible: true, prop: 'categoryDesc' },
      { key: 3, label: `关联服务数`, visible: true, prop: 'apiCount' },
      { key: 4, label: `修改时间`, visible: true, prop: 'updateTime' },
    ];

    tableData.value = ref.rows;
    total.value = ref.total;
  };
  const determine = () => {
    if (activeName.value === 'first') {
      getAppListUtil();
    } else if (activeName.value === 'second') {
      getCategoryListUtil();
    }
  };

  const apiVisible = ref(false);

  const valueTree = ref([]);
  const dataTree = ref([]);

  //   const nodeProps = ref({
  //     label: 'label',
  //     children: 'children',
  //     value: 'id', // value field
  //     disabled: 'disabled',
  //   });
  const nodeProps = ref({
    label: 'apiName',
    key: 'apiId',
    disabled: 'disabled',
  });
  const submitApi = async () => {
    console.log(valueTree.value);
    const reqData = {
      apiIds: valueTree.value,
    };
    await setChoosedList(reqData).then((res) => {
      if (res.msg === '操作成功') {
        ElMessage.success('授权成功');
        apiVisible.value = false;
      } else {
        ElMessage.error('授权失败：' + res.msg);
        apiVisible.value = false;
      }
    });
    // apiVisible.value = false;
  };

  const closeApi = () => {
    apiVisible.value = false;
  };

  onMounted(async () => {
    await getAppListUtil();
  });

  watch(workspaceId, (val) => {
    determine();
  });

  const limitInputLength = (event) => {
    if (!event) return;
    if (event.length > 25) {
      event = event.substring(0, 25);
      form.value.appName = event;
    }
  };
  const changeStrategy = () => {
    form.value.ips = null;
  };
</script>

<style lang="scss" scoped>
  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
    // background-color: #fff;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    .table-box {
      width: 100%;
      height: calc(100% - 100px);
      border-radius: 8px;
    }
    .search-form {
      .el-form-item {
        text-align: right;
      }
    }
  }
  .api-visible-transfer {
    text-align: center;
    .el-transfer-panel {
      width: calc(50% - 90px);
    }
  }
  .pagination-container {
    margin: 10px;
  }

  .tag-box {
    width: 36px;
    height: 22px;
    padding: 4px 6px;
  }
</style>
