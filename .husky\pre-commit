#!/bin/sh
. "$(dirname "$0")/_/husky.sh"
cd xugurtp-ui-vue3
check_vite_config() {
    local config_file="vite.config.js"
    local expected_dev_api="target: 'http://10.28.23.131:9000/prod-api'"
    if ! awk -v pattern="$expected_dev_api" '$0 ~ pattern && $0 !~ /^ *\/\// {found=1} END {exit !found}' "$config_file"; then
        echo "Error: dev-api 未正确设置 $config_file"
        return 1
    fi
}
check_lint_staged() {
    output=$(npx lint-staged 2>&1)
    exit_code=$?
    if [ $exit_code -ne 0 ]; then
        echo "npx lint-staged 失败，输出如下："
        echo "$output"
        echo "Error: 请检查代码格式是否正确"
        return 1
    fi
}
# check_vite_config
# if [ $? -ne 0 ]; then
#     exit 1
# fi
echo "Error: 请检查已提交代码格式  或dev-api 设置是否正确"
#check_lint_staged
if [ $? -ne 0 ]; then
    exit 1
fi
exit 0
