<template>
  <div class="app-container" style="padding: 10px">
    <el-button type="primary" icon="ArrowLeft" @click="close">返回</el-button>
    <el-row class="box-row">
      <el-col :span="12">
        <div class="items" shadow="never">
          <div class="box-top-img">
            <img src="@/assets/images/dataAssets.png" alt="" style="width: 30px; height: 30px" />
          </div>
          <div class="items-info">
            <div>
              {{ baseInfo?.tableName }}

              <div class="copy-text" @click="copyText">
                <el-tooltip
                  class="box-item"
                  content="复制表名"
                  effect="light"
                  placement="top-start"
                >
                  <el-icon>
                    <CopyDocument />
                  </el-icon>
                </el-tooltip>
              </div>
              <!-- <el-link icon="DocumentCopy" :underline="false" @click="copyText" /> -->
            </div>

            <div>
              <el-tag
                v-for="item in btnList"
                :key="item.label"
                type="info"
                icon="Key"
                round
                @click="onClick(item)"
              >
                <el-icon>
                  <component :is="item.icon" />
                </el-icon>
                {{ item.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <div class="bottom-box">
      <!-- <splitpanes class="default-theme"> -->
      <!-- <pane min-size="8" max-size="25" :size="size" style="padding-top: 5px" class="card-info"> -->
      <el-card v-show="size !== 2" shadow="never" class="">
        <div class="TitleName">表基础信息</div>
        <!-- <Transition name="slide-fade"> -->
        <!-- <el-scrollbar> -->
        <div v-if="size !== 2">
          <el-form
            :model="form"
            label-width="auto"
            size="mini"
            label-position="left"
            class="info-form"
            @submit.native.prevent
          >
            <el-form-item label="数据源类型">
              {{ baseInfo?.datasourceType || '-' }}
            </el-form-item>
            <el-form-item label="数据源名称">
              {{ baseInfo.datasourceName || '-' }}
            </el-form-item>
            <el-form-item label="数据库"> {{ baseInfo.databaseName || '-' }} </el-form-item>
            <el-form-item label="模式"> {{ baseInfo.schemaName || '-' }} </el-form-item>
            <el-form-item label="创建人"> {{ baseInfo.createBy || '-' }} </el-form-item>
            <el-form-item label="创建日期"> {{ baseInfo.createTime || '-' }} </el-form-item>
            <el-form-item label="最后修改表时间" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.lastCollectTime || '-' }}
            </el-form-item>
            <el-form-item label="数据路径" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.path || '-' }}
            </el-form-item>
            <el-form-item label="表注释"> {{ baseInfo.tableComment || '-' }} </el-form-item>
            <el-form-item label="表映射来源" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.tableMappingSource || '-' }}
            </el-form-item>
            <el-form-item label="文件总数" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.fileCount || '0' }}
            </el-form-item>
            <el-form-item label="总行数" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.lineCount || '0' }}
            </el-form-item>
            <el-form-item label="压缩格式" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.compressedFormat || '-' }}
            </el-form-item>
            <el-form-item label="压缩后数据总大小" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.compressedSize || '-' }}
            </el-form-item>
            <el-form-item label="分区字段" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.partitionColumn || '-' }}
            </el-form-item>
            <el-form-item label="最新分区" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.newPartition || '-' }}
            </el-form-item>
            <el-form-item label="分区数量" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.partitionCount || '0' }}
            </el-form-item>
            <el-form-item label="最新分区数据大小" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.newPartitionSize || '-' }}
            </el-form-item>
            <el-form-item label="最新分区文件数" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.newPartitionFileCount || '0' }}
            </el-form-item>
            <el-form-item label="最新分区行数" v-if="baseInfo?.datasourceType === 'HIVE'">
              {{ baseInfo.newPartitionLineCount || '0' }}
            </el-form-item>

            <el-form-item label="存储容量" v-if="baseInfo?.datasourceType === 'XUGU'">
              {{ baseInfo.xuguStore || '-' }}
            </el-form-item>
            <el-form-item label="类目">
              {{ handleData(baseInfo.categoryNames) || '-' }}
            </el-form-item>
            <el-form-item label="密级">
              <span :class="getLevelClass(baseInfo?.securityLevelCode)">
                {{ baseInfo?.securityLevelCode || '-' }}
              </span>
            </el-form-item>
            <el-form-item label="标签">
              <!-- <div class="icon-tag-box">
                <div
                  class="tag-item tag-first"
                  v-if="baseInfo.tags?.length > 0 && baseInfo.tags[0] !== ''"
                  >{{ baseInfo.tags[0] }}</div
                >
                <div class="tag-item" v-if="baseInfo.tags?.length > 1">
                  <el-popover placement="top" :teleported="false" trigger="hover">
                    <template #reference> + {{ baseInfo.tags?.length - 1 }} </template>
                    <div class="tag-item-popover">
                      <div
                        class="tag-item"
                        v-for="(tagsName, index) in baseInfo.tags"
                        :key="`tag-item-${tagsName}`"
                      >
                        {{ tagsName }}
                      </div>
                    </div>
                  </el-popover>
                </div>
                <div class="tag-btn" @click="showTagDialog(baseInfo.tags)">
                  <IconEdit />
                  编辑
                </div>
              </div> -->
              <TagsEdit
                :baseInfo="baseInfo"
                :assetId="assetData.id"
                :assetName="baseInfo?.tableName"
                type="table"
                @afterEdit="getTechDetailUtil"
              ></TagsEdit>
            </el-form-item>
            <el-form-item label="业务备注">
              <div class="remark-box">
                <!-- <el-tooltip :content="baseInfo.bizRemark" placement="top"> -->
                <el-input
                  v-model="baseInfo.bizRemark"
                  width="100%"
                  type="textarea"
                  placeholder="请输入业务备注"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  maxlength="100"
                  show-word-limit
                  @focus.native.capture="handleFocus"
                  @blur.native.capture="handleBlur"
                />
                <!-- </el-tooltip> -->
                <svg
                  v-if="baseInfo.showSaveIcon"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="9.55078" cy="9.42501" r="6" fill="#52C41A" />
                  <path
                    d="M11.7492 8.02502C11.7492 8.02502 9.48598 9.81179 8.8904 12.075L7.69922 10.4074"
                    stroke="white"
                    stroke-width="0.685714"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </el-form-item>
            <el-form-item label="最后采集时间">
              {{ baseInfo.lastCollectTime || '-' }}
            </el-form-item>
            <el-form-item label="最后更新时间">
              {{ baseInfo.updateTime || '-' }}
            </el-form-item>
          </el-form>
        </div>
        <!-- </el-scrollbar> -->
        <!-- </Transition> -->
      </el-card>
      <!-- <el-link icon="Operation" class="link-info" @click="size = size === 2 ? 20 : 2" /> -->
      <!-- </pane> -->
      <!-- <pane style="padding-top: 10px"> -->
      <div class="bottom-tags">
        <TabSwitch class="tab-container" :title-list="tab" @change="getData" />
        <component :is="currentComponent" :asset-data="assetData" />
      </div>
    </div>
    <!-- </pane> -->
    <!-- </splitpanes> -->
  </div>
  <el-dialog
    v-model="dialog.visible"
    :title="dialog.title"
    width="50%"
    append-to-body
    @close="closeDialog"
  >
    <el-form :model="form" label-width="80px">
      <el-form-item label="表名"> {{ baseInfo?.tableName }} </el-form-item>
      <!-- 类目 -->
      <el-form-item v-if="dialog.title === '加入类目'" label="类目">
        <el-tree
          ref="techRef"
          class="tree-border"
          :data="categoryList"
          show-checkbox
          node-key="id"
          :check-strictly="false"
          empty-text="暂无数据"
          :props="{ label: 'categoryName', children: 'children' }"
          @check="handleNodeClick"
        />
      </el-form-item>
      <!-- 密级 -->
      <el-form-item v-else label="密级">
        <el-select v-model="dialog.data.level" placeholder="请选择密级">
          <el-option
            v-for="dict in security_level_code"
            :key="dict.value"
            :label="`${dict.value}/${dict.label}`"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitDialog">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- <el-dialog
    v-model="tagDialog.visible"
    :title="tagDialog.title"
    width="650"
    @close="closeTagDialog"
  >
    <div class="tags-top-prompt">
      <div class="tags-top-prompt-top">
        <IconStateTips />
      </div>
      <div class="prompt-text">
        您正在为资产“API名称”编辑标签。您可以新增标签、修改标签值、解绑标签。每个资产最多可绑定 20
        个标签，并且标签键不可重复。
      </div>
      <div class="prompt-turn"
        >若需要对标签进行统一管理，请前往<span @click="turnTo('1')">标签管理</span></div
      >
    </div>
    <div class="tags-statistics">
      <div class="statistics-title">标签</div>
      <div class="statistics-box">
        <div class="statistics-num"
          ><span>{{ editTags.length }}</span
          >/20</div
        >
        <div class="statistics-add">
          <el-button type="primary" :disabled="editTags.length >= 20" @click="addTag">
            <IconAdd />
          </el-button>
        </div>
      </div>
    </div>
    <div class="tags-edit-box">
      <el-scrollbar>
        <div class="tags-list-box" v-for="(tag, tagIndex) in editTags" :key="`tag-${tagIndex}`">
          <div class="tags-edit-label">标签：</div>
          <div class="tags-select">
            <el-cascader
              v-model="tag.tagId"
              :options="tagsShowOptions"
              placeholder="请选择"
              :props="{
                lazy: true,
                lazyLoad: loadNode,
                value: 'value',
                label: 'labelKeyName',
                children: 'children',
              }"
              collapse-tags
              collapse-tags-tooltip
              clearable
              @change="handleTagChange"
            />
          </div>
          <div class="tags-delete" @click="deleteTag(tagIndex)">
            <IconDelete />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeTagDialog">取 消</el-button>
        <el-button type="primary" @click="submitTagDialog">确 定</el-button>
      </span>
    </template>
  </el-dialog> -->
</template>

<script setup>
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';

  import TabSwitch from '@/components/tabSwitch/index';
  import TagsEdit from '@/components/TagsEdit';
  import first from './first.vue';
  import second from './second.vue';
  import third from './third.vue';
  import four from './four.vue';
  import five from './five.vue';
  import { useRouter } from 'vue-router';
  import {
    getTechDetail,
    getCategoryTree,
    changeIntoCategory,
    securityLevelById,
    dataAssetModify,
    editAsset,
  } from '@/api/dataGovernance';
  import { getLabelKey, getLabelValue } from '@/api/system/tagManagement';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { onMounted, nextTick } from 'vue';
  // 导入 vue use
  import { useClipboard } from '@vueuse/core';
  import { IconEdit, IconAdd, IconDelete } from '@arco-iconbox/vue-update-line-icon';
  import { IconStateTips } from '@arco-iconbox/vue-update-color-icon';
  import { ElMessage } from 'element-plus';

  const { proxy } = getCurrentInstance();
  const { security_level_code } = proxy.useDict('security_level_code');

  const size = ref(20);
  const props = defineProps({
    assetData: {
      type: Object,
      required: true,
    },
  });
  const { assetData } = toRefs(props);
  const baseInfo = ref([]);
  const tab = ref([
    { value: 'first', label: '元数据信息', isActive: true },
    { value: 'second', label: '工作流信息', isActive: false },
    { value: 'third', label: '血缘信息', isActive: false },
    { value: 'four', label: '数据质量', isActive: false },
    { value: 'five', label: '数据预览', isActive: false },
  ]);
  const tagDialog = ref({
    visible: false,
    title: '编辑标签',
  });
  const tagsOptions = ref([]);
  const tagsShowOptions = ref([]);
  const editTags = ref([
    {
      tagId: [],
    },
  ]);
  const hasSelectTag = ref([]);

  const componentMap = {
    first,
    second,
    third,
    four,
    five,
  };
  const currentComponent = computed(() => componentMap[activeName.value]);

  const activeName = ref('first');

  const infoList = ref([
    {
      label: '数据源类型',
      value: '创建时间',
    },
    {
      label: '数据源名称',
      value: '数据源名称',
    },
    {
      label: '数据库',
      value: '数据库名称',
    },
    {
      label: '模式',
      value: '数据库名称--------------------------',
    },
    {
      label: '创建人',
      value: '数据库名称',
    },
    {
      label: '创建日期',
      value: '',
    },
    {
      label: '表注释',
      value: '',
    },
    {
      label: '类目',
      value: '',
    },
    {
      label: '密级',
      value: 'LO 公开',
    },
    {
      label: '业务备注',
      value: '',
    },

    {
      label: '最后采集时间',
      value: '',
    },
    {
      label: '最后更新时间',
      value: '',
    },
  ]);
  const btnList = ref([
    // {
    //   label: '申请权限',
    //   value: '1',
    //   icon: 'Lock',
    // },
    // {
    //   label: '取消收藏',
    //   value: '2',
    //   icon: 'Star',
    // },
    {
      label: '生成API',
      value: '3',
      icon: 'Link',
      url: '/APIService/apiService/APIWorkbench',
    },
    {
      label: 'SQL查询',
      value: '3',
      icon: 'Search',
      url: '/dataGovernance/SQLScript',
    },
    {
      label: '加入类目',
      value: '3',
      icon: 'CirclePlus',
    },
    {
      label: '密级设置',
      value: '3',
      icon: 'Key',
    },
  ]);
  //   const arrList = ref([
  //     {
  //       label: '浏览次数',
  //       value: '1',
  //       color: 'background-color: #F7F8FB',
  //     },
  //     {
  //       label: '读取次数',
  //       value: '2',
  //       color: 'background-color: #E6F9F9',
  //     },
  //     {
  //       label: '收藏次数',
  //       value: '3',
  //       color: 'background-color: #EAF1FF',
  //     },
  //   ]);

  const emit = defineEmits(['close']);
  const close = () => {
    emit('close');
  };

  const getData = (data) => {
    if (!data) return false;
    activeName.value = data;
  };
  const { copy, isSupported } = useClipboard();
  const copyText = () => {
    // if (!isSupported.value) return proxy.$modal.msgError('您的浏览器不支持复制功能');
    // copy(baseInfo.value.tableName);
    // proxy.$modal.msgSuccess('复制成功');
    const textarea = document.createElement('textarea');
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = baseInfo.value.tableName;
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    proxy.$modal.msgSuccess('复制成功');
  };
  const router = useRouter();
  const onClick = (item) => {
    if (!item) return false;

    if (item.label === '生成API' || item.label === 'SQL查询') {
      if (item.label === '生成API') {
        router.push(item.url);
        // 携带参数跳转到 api 页面 如源 库 模式
        // const query = {
        //   datasourceName: baseInfo.value.datasourceName,
        //   databaseName: baseInfo.value.databaseName,
        //   schemaName: baseInfo.value.schemaName,
        // };

        // localStorage.setItem('self', query);
      } else {
        router.push(item.url);
      }
    } else if (item.label === '加入类目') {
      dialog.visible = true;
      dialog.title = item.label;
      dialog.data = item;
      getCategoryTreeUtil();
      nextTick(() => {
        // proxy.$refs.categoryRef.setChecked(baseInfo.value.categoryIds, true, false);
      });
    } else if (item.label === '密级设置') {
      dialog.visible = true;
      dialog.title = item.label;
      dialog.data = {};
      console.log('baseInfo.value.securityLevelCode', baseInfo.value.securityLevelCode);
      if (baseInfo.value.securityLevelCode) {
        dialog.data.level = baseInfo.value.securityLevelCode;
      }
    } else if (item.label === '申请权限') {
      console.log(3);
    } else if (item.label === '取消收藏') {
      console.log(4);
    }
  };

  const dialog = reactive({
    visible: false,
    title: '',
    data: {},
  });

  const getLevelClass = (item) => {
    // `status-span status-span-${dialog.row?.dictValue}`
    return `status-span status-span-${item}`;
  };

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());

  const getTechDetailUtil = async () => {
    const res = await getTechDetail({ dataAssetId: assetData.value.id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    res.data.tags = res.data.labels?.split(',');
    if (res.data.tags && res.data.tags[res.data.tags.length - 1] === '') {
      res.data.tags = res.data.tags.splice(0, res.data.tags.length - 1);
    }
    baseInfo.value = res.data;
  };
  const categoryList = ref(null);

  const getCategoryTreeUtil = async () => {
    const res = await getCategoryTree({ workspaceId: workspaceId.value });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    categoryList.value = res.data;
  };

  const closeDialog = () => {
    dialog.visible = false;
    dialog.title = '';
    dialog.data = {};
  };
  const submitDialog = async () => {
    if (dialog.title === '加入类目') {
      const query = {
        operateType: 'into',
        dataAssetIds: [assetData.value.id],
        categoryId: nodeTree.value,
      };
      await changeIntoCategoryUtil(query);
      await getTechDetailUtil();
    } else if (dialog.title === '密级设置') {
      const query = {
        dataAssetId: assetData.value.id,
        securityLevelCode: dialog.data.level,
      };
      await securityLevelByIdUtil(query);
      await getTechDetailUtil();
    }
  };

  const changeIntoCategoryUtil = async (data) => {
    const res = await changeIntoCategory(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    closeDialog();
  };
  const nodeTree = ref([]);
  const handleNodeClick = (data) => {
    nodeTree.value = data.id;
  };

  //重置选中的节点
  const resetTagOptions = () => {
    hasSelectTag.value = [];
    tagsShowOptions.value = JSON.parse(JSON.stringify(tagsOptions.value));

    editTags.value.map((tag) => {
      hasSelectTag.value.push(tag.tagId[0]);
    });
    hasSelectTag.value.map((item) => {
      tagsShowOptions.value.map((option) => {
        if (option.value === item) {
          option.disabled = true;
        }
      });
    });
  };

  //显示tag弹出框
  const showTagDialog = async (datas) => {
    await getTagLists();
    if (datas?.length > 0 && datas[0] !== '') {
      editTags.value = [];
      datas.map((data) => {
        const tagsKey = data.split(':');
        tagsOptions.value.map(async (option) => {
          if (option.labelKeyName === tagsKey[0]) {
            // 获取子节点数据
            const children = await getLabelValueUtil(option.value);
            // 处理子节点数据
            const child = children.map((item) => {
              item.value = item.id;
              item.labelKeyName = item.name;
              item.leaf = true;
              if (item.name === tagsKey[1]) {
                editTags.value.push({ tagId: [option.value, item.id] });
                resetTagOptions();
              }
              return item;
            });
            option.children = child;
          }
        });
      });
    } else {
      editTags.value = [];
    }
    tagDialog.value.visible = true;
  };
  //管理标签编辑弹出框
  const closeTagDialog = () => {
    tagDialog.value.visible = false;
  };
  //编辑标签
  const submitTagDialog = async () => {
    const reqData = {
      labelKey: [],
      assetId: assetData.value.id,
      name: baseInfo.value.tableName,
      type: 'table',
      workspaceId: workspaceId.value,
      isEdit: true,
    };
    editTags.value.forEach((item) => {
      reqData.labelKey.push(item.tagId);
    });

    const res = await editAsset(reqData);
    if (res.code === 200) {
      ElMessage.success('修改成功');
      tagDialog.value.visible = false;
      await getTechDetailUtil();
    }
  };
  //添加tag
  const addTag = () => {
    editTags.value.push({ tagId: [] });
  };

  //跳转
  const turnTo = () => {
    // router.push({
    //   path: '/dataGovernance/assetDetail',
    //   query: {
    //     id: assetData.value.id,
    //     activeName: activeName.value,
    //   },
    // });
  };
  //改变了标签值
  const handleTagChange = (res) => {
    console.log(res, 123);
    // tagsOptions.value.map((option) => {
    //   if (option.value === res[0]) {
    //     option.disabled = true;
    //   }
    // });
    resetTagOptions();
  };
  //获取标签值
  const getTagLists = async () => {
    const res = await getLabelKey({ id: assetData.value.id, workspaceId: workspaceId.value });
    tagsOptions.value = res.data.map((item) => {
      item.value = item.id;
      return item;
    });
    tagsShowOptions.value = JSON.parse(JSON.stringify(tagsOptions.value));
  };

  const getLabelValueUtil = async (id) => {
    const res = await getLabelValue({ id: id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    return res.data;
  };
  //获取标签
  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      // 第一级已经加载，不需要再次加载
      return resolve([]);
    }

    // 获取子节点数据
    const children = await getLabelValueUtil(node.data.id);
    // 处理子节点数据
    resolve(
      children.map((child) => ({
        value: child.id,
        labelKeyName: child.name,
        leaf: true, // 子节点为叶子节点
      })),
    );
  };
  //删除某个标签
  const deleteTag = (index) => {
    editTags.value.splice(index, 1);
    resetTagOptions();
  };

  onMounted(async () => {
    if (assetData.value.activeName) {
      tab.value.forEach((res) => {
        res.isActive = false;
        if (res.value == 'third') {
          res.isActive = true;
        }
      });
      activeName.value = 'third';
    }
    await getTechDetailUtil();
  });
  watch(workspaceId, () => {
    close();
  });
  const securityLevelByIdUtil = async (data) => {
    const res = await securityLevelById(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    closeDialog();
  };

  const editRemark = async (id) => {
    const query = {
      id,
      bizRemark: baseInfo.value.bizRemark,
    };
    const res = await dataAssetModify(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };

  const handleData = (data) => {
    if (!data) return;
    if (!data.length) return;
    return data;
  };

  const handleFocus = () => {
    baseInfo.value.isEditing = true;
    baseInfo.value.originalValue = baseInfo.value.bizRemark;
    baseInfo.value.showSaveIcon = false;
  };

  const remarkInput = ref();
  const handleBlur = async () => {
    baseInfo.value.isEditing = false;
    if (baseInfo.value.bizRemark !== baseInfo.value.originalValue) {
      await editRemark(baseInfo.value.id);
      baseInfo.value.showSaveIcon = true;
      setTimeout(() => {
        baseInfo.value.showSaveIcon = false;
      }, 3000); // Hide the icon after 3 seconds
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;
  }
  :deep .splitpanes.default-theme {
    height: calc(100% - 200px);
    .splitpanes__pane {
      background: none;
    }
    .splitpanes__splitter {
      background-color: #ffffff00;
      box-sizing: border-box;
      position: relative;
      -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
      flex-shrink: 0;
    }
    .el-card {
      border: none;
      border-radius: 8px;

      .el-card__body {
      }
    }
  }

  .box-row {
    padding: 20px;
    background-color: #ffffff;
    margin: 20px 10px;
    // box-shadow: 0 0 3px #ddd;
    border-radius: 8px;

    .items {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .items-info {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        margin: 15px;
        cursor: pointer;
        .copy-text {
          display: inline-block;
          vertical-align: bottom;
          margin-left: 6px;
        }
        :deep .el-tag--info {
          margin-right: 10px;
          border-color: #d2d8e0;
          color: #434343;
          .el-icon {
            vertical-align: top;
          }
        }
      }
    }

    .right-items {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .items-info {
        width: 100%;
        min-height: 30px;
        margin-right: 20px;
        padding: 10px;
        text-align: center;
        border-radius: 4px;
      }
    }
  }
  .splitpanes--vertical {
    padding: 0px 10px;
    .splitpanes__pane {
    }
  }
  .splitpanes__splitter {
    margin: 0 10px;
  }

  :deep .TitleName {
    margin: 0px 0px 10px 0px;
    padding-left: 10px;
    white-space: nowrap;
    position: relative;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: #1269ff;
      position: absolute;
      left: 0;
      top: 4px;
      bottom: 0;
      margin: auto;
    }
  }
  .bottom-box {
    height: calc(100% - 200px);
    padding: 0px 10px;
    display: flex;
    justify-content: space-between;
    align-content: center;
    & > .el-card {
      width: 400px;
      height: 100%;
      border: none;
    }
    .bottom-tags {
      width: calc(100% - 420px);
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      padding: 20px;
    }
    .info-form {
      .icon-tag-box {
        width: 100%;
        & > div {
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
          &.tag-item {
            height: 18px;
            line-height: 18px;
            padding: 0px 4px;
            background: $--base-color-tag-bg3;
            border-radius: 4px;
            color: $--base-color-primary;
            font-size: 12px;
          }
          &.tag-btn {
            font-size: 14px;
            color: $--base-color-primary;
            cursor: pointer;
            :deep .update-line-icon-icon-edit {
              vertical-align: -1px;
              path {
                stroke: $--base-color-primary;
                fill: #ffffff;
              }
            }
          }
        }
      }
    }
  }

  //   基础信息
  .base-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
    align-items: center;
    margin: 10px;
    text-align: left;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
  }

  .slide-fade-enter-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-leave-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .card-info {
    position: relative;
  }

  .link-info {
    position: absolute;
    z-index: 999;
    /* 你可以根据需要添加 top, left, right, bottom 属性来精确定位 */
    top: 10px;
  }
  .base-info {
    .el-form {
      background-color: maroon;
      .el-form-item {
        background-color: rgb(0, 102, 128);
      }
    }
  }
  .status-span {
    display: inline-block;
    width: 37px;
    height: 24px;
    padding: 0 10px;
    line-height: 24px;
    &.status-span-L0 {
      background: #dce5f5;
      color: #434343;
    }

    &.status-span-L1 {
      background: #eaf1ff;
      color: #1269ff;
    }

    &.status-span-L2 {
      background: #e6fffb;
      color: #36cfc9;
    }

    &.status-span-L3 {
      background: #fff7e6;
      color: #faad14;
    }

    &.status-span-L4 {
      background: #f9f0ff;
      color: #722ed1;
    }

    &.status-span-L5 {
      background: #fff0f6;
      color: #eb2f96;
    }

    &.status-span-L6 {
      background: #fbeae9;
      color: #f84031;
    }
  }
  .remark-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  :deep .el-form {
    height: 550px;
    overflow-y: scroll;
    .el-form-item__label {
      font-size: 12px !important;
      margin-bottom: -100px;
    }
    .el-form-item__content {
      font-size: 12px !important;
    }
  }
  :deep .splitpanes__splitter {
    margin: 0px 10px !important;
  }
  :deep .tab-container {
    width: 70%;
    margin: auto;
    height: 30px;
    border-radius: 4px;
    .title {
      height: 26px;
      line-height: 26px;
      flex: 1;
      padding: 0;
      text-align: center;
      .content {
        height: 22px;
        font-size: 16px;
        line-height: 22px;
        color: #000000;
      }
      &.select {
        .content {
          color: #1269ff;
        }
      }
    }
  }
  .box-top-img {
    width: 46px;
    height: 46px;
    background: #eaeff5;
    border-radius: 4px;
    padding: 7px;
    text-align: center;
  }
  .tags-top-prompt {
    height: 80px;
    width: 100%;
    padding: 10px 10px 10px 36px;
    background: $--base-color-tag-primary;
    font-size: 12px;
    color: $--base-color-text2;
    position: relative;
    border-radius: 4px;
    .tags-top-prompt-top {
      width: 16px;
      height: 16px;
      position: absolute;
      top: calc(50% - 8px);
      left: 10px;
      font-size: 16px;
    }
    .prompt-text {
      line-height: 20px;
    }
  }
  .tags-statistics {
    width: 100%;
    height: 26px;
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-top: 20px;
    .statistics-title {
      height: 26px;
      line-height: 26px;
      font-size: 14px;
    }
    .statistics-box {
      width: 260px;
      height: 100%;
      text-align: right;

      & > div {
        display: inline-block;
        vertical-align: middle;
      }
      .statistics-num {
        font-size: 12px;
        color: $--base-color-text1;
        margin-right: 20px;
        & > span {
          font-size: 18px;
        }
      }
      .statistics-add {
      }
    }
  }
  .tags-edit-box {
    width: 100%;
    height: 240px;
    background: $--base-color-box-bg;
    margin-top: 20px;
    padding: 10px;
    border-radius: 8px;
    .tags-list-box {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      display: flex;
      .tags-edit-label {
        width: 46px;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        color: $--base-color-text1;
      }
      .tags-select {
        width: calc(100% - 82px);
        :deep .el-cascader {
          width: 100%;
        }
      }
      .tags-delete {
        width: 32px;
        height: 32px;
        cursor: pointer;
        text-align: center;
        line-height: 32px;
        margin-left: 10px;
        svg {
          margin-top: 3px;
          font-size: 24px;
          fill: $--base-color-box-bg;
          :deep path {
            stroke: $--base-color-primary;
          }
        }
      }
    }
  }
  :deep .tag-item-popover {
    .tag-item {
      height: 18px;
      line-height: 18px;
      padding: 0px 4px;
      background: $--base-color-tag-bg3;
      border-radius: 4px;
      color: $--base-color-primary;
      font-size: 12px;
      display: inline-block;
      margin: 0px 10px 10px 0px;
      &:last-child {
        margin-bottom: 0px;
      }
    }
  }
</style>
