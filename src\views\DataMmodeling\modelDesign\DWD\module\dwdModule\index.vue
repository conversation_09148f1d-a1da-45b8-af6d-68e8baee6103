<template>
  <div class="container">
    <div class="container-top">
      <div>
        <el-button type="text" @click="toBack"> {{ '<' }} 返回上一层 </el-button>
      </div>

      <div>
        <!-- 代码预览 -->
        <el-button v-if="activeName == 'second'" @click="open">代码预览</el-button>
        <el-button v-if="activeName != 'second'" bg @click="addStep">下一步</el-button>
        <el-button v-if="activeName == 'second'" @click="delStep">上一步</el-button>
        <el-button
          v-if="activeName == 'second'"
          :class="{ 'disabled-tree': disableData }"
          type="primary"
          @click="fulfill"
        >
          完成</el-button
        >
        <el-button v-if="activeName == 'second'" type="danger" plain @click="toBack"
          >关闭</el-button
        >
      </div>
    </div>

    <div class="demo-tabs" :class="{ 'disabled-tree': disableData }">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="基本配置 DWD" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="auto"
            style="width: 40%; margin: 0 auto"
          >
            <el-form-item label="数仓分层" prop="dwLevel">
              <el-input v-model="form.dwLevel" :disabled="true"></el-input>
            </el-form-item>

            <el-form-item label="所属主题" prop="thmeId">
              <el-input v-model="form.thmeId" :disabled="true"></el-input>
            </el-form-item>
            <!-- <el-row :gutter="20"> -->
            <!-- <el-col :span="12"> -->
            <el-form-item label="明细表名称" prop="code">
              <el-row>
                <!-- <el-col :span="8"> -->
                <!-- <el-tooltip -->
                <!-- class="box-item" -->
                <!-- effect="dark" -->
                <!-- :content="form.thmeInfo" -->
                <!-- placement="top-start" -->
                <!-- > -->
                <!-- <el-input v-model="form.thmeInfo" :disabled="true" /> -->
                <!-- </el-tooltip> -->
                <!-- </el-col> -->
                <el-col>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="form.code"
                    placement="top-start"
                  >
                    <el-input v-model="form.code" style="min-width: 20dvw" />
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-form-item>
            <!-- </el-col> -->
            <!-- <el-col :span="12"> -->
            <el-form-item label="表注释">
              <el-input v-model="form.name"></el-input>
              <!-- <i style="color: darkgray; font-size: 12px;"> -->
              <!-- 注：该处表英文名称将会用于转换物理模型时的表名称 -->
              <!-- </i> -->
            </el-form-item>
            <!-- </el-col> -->
            <!-- </el-row> -->

            <el-form-item :label="`${isXugu ? '模式' : '储存库'}`" prop="dwDatabase">
              <!-- 下拉框-->
              <el-select v-model="form.dwDatabase" placeholder="请选择" clearable>
                <el-option v-for="dict in dwDatabaseList" :key="dict" :label="dict" :value="dict" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="!isXugu" label="表类型" prop="tableType">
              <el-radio-group v-model="form.tableType" class="rgroup" @change="onChangetableType">
                <el-radio label="2">事实事务表</el-radio>
                <el-radio label="3">周期事务表</el-radio>
                <el-radio label="4">累计事务表</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="!isXugu" label="存储类型" prop="isExternal">
              <el-radio-group v-model="form.isExternal" class="rgroup">
                <el-radio label="1">外表</el-radio>
                <el-radio label="2">内表</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="字段管理" name="second">
          <el-row :gutter="20" class="boxA">
            <el-col :span="2">
              <section class="fieldth">从已有导入：</section>
            </el-col>
            <el-col :span="18" class="boxB">
              <el-row :gutter="5" class="boxBT">
                <el-col :span="2">
                  <span> 选择来源表 </span>
                </el-col>
                <el-col :span="6">
                  <el-select
                    v-model="srcModelLevel"
                    placeholder="层级"
                    :disabled="vbtnDisable"
                    clearable
                    @change="getListDatamodelUtil"
                  >
                    <el-option
                      v-for="dict in srcModelCodeList"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-col>

                <el-col :span="6">
                  <el-select
                    v-model="srcModelId"
                    placeholder="表名"
                    :disabled="vbtnDisable"
                    :class="srcModelLevel ? 'inputRed' : ''"
                    @change="handleModelChange"
                  >
                    <el-option
                      v-for="dict in srcModelLevelCollection[srcModelLevel == 'ODS' ? 0 : 1]"
                      :key="dict.id"
                      :label="dict.code"
                      :value="dict.id"
                    />
                  </el-select>
                </el-col>

                <el-col :span="6">
                  <el-input
                    v-model="srcModelAlias"
                    placeholder="表别名"
                    :disabled="vbtnDisable"
                    :class="srcModelLevel ? 'inputRed' : ''"
                  />
                </el-col>
                <el-col :span="3">
                  <el-button link :disabled="false" @click="addSyncChange(1)">
                    <svg
                      t="1699442878434"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="4897"
                      width="20"
                      height="20"
                    >
                      <path
                        d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
                        p-id="4898"
                        fill="#1296db"
                      ></path>
                    </svg>
                  </el-button>
                </el-col>
              </el-row>

              <div class="boxC">
                <el-form
                  v-for="(syncChange, index) in fieldMappings"
                  ref="syncChangeForm"
                  :key="syncChange.key"
                  :model="syncChange"
                >
                  <el-row
                    :gutter="5"
                    :style="{
                      paddingTop: 20 + 'px',
                      paddingLeft: (syncChange.tier === 1 ? 20 : 0) + 'px',
                    }"
                  >
                    <el-col :span="2">
                      <span>JOIN 表</span>
                    </el-col>
                    <el-col :span="6">
                      <el-input
                        v-model="syncChange.joinModelLevel"
                        placeholder="层级"
                        :disabled="true"
                      />
                    </el-col>
                    <el-col :span="6">
                      <el-select
                        v-model="syncChange.joinModelId"
                        placeholder="表名"
                        :disabled="true"
                      >
                        <el-option
                          v-for="dict in srcModelLevelCollection[
                            syncChange.joinModelLevel == 'ODS' ? 0 : 1
                          ]"
                          :key="dict.id"
                          :label="dict.code"
                          :value="dict.id"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">
                      <el-input
                        v-model="syncChange.joinModelAlias"
                        placeholder="表别名"
                        :disabled="true"
                      />
                    </el-col>

                    <el-col :span="4">
                      <el-button v-if="btnDIsableDelet" link @click="deleteSyncChange(index)">
                        <svg
                          t="1699442953096"
                          class="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="6096"
                          width="20"
                          height="20"
                        >
                          <path
                            d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                            fill="#d81e06"
                            p-id="6097"
                          ></path>
                        </svg>
                      </el-button>
                      <el-button link :disabled="false" @click="addSyncChange(2, syncChange)">
                        <svg
                          t="1699442878434"
                          class="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="4897"
                          width="20"
                          height="20"
                        >
                          <path
                            d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
                            p-id="4898"
                            fill="#1296db"
                          ></path>
                        </svg>
                      </el-button>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </el-col>

            <el-col :span="4">
              <el-button type="primary" :disabled="ableClick" @click="impfield">导入字段</el-button>
              <!-- :loading="impfieldLoading" -->
              <el-button @click="reset">重置</el-button>
            </el-col>
          </el-row>

          <!-- <el-divider></el-divider> -->

          <!-- #region -->

          <el-button type="" style="margin-bottom: 10px; margin-top: 10px" @click="addTableData"
            >自定义新增字段
          </el-button>

          <el-table
            v-if="tableData.length >= 1"
            ref="tableRef"
            row-key="date"
            :data="tableData"
            :height="tableHeightForCustom"
          >
            <!-- 选择框 -->
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 序号 -->
            <el-table-column type="index" width="55" />
            <el-table-column prop="code" label="字段名称" width="240" :show-overflow-tooltip="true">
              <template #default="scope">
                <el-input v-model="scope.row.code" placeholder=""></el-input>
              </template>
            </el-table-column>

            <el-table-column prop="name" label="字段注释" width="240" :show-overflow-tooltip="true">
              <template #default="scope">
                <el-input v-model="scope.row.name" placeholder=""></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="来源表" width="100" :show-overflow-tooltip="true">
              <template #default="scope">
                {{ scope.row.srcModelCode }}
              </template>
            </el-table-column>
            <!-- 数据类型 -->
            <el-table-column
              prop="columnType"
              label="数据类型"
              width="240"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <el-select v-model="scope.row.type" placeholder="请选择">
                  <el-option
                    v-for="item in customerIdList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <!-- 数据标准 -->
            <el-table-column
              prop="standardType"
              label="数据标准"
              width="240"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <el-select v-model="scope.row.dataStandardId" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in standardList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <!-- 关联维度 -->
            <el-table-column prop="comment" label="关联维度" width="240">
              <!-- 设置 icon 的 button -->

              <template #default="scope">
                <!-- 下拉框 -->
                <el-cascader
                  v-model="scope.row.dimensionData"
                  :options="correlationList"
                  :props="{ value: 'value', label: 'label', children: 'children' }"
                  clearable
                  :change-on-select="false"
                  :filter-method="filterMethod"
                  @change="handleCascaderChangeDimens(scope.row)"
                />
              </template>
            </el-table-column>

            <!-- 主键 -->
            <el-table-column prop="comment" label="主键" width="80">
              <template #default="scope">
                <el-switch v-model="scope.row.primaryKey"></el-switch>
              </template>
            </el-table-column>
            <!-- <!~~ 分区 ~~> -->
            <!-- <el-table-column prop="comment" label="分区" width="80"> -->
            <!-- <template #default="scope"> -->
            <!-- <el-switch v-model="scope.row.partitionField" :disabled="true"></el-switch> -->
            <!-- </template> -->
            <!-- </el-table-column> -->
            <!-- <!~~ 不为空 ~~> -->
            <!-- <el-table-column prop="comment" label="不为空" width="80"> -->
            <!-- <template #default="scope"> -->
            <!-- <el-switch v-model="scope.row.notNull" :disabled="true"></el-switch> -->
            <!-- </template> -->
            <!-- </el-table-column> -->
            <!-- 描述 -->
            <el-table-column prop="remark" label="描述" width="240">
              <template #default="scope">
                <el-input v-model="scope.row.remark" placeholder=""></el-input>
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column fixed="right" label="操作" width="auto">
              <template #default="scope">
                <el-button circle icon="Delete" @click="delTableData(scope.row)" />
              </template>
            </el-table-column>
          </el-table>

          <br />

          <!-- <el-divider v-if="!tableData.length"></el-divider> -->

          <div v-if="true">
            <span class="TitleName">分区字段管理</span>
            <br />
            <el-button
              type=""
              style="margin-bottom: 20px; margin-top: 20px"
              @click="addCodetableField(0)"
              >自定义分区字段
            </el-button>
            <br />
            <!-- <br /> -->
            <div v-if="props.rowData.dataType !== 'XUGU'">
              <el-table
                ref="tableRef"
                row-key="date"
                :data="codetableField"
                style="width: 100%"
                :height="tableHeightForCodeTab"
                @selection-change="handleSelectionChange"
              >
                <!-- 选择框 -->
                <!-- <el-table-column type="selection" width="55" align="center" /> -->
                <el-table-column type="index" width="55" />
                <el-table-column
                  prop="name"
                  label="字段注释"
                  width="200"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.name"
                      placeholder=""
                      :disabled="scope.$index == 0"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="code"
                  label="字段名称"
                  width="200"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.code"
                      placeholder=""
                      :disabled="scope.$index == 0"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="类型" width="200" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.type"
                      placeholder="请选择"
                      :disabled="scope.$index == 0"
                    >
                      <el-option
                        v-for="item in customerIdList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="取值方式"
                  width="200"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.partitionType"
                      placeholder="请选择"
                      :disabled="scope.$index == 0"
                    >
                      <el-option
                        v-for="item in retrieval_ethod"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        :disabled="item.value == '1'"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="取值来源"
                  width="200"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <!-- 全局变量  -->
                    <el-select
                      v-if="scope.row.partitionType == '0'"
                      v-model="scope.row.partitionCodeId"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in partitionCodeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <el-select
                      v-if="scope.row.partitionType == '1'"
                      v-model="scope.row.srcFiledName"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in cascaderOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="remark"
                  label="描述"
                  width="320"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <el-input v-model="scope.row.remark" placeholder=""></el-input>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="auto">
                  <template #default="scope">
                    <el-button
                      circle
                      icon="Delete"
                      :disabled="scope.$index == 0"
                      @click="delCodetableField(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="props.rowData.dataType === 'XUGU'" class="boxC">
              <table class="table-bordered">
                <thead>
                  <tr>
                    <th class="no-text width-less">序号</th>
                    <th>字段名称</th>
                    <th>字段注释</th>
                    <th>数据类型</th>
                    <th>分区类型</th>
                    <th class="partition-setting">分区值配置</th>
                    <!-- <th>分区层级</th> -->
                    <th>描述</th>
                    <th class="width-less">操作</th>
                  </tr>
                </thead>
              </table>
              <el-form
                v-for="(syncChange, index) in codetableField"
                ref="syncChangeForm"
                :key="syncChange"
                :model="syncChange"
              >
                <div

                  class="tableContent"
                >
                  <div class="width-less">
                    <!-- 序号 -->
                    <span
                      :style="{ paddingLeft: (syncChange.partitionLevel === 1 ? 20 : 8) + 'px' }"
                      >{{ syncChange.partitionLevel === 1 ? '二级' : '一级' }}</span
                    >
                  </div>

                  <div>
                    <!-- 字段名称 -->
                    <el-input v-model="syncChange.code" />
                  </div>

                  <div>
                    <!-- 中文名称 -->
                    <el-input v-model="syncChange.name" />
                  </div>

                  <div>
                    <!-- 数据类型 -->
                    <el-select v-model="syncChange.type" placeholder="请选择">
                      <!-- customerIdList -->
                      <el-option
                        v-for="item in customerIdList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id"
                      />
                    </el-select>
                  </div>
                  <div>
                    <!-- 分区类型 -->
                    <!-- 下拉框 -->
                    <el-select
                      v-model="syncChange.partitionStyle"
                      placeholder="请选择"
                      @change="changePartitionStyle(index)"
                    >
                      <el-option
                        v-for="item in XuguRetrievalRthod"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div class="partition-setting">
                    <!-- 分区值 -->
                    <el-row v-if="syncChange.partitionStyle !== 3" class="over-width">
                      <el-input
                        v-if="syncChange.partitionStyle !== 2"
                        v-model="syncChange.partitionValue"
                        placeholder="请通过英文逗号分割"
                        size="mini"
                        style="width: 100%"
                      />
                      <el-input
                        v-if="syncChange.partitionStyle === 2"
                        v-model="syncChange.partitionValue"
                        placeholder="仅输入数字"
                        size="mini"
                        type="number"
                      />
                    </el-row>
                    <el-row v-if="syncChange.partitionStyle === 3" :gutter="2">
                      <el-col :span="5">
                        <el-input
                          v-model="syncChange.partitionValueGap"
                          placeholder="请通过英文逗号分割"
                          size="mini"
                          type="number"
                        />
                      </el-col>
                      <el-col :span="7">
                        <!-- <el-input v-model="syncChange.partitionValueType" placeholder="类型" /> -->
                        <!-- 改为下拉框 天 月 年 -->
                        <el-select
                          v-model="syncChange.partitionValueType"
                          placeholder="请选择"
                          size="mini"
                        >
                          <el-option label="天" value="DAY" />
                          <el-option label="月" value="MONTH" />
                          <el-option label="年" value="YEAR" />
                        </el-select>
                      </el-col>
                      <el-col :span="12">
                        <!-- <el-input v-model="syncChange.partitionValueTime" placeholder="起始时间" -->
                        <!-- /> -->
                        <!-- 日期选择框 -->
                        <el-date-picker
                          v-model="syncChange.partitionValueTime"
                          type="date"
                          size="mini"
                          placeholder="请选择日期"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </el-col>
                    </el-row>
                  </div>
                  <!-- <div> -->
                  <!-- <!~~ 分区层级 ~~> -->
                  <!-- <el-select v-model="syncChange.partitionLevel"> -->
                  <!-- <el-option -->
                  <!-- v-for="item in XuguRetrievalRthod" -->
                  <!-- :key="item.value" -->
                  <!-- :label="item.label" -->
                  <!-- :value="item.value" -->
                  <!-- /> -->
                  <!-- </el-select> -->
                  <!-- </div> -->
                  <div>
                    <!-- 描述 -->
                    <el-input v-model="syncChange.remark" />
                  </div>
                  <div class="width-less">
                    <el-button circle icon="Delete" @click="delCodeSync(index)" />

                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      content="新增二级分区"
                      placement="top-start"
                    >
                      <el-button
                        v-if="syncChange.partitionLevel != 1"
                        circle
                        :disabled="syncChange.partitionStyle === 3"
                        icon="CirclePlus"
                        @click="addCodetableField(1, index)"
                      />
                    </el-tooltip>
                  </div>
                </div>
              </el-form>
            </div>
          </div>
          <!-- #endregion -->
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

  <el-dialog
    v-model="impfieldVisible"
    title="选择导入字段"
    width="70%"
    append-to-body
    :draggable="true"
  >
    <el-table
      ref="tableRef"
      row-key="date"
      :data="impfieldtableData"
      @selection-change="handleSelectionImpfieldVisible"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" width="55" />

      <el-table-column
        prop="srcModelCode"
        label="来源表名"
        width="200"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="srcModelName"
        label="表注释"
        width="200"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="srcModelAlias"
        label="表别名"
        width="200"
        :show-overflow-tooltip="true"
      />

      <el-table-column prop="name" label="字段注释" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="code" label="字段名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="type" label="数据类型" width="210" :show-overflow-tooltip="true" />
      <el-table-column prop="remark" label="描述" width="140" />
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="impfieldVisible = false">取 消</el-button>
        <el-button type="primary" @click="importField">导 入</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="JOINVisible" title="JOIN条件" width="40%" append-to-body :draggable="true">
    <!-- 源逻辑实体 -->
    <el-form
      ref="JOINVformRef"
      :model="JOINVform"
      :rules="JOINVrules"
      label-position="left"
      label-width="auto"
      class="Eform"
      style="width: 100%"
    >
      <el-form-item label="来源表">
        {{ currentSrcModelCode }}
      </el-form-item>
      <el-form-item label="JOIN表" prop="joinModelAlias">
        <el-row :gutter="20" style="width: 100%">
          <el-col :span="6">
            <el-select
              v-model="JOINVform.joinModelLevel"
              placeholder="层级"
              @change="getListDatamodelUtil(JOINVform.joinModelLevel)"
              style="width: 140px"
            >
              <!-- srcModelCodeList -->
              <el-option
                v-for="dict in srcModelCodeList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="JOINVform.joinModelId"
              placeholder="表名"
              @change="getFieldsByModelUtil(JOINVform.joinModelId, 2)"
              style="width: 140px"
            >
              <el-option
                v-for="dict in srcModelLevelCollection[JOINVform.joinModelLevel == 'ODS' ? 0 : 1]"
                :key="dict.id"
                :label="dict.code"
                :value="dict.id"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input v-model="JOINVform.joinModelAlias" placeholder="表别名" />
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="JOIN方式" prop="joinType">
        <br />
        <br />
        <br />
        <br />

        <!-- 输入框 -->
        <el-input v-show="false" v-model="JOINVform.joinType" placeholder="请输入内容"></el-input>

        <span
          v-for="(item, index) in joinTypes"
          :key="index"
          class="piece"
          :class="{ active: JOINVform.joinType == item.type }"
        >
          <span>{{ item.name }}</span>
          <el-button link @click="linkClick(item.type)">
            <div :class="['circle-white-' + item.class]" />
            <div :class="['circle-blue-' + item.class]" />
          </el-button>
        </span>
      </el-form-item>
      <section class="vbtnDisable"> {{ vbtnDisableText }}</section>

      <el-form-item label="JOIN属性" class="formRed">
        <el-row :gutter="20">
          <template v-for="(syncChange, index) in property" :key="syncChange.key">
            <span class="JOINquality">
              <el-select
                v-model="syncChange.srcFieldId"
                placeholder="请选择"
                @change="srcFieldNameVerify(syncChange)"
                style="width: 140px"
              >
                <el-option
                  v-for="dict in srcFieldNameList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </span>
            <span class="JOINtext">------------------------------------> </span>
            <span class="JOINquality">
              <el-select
                v-model="syncChange.fieldId"
                placeholder="请选择"
                @change="fieldNameVerify(syncChange)"
                style="width: 140px"
              >
                <el-option
                  v-for="dict in fieldNamelist"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </span>

            <div class="item">
              <el-button link @click="deleteProperty(index)">
                <svg
                  t="1699442953096"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="6096"
                  width="20"
                  height="20"
                >
                  <path
                    d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                    fill="#d81e06"
                    p-id="6097"
                  ></path>
                </svg>
              </el-button>
            </div>
          </template>

          <el-button link @click="addProperty">
            <svg
              t="1699442878434"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="4897"
              width="20"
              height="20"
            >
              <path
                d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
                p-id="4898"
                fill="#1296db"
              ></path>
            </svg>
          </el-button>
        </el-row>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="JOINVisibleclose">取 消</el-button>
        <el-button type="primary" @click="JOINVisibleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-drawer v-model="drawer" title="代码预览" :direction="'btt'" size="80%" destroy-on-close>
    <div class="custom-btn">
      <el-radio-group v-model="radioBtn" @change="changeRadio">
        <el-radio-button label="1">DDL</el-radio-button>
        <el-radio-button label="2" :disabled="codeSqlBtn">DML</el-radio-button>
      </el-radio-group>
    </div>

    <Codemirror
      v-model="codeDataSql"
      style="width: 100%; height: 95%; min-height: 100px"
      :disabled-type="true"
    />
  </el-drawer>
</template>

<script setup>
  import {
    addDataModelLogic,
    getDimensionTree,
    getDwDatabaseList,
    getFieldsByModel,
    getFiledType,
    getGlobalVarListByModel,
    getImportFields,
    getImportFieldsTree,
    getListDatamodel,
    getStandardList,
    previewSql,
    updateDataModelLogicData,
  } from '@/api/datamodel';
  import Codemirror from '@/components/Codemirror';
  import { ref } from 'vue';

  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    dataNode: {
      type: Object,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  // 导入字段按钮增加禁止点击限制
  const ableClick = computed(() => {
    return srcModelLevel.value && srcModelId.value && srcModelAlias.value ? false : true;
  });
  const { nodeClick, workspaceId, dataNode, rowData } = toRefs(props);
  const { proxy } = getCurrentInstance();
  const { retrieval_ethod } = proxy.useDict('retrieval_ethod');
  const XuguRetrievalRthod = ref([
    {
      value: 0,
      label: '列表',
    },
    {
      value: 1,
      label: '范围',
    },
    {
      value: 2,
      label: '哈希',
    },
    {
      value: 3,
      label: '自动扩展',
    },
  ]);
  console.log(nodeClick.value);
  console.log(dataNode?.value);
  const emit = defineEmits(); // 编辑器
  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      dwDatabase: [{ required: true, message: '请选择储存库', trigger: 'change' }],
      tableType: [{ required: true, message: '请选择表类型', trigger: 'change' }],
      isExternal: [{ required: true, message: '请选择建模方式', trigger: 'change' }],
      type: [{ required: true, message: '请选择存储类型', trigger: 'change' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    JOINVform: {},
    JOINVrules: {
      joinModelAlias: [{ required: true, message: '请输入别称', trigger: 'blur' }],
      joinType: [{ required: true, message: '请选择关联类型', trigger: 'change' }],
      property: [{ required: true, message: '请选择属性', trigger: 'blur' }],
    },
  });
  const dwDatabaseList = ref([]);
  const getDwDatabaseListUtil = async (data) => {
    const res = await getDwDatabaseList({ catalogId: data });
    dwDatabaseList.value = res.data.map((item) => item);
  };
  const isXugu = ref(props.rowData.dataType === 'XUGU');

  const { form, rules, JOINVform, JOINVrules } = toRefs(data);
  const toBack = async () => {
    if (disableData.value) {
      emit('toBack', true);
      return;
    }
    try {
      await proxy.$confirm('你所做的更改可能未保存', '是否离开当前页面？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      emit('toBack', true);
      // 清空数据
      form.value = {};
      // 清空数据
      tableData.value = [];
      activeName.value = 'first';
    } catch {
      console.log('取消');
    }
  };

  const codetableField = ref(
    props.rowData.dataType === 'XUGU'
      ? []
      : [
          {
            code: 'bizdate',
            name: '业务日期',
            type: 'string',
            partitionType: '0',
            remark: '',
            primaryKey: false,
            isPartition: null,
            notNul: null,
            partitionField: true,
          },
        ],
  );

  const addCodetableField = (typeIndex = 0, index) => {
    // 设置限制只能新增一次 0 级 和 1 级

    // 检查是否已经存在指定级别的数据
    const hasData = codetableField.value.some((item) => item.partitionLevel === typeIndex);

    if (!hasData) {
      // 如果不存在指定级别的数据，则执行添加操作
      const newData = {
        code: '',
        name: '',
        type: '',
        remark: '',
        primaryKey: false,
        partitionField: true,
        notNul: null,
        partitionLevel: typeIndex,
      };

      if (index !== undefined) {
        // 如果传递了索引，则使用 splice 方法插入数据
        codetableField.value.splice(index + 1, 0, newData);
      } else {
        // 如果没有传递索引，则默认使用 push 方法添加到末尾
        codetableField.value.push(newData);
      }
    } else {
      // 如果已经存在指定级别的数据，则不执行添加操作
      console.log(`已经存在 ${typeIndex} 级别的数据，无法再次添加。`);
    }
  };
  const delCodeSync = (index) => {
    if (codetableField.value[index].partitionLevel === 0) {
      codetableField.value = [];
    } else {
      codetableField.value.splice(index, 1);
    }
  };
  const delCodetableField = (row) => {
    codetableField.value.splice(codetableField.value.indexOf(row), 1);
  };

  const standardList = ref();
  const getStandardListUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
    };
    const res = await getStandardList(query);
    if (res.code === 200) {
      standardList.value = res.data;
    }
  };
  // watch(() => codetableField.value, async (newValue) => {
  //     console.log(newValue.length === 1)
  //     if (newValue && newValue.length === 1) {
  //         // await dataPro()
  //         await cascaderOptionsHandle()
  //     }
  // },
  //     {
  //         deep: true
  //     }
  // );

  // #region

  const btnDIsableDelet = computed(() => {
    if (tableData.value.length > 0 || codetableField.value.length > 0) {
      return false;
    } else {
      return true;
    }
  });

  // 重置按钮
  const reset = () => {
    // 确定清空吗
    proxy
      .$confirm('确定要清空吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
      .then(async () => {
        // 清空
        form.value.dataSourceType = '';
        form.value.dataSource = '';
        form.value.database = '';
        form.value.datasheet = '';

        srcModelLevel.value = '';
        srcModelId.value = '';
        srcModelAlias.value = '';
        // 清空
        tableData.value = [];
        codetableField.value =
          props.rowData.dataType === 'XUGU'
            ? []
            : [
                {
                  code: 'bizdate',
                  name: '业务日期',
                  type: 'string',
                  partitionType: '0',
                  remark: '',
                  primaryKey: false,
                  isPartition: null,
                  notNul: null,
                  partitionField: true,
                },
              ];

        dataModelRelationBos.value = [];
        fieldMappings.value = [];
        impfieldtableData.value = [];
        btnDisable.value = false;
      })
      .catch(() => {
        // 取消
      });
  };

  const addTableData = () => {
    // 判断是否有值 如果没有提示用户
    // if (!srcModelId.value || !srcModelAlias.value) return proxy.$modal.msgWarning('请先选择数据');

    tableData.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: false,
      partitionField: false,
      notNull: false,
    });
    // 等待视图更新完成
    nextTick(() => {
      // 获取新增数据所在的 DOM 元素
      const lastIndex = tableData.value.length - 1;
      const newRow = proxy.$refs.tableRef.$el.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
      );

      // 将新增数据所在的 DOM 元素滚动到可视区域
      newRow.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
    });
  };
  const delTableData = (row) => {
    tableData.value.splice(tableData.value.indexOf(row), 1);
  };

  const dataModelRelationBos = ref([]);
  const impfield = async () => {
    impfieldLoading.value = true;

    form.value.dataSourceType = '';
    form.value.dataSource = '';
    form.value.database = '';
    form.value.datasheet = '';

    impfieldLoading.value = false;
    impfieldVisible.value = true;
    await getImportFieldsUtil();
  };

  const getCodeByLevel = () => {
    let code = '';
    srcModelLevelCollection.value.forEach((val) => {
      if (!code) {
        code = val?.find((item) => item.id === srcModelId.value)?.code;
      }
    });
    return code;
  };

  // 处理数据
  const getImportFieldsUtil = async () => {
    const code = getCodeByLevel();

    dataModelRelationBos.value = [
      {
        srcModelLevel: srcModelLevel.value,
        srcModelCode: code,
        srcModelAlias: srcModelAlias.value,
        srcModelId: srcModelId.value,
      },
      ...fieldMappings.value,
    ];
    const res = await getImportFields(dataModelRelationBos.value);
    if (res.code === 200) {
      impfieldtableData.value = res.data;
    } else {
      impfieldtableData.value = [];
      return proxy.$modal.msgError(res.message);
    }
  };

  const selectionS = ref([]);
  const handleSelectionImpfieldVisible = (selection) => {
    selectionS.value = selection;
  };
  const btnDisable = ref(false);
  // 使用计算属性
  const vbtnDisable = computed(() => {
    // 如果 srcModelAlias 有值并且  fieldMappings 里有值
    console.log(srcModelAlias.value);
    console.log(fieldMappings.value.length);
    if ((srcModelAlias.value && fieldMappings.value.length) || btnDisable.value) {
      return true;
    }
  });

  const vbtnDisableText = computed(() => {
    if (JOINVform.value.joinType !== undefined) {
      switch (JOINVform.value.joinType) {
        case 0:
          return '左外连接：返回左表中的所有记录，以及右表中与左表连接字段相匹配的记录。如果左表中的某条记录在右表中没有匹配项，那么结果集中右表的部分将包含 NULL 值。';
        case 1:
          return '右外连接：返回右表中所有的记录，以及左表中与右表连接字段相匹配的记录。如果右表中的某条记录在左表中没有匹配项，那么结果集中左表的部分将包含 NULL 值。';
        case 2:
          return '内连接：仅返回两个表中连接字段相等的记录。也就是说，只有当左表和右表中的记录有匹配时，这些记录才会出现在结果集中。如果没有匹配项，则相关记录不会被返回。';
        case 3:
          return '外连接：是左外连接和右外连接的统称。它允许从一个表中选择所有的记录，而无论是否在另一个表中有匹配的记录。如果没有匹配项，则结果集中的对应字段将为 NULL。';
        default:
          return '';
      }
    } else {
      return '';
    }
  });

  const importField = () => {
    console.log(selectionS.value);
    selectionS.value = selectionS.value.map((item) => {
      delete item.id;
      delete item.modelId;
      delete item.dimensionId;
      delete item.dimensionFieldName;
      delete item.partitionCodeId;
      delete item.partitionCode;
      delete item.partitionType;
      delete item.dataStandardId;
      item.partitionField = false;
      return item;
    });
    impfieldVisible.value = false;
    btnDisable.value = true;
    tableData.value = [...tableData.value, ...selectionS.value];
    tableData.value = tableData.value.map((item) => {
      return {
        code: item.name + item.code,
        srcFieldName: item.code,
        ...item,
      };
    });
  };

  const activeName = ref('first');
  const tableData = ref([]);
  const beforeleave = () => {
    return new Promise((resolve) => {
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.code &&
        form.value.dwDatabase &&
        (isXugu.value || (form.value.tableType && form.value.isExternal))
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const addStep = async () => {
    const refResult = await proxy.$refs.formRef.validate((valid) => valid);
    if (!refResult) return;
    const res = await beforeleave();
    if (!res) {
      return proxy.$modal.msgWarning('请先完善基本配置');
    }
    activeName.value = 'second';
  };
  const delStep = () => {
    console.log(1);
    activeName.value = 'first';
  };

  // 校验数据
  const validate = async () => {
    if (!tableData.value.length) {
      proxy.$modal.msgWarning('请先完善基本配置');
      return false;
    }
    // 检查是否为空 或者字段内容重复 tableData.value [{ code }]
    if (tableData.value.length === 0) {
      proxy.$modal.msgWarning('请添加字段');
      return false;
    } else if (tableData.value.some((item) => item.code === '')) {
      proxy.$modal.msgWarning('字段名称不能为空');
      return false;
    } else if (
      tableData.value.some((item, index) =>
        tableData.value.some((item2, index2) => index !== index2 && item.code === item2.code),
      )
    ) {
      proxy.$modal.msgWarning('字段名称不能重复');
      return false;
    } else if (tableData.value.some((item) => item.type === '')) {
      proxy.$modal.msgWarning('字段类型不能为空');
      return false;
    }

    console.log(srcModelLevel.value);
    console.log(srcModelId.value);
    console.log(srcModelAlias.value);

    if (!srcModelLevel.value && !srcModelId.value && !srcModelAlias.value) {
      // 如果所有字段都为空，则允许提交
      console.log(1);
    } else if (
      srcModelLevel.value === '' ||
      srcModelId.value === '' ||
      srcModelAlias.value === ''
    ) {
      // 如果有任何一个字段为空，则阻止提交
      proxy.$modal.msgWarning('选择来源表字段不能缺省');
      return false;
    } else if (srcModelLevel.value && srcModelId.value && srcModelAlias.value) {
      // 如果所有字段都有值，则判断内部数据
      if (fieldMappings.value.length > 0) {
        // 判断 fieldMappings 里的 joinModelLevel joinModelId joinModelAlias 有任意一个为空
        if (
          fieldMappings.value.some(
            (item) =>
              item.joinModelLevel === '' || item.joinModelId === '' || item.joinModelAlias === '',
          )
        ) {
          // 如果有任何一个字段为空，则阻止提交
          proxy.$modal.msgWarning('选择来源表字段不能缺省');
          return false;
        }
      }
    } else {
      console.log(4);
      return false;
    }
    return true;
  };

  const drawer = ref(false);
  const codeData = ref();
  const codeDataSql = ref();
  const codeSqlBtn = ref();
  const radioBtn = ref();

  const changeRadio = (val) => {
    console.log(val);
    if (val === '1') {
      codeDataSql.value = codeData.value?.ddlSql;
    } else {
      codeDataSql.value = codeData.value?.dmlSql;
    }
  };
  const disposeCodetableField = () => {
    codetableField.value.forEach((item) => {
      if (item.partitionStyle === 3) {
        item.partitionValue = `${item.partitionValueGap},${
          item.partitionValueType
        },${item?.partitionValueTime?.trim()}`;
        // delete item.partitionValueGap;
        // delete item.partitionValueType;
        // delete item.partitionValueTime;
      }
    });
  };
  const open = async () => {
    let fieldBoList = [];

    if (codetableField.value.length) {
      disposeCodetableField();
      codetableField.value.forEach((item) => {
        // 在 cascaderOptions 中查找与 item.srcModelId 相等的 value 或 children 的 value
        const matchingOption = cascaderOptions.value.find(
          (option) =>
            option.value === item.srcModelId ||
            (option.children && option.children.some((child) => child.value === item.srcModelId)),
        );

        console.log(matchingOption);
        // 如果找到了匹配的 option，将其 srcFiledName、srcModelAlias、srcModelLevel 和 srcModelCode 赋值到 item
        if (matchingOption && matchingOption.srcModelCode) {
          item.srcFieldName = matchingOption.srcFieldName
            ? matchingOption.srcFieldName
            : matchingOption.srcModelCode;
          item.srcModelAlias = matchingOption.srcModelAlias;
          item.srcModelLevel = matchingOption.srcModelLevel;
          item.srcModelCode = matchingOption.srcModelCode;
        }

        // if (item.partitionStyle === 3) {
        //   item.partitionValue = `${item.partitionValueGap},${item.partitionValueTime},${item.partitionValueType}`;
        // }
      });

      console.log('****************', codetableField.value);
      fieldBoList = [...codetableField.value, ...tableData.value];
    } else {
      fieldBoList = tableData.value;
    }
    // codetableField.value 有值吗 有值就和 tableData.value 合并 一个新的 没值使用 tableData.value
    const data = {
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      dwLevel: form.value.dwLevel,
      dwDatabase: form.value.dwDatabase,
      databaseName: form.value.database,
      datasourceId: form.value.dataSource,
      isExternal: form.value.isExternal === 1,
      tableName: form.value.datasheet,
      fieldBoList,
      dataModelRelationBos: dataModelRelationBos.value,
      workspaceId: workspaceId.value,
    };
    await previewSqlUtil(data);
  };

  const previewSqlUtil = async (data) => {
    const res = await previewSql(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    drawer.value = true;

    radioBtn.value = '1';
    codeData.value = res.data;
    codeDataSql.value = codeData.value?.ddlSql;

    if (codeData.value.dmlSql) {
      codeSqlBtn.value = false;
    } else {
      codeSqlBtn.value = true;
    }
  };

  const fulfill = async () => {
    const isValid = await validate();
    if (!isValid) return;

    let fieldBoList = [];

    if (codetableField.value.length) {
      disposeCodetableField();
      codetableField.value.forEach((item) => {
        // 在 cascaderOptions 中查找与 item.srcModelId 相等的 value 或 children 的 value
        const matchingOption = cascaderOptions.value.find(
          (option) =>
            option.value === item.srcModelId ||
            (option.children && option.children.some((child) => child.value === item.srcModelId)),
        );

        console.log(matchingOption);
        // 如果找到了匹配的 option，将其 srcFiledName、srcModelAlias、srcModelLevel 和 srcModelCode 赋值到 item
        if (matchingOption && matchingOption.srcModelCode) {
          item.srcFieldName = matchingOption.srcFieldName
            ? matchingOption.srcFieldName
            : matchingOption.srcModelCode;
          item.srcModelAlias = matchingOption.srcModelAlias;
          item.srcModelLevel = matchingOption.srcModelLevel;
          item.srcModelCode = matchingOption.srcModelCode;
        }
      });

      console.log('****************', codetableField.value);
      fieldBoList = [...codetableField.value, ...tableData.value];
    } else {
      fieldBoList = tableData.value;
    }

    // codetableField.value 有值吗 有值就和 tableData.value 合并 一个新的 没值使用 tableData.value

    const data = {
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      dwLevel: form.value.dwLevel,

      // modMethods: form.value.modMethods, //后端不需要 回显需要自己处理

      dwDatabase: form.value.dwDatabase,

      databaseName: form.value.database,
      datasourceId: form.value.dataSource,
      tableType: form.value.tableType,
      isExternal: form.value.isExternal === 1,
      tableName: form.value.datasheet,
      fieldBoList,
      dataModelRelationBos: dataModelRelationBos.value,
    };
    if (rowData.value.id) {
      data.id = rowData.value.id;
      await updateDataModelLogicUtil(data, 'ODS');
    } else {
      await addDataModelLogicUtil(data, 'DWI');
    }
  };

  const addDataModelLogicUtil = async (e) => {
    console.log(dataNode?.value);
    e.thmeId = dataNode?.value?.id;
    e.workspaceId = workspaceId.value;
    const res = await addDataModelLogic(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      rowData.value.id = res.data;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const updateDataModelLogicUtil = async (e) => {
    e.thmeId = dataNode.value?.id;
    e.workspaceId = workspaceId.value;
    const res = await updateDataModelLogicData(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const customerIdList = ref();
  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: props.rowData.dataType.toLowerCase(),
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  // 导入字段 button log
  const impfieldLoading = ref(false);
  const impfieldVisible = ref(false);
  const impfieldtableData = ref();
  const srcModelCode = ref();
  const srcModelId = ref();

  const srcModelLevel = ref();
  const srcModelAlias = ref();

  const fieldMappings = ref([]);
  const srcModelLevelList = ref();
  const srcModelLevelCollection = ref([]);
  const srcModelCodeList = ref([
    {
      label: 'ODS',
      value: 'ODS',
    },
    {
      label: 'DWI',
      value: 'DWI',
    },
  ]);

  const addType = ref();
  const addRow = ref();
  const addSyncChange = async (type, row) => {
    console.log(type, row);

    addType.value = type;
    addRow.value = row;

    console.log(addRow.value);

    JOINVform.value = {};
    property.value = [];
    // 判断是否有数据 任意一个无数据 就不显示
    if (!srcModelLevel.value || !srcModelId.value || !srcModelAlias.value) {
      return;
    } else {
      if (row) {
        currentSrcModelCode.value = row.joinModelCode;
        await getFieldsByModelUtil(row.joinModelId, 1);
      } else {
        currentSrcModelCode.value = srcModelCode.value;
        await getFieldsByModelUtil(srcModelId.value, 1);
      }
      await openJOINVisible();
    }
    console.log('---***-**', fieldMappings.value);
  };

  const deleteSyncChange = (index) => {
    fieldMappings.value.splice(index, 1);
  };

  const fieldNameVerify = (row) => {
    const filedName = fieldNamelist.value.find((item) => item.value === row.fieldId).code;
    row.fieldName = filedName;
  };

  const srcFieldNameVerify = (row) => {
    const srcFieldName = srcFieldNameList?.value.find((item) => item.value === row.srcFieldId).code;
    row.srcFieldName = srcFieldName;
  };

  const joinTypes = ref([
    { type: 0, name: '左连接', class: 'left' },
    { type: 1, name: '右连接', class: 'right' },
    { type: 2, name: '内连接', class: 'center' },
    { type: 3, name: '外连接', class: 'outer' },
  ]);
  const JOINVisible = ref(false);

  const openJOINVisible = async () => {
    JOINVisible.value = true;
    // await getFieldsByModelUtil(srcModelId.value, 1);
  };
  const propertyRules = () => {
    if (!property.value.length) return false;
    let isValid = true; // Initialize isValid flag

    property.value.forEach((item) => {
      if (!item.fieldId) {
        isValid = false; // Set isValid to false if condition fails
      }
      if (!item.srcFieldId) {
        isValid = false; // Set isValid to false if condition fails
      }
    });
    if (!isValid) proxy.$modal.msgError('请选择字段');

    return isValid; // Return isValid flag
  };
  const currentSrcModelCode = ref();
  const JOINVisibleSubmit = async () => {
    // 校验 JOINVform
    const res = await proxy.$refs.JOINVformRef.validate((valid) => valid);
    if (!res) return;
    const re = await propertyRules();
    if (!re) return;
    //  判断是否有数据 任意一个无数据 就不显示

    // 使用 id 在 srcModelLevelList 找到 code
    let srcModel;
    let JOINModel;
    srcModelLevelCollection.value.forEach((val) => {
      console.log('123444444', val);
      if (!srcModel) {
        srcModel = val.find((item) => item.id === srcModelId.value);
      }
      if (!JOINModel) {
        JOINModel = val.find((item) => item.id === JOINVform.value.joinModelId);
      }
    });

    // 如果找到了匹配的项，则获取其 code 值，否则设为空字符串
    const srcModelCode = srcModel ? srcModel.code : '';
    const JOINModelCode = JOINModel ? JOINModel.code : '';

    // 检查是否重复
    let isRepeat = fieldMappings.value.some(
      (item) =>
        item.srcModelAlias === JOINVform.value.joinModelAlias &&
        item.joinModelAlias === JOINVform.value.joinModelAlias,
    );
    isRepeat = isRepeat || srcModelAlias.value === JOINVform.value.joinModelAlias;
    if (isRepeat) {
      return proxy.$modal.msgWarning('表别名重复');
    }

    // 如果没有重复，那么添加新的映射
    if (addType.value === 1) {
      fieldMappings.value.push({
        joinType: JOINVform.value.joinType,
        srcModelLevel: srcModelLevel.value,
        srcModelCode,
        srcModelAlias: srcModelAlias.value,
        srcModelId: srcModelId.value,

        joinModelLevel: JOINVform.value.joinModelLevel,
        joinModelCode: JOINModelCode,
        joinModelAlias: JOINVform.value.joinModelAlias,
        joinModelId: JOINVform.value.joinModelId,
        joinRelationBoList: property.value,
        tier: 0,
      });
      vbtnDisable.value = true;
    } else if (addType.value === 2) {
      fieldMappings.value.push({
        joinType: JOINVform.value.joinType,

        srcModelLevel: addRow.value.joinModelLevel,
        srcModelCode: addRow.value.joinModelCode,
        srcModelAlias: addRow.value.joinModelAlias,
        srcModelId: addRow.value.joinModelId,

        joinModelLevel: JOINVform.value.joinModelLevel,
        joinModelCode: JOINModelCode,
        joinModelAlias: JOINVform.value.joinModelAlias,
        joinModelId: JOINVform.value.joinModelId,
        joinRelationBoList: property.value,
        tier: 1,
      });
    }

    JOINVisible.value = false;
  };

  const JOINVisibleclose = async () => {
    JOINVisible.value = false;
    // 清空数据
    JOINVform.value = {};
    property.value = [];
  };

  const property = ref([]);

  const deleteProperty = (index) => {
    property.value.splice(index, 1);
  };

  const addProperty = () => {
    property.value.push({});
  };

  const linkClick = (e) => {
    JOINVform.value.joinType = e;
  };

  const getListDatamodelUtil = async (data) => {
    console.log(data);
    const query = {
      workspaceId: workspaceId.value,
      thmeId: nodeClick.value.key,
      level: data,
    };
    if (!data) {
      srcModelId.value = '';
      srcModelAlias.value = '';
      srcModelLevelList.value = [];

      tableData.value = [];
      codetableField.value =
        props.rowData.dataType === 'XUGU'
          ? []
          : [
              {
                code: 'bizdate',
                name: '业务日期',
                type: 'string',
                partitionType: '0',
                remark: '',
                primaryKey: false,
                isPartition: null,
                notNul: null,
                partitionField: true,
              },
            ];

      fieldMappings.value = [];
      btnDisable.value = false;
      return proxy.$message.warning('请选择模型层级');
    }
    const res = await getListDatamodel(query);
    srcModelLevelList.value = res.data.map((item) => {
      return {
        ...item,
      };
    });
    if (data === 'ODS') {
      srcModelLevelCollection.value[0] = srcModelLevelList.value;
    } else if (data === 'DWI') {
      srcModelLevelCollection.value[1] = srcModelLevelList.value;
    } else {
      srcModelLevelCollection.value[3] = srcModelLevelList.value;
    }
  };

  const fieldNamelist = ref();
  const srcFieldNameList = ref();
  const getFieldsByModelUtil = async (data, type) => {
    const res = await getFieldsByModel({ modelId: data });
    if (type === 1) {
      srcFieldNameList.value = res.data.map((item) => ({
        value: item.id,
        label: item.code,
        ...item,
      }));
    } else if (type === 2) {
      fieldNamelist.value = res.data.map((item) => ({
        value: item.id,
        label: item.code,
        ...item,
      }));
    } else if (type === 3) {
      return res.data.map((item) => item);
    }
  };
  const handleModelChange = async (value) => {
    // 在选项变化时更新 srcModelId 和 srcModelCode
    if (srcModelLevelList.value) {
      const selectedModel = srcModelLevelList.value.find((dict) => dict.id === value);
      if (selectedModel) {
        srcModelCode.value = selectedModel.code;
      }
    }
  };
  const onChangetableType = () => {
    form.value.dataSourceType = '';
    form.value.dataSource = '';
    form.value.database = '';
    form.value.datasheet = '';

    srcModelLevel.value = '';
    srcModelId.value = '';
    srcModelAlias.value = '';
    // 清空
    tableData.value = [];
    codetableField.value =
      props.rowData.dataType === 'XUGU'
        ? []
        : [
            {
              code: 'bizdate',
              name: '业务日期',
              type: 'string',
              partitionType: '0',
              remark: '',
              primaryKey: false,
              isPartition: null,
              notNul: null,
              partitionField: true,
            },
          ];

    fieldMappings.value = [];
    btnDisable.value = false;
  };

  const handleCascaderChangeDimens = (row) => {
    row.dimensionModelId = row.dimensionData[1];
    row.dimensionFieldName = row.dimensionData[2];
  };

  const handleCascaderChange = (row) => {
    console.log(row);

    row.srcModelId = row.srcModelInfo[0].srcModelId;
    row.srcModelAlias = row.srcModelInfo[0].srcModelAlias;
    row.srcFieldName = row.srcModelInfo[1];
  };
  const partitionCodeList = ref([]);
  const changePartitionStyle = (val) => {
    // 每次变更数据 清空 对应行的 partitionValue
    codetableField.value[val].partitionValue = null;
    codetableField.value[val].partitionValueGap = null;
    codetableField.value[val].partitionValueType = null;
    codetableField.value[val].partitionValueTime = null;
  };
  const getGlobalVarListByModelUtil = async () => {
    const res = await getGlobalVarListByModel({
      workspaceId: workspaceId.value,
    });
    // console.log(res)
    partitionCodeList.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
        ...item,
      };
    });
  };

  const cascaderOptions = ref([]);

  const correlationList = ref();
  const getDimensionTreeUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      catalogId: nodeClick.value.key,
    };
    const ref = await getDimensionTree(query);
    if (ref.code === 200) {
      correlationList.value = ref.data;
    }
  };
  const filterMethod = (node) => {
    console.log(node);
    // 只有当 node 在第三层时，才返回 true
    return node.level === 3;
  };

  const getImportFieldsTreeUtil = async (data) => {
    const res = await getImportFieldsTree(data);
    cascaderOptions.value = res.data;
  };

  const disableData = ref();

  const notXuguTodu = () => {
    form.value.tableType = rowData.value.tableType;
    form.value.isExternal = rowData.value.isExternal ? '1' : '2';
  };
  onMounted(async () => {
    form.value.dwLevel = 'DWD';
    form.value.thmeId = nodeClick.value.label;
    form.value.thmeInfo =
      form.value.dwLevel +
      '_' +
      nodeClick.value.parent.parent.data.code +
      '_' +
      nodeClick.value.parent.data.code +
      '_' +
      nodeClick.value.data.code;
    form.value.code = form.value.thmeInfo;

    if (rowData.value && rowData.value.id) {
      codetableField.value = [];
      form.value.name = rowData.value.name;

      //   const codeParts = rowData.value.code.split('_');
      //   form.value.thmeInfo = codeParts.slice(0, 1).join('_');
      form.value.code = rowData.value.code;

      form.value.dwDatabase = rowData.value.dwDatabase;
      console.log(rowData.value.tableType);
      form.value.remark = rowData.value.remark;

      // 不是虚谷数仓情况需要对建模方式、存储类型及其衍生操作进行更改
      if (!isXugu.value) {
        notXuguTodu();
      }

      if (rowData.value.relationVoList && rowData.value.relationVoList.length > 0) {
        srcModelLevel.value = rowData.value.relationVoList[0]?.srcModelLevel;
        rowData.value.relationVoList.forEach((item, index) => {
          if (index === 0) {
            getListDatamodelUtil(item.srcModelLevel);
          } else {
            getListDatamodelUtil(item.joinModelLevel);
          }
        });
        srcModelId.value = rowData.value.relationVoList[0]?.srcModelId;
        srcModelAlias.value = rowData.value.relationVoList[0]?.srcModelAlias;

        fieldMappings.value = rowData.value.relationVoList
          .filter((item, index) => index > 0)
          .map((item) => {
            const newItem = { ...item };
            newItem.joinRelationBoList = newItem.joinRelationVoList;
            delete newItem.joinRelationVoList;
            codeParts;
            return newItem;
          });
        rowData.value.relationVoList.forEach((item) => {
          item.joinRelationBoList = item.joinRelationVoList;
          delete item.joinRelationVoList;
        });
        console.log(rowData.value.relationVoList);

        await getImportFieldsTreeUtil(rowData.value.relationVoList);
        impfieldtableData.value = rowData.value.relationVoList;
        dataModelRelationBos.value = rowData.value.relationVoList;
      }

      rowData.value.modelFieldVoList.forEach((item) => {
        if (item.partitionField === true) {
          codetableField.value.push(item);
        } else {
          // dimensionData
          tableData.value.push(item);
        }
      });

      codetableField.value.forEach((item) => {
        item.partitionType = item.partitionType?.toString();
        item.srcModelInfo = [
          {
            srcModelAlias: item.srcModelAlias,
            srcModelId: item.srcModelId,
          },
          item.srcFieldName,
        ];
        if (isXugu.value) {
          item.partitionValueGap = item.partitionValue?.split(',')[0];
          item.partitionValueType = item.partitionValue?.split(',')[1];
          item.partitionValueTime = item.partitionValue?.split(',')[2];
        }
      });
      console.log(codetableField.value);

      if (rowData.value.status === 2) {
        disableData.value = false;
      } else {
        disableData.value = true;
      }
    }
    nextTick(() => {
      getDwDatabaseListUtil(nodeClick.value.key);
    });
    await getFiledTypeUtil();
    await getGlobalVarListByModelUtil();
    await getDimensionTreeUtil();
    await getStandardListUtil();
  });

  watch(
    () => nodeClick.value,
    (val) => {
      console.log(val);
      form.value.dwLevel = 'DWD';
      form.value.thmeId = val.label;
      nextTick(() => {
        getDwDatabaseListUtil(val.key);
      });
    },
    {
      deep: true,
    },
  );
  const tableHeightForCustom = computed(() => {
    const rowsCount = tableData.value.length;
    if (rowsCount === 0) {
      return 100; // Minimum height
    } else if (rowsCount <= 20) {
      return rowsCount * 39 + 40; // Estimate height based on rows
    } else {
      return 820; // Max height
    }
  });

  const tableHeightForCodeTab = computed(() => {
    const rowsCount = codetableField.value.length;
    if (rowsCount === 0) {
      return 100; // Minimum height
    } else if (rowsCount <= 20) {
      return rowsCount * 39 + 40; // Estimate height based on rows
    } else {
      return 820; // Max height
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 10px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    // margin: 5px;
    height: 100%;
    overflow: auto;
    .container-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .demo-tabs {
    padding: 5px 25px 0;
  }



  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .boxC {
    // max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 14px;
  }

  .boxB {
    // margin: 5px 0;
    // background-color: #f5f7fa;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 5px;

    .boxBT {
      // border-bottom: 1px solid #ebeef5;
      font-size: 14px;
      font-weight: 700;
    }
  }

  .boxA {
    padding: 10px;
    border-radius: 5px;
    // outline: 1px solid #e6e6e652;
  }

  // $circle-size: 25px;
  // $circle-position-top: 5px;
  // $circle-position-left: 25px;
  // $circle-position-left-overlap: 40px;
  // $circle-background: #ffffff48;
  // $circle-border: 1px solid #fff;

  // .piece {
  //     width: 100px;
  //     height: 33px;
  //     background: #1b96fa6c;
  //     position: relative;
  //     margin: 20px auto;

  //     span {
  //         position: absolute;
  //         left: -50%;
  //         bottom: -3%;
  //         font-size: 12px;
  //     }

  //     .circle-white-left,
  //     .circle-white-right,
  //     .circle-white-center,
  //     .circle-white-outer {
  //         width: $circle-size;
  //         height: $circle-size;
  //         border-radius: 50%;
  //         position: absolute;
  //         top: $circle-position-top;
  //         left: $circle-position-left;
  //     }

  //     .circle-blue-left,
  //     .circle-blue-right,
  //     .circle-blue-center,
  //     .circle-blue-outer {
  //         width: $circle-size;
  //         height: $circle-size;
  //         border-radius: 50%;
  //         border: $circle-border;
  //         position: absolute;
  //         top: $circle-position-top;
  //         left: $circle-position-left-overlap;
  //         z-index: 2;
  //     }

  //     .circle-white-left,
  //     .circle-blue-right,
  //     .circle-blue-center {
  //         background: $circle-background;
  //     }

  //     .circle-white-right,
  //     .circle-white-outer {
  //         border: $circle-border;
  //     }
  // }

  .piece {
    // 四方块蓝色 内部是两个并排的的圆圈 重叠了一小部分
    width: 100px;
    height: 33px;
    background: #1b96fa6c;
    position: relative;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;

    span {
      position: absolute;
      left: -50%;
      bottom: -3%;
      font-size: 12px;
    }

    .circle-white-left {
      width: 25px;
      height: 25px;
      background: #ffffff48;
      border-radius: 50%;
      position: absolute;
      top: 5px;
      left: 25px;
    }

    .circle-blue-left {
      width: 25px;
      height: 25px;
      // background: #fff;
      border-radius: 50%;
      border: 1px solid #fff;
      position: absolute;
      top: 5px;
      left: 40px;
      z-index: 2;
    }

    .circle-white-right {
      width: 25px;
      height: 25px;
      // background: #fff;
      border-radius: 50%;
      border: 1px solid #fff;
      position: absolute;
      top: 5px;
      left: 25px;
    }

    .circle-blue-right {
      width: 25px;
      height: 25px;
      background: #ffffff48;
      border-radius: 50%;
      position: absolute;
      top: 5px;
      left: 40px;
      z-index: 2;
    }

    // 内
    .circle-white-center {
      width: 25px;
      height: 25px;
      background: #ffffff48;
      border-radius: 50%;
      position: absolute;
      top: 5px;
      left: 25px;
      z-index: 1;
    }

    .circle-blue-center {
      width: 25px;
      height: 25px;
      background: #ffffff48;
      border-radius: 50%;
      position: absolute;
      top: 5px;
      left: 40px;
      z-index: 2;
    }

    .circle-white-outer {
      width: 25px;
      height: 25px;
      // background: #fff;
      border-radius: 50%;
      border: 1px solid #fff;
      position: absolute;
      top: 5px;
      left: 25px;
    }

    .circle-blue-outer {
      width: 25px;
      height: 25px;
      // background: #fff;
      border-radius: 50%;
      border: 1px solid #fff;
      position: absolute;
      top: 5px;
      left: 40px;
      z-index: 2;
    }
  }

  .JOINquality {
    // width: 220px;
    // height: 25px;
    // background: #E0EBFE;
    // border: 1px solid #fff;
    // margin: 0 auto;
    // text-align: center;
    // line-height: 25px;
    border-radius: 3px;
  }

  .JOINtext {
    text-align: center;
    line-height: 25px;
  }

  .active {
    background: #1890ff;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
    border-top: 1px solid #e6e6e6;
    padding-top: 20px;
    margin-bottom: 0;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .custom-btn {
    margin-bottom: 10px;
    margin-left: 10px;
    margin-top: -10px;
  }

  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 0;
      left: -16px;
      background-color: red;
      color: red;
    }
  }

  .vbtnDisable {
    padding: 20px;
    background: #f6faff;
    min-height: 50px;
    max-height: 150px;
    margin: 20px;
    margin-top: -5%;
    font-family: jc500;
    font-weight: normal;
  }

  .Eform {
    position: relative;
  }

  .formRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 0;
      left: -15px;
      margin-top: 10px;
      background-color: red;
      color: red;
    }
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f0f5ff;
      width: 100%;
    }

    tr {
      color: #606266;
      height: 39px;
      padding: 5px 0;
      display: flex;
      justify-content: start;
      flex-wrap: nowrap;

      text-align: center;
      line-height: 40px;
      //   margin: 0 15px;
    }

    th {
      flex: 2;
      min-width: 100px;
      max-width: 265px;
      color: black;
      font-size: 0.8125rem;
      text-align: left;
      margin: 0 5px;
      display: inline-block;
      vertical-align: top;
      line-height: 33px;
      &.partition-setting {
        width: 400px;
        max-width: 400px;
        flex: 6;
      }
      &.width-less {
        flex: 1;
      }
      &.no-text {
        opacity: 0;
      }
    }
  }
  .tableContent {
    display: flex;
    justify-content: start;
    flex-wrap: nowrap;
    padding: 3px 0;
    border-bottom: 1px solid #ebeef5;
    > div {
      min-width: 100px;
      max-width: 265px;
      text-align: left;
      background-color: #ffffff;
      flex: 2;
      //   padding: 0 20px;
      margin: 0 5px;
      &.partition-setting {
        width: 400px;
        max-width: 400px;
        display: flex;
        justify-content: start;
        flex-wrap: nowrap;
        flex: 6;
        .over-width {
          width: 100%;
        }
      }
      &.width-less {
        flex: 1;
      }
      //   &.partition-setting-select {
      //     width: 80px;
      //     max-width: 80px;
      //     min-width: 80px;
      //   }
      > span {
        width: 100%;
        line-height: 32px;
        display: inline-block;
        padding-left: 5px;
        text-align: left;
      }
    }
    // > div:nth-child(1) {
    //   margin: 0 0px;
    //   max-width: 10px;
    //   margin: 0 5px;
    // }
    ::v-deep .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100% !important;
    }
  }
  :deep .el-tabs__nav-scroll {
    opacity: 0;
  }
  :deep .el-table--large .el-table__cell {
    padding: 3px 0;
  }
  :deep .el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    height: 39px !important;
  }
</style>
