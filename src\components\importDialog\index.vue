<template>
  <div>
    <el-dialog
      v-model="dialogView"
      title="导入目录"
      width="650px"
      :draggable="true"
      @close="closeDialog"
    >
      <!-- <div style="width: 320px; margin: 20px auto">
        <el-radio-group v-model="tabName" @change="changeTab">
          <el-radio-button label="configuration">导入配置</el-radio-button>
          <el-radio-button label="record">导入记录</el-radio-button>
        </el-radio-group>
      </div> -->

      <div style="margin: 0 auto" v-if="tabName == 'configuration'">
        <div class="tip-container"
          >文件格式需按模板填写，点击下载：
          <span class="textBtn" @click="downloadModal">目录导入模板</span>
        </div>
        <el-form ref="importRef" :model="form" :rules="rule">
          <el-form-item label="上传文件" prop="file">
            <fileUpload
              v-model="form.file"
              returnType="all"
              :limit="1"
              :workspace-id="workspaceId"
              :file-type="['xlsx', 'xls']"
              :file-size="null"
            />
          </el-form-item>
        </el-form>
      </div>
      <!-- <div style="margin: 0 auto" v-else>
        <el-table :data="recordList">
          <el-table-column align="center" label="文件名称" prop="fileName"></el-table-column>
          <el-table-column align="center" label="导入结果" prop="status"></el-table-column>
          <el-table-column align="center" label="导入时间" prop="time"></el-table-column>
          <el-table-column align="center" label="操作人" prop="operator"></el-table-column>
          <el-table-column align="center" label="错误信息" prop="errorMsg"></el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </div> -->

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitCommit">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getUserProfile } from '@/api/system/user';
  import { getCurrentInstance } from 'vue';
  import { importCatalog } from '@/api/system/exportAndImport';
  import { getToken } from '@/utils/auth';

  const baseUrl = import.meta.env.VITE_APP_BASE_API;

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  let userInfo = reactive({});
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    treeData: {
      type: Array,
      default: () => [],
    },
    // 是否需要区分模块
    moduleName: {
      type: String,
      default: '',
    },
    dataType: {
      type: Number,
      default: 1,
    },
  });
  const emit = defineEmits(['close', 'submit']);

  const typeContant = ref({
    Theme: '主题',
    CodeTable: '码表',
    DataStandard: '数据标准',
    GlobalVariable: '全局变量',
    Flow: '流程',
    AssetCategory: '类目',
    SensitiveData: '敏感数据',
    ApiGroup_BACKEND: 'API分组（工作台）',
    ApiGroup_HUB: 'API分组（集市列表）',
  });

  const closeDialog = () => {
    if (tabName.value == 'configuration') {
      reset();
    }
    dialogView.value = false;
    emit('close');
  };
  const reset = () => {
    form.value.file = null;
    proxy.resetForm('importRef');
  };
  const submitCommit = async () => {
    if (!form.value.file || form.value.file.length === 0) {
      proxy.$message.error('请选择文件');
      return;
    }
    const req = {
      fileUrl: form.value.file[0].url,
      workspaceId: workspaceId.value,
      catalogType: props.moduleName,
      fileName: form.value.file[0].name,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      req.tenantId = tenantId.value;
    }
    const res = await importCatalog(req);
    if (res.code === 200) {
      proxy.$message.success('导入成功');
      emit('submit');
      emit('close');
    } else {
      proxy.$message.error(res.msg || '导入失败');
    }
    dialogView.value = false;
  };
  const rule = reactive({
    file: [{ required: true, message: '请选择文件', trigger: 'change' }],
  });
  const form = ref({});
  const dialogView = ref(true);
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const tabName = ref('configuration');
  const changeTab = (data) => {
    if (!data) return;
    tabName.value = data;
  };

  const recordList = ref([]);
  const getList = async () => {
    // const res = await getData(queryParams);
    // recordList.value = res.rows
    // total.value = res.total;
  };

  //下载导入模板
  const downloadModal = async () => {
    try {
      // 加上 headers
      // window.open(baseUrl + "/codetable/export?id=" + row.id)',
      const headers = { Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value };
      console.log(headers);
      const response = await fetch(
        baseUrl + '/resource/catalogIO/templateFile?catalogType=' + props.moduleName,
        {
          headers,
        },
      );

      const blob = await response.blob();
      const filename = '导入模板.xlsx';

      saveAs(blob, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
      // Handle error
    }
  };

  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
  };

  init();
  watch(tabName, (val) => {
    // 是否需要清空文件？
    if (val == 'record') {
      queryParams.pageNum = 1;
      getList();
    }
  });
</script>

<style lang="scss" scoped>
  .tip-container {
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #8c8c8c;
    .textBtn {
      font-size: 14px;
      color: #1269ff;
      cursor: pointer;
    }
  }
</style>
