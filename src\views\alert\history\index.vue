<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['alert:HISTORY:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['alert:HISTORY:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          >编辑</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['alert:HISTORY:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['alert:HISTORY:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="HISTORYList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column v-if="true" label="${comment}" align="center" prop="ID" />
      <el-table-column v-if="true" label="租户ID" align="center" prop="tenantId" />
      <el-table-column v-if="true" label="用户ID" align="center" prop="userId" />
      <el-table-column v-if="true" label="关联业务ID" align="center" prop="bizId" />
      <el-table-column v-if="true" label="告警标题" align="center" prop="TITLE" />
      <el-table-column v-if="true" label="告警内容" align="center" prop="CONTENT" />
      <el-table-column v-if="true" label="告警时间" align="center" prop="createTime" />
      <el-table-column v-if="true" label="1邮件2短信" align="center" prop="alertType" />
      <el-table-column v-if="true" label="${comment}" align="center" prop="workspaceId" />
      <el-table-column v-if="true" label="执行状态" align="center" prop="executionStatus" />
      <el-table-column v-if="true" label="业务名称" align="center" prop="bizName" />
      <el-table-column v-if="true" label="业务实例id" align="center" prop="bizInstanceId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            v-hasPermi="['alert:HISTORY:edit']"
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasPermi="['alert:HISTORY:remove']"
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改告警历史对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="HISTORYRef" :model="form" :rules="rules" label-width="80px"> </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HISTORY">
  import { listHISTORY, delHISTORY, addHISTORY, updateHISTORY } from '@/api/alert/history';

  const { proxy } = getCurrentInstance();

  const HISTORYList = ref([]);
  const open = ref(false);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    rules: {
      ID: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      tenantId: [{ required: true, message: '租户ID不能为空', trigger: 'blur' }],
      userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
      bizId: [{ required: true, message: '关联业务ID不能为空', trigger: 'blur' }],
      TITLE: [{ required: true, message: '告警标题不能为空', trigger: 'blur' }],
      CONTENT: [{ required: true, message: '告警内容不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '告警时间不能为空', trigger: 'blur' }],
      alertType: [{ required: true, message: '1邮件2短信不能为空', trigger: 'change' }],
      workspaceId: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      executionStatus: [{ required: true, message: '执行状态不能为空', trigger: 'change' }],
      bizName: [{ required: true, message: '业务名称不能为空', trigger: 'blur' }],
      bizInstanceId: [{ required: true, message: '业务实例id不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询告警历史列表 */
  function getList() {
    loading.value = true;
    listHISTORY(queryParams.value).then((response) => {
      HISTORYList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      ID: null,
      tenantId: null,
      userId: null,
      bizId: null,
      TITLE: null,
      CONTENT: null,
      createTime: null,
      alertType: null,
      workspaceId: null,
      executionStatus: null,
      bizName: null,
      bizInstanceId: null,
    };
    proxy.resetForm('HISTORYRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.ID);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加告警历史';
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    loading.value = true;
    reset();
    const _ID = row.ID || ids.value;
    getHISTORY(_ID).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '编辑告警历史';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.HISTORYRef.validate((valid) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.ID != null) {
          updateHISTORY(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          addHISTORY(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _IDs = row.ID || ids.value;
    proxy.$modal
      .confirm('是否确定删除告警历史编号为"' + _IDs + '"的数据项？')
      .then(function () {
        loading.value = true;
        return delHISTORY(_IDs);
      })
      .then(() => {
        loading.value = true;
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'alert/HISTORY/export',
      {
        ...queryParams.value,
      },
      `HISTORY_${new Date().getTime()}.xlsx`,
    );
  }

  getList();
</script>
