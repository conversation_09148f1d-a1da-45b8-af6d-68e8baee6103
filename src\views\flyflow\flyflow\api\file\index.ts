import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FileInfo } from './types';

/**
 * 上传文件
 *
 * @param file
 */
export function uploadFileApi(file: File, workspaceId: Number): AxiosPromise<FileInfo> {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    // url: '/xugurtp-flow/file/upload',
    url: '/resource/oss/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      workspaceId: workspaceId,
    },
  });
}

/**
 * 删除文件
 *
 * @param filePath 文件完整路径
 */
export function deleteFileApi(filePath?: string) {
  return request({
    url: '/xugurtp-flow/api/v1/files',
    method: 'delete',
    params: { filePath: filePath },
  });
}

/**
 * pdf 转图片
 *
 * @param filePath 文件完整路径
 */
export function pdfToImgList(filePath?: string, page, count) {
  return request({
    url: '/xugurtp-flow/file/pdfToImgList',
    method: 'get',
    params: { url: filePath, page: page, count: count },
  });
}
