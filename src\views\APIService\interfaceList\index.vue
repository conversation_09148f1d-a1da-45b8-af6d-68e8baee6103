<template>
  <SplitPanes class="App-theme">
    <template #left>
      <div class="interface-left-box">
        <div class="tree-radio-box">
          <el-radio-group v-model="dataType" @change="changeDataSource">
            <el-radio-button label="1">分组</el-radio-button>
            <!-- <el-radio-button label="2">主题</el-radio-button> -->
          </el-radio-group>
        </div>
        <div class="right-btn-box">
          <ExportAndImport
            moduleName="ApiGroup_BACKEND"
            :allowClick="{
              output: { disabled: false, msg: '' },
              input: { disabled: false, msg: '' },
              logs: { disabled: false, msg: '' },
            }"
            @reload="getGroupsTree"
          ></ExportAndImport>
          <el-tooltip content="新增目录" effect="light" placement="top">
            <el-button class="right-btn-add" type="primary" icon="Plus" @click="addGroupBtn" />
          </el-tooltip>
        </div>
        <el-input
          v-if="dataType === '1'"
          v-model="treeSearchText"
          v-input="searchTree"
          class="tree-search"
          placeholder="请输入搜索内容"
          suffix-icon="Search"
        >
          <!-- <template #append>
            <el-button icon="plus" @click="addGroupBtn" />
          </template> -->
        </el-input>
        <el-tree
          v-if="dataType === '1'"
          ref="treeRef"
          class="left-tree-box"
          :data="allTreeData.treeData"
          :props="propsGroupTree"
          :highlight-current="true"
          :filter-node-method="filterNode"
        >
          <template #default="items">
            <div v-if="items.node.level == 1" class="tree-item" @click="handleNodeClick(items)">
              <div class="tree-item-box">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                <el-tooltip
                  :content="items.data.groupName"
                  placement="top"
                  :disabled="items.data.groupName.length < 10"
                >
                  {{
                    items.data.groupName.length > 10
                      ? items.data.groupName.slice(0, 10) + '...'
                      : items.data.groupName
                  }}
                </el-tooltip>
                <!-- {{ items.data.groupName }} -->
              </div>
              <div class="tree-btn-box">
                <span class="tree-icon" @click.stop="addGroupBtn(items)">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </span>
                <span v-if="items.data.groupName" class="tree-icon" @click.stop="editGroup(items)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </span>
                <span
                  v-if="
                    items.data.apiStatus === 'edit' ||
                    (items.data.groupName && items.data.children.length === 0)
                  "
                  class="tree-icon"
                  @click.stop="deleteGroup(items)"
                >
                  <el-icon>
                    <Delete />
                  </el-icon>
                </span>
              </div>
            </div>
            <div v-else class="tree-item" @click="handleNodeClick(items)">
              <div class="tree-item-box">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                <!-- {{ deelDataSearch(items.data.groupName || items.data.apiName) }} -->
                {{ items.data.groupName || items.data.apiName }}
              </div>
              <div class="tree-btn-box">
                <span v-if="items.data.groupName" class="tree-icon" @click.stop="editGroup(items)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </span>
                <span
                  v-if="items.data.groupName && items.data.children.length === 0"
                  class="tree-icon"
                  @click.stop="deleteGroup(items)"
                >
                  <el-icon>
                    <Delete />
                  </el-icon>
                </span>
              </div>
            </div>
          </template>
        </el-tree>
        <el-tree
          v-if="dataType === '2'"
          class="left-tree-box"
          :data="allTreeData.dataBase"
          :props="propsTree"
          :highlight-current="true"
        >
          <template #default="items">
            <span v-if="items.node.level == 1" @click="handleNodeClick(items)">
              <el-icon>
                <House />
              </el-icon>
              {{ items.data.categoryName }}
            </span>
            <span v-else @click="handleNodeClick(items)">
              <el-icon>
                <Cpu />
              </el-icon>
              {{ items.data.categoryName }}
            </span>
          </template>
        </el-tree>
      </div>
    </template>
    <template #right>
      <div class="interface-right-box">
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
        <el-form ref="" inline :model="searchForm" label-position="right" label-width="auto">
          <el-form-item label="服务名称" prop="roleName">
            <!-- <el-row :gutter="10"> -->
            <!-- <el-col :span="14"> -->
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入名称"
              clearable
              style="width: 100%"
            />
            <!-- </el-col> -->
            <!-- <el-col :span="1.5"> -->

            <!-- </el-col> -->
            <!-- <el-col :span="1.5"> -->

            <!-- </el-col> -->
            <!-- </el-row> -->
          </el-form-item>
          <el-form-item label="类型" prop="apiType">
            <!-- <el-input -->
            <!-- v-model="searchForm.type" -->
            <!-- placeholder="请选择类型" -->
            <!-- clearable -->
            <!-- style="width: 200px" -->
            <!-- /> -->
            <el-select
              v-model="searchForm.apiType"
              placeholder="请选择类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="服务状态" prop="apiStatus">
            <el-select
              v-model="searchForm.apiStatus"
              placeholder="请选择服务状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in devTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="集市状态" prop="apiHubStatus">
            <el-select
              v-model="searchForm.apiHubStatus"
              placeholder="请选择集市状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in apiStatusForApiMarket"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="table-search-btn">
          <el-button type="primary" icon="Search" class="icon-btn" @click="listPage(1)"></el-button>
          <el-button icon="Refresh" class="icon-btn" @click="searchReSet"></el-button>
        </div>
        <div class="table-btn">
          <el-button
            icon="Plus"
            type="primary"
            :style="{ visibility: activeName !== 'first' ? 'hidden' : 'visible' }"
            @click="revamp"
          >
            新增
          </el-button>
          <!-- <el-button
          icon="Delete"
          type="danger"
          :style="{ visibility: activeName !== 'first' ? 'hidden' : 'visible' }"
          @click="batchDeletion"
          :disabled="!tableSelection.length"
        >
          删除
        </el-button> -->
        </div>
        <!-- <el-tab-pane label="开发中" name="first"> -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            :header-cell-class-name="addHeaderCellClassName"
            row-class-name="table-box"
            empty-text="暂无数据"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column type="index" width="60" label="序号" align="center" />
            <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
              <!-- apiName -->
              <template v-if="item.prop === 'apiName'" #default="scope">
                <el-button type="text" size="small" @click="turnToDetail(scope)">
                  {{ scope.row.apiName }}
                </el-button>
              </template>
              <template v-else-if="item.prop === 'apiType'" #default="scope">
                {{ showType(scope.row.apiType) }}
              </template>
              <template v-else-if="item.prop === 'apiStatus'" #default="scope">
                <div :class="`api-status-content api-status-${scope.row.apiStatus}`">
                  {{ showStatus(scope.row.apiStatus) }}
                </div>
              </template>
              <template v-else-if="item.prop === 'apiHubStatus'" #default="scope">
                <div :class="`api-status-content api-status-${scope.row.apiHubStatus}`">
                  {{ showApiHubStatus(scope.row.apiHubStatus) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" min-width="300" width="auto">
              <template #header>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="API状态为停用或草稿时可对API进行编辑"
                  placement="top-start"
                >
                  <span
                    >操作<el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff"
                      ><WarningFilled /></el-icon
                  ></span>
                </el-tooltip>
              </template>
              <template v-if="activeName !== 'third'" #default="scope">
                <el-button
                  v-if="scope.row.apiStatus == 'edit' || scope.row.apiStatus == 'disable'"
                  type="text"
                  icon="VideoPlay"
                  size="small"
                  @click="submitEnableRequest(scope, 2)"
                  >启用</el-button
                >
                <el-button
                  v-if="
                    scope.row.apiStatus == 'audit' ||
                    (scope.row.apiStatus == 'release' && scope.row.apiHubStatus == 'audit')
                  "
                  type="text"
                  icon="ArrowLeft"
                  size="small"
                  :disabled="scope.row.updateBy !== userInfo.userName"
                  @click="revokeApiEnableRequest(scope, 1)"
                  >撤销申请</el-button
                >
                <el-button
                  v-if="
                    (scope.row.apiStatus == 'release' && !scope.row.apiHubStatus) ||
                    (scope.row.apiStatus == 'release' && scope.row.apiHubStatus == 'default') ||
                    (scope.row.apiStatus == 'release' && scope.row.apiHubStatus == 'off_hub')
                  "
                  type="text"
                  icon="Upload"
                  size="small"
                  @click="upShelves(scope)"
                  >上架</el-button
                >
                <el-button
                  v-if="scope.row.apiStatus == 'release' && scope.row.apiHubStatus == 'on_hub'"
                  type="text"
                  icon="Download"
                  size="small"
                  @click="unShelves(scope)"
                  >下架</el-button
                >
                <!-- <el-button type="text" icon="Document" size="small" @click="revamp(scope)"
              >详情</el-button
            > -->
                <el-button
                  v-if="scope.row.apiStatus == 'release'"
                  type="text"
                  icon="VideoPause"
                  size="small"
                  :disabled="
                    scope.row.apiHubStatus == 'on_hub' || scope.row.apiHubStatus == 'audit'
                  "
                  @click="disableApiRequest(scope, 3)"
                  >停用</el-button
                >
                <!-- 编辑-->
                <el-button
                  v-if="scope.row.apiStatus != 'release'"
                  :disabled="scope.row.apiStatus == 'audit'"
                  type="text"
                  icon="EditPen"
                  size="small"
                  @click="revamp(scope)"
                  >编辑</el-button
                >
                <el-button
                  :disabled="!(scope.row.apiStatus == 'edit' || scope.row.apiStatus == 'disable')"
                  type="text"
                  icon="Delete"
                  size="small"
                  @click="del(scope)"
                  >删除</el-button
                >
                <!-- 删除  -->
                <!-- 上线下线 -->
                <!-- <el-button
                  v-if="activeName === 'second'"
                  type="text"
                  size="small"
                  @click="line(scope)"
                >
                  {{ scope.row.isOnline ? '下线' : '上线' }}
                </el-button> -->

                <!-- 版本管理 -->
                <!-- <el-button
                  v-if="activeName === 'second'"
                  type="text"
                  size="small"
                  @click="version(scope)"
                  >版本管理</el-button
                > -->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- </el-tab-pane> -->

        <!-- <el-tab-pane label="已发布" name="second"> -->
        <!-- <el-table -->
        <!-- ref="tableRef" -->
        <!-- :data="tableData" -->
        <!-- :header-cell-class-name="addHeaderCellClassName" -->
        <!-- row-class-name="rowClass" -->
        <!-- empty-text="暂无数据" -->
        <!-- > -->
        <!-- <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item"> -->
        <!-- <!~~ apiName ~~> -->
        <!-- <template v-if="item.prop === 'apiName'" #default="scope"> -->
        <!-- <el-button type="text" size="small" @click="turnToDetail(scope)"> -->
        <!-- {{ scope.row.apiName }} -->
        <!-- </el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- <el-table-column label="操作" fixed="right" min-width="360" width="auto"> -->
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="line(scope)" -->
        <!-- > -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- </el-table> -->
        <!-- </el-tab-pane> -->
        <!--  -->
        <!-- <el-tab-pane label="已申请" name="third"> -->
        <!-- <el-table -->
        <!-- ref="tableRef" -->
        <!-- :data="tableData" -->
        <!-- :header-cell-class-name="addHeaderCellClassName" -->
        <!-- row-class-name="rowClass" -->
        <!-- empty-text="暂无数据" -->
        <!-- > -->
        <!-- <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item"> -->
        <!-- <!~~ apiName ~~> -->
        <!-- <template v-if="item.prop === 'apiName'" #default="scope"> -->
        <!-- <el-button type="text" size="small" @click="turnToDetail(scope)"> -->
        <!-- {{ scope.row.apiName }} -->
        <!-- </el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- <el-table-column label="操作" fixed="right" min-width="260" width="auto"> -->
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="line(scope)" -->
        <!-- > -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- </el-table> -->
        <!-- </el-tab-pane> -->

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :pager-count="maxCount"
          :total="total"
          @pagination="listPage"
        />
        <!-- </el-tabs> -->
      </div>
    </template>
  </SplitPanes>
  <el-dialog
    v-model="spatialVisible"
    :title="spatialTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <el-table
      ref="tableRef"
      :data="tableData"
      :header-cell-class-name="addHeaderCellClassName"
      row-class-name="rowClass"
      empty-text="暂无数据"
    >
      <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
        <!-- apiName -->
        <template v-if="item.prop === 'apiName'" #default="scope">
          <el-button type="text" size="small" @click="turnToDetail(scope)">
            {{ scope.row.apiName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="260" width="auto">
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button v-if="activeName === 'second'" type="text" size="small" @click="line(scope)"> -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
      </el-table-column>
    </el-table>
    <template #footer>
      <!-- <span class="dialog-footer"> -->
      <!-- <el-button type="primary" @click="submitSpatial">确 定</el-button> -->
      <!-- <el-button @click="closeSpatial">取 消</el-button> -->
      <!-- </span> -->
    </template>
  </el-dialog>
  <el-dialog
    v-model="addGroupDialog"
    :title="addGroupDialogTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <APIAddGroup
      v-if="addGroupDialog"
      ref="addGroupRef"
      :form-data="editGroupForm"
      :active-name="'first'"
    ></APIAddGroup>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="addGroupCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="shelvesModal"
    :title="shelvesTitle"
    width="400px"
    append-to-body
    :draggable="true"
    @close="shelvesCancel"
  >
    <div style="text-align: center">
      <h3>是否{{ shelvesInfo?.h3 }}</h3>
      <p v-if="shelvesInfo.type == 2">{{ shelvesInfo?.p }}</p>
      <p v-else>需等待审批通过后{{ shelvesInfo?.p }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="shelvesCancel">取 消</el-button>
        <el-button type="primary" @click="shelvesCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="flowDialog" title="发起申请" width="30%" append-to-body :draggable="true">
    <FormUI ref="formUIRef" @form-value-change="formValueChange"></FormUI>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeFlowDialog">取消</el-button>
        <el-button type="primary" @click="flowDialogCommit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <StartFlowPageList v-show="false" ref="StartFlowPageListRef"></StartFlowPageList>
</template>

<script setup>
  import {
    delApiType,
    getApiTypeList,
    updateApiState,
    getGroupTree,
    updateGroup,
    addGroup,
    getCategoryList,
    delGroup,
  } from '@/api/APIService';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { ref, reactive } from 'vue';
  import { getUserProfile } from '@/api/system/user';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import APIAddGroup from '../components/APIAddGroup/index.vue';
  import { queryMineStartGroupFlowList } from '@/views/flyflow/flyflow/api/group';
  import StartFlowPageList from '@/views/flyflow/flyflow/components/start-flow-page-list.vue';
  import { startFlow, startFlowByFrom } from '@/views/flyflow/flyflow/api/flow';
  import { getFlowByType } from '../../flyflow/flyflow/api/group';
  import { cancelFlow } from '../../flyflow/flyflow/api/task';
  import SplitPanes from '@/components/SplitPanes/index';

  import FormUI from '@/views/flyflow/flyflow/components/task/handler/formUIPc.vue';

  import { getFormDetail } from '@/views/flyflow/flyflow/api/form';
  import { nextTick } from 'vue';

  const StartFlowPageListRef = ref();
  // eslint-disable-next-line prettier/prettier
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      flowName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      unitTime: [
        { required: true, message: '请输入时间窗口', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      flowCount: [
        { required: true, message: '请输入阈值', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      ruleName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      ruleCode: [
        { required: true, message: '请选择熔断策略', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      slowRatioThreshold: [
        { required: true, message: '请输入慢调用比例阈值', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      count: [
        { required: true, message: '请输入阈值', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],

      statIntervalMs: [
        { required: true, message: '请输入统计间隔', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      minRequestAmount: [
        { required: true, message: '请输入最小请求数', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      timeWindow: [
        { required: true, message: '请输入熔断时长', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [{ required: true, message: '请输入类型', trigger: 'change' }],
      cacheTime: [
        { required: true, message: '请输入缓存时间', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      maxCacheBody: [
        { required: true, message: '请输入缓存大小', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  // 树信息
  const dataType = ref('1'); // 数据类型
  const dataTree = ref([]); // 左侧树数据
  const allTreeData = reactive({});
  let userInfo = reactive({});
  let editGroupForm = reactive({});
  const propsTree = {
    value: 'categoryId',
    label: 'categoryName',
    children: 'children',
  };

  const tableRef = ref(null);
  const tableSelection = ref([]);

  const { form, queryParams } = toRefs(data);

  const maxCount = ref(5);
  const total = ref(1);
  const searchForm = reactive({
    keyword: '',
    apiStatus: '',
    type: '',
    apiHubStatus: '',
  });
  // API 状态
  const devTypeOptions = [
    { value: '', label: '全部' },
    // {
    //   //   label: '草稿',
    //   label: '未启用',
    //   value: 'edit',
    // },
    {
      label: '启用',
      value: 'release',
    },
    {
      //   label: '停用',
      label: '未启用',
      value: 'disable',
    },
    {
      label: '审批中',
      value: 'audit',
    },
    // {
    //   label: '已拒绝',
    //   value: 'refuse',
    // },
  ];
  // 接口在 API 集市状态
  const apiStatusForApiMarket = [
    { value: '', label: '全部' },
    {
      label: '上架',
      value: 'on_hub',
    },
    {
      label: '下架',
      value: 'off_hub',
    },
    {
      label: '审批中',
      value: 'audit',
    },
    // {
    //   label: '已拒绝',
    //   value: 'refuse',
    // },
  ];

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([]);

  const activeName = ref('first');

  //动态表单信息
  const flowDialog = ref(false);
  const flowForm = ref();

  const listPage = async (data) => {
    if (data === 1) {
      queryParams.value.pageNum = 1;
    }
    if (activeName.value === 'first') {
      getDataUtil();
    } else if (activeName.value === 'second') {
      getDataUtil('release', data);
    } else if (activeName.value === 'third') {
      getDataUtil('audit', data);
    }
  };
  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getDataUtil = async (type = 'edit') => {
    closeSpatial();

    if (type === 'edit') {
      columns.value = [
        {
          key: 1,
          label: `服务名称`,
          visible: true,
          prop: 'apiName',
          width: '150',
          showOverflowTooltip: true,
          // 左对齐
          align: 'center',
        },
        {
          key: 2,
          label: `类型`,
          visible: true,
          prop: 'apiType',
          width: '100',
          showOverflowTooltip: true,
        },
        {
          key: 3,
          label: `分组`,
          visible: true,
          prop: 'groupName',
          width: '100',
          showOverflowTooltip: true,
        },
        {
          key: 4,
          label: `创建人`,
          visible: true,
          prop: 'createBy',
          width: '180',
        },
        {
          key: 5,
          label: `服务状态`,
          visible: true,
          prop: 'apiStatus',
          width: '180',
        },
        {
          key: 6,
          label: `集市状态`,
          visible: true,
          prop: 'apiHubStatus',
          width: '180',
        },
        {
          key: 7,
          label: `备注`,
          visible: true,
          prop: 'remarks',
          width: '180',
        },
        {
          key: 8,
          label: `更新时间`,
          visible: true,
          prop: 'updateTime',
          width: '200',
          showOverflowTooltip: true,
        },
        // {
        //   key: 8,
        //   label: `主题`,
        //   visible: true,
        //   prop: 'categoryName',
        //   width: '180',
        // },
        // {
        //   key: 2,
        //   label: `请求方式`,
        //   visible: true,
        //   prop: 'apiMethod',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `接口地址`,
        //   visible: true,
        //   prop: 'apiPath',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `认证类型`,
        //   visible: true,
        //   prop: 'apiType',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `数据库`,
        //   visible: true,
        //   prop: 'datasourceName',
        //   width: '200',
        // },
        // {
        //   key: 4,
        //   label: `版本`,
        //   visible: true,
        //   prop: 'version',
        //   width: '200',
        // },
      ];
    } else if (type === 'release') {
      columns.value = [
        {
          key: 0,
          label: `接口编号`,
          visible: true,
          prop: 'apiId',
          width: '200',
        },
        {
          key: 1,
          label: `接口名称`,
          visible: true,
          prop: 'apiName',
          width: '200',
          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `接口地址`,
          visible: true,
          prop: 'apiPath',
          width: '200',
        },
        {
          key: 3,
          label: `发布状态`,
          visible: true,
          prop: 'apiStatus',
          width: '200',
        },
        {
          key: 4,
          label: `认证类型`,
          visible: true,
          prop: 'apiType',
          width: '200',
        },
        {
          key: 5,
          label: `级别`,
          visible: true,
          prop: 'apiLevel',
          width: '200',
        },
        {
          key: 6,
          label: `发行版本`,
          visible: true,
          prop: 'version',
          width: '200',
        },
        {
          key: 7,
          label: `发布时间`,
          visible: true,
          prop: 'updateTime',
          width: '200',
          showOverflowTooltip: true,
        },
      ];
    } else if (type === 'audit') {
      columns.value = [
        {
          key: 0,
          label: `接口编号`,
          visible: true,
          prop: 'apiId',
          width: '200',
        },
        {
          key: 1,
          label: `接口名称`,
          visible: true,
          prop: 'apiName',

          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `接口地址`,
          visible: true,
          prop: 'apiPath',
        },
        {
          key: 3,
          label: `发布状态`,
          visible: true,
          prop: 'apiStatus',
        },
        {
          key: 4,
          label: `认证类型`,
          visible: true,
          prop: 'apiType',
        },
        {
          key: 5,
          label: `级别`,
          visible: true,
          prop: 'apiLevel',
        },
        {
          key: 6,
          label: `发行版本`,
          visible: true,
          prop: 'version',
        },
        {
          key: 7,
          label: `发布时间`,
          visible: true,
          prop: 'updateTime',
          showOverflowTooltip: true,
        },
      ];
    }

    try {
      const result = await getApiTypeList({
        ...queryParams.value,
        ...searchForm,
        groupId: groupId.value,
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
        operateType: 'API_LIST',
      });
      tableData.value = result.data.list;
      total.value = result.data.total;
    } catch (error) {
      console.error('Error fetching table data:', error);
    }
  };
  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
    proxy.$refs.formRef?.resetFields();
  };
  const del = async (row) => {
    const result = await proxy.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    if (!result) return;

    const res = await delApiType({
      apiId: row.row.apiId,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getDataUtil();
  };

  const line = async (row) => {
    const res = await updateApiState({
      apiId: row.apiId,
      enabled: 1,
      apiStatus: 'disable',
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    shelvesCancel();
  };

  const router = useRouter();
  const revamp = (row) => {
    //   使用 router 跳转到修改页面
    router.push({
      path: '/APIService/apiService/APIWorkbench',
      query: { apiId: row.row?.apiId || '' },
    });
  };
  const turnToDetail = (row) => {
    console.log(row);
    // debugger;
    router.push({ path: '/APIService/APIDetail', query: { apiId: row.row.apiId } });
  };

  // 修改数据源类型
  const changeDataSource = (type) => {
    dataTree.value = type === '1' ? allTreeData.treeData : allTreeData.dataBase;
    // treeLoad()
  };

  const propsGroupTree = {
    value: 'groupId',
    label: 'groupName',
    children: 'children',
  };

  // 表头搜索对应操作
  const searchReSet = () => {
    searchForm.keyword = '';
    searchForm.apiStatus = '';
    searchForm.apiType = '';
    searchForm.apiHubStatus = '';
    // 重新发送请求
    getDataUtil();
  };

  // const batchDeletion = async () => {
  //   // debugger;
  //   // console.log(tableRef.value.multipleSelection);
  //   if (tableSelection.value.length <= 0) {
  //     return ElMessage.error('请选择需要删除的数据！');
  //   }
  //   const result = await proxy.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
  //     confirmButtonText: '确定',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //   });
  //   if (!result) return;
  //   const ids = tableSelection.value.map((res) => res.apiId);
  //   console.log(ids);
  //   const res = await delApiType({
  //     apiId: row.row.apiId,
  //     workspaceId: workspaceId.value,
  //     tenantId: tenantId.value,
  //   });
  //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //   proxy.$modal.msgSuccess(res.msg);

  //   getDataUtil();
  // };
  const handleSelectionChange = (value) => {
    tableSelection.value = value;
  };

  // 树监听

  //   const deelDataSearch = (data) => {
  //     let returnData = '';
  //     const thisShowTexts = data.split(treeSearchText);
  //     if (thisShowTexts.length > 1) {
  //       for (const text of thisShowTexts) {
  //         returnData += text + `<span>${treeSearchText.value}</span>`;
  //       }
  //     } else {
  //       returnData = data;
  //     }
  //     return returnData;
  //   };

  // 获取分组树
  const getGroupsTree = async () => {
    const resData = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      resData.tenantId = tenantId.value;
    }
    const resDatas = await getGroupTree(resData);
    allTreeData.treeData = resDatas.data;
  };

  // 获取主题列表
  const getCategoryLists = async () => {
    const resData = {
      workspaceId: workspaceId.value,
      pageNum: 1,
      pageSize: 9999,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      resData.tenantId = tenantId.value;
    }
    const resDatas = await getCategoryList(resData);
    allTreeData.dataBase = resDatas.rows;
  };

  // 添加分组弹出框配置
  const addGroupDialog = ref(false);
  const addGroupRef = ref(null);
  const addGroupDialogTitle = ref('添加分组');
  const addGroupBtn = () => {
    addGroupDialog.value = true;
  };
  const closeAddGroupDialog = () => {
    addGroupDialogTitle.value = '添加分组';
    addGroupDialog.value = false;
  };
  // 删除分组、接口、转发
  const deleteGroup = async (item) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${item.data.groupName}数据项？`);
    if (!confirm) return;
    // if (item.groupId) {
    //   res = await delApiType(item.data.groupId);
    // } else if (item.apiType === 'SQL') {
    //   res = await delGroup(item.data.groupId);
    // } else {
    //   res = await delApiType(item.data.groupId);
    // }
    const res = await delGroup(item.data.groupId);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };
  // 编辑分组
  const editGroup = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑分组';
    });
  };
  // 添加分组
  const addGroupCommit = async () => {
    const addForm = addGroupRef.value.addForm();
    let res = {};
    let textTitle = '添加';
    const reqData = {
      ...addForm,
      workspaceId: workspaceId.value,
      groupType: 'BACKEND',
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      reqData.tenantId = tenantId.value;
    }
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑分组') {
      textTitle = '编辑';

      res = await updateGroup(reqData);
    } else {
      res = await addGroup(reqData);
    }

    if (res.code === 200) {
      ElMessage.success(textTitle + '成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error(textTitle + '失败：' + res.msg);
    }
  };
  const groupId = ref(null);
  // 点击左侧树
  const handleNodeClick = (item) => {
    const treeData = {
      type: '',
    };
    if (dataType.value === '1') {
      treeData.type = '';
      treeData.groupId = item.data.groupId;
      groupId.value = item.data.groupId;
    }
    listPage(1);
    console.log(item);
  };
  const GroupFlowList = ref([]);
  const queryMineStartGroupFlowListUtil = async (hidden) => {
    const res = await queryMineStartGroupFlowList(hidden);
    if (res.code !== 200) return;
    GroupFlowList.value = res.data;
  };

  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    await getGroupsTree();
    await getCategoryLists();
    await getDataUtil();
    await queryMineStartGroupFlowListUtil();
  };

  const FlowList = ref([]);
  const getFlowByTypeUtil = async (type) => {
    const res = await getFlowByType({
      flowType: type,
      tenantId: getTenantId(),
    });
    if (res.code !== 200) return;
    FlowList.value = res.data;
  };

  const getTenantId = () => {
    if (userInfo?.userType === 'sys_user') {
      return tenantId.value;
    } else {
      return userInfo?.tenantId;
    }
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
  });

  const shelvesModal = ref(false);
  const shelvesTitle = ref('');
  const shelvesForm = ref({});
  const shelvesInfo = ref({});
  const shelvesType = ref('');
  const openShelvesModal = (title, data, info) => {
    shelvesModal.value = true;
    shelvesTitle.value = title;
    shelvesForm.value = data;
    shelvesInfo.value = info;
  };

  //自定义动态表单数据保存
  const autoForm = ref();

  const formValueChange = (data) => {
    autoForm.value = data;
  };
  const startEditFlow = async () => {
    const res = await startFlow({
      flowId: FlowList.value.flowId,
      uniqueId: FlowList.value.uniqueId,
      apiId: shelvesForm.value.apiId,
      paramMap: {
        ...autoForm.value,
        apiname: shelvesForm.value.apiName,
        workspaceId: workspaceId.value,
      },
      flowType: shelvesType.value,
    });
    if (res.code !== 200) return;
    flowDialog.value = false;
    ElMessage.success(res.msg);
    shelvesCancel();
  };

  /**  撤销申请
   *
   * @param item
   */
  const revokeApiEnableRequest = (item) => {
    const title = '操作确认';
    const data = item.row;
    const info = {
      h3: '确定撤销该申请？',
      p: '撤销申请后可再对应页面继续申请',
      type: 2,
    };
    shelvesType.value = 'api_unable_flow';
    openShelvesModal(title, data, info);
  };

  //初始化动态表单
  //设置自定义表单
  const formUIRef = ref();

  //提交申请流程
  const flowDialogCommit = () => {
    formUIRef.value.validate(function (uiValid, fv) {
      if (uiValid) {
        startEditFlow();
      }
    });
  };
  const closeFlowDialog = () => {
    flowDialog.value = false;
  };

  const beforeOpenShelves = async (title, data, info) => {
    await getFlowByTypeUtil(shelvesType.value);
    console.log(FlowList.value);
    const res = await getFormDetail({ flowId: FlowList.value.flowId, from: 'start' });
    const hasForm = res.data?.formList.find((list) => {
      return list.perm !== 'H';
    });
    if (res.code === 200 && hasForm) {
      flowDialog.value = true;
      nextTick(() => {
        const data = res.data;
        formUIRef.value.loadData(
          data?.formList,
          FlowList.value.flowId,
          undefined,
          undefined,
          undefined,
          undefined,
          data.dynamic,
        );
      });
    } else {
      openShelvesModal(title, data, info);
    }
  };
  /**  提交启用
   *
   * @param item
   */
  const submitEnableRequest = async (item) => {
    const title = '操作确认 ';
    const data = item.row;
    const info = {
      h3: '将该服务启用？',
      p: '审批通过后启用',
    };
    shelvesType.value = 'api_enable_flow';
    shelvesForm.value = data;
    beforeOpenShelves(title, data, info);
  };

  /**  停用
   *
   * @param item
   */
  const disableApiRequest = (item) => {
    const title = '操作确认 ';
    const data = item.row;
    const info = {
      h3: '将该服务停用？',
      p: '审批通过后停用',
    };
    shelvesType.value = 'disable';
    openShelvesModal(title, data, info);
  };

  /**  上架
   *
   * @param item
   */
  const upShelves = async (item) => {
    const title = '操作确认';
    const data = item.row;
    const info = {
      h3: '将该服务上架到集市？',
      p: '发布到集市',
    };
    shelvesType.value = 'api_online_flow';
    // openShelvesModal(title, data, info);
    shelvesForm.value = data;
    beforeOpenShelves(title, data, info);
  };
  /**  下架
   *
   * @param item
   */
  const unShelves = async (item) => {
    const title = '操作确认 ';
    const data = item.row;
    const info = {
      h3: '将该服务从集市下架？',
      p: '下架',
    };
    shelvesType.value = 'api_offline_flow';
    // openShelvesModal(title, data, info);
    shelvesForm.value = data;
    beforeOpenShelves(title, data, info);
  };

  const getStartProcess = async (keyWord = '') => {
    if (!keyWord) return;
    // let found = false;
    if (shelvesType.value === 'disable') {
      line(shelvesForm.value);
    } else if (shelvesType.value === 'api_unable_flow') {
      await cancelFlowUtil(shelvesForm.value);
    } else {
      await getFlowByTypeUtil(shelvesType.value);
      await startFlowUtil(FlowList.value);
    }
  };

  const startFlowUtil = async (item) => {
    const res = await startFlow({
      flowId: item.flowId,
      uniqueId: item.uniqueId,
      apiId: shelvesForm.value.apiId,
      flowType: shelvesType.value,
      paramMap: {
        flowId: item.flowId,
        apiname: shelvesForm.value.apiName,
        workspaceId: workspaceId.value,
      },
    });
    if (res.code !== 200) return;
    ElMessage.success(res.msg);
    shelvesCancel();
  };

  const cancelFlowUtil = async (item) => {
    const res = await cancelFlow({
      apiId: item.apiId,
    });
    if (res.code !== 200) return;
    ElMessage.success(res.msg);
    shelvesCancel();
  };

  //   const processAction = (type) => {
  // const action = actionMap.get(type);
  // if (action) {
  // getStartProcess(action);
  // } else {
  //   ElMessage.error(`未知的操作类型：${type}`);
  // }
  //   };

  const shelvesCommit = async () => {
    debugger;
    const res = await getStartProcess(shelvesType.value);
    getDataUtil();
    shelvesModal.value = false;
  };

  const shelvesCancel = async () => {
    shelvesModal.value = false;
    shelvesTitle.value = '';
    shelvesForm.value = {};
    shelvesInfo.value = {};
    shelvesType.value = '';
  };
  const showType = (type) => {
    return typeOptions.find((item) => item.value === type)?.label;
  };
  const showStatus = (type) => {
    return devTypeOptions.find((item) => item.value === type)?.label;
  };
  const showApiHubStatus = (type) => {
    return apiStatusForApiMarket.find((item) => item.value === type)?.label || '-';
  };

  const typeOptions = [
    { value: '', label: '全部' },
    { value: 'SQL', label: '自有接口' },
    { value: 'API', label: '转发接口' },
    { value: 'SHARE', label: '库表共享' },
    { value: 'WEBSOCKET', label: 'Websocket' },
  ];

  const treeSearchText = ref('');
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.groupName.includes(value); // 使用节点的数据进行比较
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .App-theme {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    background: $--base-color-bg;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    .interface-left-box {
      //   width: 260px;
      height: 100%;
      background: $--base-color-item-light;
      padding: 10px;
      .tree-radio-box,
      .tree-search {
        margin-bottom: 20px;
      }
      .add-btn-box {
        width: 100%;
        height: 32px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        .el-button {
          flex: 1;
        }
      }
      .left-tree-box {
        height: calc(100% - 132px);
        background: $--base-color-item-light;
        padding: 10px;
        overflow: auto;
      }
      .right-btn-box {
        width: 100%;
        text-align: right;
        margin-bottom: 10px;
        .right-btn-add {
          width: 28px;
          height: 28px;
        }
      }
      .export-and-import {
        display: inline-block;
        margin-right: 10px;
      }
    }
    .interface-right-box {
      //   width: calc(100% - 280px);
      height: 100%;
      //   background: $--base-color-item-light;
      ::v-deep .el-form--label-right {
        width: calc(100% - 100px);
        display: inline-block;
        text-align: right;
      }
      .table-search-btn {
        width: 100px;
        display: inline-block;
        vertical-align: top;
      }
      ::v-deep .el-table {
        padding: 10px;
        background: $--base-color-item-light;
        border-radius: 8px;
        .el-tabs--top {
          width: 100%;
          height: 100%;
          .el-tabs__content {
            height: calc(100% - 40px);
            padding: 20px;
            box-shadow: 0px 4px 12px rgba(1, 102, 243, 0.08);
            .el-tab-pane {
              height: calc(98% - 150px);
            }
          }
        }
      }
      .pagination-container {
        margin: 10px;
        ::v-deep .el-pagination {
          right: 40px;
        }
      }

      .asterisk-left {
        width: calc(25% - 40px);
        margin-right: 12px;
      }
      .table-box {
        height: calc(100% - 164px);
        .api-status-content {
          &.api-status-edit,
          &.api-status-off_hub {
            // color: $--base-color-yellow;
            // &::before {
            //   background-color: $--base-color-yellow;
            // }
            color: $--base-color-text2;
            &::before {
              background-color: $--base-color-text2;
            }
          }
          &.api-status-release,
          &.api-status-on_hub {
            color: $--base-color-green;
            &::before {
              background-color: $--base-color-green;
            }
          }
          &.api-status-audit {
            color: $--base-color-primary;
            &::before {
              background-color: $--base-color-primary;
            }
          }
          &.api-status-null {
            &::before {
              display: none;
            }
          }
          &::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-right: 4px;
            display: inline-block;
            background-color: $--base-color-text2;
          }
        }
      }
    }
    .table-search-btn {
      text-align: right;
      margin-bottom: 20px;
      // display: inline-block;
      // vertical-align: top;
    }
    .table-btn {
      text-align: left;
      margin-bottom: 20px;
      //   padding: 0px 20px;
    }
    .table-box {
      //   height: calc(100% -);
    }
  }
</style>
