<template>
  <div class="page-container">
    <div class="title-container">
      <div class="title">敏感数据识别</div>
      <div style="width: 596px; margin: 0 auto">
        <el-radio-group v-model="tabName" @change="changeDataType">
          <el-radio-button label="identifyRule">数据识别规则</el-radio-button>
          <el-radio-button label="dataFound">敏感数据发现</el-radio-button>
          <el-radio-button label="dataRevise">手动修正数据</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="content-container">
      <IdentifyRule v-if="tabName == 'identifyRule'" :title-name="tabName"></IdentifyRule>
      <DataFound v-else-if="tabName == 'dataFound'" :title-name="tabName"></DataFound>
      <DataRevise v-else></DataRevise>
    </div>
  </div>
</template>
<script setup>
  import IdentifyRule from './identificationRule';
  import DataFound from './dataFound';
  import DataRevise from './dataRevise';

  const tabName = ref('identifyRule');

  const changeDataType = (data) => {
    if (!data) return;
    tabName.value = data;
  };
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .page-container {
    height: 100%;
    padding-top: 20px;

    .title-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .title {
        width: 150px;
        height: 20px;
        color: #000000;
        font-weight: 600;
        font-size: 20px;
        line-height: 20px;
        padding-left: 20px;
      }
    }
    .content-container {
      height: calc(100% - 52px);
    }
  }
</style>
