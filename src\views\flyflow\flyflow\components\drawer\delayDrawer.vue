<template>
  <el-drawer
    v-model="visible"
    :append-to-body="true"
    title="延时器设置"
    class="set_copyer"
    :show-close="true"
    :size="550"
    :before-close="saveDelay"
    :close-on-click-modal="true"
    @open="openEvent"
  >
    <template #header="{ close, titleId, titleClass }">
      <title-handler :node-config="config"></title-handler>
    </template>
    <div
      style="
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        align-items: center;
      "
    >
      <h4 style="width: 80px">时间点：</h4>
      <!-- <el-switch -->
      <!-- v-model="config.mode" -->
      <!-- size="large" -->
      <!-- active-text="固定时长" -->
      <!-- inactive-text="固定时间点" -->
      <!-- @change="changeMode" -->
      <!-- > -->
      <!-- </el-switch> -->
      <el-radio-group v-model="config.mode">
        <el-radio label="fixed">固定时长</el-radio>
        <el-radio label="time">固定时间点</el-radio>
      </el-radio-group>
    </div>

    <div>
      <template v-if="config.mode">
        <el-input
          v-model="config.value"
          type="number"
          placeholder="请输入时长"
          style="width: 250px"
        >
          <template #append>
            <el-select v-model="config.delayUnit" placeholder="Select" style="width: 115px">
              <el-option
                v-for="item in delayUnitOpts"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-input>
        后进入下一节点
      </template>
      <template v-else>
        <el-date-picker
          v-model="config.value"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择时间点"
        />
        进入下一节点
      </template>
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
  import $func from '../../utils/index';
  import { useStore } from '../../stores/index';
  import { delayUnitOpts, nodeData } from '../../utils/const';
  import { ref, watch, computed, reactive } from 'vue';

  import * as util from '../../utils/objutil';
  import TitleHandler from './components/titleHandler.vue';

  const config = ref({});

  const store = useStore();
  const { setDelayConfig, setDelay } = store;
  const delayDrawer = computed(() => store.delayDrawer);
  const delayConfigData = computed(() => store.delayConfigData);
  const visible = computed({
    get() {
      return delayDrawer.value;
    },
    set() {
      closeDrawer();
    },
  });
  watch(delayConfigData, (val) => {
    config.value = { ...nodeData[val.value.type], ...val.value };
  });

  const changeMode = () => {
    config.value.value = '';
  };

  const openEvent = () => {};

  const saveDelay = () => {
    config.value.error = !$func.checkDelay(config.value).ok;
    config.value.errorMsg = $func.checkDelay(config.value).msg;
    setDelayConfig({
      value: config.value,
      flag: true,
      id: delayConfigData.value.id,
    });
    closeDrawer();
  };
  const closeDrawer = () => {
    setDelay(false);
  };
</script>

<style lang="less" scoped></style>
