<template>
  <!-- table  -->
  <div style="padding-top: 20px">
    <el-table :data="tableData" height="510">
      <!-- 序号 -->
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :label="column.label"
        :prop="column.prop"
        v-bind="column"
      />
    </el-table>
  </div>
  <!-- 分页 -->
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :pager-count="maxCount"
    :total="total"
    @pagination="getPreviewDataUtil"
  />
</template>

<script setup>
  import { getPreviewData } from '~/src/api/dataGovernance';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    assetData: {
      type: String,
      required: true,
    },
  });
  const { assetData } = toRefs(props);
  // mock 数据
  const tableData = ref([]);
  const columns = ref([]);

  const total = ref(0);
  const maxCount = ref(7);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const getPreviewDataUtil = async () => {
    const { id } = assetData.value;
    const res = await getPreviewData({ dataAssetId: id, ...queryParams });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data || res.data.length === 0) return proxy.$modal.msgWarning('暂无数据');

    tableData.value = res.data;

    if (res.data.length > 0) {
      columns.value = Object.keys(res.data[0]).map((key) => ({
        label: key,
        prop: key,
      }));
    }

    total.value = res.total || 0;
  };

  onMounted(() => {
    getPreviewDataUtil();
  });
</script>

<style lang="scss" scoped></style>
