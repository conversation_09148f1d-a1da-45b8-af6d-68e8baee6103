<!-- <PERSON> Chart Component -->
<template>
  <el-card>
    <template #header> 接口访问量</template>
    <div :id="id" :class="className" :style="{ height, width }" />
  </el-card>
</template>
<script setup>
  import { onMounted } from 'vue';
  import * as echarts from 'echarts';

  const props = defineProps({
    id: {
      type: String,
      default: 'roseChart',
    },
    className: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '400px',
      required: true,
    },
    height: {
      type: String,
      default: '250px',
      required: true,
    },
  });

  const options = {
    // title: {
    //   //   text: 'Nightingale Chart',
    //   //   subtext: 'Example Data',
    //   //   left: 'center',
    // },
    // tooltip: {
    //   //   trigger: 'item',
    // },
    // legend: {
    //   //   top: 'bottom',
    // },
    toolbox: {
      //   show: true,
      //   feature: {
      // mark: { show: true },
      // dataView: { show: true, readOnly: false },
      // restore: { show: true },
      // saveAsImage: { show: true },
      //   },
    },
    series: [
      {
        name: 'Nightingale Chart',
        type: 'pie',
        radius: [30, 110],
        center: ['50%', '50%'],
        roseType: 'radius',
        itemStyle: {
          borderRadius: 8,
        },
        data: [
          { value: 40, name: 'rose 1' },
          { value: 33, name: 'rose 2' },
          { value: 28, name: 'rose 3' },
          { value: 22, name: 'rose 4' },
          { value: 20, name: 'rose 5' },
          { value: 15, name: 'rose 6' },
          { value: 12, name: 'rose 7' },
          { value: 10, name: 'rose 8' },
        ],
      },
    ],
  };

  onMounted(() => {
    const chart = echarts.init(document.getElementById(props.id));
    chart.setOption(options);

    window.addEventListener('resize', () => {
      chart.resize();
    });
  });
</script>

<style scoped></style>
