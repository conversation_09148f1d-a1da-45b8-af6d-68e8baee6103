import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClpJcZVr0gQWS4x2mnAzQWrnBx1jPSAGhQdpXGd+gHB/wZNATmJzCFG9DoqeRbixqlSiAO9GK5rAPiwmszc8MPkJy7KKSUq3DxBVBsbhGRFVBI9mmLFZZOFBhS4fuJo/AdmQBrxO/8CkgNhEQMPuEgh8mUcqDDWpZgf4DNuXYwQwIDAQAB';

const privateKey =
  'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKWklxlWvSBBZLjHaacDNBaucHHWM9IAaFB2lcZ36AcH/Bk0BOYnMIUb0Oip5FuLGqVKIA70YrmsA+LCazNzww+QnLsopJSrcPEFUGxuEZEVUEj2aYsVlk4UGFLh+4mj8B2ZAGvE7/wKSA2ERAw+4SCHyZRyoMNalmB/gM25djBDAgMBAAECgYAHZJIt2lY0k1aYfKX1g0oW3RA9tG65p7UAKlrC8eUUM0IIKe8yCnu65SPsznBXuZyl1eoaYMPrP4co3r6EHF2PSMRMcDoXcBoYZh/vMq8HTK4ulS6EhxLkfqBmXpurdm3BRGlzb8KjUyvWE7AQUyatgi3lLHxp4qodEkv7+a6MeQJBAOg6qj09g+Nc2tj5jCLv8rcvZJD6xLrx8kauOBR0HxoC0ebyb/BYNsyyZ0OtpPGtk4VeEmsU/Q+SgIuyJ+qUA+UCQQC2mRlc9X39xRk3mbbXfur5b9A8BrzDMXsW8RAf4hUFk1mvMOsCAooBWxNu377N24i7wT962QYAcm/6BC/bynEHAkEA1WnMNvlIMfKMP+edDCJceFH6Zm29y1s7Xg8PBGTujBXZVhaoHkTDH3w3/+8c7Oip8F9SJ8wi/2OP9FEl86JQrQJBAIm5SicSRvhcbFvCheVeJi8DhgVwc3mqXZP9ONNDe+WbsT5xYCBA+ARzxGGRQ2ITnrs21AF9pYg6yHjEJbDarYsCQQCjfbuS4HFjhVxwdfEB8+oQiEty9RKJP7NdDQPC1dkkq/IzZxDDoyHzvvIATpwOL56B6Sufb9h2yvcY85I4eYw1';

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey); // 设置公钥
  return encryptor.encrypt(txt); // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt();
  encryptor.setPrivateKey(privateKey); // 设置私钥
  return encryptor.decrypt(txt); // 对数据进行解密
}
