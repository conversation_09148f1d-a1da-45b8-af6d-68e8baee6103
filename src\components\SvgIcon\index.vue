<template>
  <component :is="iconComponent" :class="svgClass" aria-hidden="true" :style="{ fill: color }">
    <use v-if="!isArcoIcon" :xlink:href="iconName" />
  </component>
</template>

<script setup>
  import * as arcoIcons from '@arco-iconbox/vue-update-color-icon';
  import * as arcoLineIcons from '@arco-iconbox/vue-update-line-icon';
  import { computed } from 'vue';

  const props = defineProps({
    iconClass: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '',
    },
  });

  const isArcoIcon = computed(() => arcoIcons[props.iconClass] || arcoLineIcons[props.iconClass]);

  const iconComponent = computed(() =>
    isArcoIcon.value ? arcoIcons[props.iconClass] || arcoLineIcons[props.iconClass] : 'svg',
  );

  const iconName = computed(() => `#icon-${props.iconClass}`);

  const svgClass = computed(() => (props.className ? `svg-icon ${props.className}` : 'svg-icon'));
</script>

<style scope lang="scss">
  .sub-el-icon,
  .nav-icon {
    display: inline-block;
    font-size: 15px;
    margin-right: 12px;
    position: relative;
  }

  .svg-icon {
    width: 1rem;
    height: 1rem;
    position: relative;
    fill: currentColor;
    vertical-align: -2px;
  }
</style>
