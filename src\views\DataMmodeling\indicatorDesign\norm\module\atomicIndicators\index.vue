<template>
  <div class="container">
    <el-row>
      <el-col :span="24">
        <el-button type="text" @click="toBack"> {{ '<' }} 返回上一层</el-button>
      </el-col>
    </el-row>
    <div class="demo-tabs">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="指标配置" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
          >
            <el-form-item label="所属主题" prop="timeUnit">
              {{ nodeClick?.label }}
            </el-form-item>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="原子指标名称" prop="code">
                  <!-- 输入框 -->
                  <el-input v-model="form.code" type="text" show-word-limit></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="指标中文名称" prop="name">
                  <el-input v-model="form.name" type="text" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="数据表" prop="modelId">
              <!-- 下拉框 -->
              <el-select
                v-model="form.modelId"
                placeholder="请选择"
                @change="onChangeModel(form.modelId)"
              >
                <el-option
                  v-for="item in dataListator"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设定表达式" prop=""></el-form-item>
            <div>
              <splitpanes class="default-theme">
                <pane min-size="25" max-size="35" size="25">
                  <div>
                    <section>函数</section>
                    <el-tree-v2
                      :data="dataTree"
                      :props="treeProps"
                      :height="300"
                      @node-click="handleNodeClick"
                    >
                      <template #default="{ node, data }">
                        <span v-if="node.level == 1">
                          {{ data.label }}
                        </span>

                        <span v-if="node.level == 2">
                          {{ data.label }}
                        </span>
                      </template>
                    </el-tree-v2>
                  </div>

                  <div>
                    <section>字段</section>
                    <el-tree-v2
                      :data="dataTreeField"
                      :props="treeProps"
                      :height="300"
                      @node-click="handleNodeClickTwo"
                    >
                      <template #default="{ node, data }">
                        <span v-if="node.level == 1">
                          <el-tooltip
                            effect="dark"
                            :content="data.label"
                            placement="top"
                            :disabled="data.label.length < 35"
                          >
                            <span>{{
                              data.label.length > 35 ? data.label.slice(0, 35) + '...' : data.label
                            }}</span>
                          </el-tooltip>
                        </span>

                        <span v-if="node.level == 2">
                          <!-- 悬浮提示 -->
                          <el-tooltip
                            effect="dark"
                            :content="data.label"
                            placement="top"
                            :disabled="data.label.length < 35"
                          >
                            <span>{{
                              data.label.length > 35 ? data.label.slice(0, 35) + '...' : data.label
                            }}</span>
                          </el-tooltip>
                          <!-- {{ data.label }} -->
                        </span>
                      </template>
                    </el-tree-v2>
                  </div>
                </pane>
                <pane>
                  <div>
                    <section>表达式</section>
                    <br />
                    <el-button
                      v-for="button in buttons"
                      :key="button.type"
                      :type="button.type"
                      @click="insertSymbol(button.symbol)"
                    >
                      {{ button.symbol }}
                    </el-button>
                    <el-input
                      v-model="expression"
                      placeholder="Please enter keyword"
                      type="textarea"
                      :rows="12"
                      @input="onQueryChanged"
                    />
                  </div>
                </pane>
              </splitpanes>
            </div>

            <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <div class="bont">
          <!-- 分割线 -->
          <el-divider></el-divider>
          <el-button type="primary" @click="fulfill"> 完成</el-button>
          <el-button plain @click="toBack">关闭</el-button>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
  import {
    addTarget,
    getDwDatabaseList,
    getFieldsByModel,
    getListDatamodel,
    getTargetFunctions,
    updateTarget,
  } from '@/api/datamodel';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { nodeClick, workspaceId, rowData } = toRefs(props);
  const { proxy } = getCurrentInstance();
  console.log(nodeClick.value);
  console.log(workspaceId.value);
  console.log(rowData.value);
  const emit = defineEmits();

  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],

      code: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        // 校验
        { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' },
        // 校验
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      modelId: [{ required: true, message: '请选择表', trigger: 'blur' }],
      expression: [{ required: true, message: '请输入表达式', trigger: 'blur' }],
      store: [{ required: true, message: '请选择储存库', trigger: 'blur' }],
      isExternal: [{ required: true, message: '请选择建模方式', trigger: 'blur' }],
      type: [{ required: true, message: '请选择存储类型', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });
  const dwDatabaseList = ref([]);

  const getDwDatabaseListUtil = async (data) => {
    const res = await getDwDatabaseList({ catalogId: data });
    dwDatabaseList.value = res.data.map((item) => item);
  };

  const { form, rules } = toRefs(data);
  const toBack = () => {
    emit('toBack', true);
  };

  const treeProps = {
    value: 'value',
    label: 'label',
    children: 'children',
  };

  const dataTree = ref([
    {
      id: 413,
      label: '测试主题 001',
    },
  ]);
  const dataTreeField = ref([]);
  const expression = ref('');

  const buttons = [
    { type: '', symbol: '+' },
    { type: '', symbol: '-' },
    { type: '', symbol: '*' },
    { type: '', symbol: '/' },
    { type: '', symbol: '(' },
    { type: '', symbol: ')' },
  ];

  const insertSymbol = (symbol) => {
    expression.value += symbol;
  };

  const activeName = ref('first');
  const tableData = ref([]);

  const beforeleave = (tab, oldTab) => {
    console.log(tab, oldTab);
    return new Promise((resolve) => {
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.name &&
        form.value.code &&
        form.value.dwDatabase &&
        form.value.isExternal &&
        form.value.isFromDatasource
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const fulfill = async () => {
    console.log(nodeClick.value);
    const res = await proxy.$refs.formRef.validate((valid) => valid);
    if (!res) return;
    const data = {
      name: form.value.name,
      code: form.value.code,
      modelId: form.value.modelId,
      themeId: nodeClick.value.key,
      remark: form.value.remark,
      expression: expression.value,
      workspaceId: workspaceId.value,
    };

    console.log(data);

    console.log(rowData.value);
    if (rowData.value && rowData.value.name) {
      updateTargetDeriveUtil(data);
    } else {
      addTargetDeriveUtil(data);
    }
    // 清空数据
    // form.value = {

    // }
    // 清空数据
    // tableData.value = []
    // activeName.value = 'first'
    // toBack()
  };
  const addTargetDeriveUtil = async (data) => {
    const res = await addTarget(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgSuccess(res.msg);
    }
  };
  const updateTargetDeriveUtil = async (data) => {
    data.id = rowData.value.id;
    const res = await updateTarget(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgSuccess(res.msg);
    }
  };

  watch(
    () => nodeClick.value,
    (val) => {
      nextTick(() => {
        getDwDatabaseListUtil(val.key);
      });
    },
    {
      deep: true,
    },
  );
  const getTargetFunctionsUtil = async () => {
    const res = await getTargetFunctions();
    console.log(res);
    if (res.code === 200) {
      // tableData.value = res.data
      // res.data.forEach(item => {
      //     item.label = item.name
      //     item.value = item.id
      // })
      dataTree.value = res.data;
      console.log(res.data);
    }
  };
  const treeDataLabelOne = ref();
  const treeDataLabelTwo = ref();
  const handleNodeClick = (event, data, node) => {
    console.log(data.level);
    if (data.level == 1) return;
    treeDataLabelOne.value = data.label;
  };
  const handleNodeClickTwo = (event, data, node) => {
    console.log(data);
    treeDataLabelTwo.value = data.key;
    console.log(data.value);
    console.log(data.label);
    console.log(node);
    console.log(event);
    if (!treeDataLabelOne.value) return proxy.$modal.msgError('请先选择函数');

    expression.value += treeDataLabelOne.value + `(${treeDataLabelTwo.value})`;
    treeDataLabelOne.value = '';
  };

  const dataListator = ref([]);
  const getDataModelLogicListUtil = async () => {
    const query = {
      level: 'DWD',
      thmeId: nodeClick.value.key,
      workspaceId: workspaceId.value,
    };
    const res = await getListDatamodel(query);
    console.log(res);
    if (res.code === 200) {
      // tableData.value = res.data
      dataListator.value = res.data.map((item) => {
        return {
          label: item.code + '(' + item.name + ')',
          value: item.id,
        };
      });

      console.log(dataListator.value);
    }
  };

  const onChangeModel = (val) => {
    getFieldsByModelUtil(val);
  };
  const getFieldsByModelUtil = async (val) => {
    // 使用 val 去 dataListator 查找 对应的数据
    const itemVal = dataListator.value.find((item) => item.value === val);

    console.log(itemVal);

    const res = await getFieldsByModel({
      modelId: val,
    });
    console.log(res);
    if (res.code === 200) {
      dataTreeField.value = res.data.map((item) => {
        return {
          // 树状结构
          label: item.name + '(' + item.code + ')',
          value: item.code,
        };
      });
    }
  };

  onMounted(async () => {
    console.log(rowData.value);
    if (rowData.value && rowData.value.name) {
      form.value.targetId = rowData.value.targetId;
      form.value.name = rowData.value.name;
      form.value.code = rowData.value.code;
      form.value.remark = rowData.value.remark;

      form.value.modelId = rowData.value.modelId;
      onChangeModel(rowData.value.modelId);
      expression.value = rowData.value.expression;
    }
    await getTargetFunctionsUtil();
    await getDataModelLogicListUtil();
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 10px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    margin: 5px;
  }

  .demo-tabs {
    padding: 20px 0;
    :deep .el-tabs__content {
      padding: 20px;
      background: #ffffff;
      border-radius: 8px;
    }
  }

  .bont {
    text-align: right;
    padding: 10px 0;
  }

  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .codeCs {
    padding: 10px;
    white-space: pre;
    font-family: 'Courier New', monospace;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .rgroup {
    // border-top: 1px solid #0400ff3f;
    // border-left: 1px solid #E6E6E6;
    // border-right: 1px solid #E6E6E6;
    // border-bottom: 1px solid #E6E6E6;
    padding: 10px;
  }

  .expressionSet {
    display: flex;
    /* 使用 Flexbox 布局 */

    /* 前两个 div 固定宽度为 200px */
    > div:nth-child(1),
    > div:nth-child(2) {
      width: 200px;
    }

    /* 最后一个 div 自适应宽度 */
    > div:last-child {
      flex: 1;
      /* 自动填充剩余空间 */
    }

    div {
      border: 1px solid rgba(39, 39, 39, 0.041);
    }

    div > section {
      min-width: 200px;
      // width: 100%;
      background: #e0ebff;
      // 右边边框
      border-right: 1px solid #ffffff;
      padding: 10px;
      font-size: 12px;
    }

    margin-bottom: 20px;
  }
  .splitpanes__pane {
    display: flex;
    // justify-content: center;
    // align-items: center;
    // font-family: Helvetica, Arial, sans-serif;
    // color: rgba(255, 255, 255, 0.6);
    // font-size: 5em;
    > div:nth-child(1),
    > div:nth-child(2) {
      width: 200px;
    }

    /* 最后一个 div 自适应宽度 */
    > div:last-child {
      flex: 1;
      /* 自动填充剩余空间 */
    }

    div {
      border: 1px solid rgba(39, 39, 39, 0.041);
    }

    div > section {
      min-width: 200px;
      // width: 100%;
      background: #e0ebff;
      // 右边边框
      border-right: 1px solid #ffffff;
      padding: 10px;
      font-size: 12px;
    }

    margin-bottom: 20px;
  }
</style>
